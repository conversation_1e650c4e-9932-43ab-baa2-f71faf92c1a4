#!/bin/bash

function prj_copy_and_replace() {
    PRJ=$1
    ENV=$2
    TGR_ENV=$3

    mkdir -p "$PRJ/$TGR_ENV"
    cp -r "$PRJ/$ENV/"* "$PRJ/$TGR_ENV"

    find "$PRJ/$TGR_ENV" -type f -print0 | while IFS= read -r -d $'\0' file; do
        awk -v ENV="$ENV" -v TGR_ENV="$TGR_ENV" '{gsub(ENV, TGR_ENV)}1' "$file" > "$file.tmp" && mv "$file.tmp" "$file"
    done
}

function argo_copy_and_replace() {
    PRJ=$1
    ENV=$2
    TGR_ENV=$3
    APP=$(basename "$PRJ")

    mkdir -p "_argo/$TGR_ENV"
    cp -r "_argo/$ENV/$APP-application.yaml" "_argo/$TGR_ENV/$APP-application.yaml"
    awk -v ENV="$ENV" -v TGR_ENV="$TGR_ENV" '{gsub(ENV, TGR_ENV)}1' "_argo/$TGR_ENV/$APP-application.yaml" > "_argo/$TGR_ENV/$APP-application.yaml.tmp" && mv "_argo/$TGR_ENV/$APP-application.yaml.tmp" "_argo/$TGR_ENV/$APP-application.yaml"

    if grep -q "$APP-application.yaml" "_argo/$TGR_ENV/kustomization.yaml"
    then
        echo "match"
    else
        echo "- $APP-application.yaml" >> "_argo/$TGR_ENV/kustomization.yaml"
    fi
}

ENVS=$3

for i in $(echo $ENVS | sed "s/,/ /g")
do
    prj_copy_and_replace "$1" "$2" "$i"
    argo_copy_and_replace "$1" "$2" "$i"
done
