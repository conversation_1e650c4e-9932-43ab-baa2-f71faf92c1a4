{"type": "record", "name": "BenefitHeaderEntitlementObtainedEvent", "namespace": "cz.airbank.benman.benefit", "fields": [{"name": "benefitHeaderGeneralContractId", "type": "long", "doc": "ID of benefit header entitlement"}, {"name": "generalContractNumber", "type": "string", "doc": "General contract number (not ID)"}, {"name": "code", "type": "string", "doc": "Benefit header code"}, {"name": "type", "type": "string", "doc": "Benefit header type"}, {"name": "priority", "type": "int", "doc": "Benefit priority - lower number => higher priority"}, {"name": "conditionGroupCode", "type": ["string", "null"], "doc": "Optional condition group code that established the entitlement to the benefits header"}, {"name": "entitledFrom", "type": {"type": "string", "logicalType": "iso-zoned-date-time"}, "doc": "Beginning of entitlement"}, {"name": "entitledTo", "type": {"type": "string", "logicalType": "iso-zoned-date-time"}, "doc": "End of entitlement"}, {"name": "evaluatedOn", "type": {"type": "string", "logicalType": "iso-zoned-date-time"}, "doc": "Date and time of entitlement evaluation"}]}