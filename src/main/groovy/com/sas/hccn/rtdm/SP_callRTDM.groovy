package com.sas.hccn.rtdm

import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.ObjectMapper
import com.homecredit.sas.utils.HttpUtils
import com.homecredit.sas.utils.MappingUtils
import com.homecredit.sas.utils.enumeration.RequestMethod
import com.homecredit.sas.utils.enumeration.Status
import com.homecredit.sas.utils.model.HttpCallResponse
import com.sas.rtdm.implementation.engine.EventInfo
import org.apache.log4j.Logger

import java.nio.charset.StandardCharsets
import java.time.OffsetDateTime
import java.time.format.DateTimeFormatter

class CallRTDM implements Runnable {

    // Input variables - CHANGE THIS PART FOR CLIENT INPUTS
    String applicationId
    String applicationStatus
    String clientType
    Long cuid
    String channelCode
    String onlineRBPComment
    String productCode
    String strategyName
    String scoringResult
    Object eventTimestamp
    String eventName

    String rtdmEventId //IF109_EV_LAP_ID10_appRejected
    String rtdmHost //global variable
    String charEventTimestamp
    String correlationId

    // Output variables
    String status = Status.ERROR.getStatus()
    Long httpResponseCode;    // HTTP response code
    String errorMessage;
    String description;
    Long CUID;
    String OFFER_ID;
    String EMAIL_ADDRESS;
    String MOBILE_PHONE;
    Long TPL_ID;
    String TPL_LANGUAGE;
    Long TRACK_RESPONSE_DAYS;
    String TREATMENT_SK;
    String RESPTRACKING_CD;
    String COMMUNICATION_CD;
    String CONTRACT_NUMBER;
    Long EVALUATION_DAYS_TR;
    String CUSTOM_INFO1;
    String CUSTOM_INFO2;
    String CUSTOM_INFO3;
    String CUSTOM_INFO4;
    String CUSTOM_INFO5;
    String CUSTOM_INFO6;
    String CUSTOM_INFO7;
    String CUSTOM_INFO8;
    String CUSTOM_INFO9;
    String CUSTOM_INFO10;
    String CUSTOM_SUBJ_INFO1;
    String CUSTOM_SUBJ_INFO2;
    String CUSTOM_SUBJ_INFO3;
    String address_client;
    String agent_id;
    String amt_annuity;
    String amt_credit_total;
    String callback_call_list;
    Object callback_time;
    String client_phone_number2;
    String client_phone_number3;
    String client_phone_number4;
    String client_phone_number5;
    String clx_app_status;
    String cnt_instalment;
    String code_timezone;
    String customer_national_id;
    Object date_auto_cancel;
    Object date_clz_visit;
    String date_of_birth;
    Object date_promise;
    String father_name;
    Object first_due_date;
    String gender;
    String CH_ID;
    String last_first_name;
    Long max_credit_amount;
    String max_emi;
    String product_type;
    String script_type;
    String segment_info;
    String shift;
    String sort_key;
    Long switch_id;
    String text_loan_purpose;
    String text_note;

    // Logger
    private static final Logger log = Logger.getLogger('groovyLog');

    // Event info
    EventInfo evtInfo;

    // Constants
    private final String resourcePrefix = "/RTDM/rest/runtime/decisions/"
    private final Long version = 1;
    private final String clientTimeZone = "Asia/Jakarta";

    @Override
    void run() {

        //start log info
        log.info("Starting process CallRTDMCampaign...");

        log.info("CallRTDMCampaign - host: $rtdmHost");
        log.info("CallRTDMCampaign - resource prefix: $resourcePrefix");
        log.info("CallRTDMCampaign - applicationId: $applicationId");
        log.info("CallRTDMCampaign - applicationStatus: $applicationStatus");
        log.info("CallRTDMCampaign - clientType: $clientType");
        log.info("CallRTDMCampaign - channelCode: $channelCode");
        log.info("CallRTDMCampaign - cuid: $cuid");
        log.info("CallRTDMCampaign - onlineRBPComment: $onlineRBPComment");
        log.info("CallRTDMCampaign - eventTimestamp: $eventTimestamp");
        log.info("CallRTDMCampaign - eventName: $eventName");

        if (rtdmEventId != null && !rtdmEventId.isEmpty()) {
            if (eventTimestamp instanceof GregorianCalendar) {
                charEventTimestamp = DateTimeFormatter.ISO_OFFSET_DATE_TIME.format(((GregorianCalendar) eventTimestamp).toZonedDateTime())
            }
            log.info("CallRTDMCampaign - charEventTimestamp: $charEventTimestamp");
            // mapping of variables to request object - CHANGE THIS PART FOR MAPPING DIFFERENT CLIENT INPUTS
            Request request = new Request(
                    version: version,
                    correlationId: correlationId,
                    clientTimeZone: clientTimeZone,
                    inputs: new RequestInputs(
                            applicationId: applicationId,
                            applicationStatus: applicationStatus,
                            clientType: clientType,
                            cuid: cuid,
                            channelCode: channelCode,
                            onlineRBPComment: onlineRBPComment,
                            productCode: productCode,
                            strategyName: strategyName,
                            scoringResult: scoringResult,
                            eventTimestamp: charEventTimestamp,
                            eventName: eventName
                    )
            )

            ObjectMapper mapper = new ObjectMapper();
            String jsonMessage = mapper.writeValueAsString(request)
            byte[] data = jsonMessage.getBytes(StandardCharsets.UTF_8)

            // Setting API variables
            String uri = rtdmHost + resourcePrefix + rtdmEventId;
            log.info("CallRTDMCampaign - Endpoint URL: $uri");
            log.info("CallRTDMCampaign - Request: $jsonMessage");

            HttpCallResponse httpCallResponse = HttpUtils.runHttpProcess(
                    evtInfo.getEventName(),
                    evtInfo.getSimulationDate().getTime(),
                    uri,
                    null,
                    [(HttpUtils.CONTENT_TYPE): "application/json"],
                    RequestMethod.POST,
                    data
            );

            httpResponseCode = httpCallResponse.getHttpResponseCode();
            status = httpCallResponse.getStatus().getStatus();
            errorMessage = httpCallResponse.getErrorMessage();
            String responseString = httpCallResponse.getResponse()
            if (responseString == null) {
                return
            }

            log.info("CallRTDMCampaign - httpResponseCode:" + httpResponseCode);
            log.info("CallRTDMCampaign - response:" + responseString);

            MyResponse responseObject = MappingUtils.mapToObject(responseString, MyResponse.class)
            if (responseObject == null) {
                status = Status.ERROR.getStatus();
                errorMessage = "Failed to map response. Wrong response data format."
                return
            }

            if (httpResponseCode == 201) {

                // setting values - CHANGE THIS TO REFLECT OUTPUT FROM RTDM PROCESS
                status = responseObject.outputs.status;
                description = responseObject.outputs.description;
                CUID = responseObject.outputs.cuid;
                OFFER_ID = responseObject.outputs.offer_id;
                EMAIL_ADDRESS = responseObject.outputs.email_address;
                MOBILE_PHONE = responseObject.outputs.mobile_phone;
                TPL_ID = responseObject.outputs.tpl_id;
                TPL_LANGUAGE = responseObject.outputs.tpl_language;
                TRACK_RESPONSE_DAYS = responseObject.outputs.track_response_days;
                TREATMENT_SK = responseObject.outputs.treatment_sk;
                RESPTRACKING_CD = responseObject.outputs.resptracking_cd;
                COMMUNICATION_CD = responseObject.outputs.communication_cd;
                CONTRACT_NUMBER = responseObject.outputs.contract_number;
                EVALUATION_DAYS_TR = responseObject.outputs.evaluation_days_tr;
                CUSTOM_INFO1 = responseObject.outputs.custom_info1;
                CUSTOM_INFO2 = responseObject.outputs.custom_info2;
                CUSTOM_INFO3 = responseObject.outputs.custom_info3;
                CUSTOM_INFO4 = responseObject.outputs.custom_info4;
                CUSTOM_INFO5 = responseObject.outputs.custom_info5;
                CUSTOM_INFO6 = responseObject.outputs.custom_info6;
                CUSTOM_INFO7 = responseObject.outputs.custom_info7;
                CUSTOM_INFO8 = responseObject.outputs.custom_info8;
                CUSTOM_INFO9 = responseObject.outputs.custom_info9;
                CUSTOM_INFO10 = responseObject.outputs.custom_info10;
                CUSTOM_SUBJ_INFO1 = responseObject.outputs.custom_subj_info1;
                CUSTOM_SUBJ_INFO2 = responseObject.outputs.custom_subj_info2;
                CUSTOM_SUBJ_INFO3 = responseObject.outputs.custom_subj_info3;
                address_client = responseObject.outputs.address_client;
                agent_id = responseObject.outputs.agent_id;
                amt_annuity = responseObject.outputs.amt_annuity;
                amt_credit_total = responseObject.outputs.amt_credit_total;
                callback_call_list = responseObject.outputs.callback_call_list;
                if (responseObject.outputs.callback_time != null && !responseObject.outputs.callback_time.isEmpty()) {
                    callback_time = GregorianCalendar.from(OffsetDateTime.parse(responseObject.outputs.callback_time).toZonedDateTime())
                }
                client_phone_number2 = responseObject.outputs.client_phone_number2;
                client_phone_number3 = responseObject.outputs.client_phone_number3;
                client_phone_number4 = responseObject.outputs.client_phone_number4;
                client_phone_number5 = responseObject.outputs.client_phone_number5;
                clx_app_status = responseObject.outputs.clx_app_status;
                cnt_instalment = responseObject.outputs.cnt_instalment;
                code_timezone = responseObject.outputs.code_timezone;
                customer_national_id = responseObject.outputs.customer_national_id;
                if (responseObject.outputs.date_auto_cancel != null && !responseObject.outputs.date_auto_cancel.isEmpty()) {
                    date_auto_cancel = GregorianCalendar.from(OffsetDateTime.parse(responseObject.outputs.date_auto_cancel).toZonedDateTime())
                }
                if (responseObject.outputs.date_clz_visit != null && !responseObject.outputs.date_clz_visit.isEmpty()) {
                    date_clz_visit = GregorianCalendar.from(OffsetDateTime.parse(responseObject.outputs.date_clz_visit).toZonedDateTime())
                }
                date_of_birth = responseObject.outputs.date_of_birth;
                if (responseObject.outputs.date_promise != null && !responseObject.outputs.date_promise.isEmpty()) {
                    date_promise = GregorianCalendar.from(OffsetDateTime.parse(responseObject.outputs.date_promise).toZonedDateTime())
                }
                father_name = responseObject.outputs.father_name;
                if (responseObject.outputs.first_due_date != null && !responseObject.outputs.first_due_date.isEmpty()) {
                    first_due_date = GregorianCalendar.from(OffsetDateTime.parse(responseObject.outputs.first_due_date).toZonedDateTime())
                }
                gender = responseObject.outputs.gender;
                CH_ID = responseObject.outputs.ch_id;
                last_first_name = responseObject.outputs.last_first_name;
                max_credit_amount = responseObject.outputs.max_credit_amount;
                max_emi = responseObject.outputs.max_emi;
                product_type = responseObject.outputs.product_type;
                script_type = responseObject.outputs.script_type;
                segment_info = responseObject.outputs.segment_info;
                shift = responseObject.outputs.shift;
                sort_key = responseObject.outputs.sort_key;
                switch_id = responseObject.outputs.switch_id;
                text_loan_purpose = responseObject.outputs.text_loan_purpose;
                text_note = responseObject.outputs.text_note;

            }
        } else {
            log.trace("CallRTDMCampaign - No input clients");
        }
    }
}

// example of request - CHANGE THIS PART FOR MAPPING DIFFERENT CLIENT INPUTS
class Request implements Serializable {
    Long version
    String correlationId
    String clientTimeZone
    RequestInputs inputs
}

class RequestInputs implements Serializable {
    String applicationId
    String applicationStatus
    String clientType
    Long cuid
    String channelCode
    String onlineRBPComment
    String productCode
    String strategyName
    String scoringResult
    String eventTimestamp
    String eventName
}

// example of response - CHANGE THIS TO REFLECT OUTPUT FROM RTDM PROCESS
class MyResponse implements Serializable {
    Long version;
    String correlationId;
    String startTimestamp;
    String endTimestamp;
    ResponseItem outputs;
}

class ResponseItem implements Serializable {
    String status;
    String description;
    @JsonProperty("CUID")
    Long cuid;
    @JsonProperty("OFFER_ID")
    String offer_id;
    @JsonProperty("EMAIL_ADDRESS")
    String email_address;
    @JsonProperty("MOBILE_PHONE")
    String mobile_phone;
    @JsonProperty("TPL_ID")
    Long tpl_id;
    @JsonProperty("TPL_LANGUAGE")
    String tpl_language;
    @JsonProperty("TRACK_RESPONSE_DAYS")
    Long track_response_days;
    @JsonProperty("TREATMENT_SK")
    String treatment_sk;
    @JsonProperty("RESPTRACKING_CD")
    String resptracking_cd;
    @JsonProperty("COMMUNICATION_CD")
    String communication_cd;
    @JsonProperty("CONTRACT_NUMBER")
    String contract_number;
    @JsonProperty("EVALUATION_DAYS_TR")
    Long evaluation_days_tr;
    @JsonProperty("CUSTOM_INFO1")
    String custom_info1;
    @JsonProperty("CUSTOM_INFO2")
    String custom_info2;
    @JsonProperty("CUSTOM_INFO3")
    String custom_info3;
    @JsonProperty("CUSTOM_INFO4")
    String custom_info4;
    @JsonProperty("CUSTOM_INFO5")
    String custom_info5;
    @JsonProperty("CUSTOM_INFO6")
    String custom_info6;
    @JsonProperty("CUSTOM_INFO7")
    String custom_info7;
    @JsonProperty("CUSTOM_INFO8")
    String custom_info8;
    @JsonProperty("CUSTOM_INFO9")
    String custom_info9;
    @JsonProperty("CUSTOM_INFO10")
    String custom_info10;
    @JsonProperty("CUSTOM_SUBJ_INFO1")
    String custom_subj_info1;
    @JsonProperty("CUSTOM_SUBJ_INFO2")
    String custom_subj_info2;
    @JsonProperty("CUSTOM_SUBJ_INFO3")
    String custom_subj_info3;
    String address_client;
    String agent_id;
    String amt_annuity;
    String amt_credit_total;
    String callback_call_list;
    String callback_time;
    String client_phone_number2;
    String client_phone_number3;
    String client_phone_number4;
    String client_phone_number5;
    String clx_app_status;
    String cnt_instalment;
    String code_timezone;
    String customer_national_id;
    String date_auto_cancel;
    String date_clz_visit;
    String date_of_birth;
    String date_promise;
    String father_name;
    String first_due_date;
    String gender;
    @JsonProperty("CH_ID")
    String ch_id;
    String last_first_name;
    Long max_credit_amount;
    String max_emi;
    String product_type;
    String script_type;
    String segment_info;
    String shift;
    String sort_key;
    Long switch_id;
    String text_loan_purpose;
    String text_note;
}
