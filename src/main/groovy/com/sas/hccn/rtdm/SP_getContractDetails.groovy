package com.sas.hccn.rtdm

import com.homecredit.sas.utils.HttpUtils
import com.homecredit.sas.utils.MappingUtils
import com.homecredit.sas.utils.PropertiesUtils
import com.homecredit.sas.utils.TokenUtils
import com.homecredit.sas.utils.enumeration.RequestMethod
import com.homecredit.sas.utils.enumeration.Status
import com.homecredit.sas.utils.model.HttpCallResponse
import com.homecredit.sas.utils.model.PropertiesResponse
import com.homecredit.sas.utils.model.TokenResponse
import com.sas.analytics.ph.common.RTDMTable
import com.sas.analytics.ph.common.RTDMTable.Row
import com.sas.analytics.ph.common.jaxb.DataTypes
import com.sas.rtdm.implementation.engine.EventInfo
import org.apache.log4j.Logger

import javax.sql.DataSource

/**
 * Get Contract details
 * @version 05/11/22-004
 */
class GetDetailForClientList implements Runnable {

    private Map <String, DataSource> mapJDBC = null;
    void setMapJDBC(Map <String, DataSource> input) {
        mapJDBC = input;
    }

    // Input variables
    String contractCode;

    // Output variables
    String status = Status.ERROR.getStatus()
    Long httpResponseCode;    // HTTP response code
    String errorMessage;
    RTDMTable ClientDetails;

    // Logger
    private static final Logger log = Logger.getLogger('groovyLog');

    // Event info
    EventInfo evtInfo;

    // Constants
    private final String resourcePrefix = "/rest/v12/contracts/";
    private final String resourceSuffix = "?projection=BUSINESS_EVENT_DEFAULT"

    // Variables from properties
    private String username;
    private String password;
    private String host;
    private String authPath; // = "https://sso.vn00c1.vn.infra/auth/realms/hci/protocol/openid-connect/token";

    private final String CONFIG_FILE = "/sas/groovy/Connections/openid.properties";

    @Override
    void run() {

        //start log info
        log.info("Starting process GetContractDetails...");
        log.info("GetContractDetails - contractCode: $contractCode");

        PropertiesResponse propertiesResponse = PropertiesUtils.getProperties(CONFIG_FILE)
        if (propertiesResponse.getProperties() == null) {
            status = propertiesResponse.getStatus().getStatus();
            errorMessage = propertiesResponse.getErrorMessage();
            return;
        }
        Properties config = propertiesResponse.getProperties();

        username = config.getProperty("openid.username");
        password = config.getProperty("openid.password");
        host = config.getProperty("openid.host");
        authPath = config.getProperty("openid.authpath");

        log.info("GetContracts - host: $host");
        log.info("GetContracts - resource prefix: $resourcePrefix");

        if (contractCode != null && !contractCode.isEmpty()) {

            // Create empty table
            ClientDetails = new RTDMTable();
            ClientDetails.columnAdd("code", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("Status", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("Type", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("BusinessEvents.type", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("BusinessEvents.salesroomcode", DataTypes.STRING, Collections.emptyList());

            // Setting API variables
            TokenResponse tokenResponse = TokenUtils.getToken(
                    mapJDBC,
                    evtInfo.getEventName(),
                    evtInfo.getSimulationDate().getTime(),
                    username,
                    password,
                    authPath,
                    "MA_TEMP_JDBC",
                    "RTDM_TEMP.SSO_TOKEN"
            )
            if (tokenResponse.getToken() == null) {
                status = tokenResponse.getStatus().getStatus();
                errorMessage = tokenResponse.getErrorMessage();
                return;
            }
            String authToken = tokenResponse.getToken();

            String uri = host + resourcePrefix + contractCode + resourceSuffix;
            log.info("GetContractDetails - Endpoint URL: $uri");

            HttpCallResponse httpCallResponse = HttpUtils.runHttpProcess(
                    evtInfo.getEventName(),
                    evtInfo.getSimulationDate().getTime(),
                    uri,
                    HttpUtils.getBearerAuthToken(authToken),
                    [(HttpUtils.CONTENT_TYPE): "application/json"],
                    RequestMethod.GET,
                    null
            );

            httpResponseCode = httpCallResponse.getHttpResponseCode();
            status = httpCallResponse.getStatus().getStatus();
            errorMessage = httpCallResponse.getErrorMessage();
            String responseString = httpCallResponse.getResponse()
            if (responseString == null) {
                return
            }

            log.trace("GetContractDetails - httpResponseCode:" + httpResponseCode);
            log.trace("GetContractDetails - response:" + responseString);

            Response responseObject = MappingUtils.mapToObject(responseString, Response.class)
            if (responseObject == null) {
                status = Status.ERROR.getStatus();
                errorMessage = "Failed to map response. Wrong response data format."
                return
            }

            if (httpResponseCode == 200) {
                if (responseObject.businessEvents == null || responseObject.businessEvents.isEmpty()) {
                    Row newRow = ClientDetails.rowAdd();
                    newRow.columnDataSet("code", responseObject.code);
                    newRow.columnDataSet("Status", responseObject.status);
                    newRow.columnDataSet("Type", responseObject.type);
                } else {
                    for (BusinessEvent event : responseObject.businessEvents) {
                        Row newRow = ClientDetails.rowAdd();
                        newRow.columnDataSet("code", responseObject.code);
                        newRow.columnDataSet("Status", responseObject.status);
                        newRow.columnDataSet("Type", responseObject.type);
                        newRow.columnDataSet("BusinessEvents.type", event.type);
                        newRow.columnDataSet("BusinessEvents.salesroomcode", event.salesroomCode);
                    }
                }

                status = "OK";
            }
        } else {
            log.trace("GetContractDetails - No input clients");
        }
    }
}

class Response implements Serializable {
    String code;
    String status;
    String type;
    List<BusinessEvent> businessEvents;
}

class BusinessEvent implements Serializable {
    String type;
    String businessDate;
    String salesroomCode;
}
