package com.sas.hccn.rtdm

import com.homecredit.sas.utils.enumeration.Status
import com.sas.analytics.ph.common.RTDMTable
import com.sas.analytics.ph.common.RTDMTable.Row
import com.sas.analytics.ph.common.jaxb.DataTypes
import com.sas.rtdm.implementation.engine.EventInfo
import org.apache.log4j.Logger
import javax.sql.DataSource
import java.sql.*

/**
 * Get FT CONTRACT AD
 * @version 24/08/14-001
 */
class GetDetailForClientList implements Runnable {

    private Map <String, DataSource> mapJDBC = null;
    void setMapJDBC(Map <String, DataSource> input) {
        mapJDBC = input;
    }

    // Input variables
    Long cuid;


    // Output variables
    String status = Status.ERROR.getStatus();
    String errorMessage;
    RTDMTable datagrid;

    // Event info
    EventInfo evtInfo;
    // Logger
    private static final Logger log = Logger.getLogger('groovyLog');

    @Override
    void run() {
        if (cuid == null) {
            log.trace("SP_getFtContractAd: cuid is null");
            return;
        }

        // Create empty table
        datagrid = new RTDMTable();
        datagrid.columnAdd("ID_CUID", DataTypes.INT, Collections.emptyList());
        datagrid.columnAdd("SKP_CLIENT", DataTypes.INT, Collections.emptyList());
        datagrid.columnAdd("SKP_CREDIT_CASE", DataTypes.INT, Collections.emptyList());
        datagrid.columnAdd("CODE_PRODUCT", DataTypes.STRING, Collections.emptyList());
        datagrid.columnAdd("CONTRACT_NUMBER", DataTypes.STRING, Collections.emptyList());
        datagrid.columnAdd("NAME_CREDIT_STATUS", DataTypes.STRING, Collections.emptyList());
        datagrid.columnAdd("NAME_PRODUCT", DataTypes.STRING, Collections.emptyList());
        datagrid.columnAdd("NAME_TYPE_INSURANCE", DataTypes.STRING, Collections.emptyList());
        datagrid.columnAdd("AMT_CREDIT", DataTypes.INT, Collections.emptyList());
        datagrid.columnAdd("AMT_CREDIT_TOTAL", DataTypes.INT, Collections.emptyList());
        datagrid.columnAdd("AMT_DOWN_PAYMENT", DataTypes.INT, Collections.emptyList());
        datagrid.columnAdd("CNT_PAYMENTS", DataTypes.INT, Collections.emptyList());
        datagrid.columnAdd("RATE_INTEREST", DataTypes.FLOAT, Collections.emptyList());
        datagrid.columnAdd("DATE_CONTRACT_SIGN", DataTypes.STRING, Collections.emptyList());
        datagrid.columnAdd("DATE_DECISION", DataTypes.STRING, Collections.emptyList());
        datagrid.columnAdd("AMT_DISBURSED", DataTypes.INT, Collections.emptyList());
        datagrid.columnAdd("DAYS_IN_PREPROCESS", DataTypes.INT, Collections.emptyList());
        datagrid.columnAdd("DATE_CONTRACT_LAST_UPDATED", DataTypes.STRING, Collections.emptyList());
        datagrid.columnAdd("FLAG_LAST_SIGNED", DataTypes.STRING, Collections.emptyList());
        datagrid.columnAdd("FLAG_LAST_CONTRACT", DataTypes.STRING, Collections.emptyList());
        datagrid.columnAdd("FLAG_LAST_POS_CONTRACT", DataTypes.STRING, Collections.emptyList());
        datagrid.columnAdd("CNT_INSTALMENT", DataTypes.INT, Collections.emptyList());
        datagrid.columnAdd("CNT_REMAINING_INSTALMENT", DataTypes.INT, Collections.emptyList());
        datagrid.columnAdd("AMT_INSTALMENT", DataTypes.INT, Collections.emptyList());
        datagrid.columnAdd("CNT_DAYS_PAST_DUE", DataTypes.INT, Collections.emptyList());
        datagrid.columnAdd("CODE_CONTRACT_SECUR_STATUS", DataTypes.STRING, Collections.emptyList());
        datagrid.columnAdd("DATE_LAST_EARLY_REPAY", DataTypes.STRING, Collections.emptyList());
        datagrid.columnAdd("FLAG_ADA", DataTypes.STRING, Collections.emptyList());
        datagrid.columnAdd("AMT_OUTSTANDING_BALANCE", DataTypes.INT, Collections.emptyList());
        datagrid.columnAdd("DAYS_SIGNED_NOT_DISBURSED", DataTypes.INT, Collections.emptyList());
        datagrid.columnAdd("DATE_DISBURSEMENT", DataTypes.STRING, Collections.emptyList());
        datagrid.columnAdd("DATE_NEXT_INST", DataTypes.STRING, Collections.emptyList());
        datagrid.columnAdd("SUM_DPD_3M", DataTypes.INT, Collections.emptyList());
        datagrid.columnAdd("SUM_DPD_6M", DataTypes.INT, Collections.emptyList());
        datagrid.columnAdd("DATE_EFFECTIVE", DataTypes.STRING, Collections.emptyList());
        datagrid.columnAdd("SKP_PROC_INSERTED", DataTypes.INT, Collections.emptyList());
        datagrid.columnAdd("SKP_PROC_UPDATED", DataTypes.INT, Collections.emptyList());
        datagrid.columnAdd("FLAG_DELETED", DataTypes.STRING, Collections.emptyList());
        datagrid.columnAdd("DTIME_INSERTED", DataTypes.STRING, Collections.emptyList());
        datagrid.columnAdd("DTIME_UPDATED", DataTypes.STRING, Collections.emptyList());
        datagrid.columnAdd("SKP_PRODUCT", DataTypes.INT, Collections.emptyList());
        datagrid.columnAdd("SKP_CREDIT_TYPE", DataTypes.INT, Collections.emptyList());
        datagrid.columnAdd("COLL_SEGMENT_NAME", DataTypes.STRING, Collections.emptyList());
        datagrid.columnAdd("CNT_DAYS_PAST_DUE_TOL_MAX", DataTypes.INT, Collections.emptyList());
        datagrid.columnAdd("CNT_DAYS_PAST_DUE_MAX", DataTypes.INT, Collections.emptyList());

        Connection conn = mapJDBC.get('MA_TEMP_JDBC').getConnection()
        PreparedStatement st = null
        Statement stmt = conn.createStatement();
        try {
            if (conn != null) {
                log.trace("SP_getFtContractAd: Obtaining data from DB");

                String query = "SELECT * FROM SDM.FT_CONTRACT_AD WHERE ID_CUID = ?"
                st = conn.prepareStatement(query);

                log.trace("SP_getFtContractAd: SQL parameter cuid: 1 = " + cuid);
                st.setLong(1, cuid);

                log.trace("SP_getFtContractAd: SQL query: " + query);
                ResultSet rs = st.executeQuery();

                while (rs.next()) {
                    Row newRow = datagrid.rowAdd();
                    newRow.columnDataSet("ID_CUID", rs.getLong("ID_CUID"));
                    newRow.columnDataSet("SKP_CLIENT", rs.getLong("SKP_CLIENT"));
                    newRow.columnDataSet("SKP_CREDIT_CASE", rs.getLong("SKP_CREDIT_CASE"));
                    newRow.columnDataSet("CODE_PRODUCT", rs.getString("CODE_PRODUCT"));
                    newRow.columnDataSet("CONTRACT_NUMBER", rs.getString("CONTRACT_NUMBER"));
                    newRow.columnDataSet("NAME_CREDIT_STATUS", rs.getString("NAME_CREDIT_STATUS"));
                    newRow.columnDataSet("NAME_PRODUCT", rs.getString("NAME_PRODUCT"));
                    newRow.columnDataSet("NAME_TYPE_INSURANCE", rs.getString("NAME_TYPE_INSURANCE"));
                    newRow.columnDataSet("AMT_CREDIT", rs.getLong("AMT_CREDIT"));
                    newRow.columnDataSet("AMT_CREDIT_TOTAL", rs.getLong("AMT_CREDIT_TOTAL"));
                    newRow.columnDataSet("AMT_DOWN_PAYMENT", rs.getLong("AMT_DOWN_PAYMENT"));
                    newRow.columnDataSet("CNT_PAYMENTS", rs.getLong("CNT_PAYMENTS"));
                    newRow.columnDataSet("RATE_INTEREST", rs.getDouble("RATE_INTEREST"));
                    newRow.columnDataSet("DATE_CONTRACT_SIGN", rs.getString("DATE_CONTRACT_SIGN"));
                    newRow.columnDataSet("DATE_DECISION", rs.getString("DATE_DECISION"));
                    newRow.columnDataSet("AMT_DISBURSED", rs.getLong("AMT_DISBURSED"));
                    newRow.columnDataSet("DAYS_IN_PREPROCESS", rs.getLong("DAYS_IN_PREPROCESS"));
                    newRow.columnDataSet("DATE_CONTRACT_LAST_UPDATED", rs.getString("DATE_CONTRACT_LAST_UPDATED"));
                    newRow.columnDataSet("FLAG_LAST_SIGNED", rs.getString("FLAG_LAST_SIGNED"));
                    newRow.columnDataSet("FLAG_LAST_CONTRACT", rs.getString("FLAG_LAST_CONTRACT"));
                    newRow.columnDataSet("FLAG_LAST_POS_CONTRACT", rs.getString("FLAG_LAST_POS_CONTRACT"));
                    newRow.columnDataSet("CNT_INSTALMENT", rs.getLong("CNT_INSTALMENT"));
                    newRow.columnDataSet("CNT_REMAINING_INSTALMENT", rs.getLong("CNT_REMAINING_INSTALMENT"));
                    newRow.columnDataSet("AMT_INSTALMENT", rs.getLong("AMT_INSTALMENT"));
                    newRow.columnDataSet("CNT_DAYS_PAST_DUE", rs.getLong("CNT_DAYS_PAST_DUE"));
                    newRow.columnDataSet("CODE_CONTRACT_SECUR_STATUS", rs.getString("CODE_CONTRACT_SECUR_STATUS"));
                    newRow.columnDataSet("DATE_LAST_EARLY_REPAY", rs.getString("DATE_LAST_EARLY_REPAY"));
                    newRow.columnDataSet("FLAG_ADA", rs.getString("FLAG_ADA"));
                    newRow.columnDataSet("AMT_OUTSTANDING_BALANCE", rs.getLong("AMT_OUTSTANDING_BALANCE"));
                    newRow.columnDataSet("DAYS_SIGNED_NOT_DISBURSED", rs.getLong("DAYS_SIGNED_NOT_DISBURSED"));
                    newRow.columnDataSet("DATE_DISBURSEMENT", rs.getString("DATE_DISBURSEMENT"));
                    newRow.columnDataSet("DATE_NEXT_INST", rs.getString("DATE_NEXT_INST"));
                    newRow.columnDataSet("SUM_DPD_3M", rs.getLong("SUM_DPD_3M"));
                    newRow.columnDataSet("SUM_DPD_6M", rs.getLong("SUM_DPD_6M"));
                    newRow.columnDataSet("DATE_EFFECTIVE", rs.getString("DATE_EFFECTIVE"));
                    newRow.columnDataSet("SKP_PROC_INSERTED", rs.getLong("SKP_PROC_INSERTED"));
                    newRow.columnDataSet("SKP_PROC_UPDATED", rs.getLong("SKP_PROC_UPDATED"));
                    newRow.columnDataSet("FLAG_DELETED", rs.getString("FLAG_DELETED"));
                    newRow.columnDataSet("DTIME_INSERTED", rs.getString("DTIME_INSERTED"));
                    newRow.columnDataSet("DTIME_UPDATED", rs.getString("DTIME_UPDATED"));
                    newRow.columnDataSet("SKP_PRODUCT", rs.getLong("SKP_PRODUCT"));
                    newRow.columnDataSet("SKP_CREDIT_TYPE", rs.getLong("SKP_CREDIT_TYPE"));
                    newRow.columnDataSet("COLL_SEGMENT_NAME", rs.getString("COLL_SEGMENT_NAME"));
                    newRow.columnDataSet("CNT_DAYS_PAST_DUE_TOL_MAX", rs.getLong("CNT_DAYS_PAST_DUE_TOL_MAX"));
                    newRow.columnDataSet("CNT_DAYS_PAST_DUE_MAX", rs.getLong("CNT_DAYS_PAST_DUE_MAX"));
                }
                status = Status.OK.getStatus();
            }
        } catch (SQLException e) {
            errorMessage = e.getLocalizedMessage();
            log.error("SP_getFtContractAd: oracle error:" + e)
        } finally {
            if (st != null) {
                st.close();
            }
            if (stmt != null) {
                stmt.close();
            }
            if (conn != null) {
                conn.close();
            }
        }
        log.trace("SP_getFtContractAd - End");
    }
}