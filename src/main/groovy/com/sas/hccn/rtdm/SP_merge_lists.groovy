package com.sas.hccn.rtdm

import com.homecredit.sas.utils.enumeration.Status
import com.sas.rtdm.implementation.engine.EventInfo
import org.apache.log4j.Logger

class SP_merge_lists implements Runnable {

    //input
    List<Long> listA;
    List<Long> listB;

    //output
    List<Long> outputList;
    String status = Status.ERROR.getStatus()

    // Event info
    EventInfo evtInfo;
    // Logger
    private static final Logger log = Logger.getLogger('groovyLog');

    void run() {
        log.trace("SP_cuid_check_CH - Start");
        if (listA == null && listB == null) {
            log.trace("SP_merge_lists: both lists are null, returning empty list");
            outputList = List.of()
            status = Status.OK.getStatus()
            return
        }

        log.trace("SP_cuid_check_CH: merging lists " + listA.toListString() + " and " + listB.toListString());
        outputList = (listA + listB).toSet().toList() as List<Long>

        log.trace("SP_cuid_check_CH: merged list: " + outputList.toListString());

        log.trace("SP_cuid_check_CH - End");
        status = Status.OK.getStatus()
    }
}