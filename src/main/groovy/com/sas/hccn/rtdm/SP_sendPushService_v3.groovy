package com.sas.hccn.rtdm;

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.ObjectMapper
import com.homecredit.sas.utils.PropertiesUtils
import com.homecredit.sas.utils.model.PropertiesResponse
import org.apache.log4j.Logger

import java.nio.charset.StandardCharsets
import java.time.format.DateTimeFormatter

/**
 * @version 08/07/24-006
 */
class GetDetailForClientList implements Runnable {

    // Input variables
    String push_token;
    String externalId;
    String systemCode;
    String messageCode;
    Object expires;
    String priority;
    String reportLevel;
    String reportContentType;
    String text;
    String logicalApplication;

    String CONTRACT_NUM;
    Long CUID;
    String FCM_TITLE;
    String SAS_LINK;
    String SAS_MESSAGE_TYPE;
    String SAS_PICTURE;
    String SAS_TEMPLATE_ID;
    String SCREEN_LABEL;

    //NEW V2 Variables
    def now = new Date();
    def CREATED_TIMESTAMP = now.toTimestamp().getTime();
    String ID_MYHC_EID;
    String ID_MYHC_MESSAGE_SUBTYPE;
    String ID_MYHC_MESSAGE_TYPE;
    String ID_MYHC_SHORT_MESSAGE;
    String FCM_MUTABLE_CONTENT;
    String FCM_CONTENT_AVAILABLE;
    String GMA_INBOX_CATEGORY;

    // Output variables
    String status = "ERROR";            // either "OK" or "ERROR"
    String errorMessage;

    // Internal variables
    private static final Logger log = Logger.getLogger('groovyLog');
    private static final Logger requestReplyLog = Logger.getLogger('groovyRequestReply')

    private final String CONFIG_FILE = "/sas/groovy/Connections/rabbitmq.properties";
    private Properties config = new Properties();

    @Override
    void run() {
        //start log info
        log.info("Starting process SendPushProspect to RabbitMQ...");
        log.info("SendPushProspect - push_token: $push_token");
        log.info("SendPushProspect - externalId: $externalId");
        log.info("SendPushProspect - systemCode: $systemCode");
        log.info("SendPushProspect - messageCode: $messageCode");
        log.info("SendPushProspect - expires: $expires");
        log.info("SendPushProspect - priority: $priority");
        log.info("SendPushProspect - reportLevel: $reportLevel");
        log.info("SendPushProspect - reportContentType: $reportContentType");
        log.info("SendPushProspect - text: $text");
        log.info("SendPushProspect - logicalApplication: $logicalApplication");
        log.info("SendPushProspect - CONTRACT_NUM: $CONTRACT_NUM");
        log.info("SendPushProspect - CUID: $CUID");
        log.info("SendPushProspect - FCM_TITLE: $FCM_TITLE");
        log.info("SendPushProspect - SAS_LINK: $SAS_LINK");
        log.info("SendPushProspect - SAS_MESSAGE_TYPE: $SAS_MESSAGE_TYPE");
        log.info("SendPushProspect - SAS_PICTURE: $SAS_PICTURE");
        log.info("SendPushProspect - SAS_TEMPLATE_ID: $SAS_TEMPLATE_ID");
        log.info("SendPushProspect - SCREEN_LABEL: $SCREEN_LABEL");

        //NEW V2 Log
        log.info("SendPushProspect - CREATED_TIMESTAMP: $CREATED_TIMESTAMP");
        log.info("SendPushProspect - ID_MYHC_EID: $ID_MYHC_EID");
        log.info("SendPushProspect - ID_MYHC_MESSAGE_SUBTYPE: $ID_MYHC_MESSAGE_SUBTYPE");
        log.info("SendPushProspect - ID_MYHC_MESSAGE_TYPE: $ID_MYHC_MESSAGE_TYPE");
        log.info("SendPushProspect - ID_MYHC_SHORT_MESSAGE: $ID_MYHC_SHORT_MESSAGE");
        log.info("SendPushProspect - FCM_MUTABLE_CONTENT: $FCM_MUTABLE_CONTENT");
        log.info("SendPushProspect - FCM_CONTENT_AVAILABLE: $FCM_CONTENT_AVAILABLE");
        log.info("SendPushProspect - GMA_INBOX_CATEGORY: $GMA_INBOX_CATEGORY");

        if (!externalId) {
            errorMessage = "externalId is null or empty"
            return;
        }
        if (!systemCode) {
            errorMessage = "systemCode is null or empty"
            return;
        }
        if (!messageCode) {
            errorMessage = "messageCode is null or empty"
            return;
        }
        if (!text) {
            errorMessage = "text is null or empty"
            return;
        }
        if (!logicalApplication) {
            errorMessage = "logicalApplication is null or empty"
            return;
        }

        PropertiesResponse propertiesResponse = PropertiesUtils.getProperties(CONFIG_FILE)
        if (propertiesResponse.getProperties() == null) {
            status = propertiesResponse.getStatus().getStatus();
            errorMessage = propertiesResponse.getErrorMessage();
            return;
        }
        Properties config = propertiesResponse.getProperties();

        String host = config.getProperty("rabbitmq.host");
        String routingKey = config.getProperty("rabbitmq.psp-routing-key");
        String username = config.getProperty("rabbitmq.username");
        String password = config.getProperty("rabbitmq.password");
        String virtualHost = config.getProperty("rabbitmq.virtual-host");
        String exchange = config.getProperty("rabbitmq.exchange");

        log.info("SendPushProspect - RabbitMQ configuration: host = $host, routingKey = $routingKey, virtualHost = $virtualHost, exchange = $exchange")
        Message message = new Message()

        message.setToken(push_token)
        message.setExternalId(externalId)
        message.setSystemCode(systemCode)
        message.setMessageCode(messageCode)
        if (expires instanceof GregorianCalendar) {
            message.setExpires(DateTimeFormatter.ISO_OFFSET_DATE_TIME.format(((GregorianCalendar) expires).toZonedDateTime()))
        }
        message.setPriority(priority)
        message.setReportLevel(reportLevel)
        message.setReportContentType(reportContentType)
        message.setText(text)
        message.setLogicalApplication(logicalApplication)
        List<Attribute> attributes = new ArrayList<Attribute>();
        //NOTIFICATION PAYLOAD
        /*
        if (CONTRACT_NUM) attributes.add(new Attribute(type: "CONTRACT_NUM", value: CONTRACT_NUM))
        if (CUID) attributes.add(new Attribute(type: "CUID", value: CUItextD.toString()))
        if (FCM_TITLE) attributes.add(new Attribute(type: "FCM_TITLE", value: FCM_TITLE))
        if (SAS_LINK) attributes.add(new Attribute(type: "SAS_LINK", value: SAS_LINK))
        if (SAS_MESSAGE_TYPE) attributes.add(new Attribute(type: "SAS_MESSAGE_TYPE", value: SAS_MESSAGE_TYPE))
        if (SAS_PICTURE) attributes.add(new Attribute(type: "FCM_IMAGE", value: SAS_PICTURE))
        if (SAS_TEMPLATE_ID) attributes.add(new Attribute(type: "SAS_TEMPLATE_ID", value: SAS_TEMPLATE_ID))
        if (SCREEN_LABEL) attributes.add(new Attribute(type: "SCREEN_LABEL", value: SCREEN_LABEL))
        */
        //DATA PAYLOAD
        if (CONTRACT_NUM) //MANDATORY, CAN BE FILLED WITH ANY STRING
        {
            attributes.add(new Attribute(type: "CONTRACT_NUM", fullValue: CONTRACT_NUM))
        }
        else
        {
            attributes.add(new Attribute(type: "CONTRACT_NUM", fullValue: "0"))
        }

        if (CUID) //NON MADANTORY FOR MOBILE
        {
            attributes.add(new Attribute(type: "CUID", fullValue: CUID))
        }
        else
        {
            attributes.add(new Attribute(type: "CUID", fullValue: ""))
        }
        //FOR IOS
        if (FCM_TITLE)
        {
            attributes.add(new Attribute(type: "FCM_TITLE", fullValue: FCM_TITLE))
        }
        else
        {
            attributes.add(new Attribute(type: "FCM_TITLE", fullValue: ""))
        }
        if (FCM_MUTABLE_CONTENT)
        {
            attributes.add(new Attribute(type: "FCM_MUTABLE_CONTENT", fullValue: FCM_MUTABLE_CONTENT))
        }
        else
        {
            attributes.add(new Attribute(type: "FCM_MUTABLE_CONTENT", fullValue: "TRUE"))
        }
        if (FCM_CONTENT_AVAILABLE)
        {
            attributes.add(new Attribute(type: "FCM_CONTENT_AVAILABLE", fullValue: FCM_CONTENT_AVAILABLE))
        }
        else
        {
            attributes.add(new Attribute(type: "FCM_CONTENT_AVAILABLE", fullValue: "TRUE"))
        }
        if (GMA_INBOX_CATEGORY)
        {
            attributes.add(new Attribute(type: "GMA_INBOX_CATEGORY", fullValue: GMA_INBOX_CATEGORY))
        }
        else
        {
            attributes.add(new Attribute(type: "GMA_INBOX_CATEGORY", fullValue: ""))
        }
        if (SAS_LINK)
        {
            attributes.add(new Attribute(type: "SAS_LINK", fullValue: SAS_LINK))
            attributes.add(new Attribute(type: "GMA_DEEPLINK", fullValue: SAS_LINK))
            attributes.add(new Attribute(type: "GMA_CLICK_ACTION", fullValue: "FLUTTER_NOTIFICATION_CLICK"))
            attributes.add(new Attribute(type: "GMA_NOTIFICATION_TYPE", fullValue: "deeplink"))
        }
        else
        {
            attributes.add(new Attribute(type: "SAS_LINK", fullValue: ""))
        }
        /*
        if (SAS_MESSAGE_TYPE)
        {
        	attributes.add(new Attribute(type: "SAS_MESSAGE_TYPE", fullValue: SAS_MESSAGE_TYPE))
        }
        else
        {
        	attributes.add(new Attribute(type: "SAS_MESSAGE_TYPE", fullValue: ""))
        }
        */
        if (SAS_PICTURE)
        {
            attributes.add(new Attribute(type: "FCM_IMAGE", fullValue: SAS_PICTURE))
        }
        else
        {
            attributes.add(new Attribute(type: "FCM_IMAGE", fullValue: ""))
        }
        if (SAS_TEMPLATE_ID)
        {
            attributes.add(new Attribute(type: "SAS_TEMPLATE_ID", fullValue: SAS_TEMPLATE_ID))
        }
        else
        {
            attributes.add(new Attribute(type: "SAS_TEMPLATE_ID", fullValue: ""))
        }
        if (SCREEN_LABEL)
        {
            attributes.add(new Attribute(type: "SCREEN_LABEL", fullValue: SCREEN_LABEL))
        }
        else
        {
            attributes.add(new Attribute(type: "SCREEN_LABEL", fullValue: ""))
        }
        message.setAttributes(attributes)
        MessageWrapper messageWrapper = new MessageWrapper()
        messageWrapper.setMessage(Arrays.asList(message))

        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        String jsonMessage = mapper.writeValueAsString(messageWrapper)
        log.info("SendPushProspect - Serialized message:" + jsonMessage)

        Map<String, Object> headerMap = new HashMap<String, Object>();
        headerMap.put("SYSTEM_CODE", systemCode);
        headerMap.put("REQUEST_ID", externalId);
        headerMap.put("priority", 0);
        headerMap.put("cuid", CUID.toString());
        headerMap.put("CorrelationID", externalId)
        headerMap.put("Type", "JMSType")
        BasicProperties messageProperties = new AMQP.BasicProperties.Builder()
                .contentType("application/json")
                .headers(headerMap)
                .build();

        ConnectionFactory factory = new ConnectionFactory();
        factory.setHost(host);
        factory.setUsername(username);
        factory.setPassword(password);
        factory.setVirtualHost(virtualHost);
        Connection connection;
        try {
            connection = factory.newConnection();
            Channel channel = connection.createChannel();
            channel.basicPublish(exchange, routingKey, messageProperties, jsonMessage.getBytes(StandardCharsets.UTF_8));
            log.info("SendPushProspect - Message sent successfully");
            requestReplyLog.info("SendPushProspect - Sent message: $jsonMessage to RabbitMQ (host = $host, routingKey = $routingKey, virtualHost = $virtualHost, exchange = $exchange)")
            status = "OK";
        } catch (Exception e) {
            log.error("SendPushProspect - Failed to send message: " + e.getMessage())
            status = "ERROR";
            throw e;
        } finally {
            if (connection != null) {
                connection.close()
            }
        }
    }
}

class MessageWrapper implements Serializable {
    List<Message> message;
}

class Message implements Serializable {
    String token;
    String externalId;
    String systemCode;
    String messageCode;
    String expires;
    List<Attribute> attributes;
    String priority;
    String reportLevel;
    String reportContentType;
    String text;
    String logicalApplication;
}

class Attribute implements Serializable {
    String type;
    String value;
    String fullValue;
}
