package com.sas.hccn.rtdm

import org.apache.log4j.Logger;
import com.sas.rtdm.implementation.engine.EventInfo;

import javax.sql.DataSource
import java.sql.*
import java.util.stream.Collectors

class MyActivity implements Runnable {

    Map<String, DataSource> mapJDBC;

    //input
    String acqChannelGroups;

    //output
    List<String> acqChannelCodes;
    String errorMessage;

    // Event info
    EventInfo evtInfo;
    // Logger
    private static final Logger log = Logger.getLogger('groovyLog');

    void run() {
        if (acqChannelGroups == null || acqChannelGroups.isEmpty()) {
            log.trace("SP_getAcqChannels_v1: acqChannelGroups is null, returning empty list of acqChannelCodes");
            acqChannelCodes = new ArrayList<>()
        }
        Connection conn = mapJDBC.get('MA_TEMP_JDBC').getConnection()
        PreparedStatement st = null
        Statement stmt = conn.createStatement();
        try {
            if (conn != null) {
                log.trace("SP_getAcqChannels_v1: Obtaining channel codes from DB");

                List<String> acqChannelGroupList = acqChannelGroups.split(",").collect({it.trim()})
                String query = String.format("SELECT ACQ_CHANNEL_CODE FROM OFS.ACQ_CHANNEL_DICT WHERE ACQ_CHANNEL_GROUP IN (%s)",
                        acqChannelGroupList.stream()
                                .map({it -> "?"})
                                .collect(Collectors.joining(", ")));
                st = conn.prepareStatement(query);

                int index = 1;
                for (String acqChannelGroup : acqChannelGroupList) {
                    log.trace("SP_getAcqChannels_v1: SQL parameter acqChannelGroup: $index = " + acqChannelGroup);
                    st.setString(index, acqChannelGroup);
                    index++;
                }

                log.trace("SP_getAcqChannels_v1: SQL query: " +  query);
                ResultSet rs = st.executeQuery();

                List<String> acqChannelCodeList = new ArrayList<>();
                while (rs.next()) {
                    acqChannelCodeList.add(rs.getString(1))
                }
                log.trace("SP_getAcqChannels_v1: found acqChannelCodes: " + acqChannelCodeList.toListString())
                acqChannelCodes = acqChannelCodeList;
            }
        } catch (SQLException e) {
            errorMessage = e.getLocalizedMessage();
            log.error("SP_getAcqChannels_v1: oracle error:" + e)
        } finally {
            if (st != null) {
                st.close();
            }
            if (stmt != null) {
                stmt.close();
            }
            if (conn != null) {
                conn.close();
            }
        }
        log.trace("SP_getAcqChannels_v1 - End");
    }
}