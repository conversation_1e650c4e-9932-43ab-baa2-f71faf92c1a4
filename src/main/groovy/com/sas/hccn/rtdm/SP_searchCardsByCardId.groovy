package com.sas.hccn.rtdm

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.ObjectMapper
import com.homecredit.sas.utils.HttpUtils
import com.homecredit.sas.utils.MappingUtils
import com.homecredit.sas.utils.PropertiesUtils
import com.homecredit.sas.utils.enumeration.RequestMethod
import com.homecredit.sas.utils.enumeration.Status
import com.homecredit.sas.utils.model.HttpCallResponse
import com.homecredit.sas.utils.model.PropertiesResponse
import com.sas.rtdm.implementation.engine.EventInfo
import org.apache.log4j.Logger

import java.time.LocalDate

/**
 * Search cards in CMS api
 * @version 17/12/24
 */
class GetDetailForClientList implements Runnable {

    // Input variables
    Long cardId

    // Output variables
    Long cuid
    Long contractCode
    String cardTypeId
    String cardTypeDescription
    String validTo

    String status = Status.ERROR.getStatus()
    Long httpResponseCode;    // HTTP response code
    String errorMessage;

    // Logger
    private static final Logger log = Logger.getLogger('groovyLog');

    // Event info
    EventInfo evtInfo;

    // Constants
    private final String resourcePrefix = "/rest-api/v2/cards?projection=CARD_SEARCH_SAS";

    // Variables from properties
    private String username;
    private String password;
    private String host;

    private final String CONFIG_FILE = "/sas/groovy/Connections/cms.properties";


    @Override
    void run() {

        //start log info
        log.info("Starting process SearchCardsById...");
        log.info("SearchCardsById - cardId: $cardId");

        PropertiesResponse propertiesResponse = PropertiesUtils.getProperties(CONFIG_FILE)
        if (propertiesResponse.getProperties() == null) {
            status = propertiesResponse.getStatus().getStatus();
            errorMessage = propertiesResponse.getErrorMessage();
            return;
        }
        Properties config = propertiesResponse.getProperties();

        username = config.getProperty("cms.username");
        password = config.getProperty("cms.password");
        host = config.getProperty("cms.host");

        log.info("SearchCardsById - host: $host");

        // Setting API variables

        String uri = host + resourcePrefix;
        log.info("SearchCardsById - Endpoint URL: $uri");
        Request request = new Request(cardId)

        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        String jsonInputString = mapper.writeValueAsString(request)
        log.info("SearchCardsById - Request body: $jsonInputString");
        byte[] postData = jsonInputString.getBytes("UTF-8");

        HttpCallResponse httpCallResponse = HttpUtils.runHttpProcess(
                evtInfo.getEventName(),
                evtInfo.getSimulationDate().getTime(),
                uri,
                HttpUtils.getBasicAuthToken(username, password),
                [(HttpUtils.CONTENT_TYPE): "application/json"],
                RequestMethod.POST,
                postData
        );

        httpResponseCode = httpCallResponse.getHttpResponseCode();
        status = httpCallResponse.getStatus().getStatus();
        errorMessage = httpCallResponse.getErrorMessage();
        String responseString = httpCallResponse.getResponse()
        if (responseString == null) {
            return
        }

        log.trace("SearchCardsById - httpResponseCode:" + httpResponseCode);
        log.trace("SearchCardsById - response:" + responseString);

        Response responseObject = MappingUtils.mapToObject(responseString, Response.class)
        if (responseObject == null) {
            status = Status.ERROR.getStatus();
            errorMessage = "Failed to map response. Wrong response data format."
            return
        }

        if (responseObject.content.size() > 0) {
            Content content = responseObject.content.get(0)
            cuid = content.cuid
            contractCode = content.accountId
            cardTypeId = content.cardType.id
            cardTypeDescription = content.cardType.description

            Plastic latestPlastic = content.plastics
                    ?.findAll {it.validFrom != null }
                    ?.max { LocalDate.parse(it.validFrom) }
                    ?: null
            validTo = latestPlastic?.validTo
        }

        status = Status.OK.getStatus()
    }
}

class Request implements Serializable {
    Request(Long value) {
        this.criteria = new ArrayList<>()
        this.criteria.add(new Criteria(value))
    }

    List<Criteria> criteria
}

class Criteria implements Serializable {
    Criteria(Long value) {
        this.field = "id"
        this.operator = "EQ"
        this.value = value
    }

    String field
    String operator
    Long value
}

class Response implements Serializable {
    String result
    List<Content> content
}

class Content implements Serializable {
    Long id
    Long pcid
    Long cuid
    Long accountId
    String status
    CardType cardType
    List<Plastic> plastics
}

class CardType implements Serializable {
    String id
    String description
}

class Plastic implements Serializable {
    String status
    String validFrom
    String validTo
}