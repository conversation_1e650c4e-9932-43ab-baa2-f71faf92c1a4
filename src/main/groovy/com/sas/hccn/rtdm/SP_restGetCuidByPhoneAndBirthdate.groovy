package com.sas.hccn.rtdm

import com.homecredit.sas.utils.HttpUtils
import com.homecredit.sas.utils.MappingUtils
import com.homecredit.sas.utils.PropertiesUtils
import com.homecredit.sas.utils.enumeration.RequestMethod
import com.homecredit.sas.utils.enumeration.Status
import com.homecredit.sas.utils.model.HttpCallResponse
import com.homecredit.sas.utils.model.PropertiesResponse
import com.sas.rtdm.implementation.engine.EventInfo
import org.apache.log4j.Logger

import java.nio.charset.StandardCharsets

/**
 * Get personal data from ESP origination-service
 * @version 22/09/07-001
 */
class GetDetailForClientList implements Runnable {

    // Input variables
    String phoneNumber;
    String birthDate;

    // Output variables
    String status = Status.ERROR.getStatus()
    Long httpResponseCode;    // HTTP response code
    String errorMessage;
    String cuid;

    // Logger
    private static final Logger log = Logger.getLogger('groovyLog');

    //TODO update sas utils and remove this part
    private static final Logger requestReplyLog = Logger.getLogger("groovyRequestReply");
    private static final int CONNECT_TM_MS = 100;        // hardcoded connection timeout                             [millisec]
    private static final int OPER_TM_MS = 200;        // reserved to finalize DB write (error/decision log)       [millisec]
    private static final int MIN_READ_TM_MS = 10;         // minimal time to response from service GetDetailForClientList

    // Event info
    EventInfo evtInfo;

    // Constants
    private final String resourcePrefix = "/profile/v1/mobile/all";
    private final String resourcePhoneNumberPart = "?number="
    private final String resourceBirthDatePart = "&birthDate="

    // Variables from properties
    private String username;
    private String password;
    private String host;

    private final String CONFIG_FILE = "/sas/groovy/Connections/esp.properties";

    @Override
    void run() {

        //start log info
        log.info("Starting process GetCuidByPhoneAndBirthdate...");
        log.info("GetCuidByPhoneAndBirthdate - phoneNumber: $phoneNumber");

        PropertiesResponse propertiesResponse = PropertiesUtils.getProperties(CONFIG_FILE)
        if (propertiesResponse.getProperties() == null) {
            status = propertiesResponse.getStatus().getStatus();
            errorMessage = propertiesResponse.getErrorMessage();
            return;
        }
        Properties config = propertiesResponse.getProperties();

        username = config.getProperty("esp.username");
        password = config.getProperty("esp.password");
        host = config.getProperty("esp.host");

        log.info("GetCuidByPhoneAndBirthdate - host: $host");
        log.info("GetCuidByPhoneAndBirthdate - resource prefix: $resourcePrefix");

        if (phoneNumber != null && !phoneNumber.isEmpty() && birthDate != null && !birthDate.isEmpty()) {

            // Setting API variables
            String uri = host + resourcePrefix + resourcePhoneNumberPart + phoneNumber + resourceBirthDatePart + birthDate;
            log.info("GetCuidByPhoneAndBirthdate - Endpoint URL: $uri");

            HttpCallResponse httpCallResponse = runHttpProcess(
                    evtInfo.getEventName(),
                    evtInfo.getSimulationDate().getTime(),
                    uri,
                    HttpUtils.getBasicAuthToken(username, password),
                    [(HttpUtils.CONTENT_TYPE): "application/json", ("x-authenticated-userid"): "{\"userName\":\"\"}"],
                    RequestMethod.GET,
                    CONNECT_TM_MS,
                    null
            );

            httpResponseCode = httpCallResponse.getHttpResponseCode();
            status = httpCallResponse.getStatus().getStatus();
            errorMessage = httpCallResponse.getErrorMessage();
            String responseString = httpCallResponse.getResponse()
            if (responseString == null) {
                return
            }

            log.trace("GetCuidByPhoneAndBirthdate - httpResponseCode:" + httpResponseCode);
            log.trace("GetCuidByPhoneAndBirthdate - response:" + responseString);

            Response responseObject = MappingUtils.mapToObject(responseString, Response.class)
            if (responseObject == null) {
                status = Status.ERROR.getStatus();
                errorMessage = "Failed to map response. Wrong response data format."
                return
            }

            if (httpResponseCode == 200) {
                cuid = responseObject.data?.externalId
                status == Status.OK.getStatus()
            }
        }
    }

    //TODO update sas-utils and remove this
    private static HttpCallResponse runHttpProcess(
            String eventName,
            Date startDate,
            String uri,
            String token,
            Map<String, String> requestProperties,
            RequestMethod requestMethod,
            Integer connectTmMs,
            byte[] data
    ) {
        Integer timeout = setTimeout(eventName, startDate, connectTmMs);
        if (timeout == null) {
            return new HttpCallResponse(null, null, Status.TIMEOUT, "REST timeout underflow. Service not called.");
        }

        try {
            HttpURLConnection connection = prepareUrlConnection(uri, token, requestProperties, requestMethod, timeout, connectTmMs, data);
            String requestBody = "empty";
            if (data != null) {
                requestBody = new String(data, StandardCharsets.UTF_8);
            }
            requestReplyLog.info("Http call - request: " + requestMethod + " " + uri + ", request body: " + requestBody);
            HttpCallResponse response = readResponse(connection);
            requestReplyLog.info("Http call - response: " + requestMethod + " " + uri + ": response code: " + response.getHttpResponseCode() + ", response: " + response.getResponse());
            return response;
        } catch (MalformedURLException e) {
            log.error("HttpUtils (read response) - MalformedURLException occurred for request! No data can be returned.");
            log.error("HttpUtils (read response) - Exception: " + e);
            e.printStackTrace();
            return new HttpCallResponse(null, null, Status.ERROR, "MalformedURLException occurred for request! No data can be returned.");
        } catch (ProtocolException e) {
            log.error("HttpUtils (read response) - ProtocolException occurred for request! No data can be returned.");
            log.error("HttpUtils (read response) - Exception: " + e);
            e.printStackTrace();
            return new HttpCallResponse(null, null, Status.ERROR, "ProtocolException occurred for request! No data can be returned.");
        } catch (IOException e) {
            log.error("HttpUtils (read response) - IOException occurred for request! No data can be returned.");
            log.error("HttpUtils (read response) - Exception: " + e);
            e.printStackTrace();
            return new HttpCallResponse(null, null, Status.ERROR, "IOException occurred for request! No data can be returned.");
        }
    }

    private static Integer setTimeout(String eventName, Date startDate, int connectTmMs) {
        //Timeout configuration logic
        log.trace("HttpUtils (setting timeout) - evtInfo: " + eventName);
        String timeoutMs = System.getProperty("com.sas.hccn.rtdm." + eventName + ".timeout.ms");
        log.trace("HttpUtils (setting timeout) - event timeoutMs: " + timeoutMs);
        if (timeoutMs == null) {
            timeoutMs = System.getProperty("com.sas.hccn.rtdm.system.timeout.ms");
        }
        log.trace("HttpUtils (setting timeout) - system timeoutMs: " + timeoutMs);
        if (timeoutMs == null) {
            timeoutMs = "10000";
        }
        log.trace("HttpUtils (setting timeout) - hardcoded timeoutMs: " + timeoutMs);
        Date currDt = new Date();
        int readTmMs = (int) (currDt.getTime() - startDate.getTime());
        readTmMs = Integer.parseInt(timeoutMs) - connectTmMs - OPER_TM_MS - readTmMs;
        if (readTmMs < MIN_READ_TM_MS) {
            log.error("HttpUtils (setting timeout) - no time for REST API call finalization; remains " + readTmMs + "ms for read");
            return null;
        }
        log.trace("HttpUtils (setting timeout) - applying " + eventName + " timeouts: byEvent=" + timeoutMs + " connect=" + connectTmMs + " read=" + readTmMs);
        return readTmMs;
    }

    private static HttpCallResponse readResponse(HttpURLConnection conn) throws IOException {
        HttpCallResponse responseModel;
        int httpResponseCode = conn.getResponseCode();
        log.info("HttpUtils (read response) - response code=" + httpResponseCode);
        switch (conn.getResponseCode()) {
            case HttpURLConnection.HTTP_OK:
            case HttpURLConnection.HTTP_CREATED:
            case HttpURLConnection.HTTP_ACCEPTED:
                String response = getResponseAsString(conn.getInputStream());
                log.info("HttpUtils (read response) - data: response=" + response);
                responseModel = new HttpCallResponse(response, httpResponseCode, Status.OK, null);
                break;
            case HttpURLConnection.HTTP_CLIENT_TIMEOUT:
                log.error("HttpUtils (read response) - Response time out.");
                conn.disconnect();
                responseModel = new HttpCallResponse(null, httpResponseCode, Status.TIMEOUT, "Timeout on server processing HTTP408");
                break;
            case HttpURLConnection.HTTP_UNAUTHORIZED:
                log.error("HttpUtils (read response) - Unauthorized.");
                conn.disconnect();
                responseModel = new HttpCallResponse(null, httpResponseCode, Status.ERROR, "Unauthorized HTTP 401");
                break;
            default:
                String error = getResponseAsString(conn.getErrorStream());
                log.error("HttpUtils (read response) - received error reply, httpResponseCode:" + httpResponseCode + ", response: " + error);
                conn.disconnect();
                responseModel = new HttpCallResponse(null, httpResponseCode, Status.ERROR, error);
        }
        try {
            conn.disconnect();
        } catch (Exception e) {
            log.error("HttpUtils (read response) - Cannot release connection to pool: " + e);
            e.printStackTrace();
        }
        return responseModel;
    }

    private static HttpURLConnection prepareUrlConnection(
            String uri, // URI to connect
            String token, // authorization token - can be null
            Map<String, String> requestProperties,
            RequestMethod requestMethod, // request method
            int readTmMs,
            int connectTmMs,
            byte[] data // can be null
    ) throws IOException {
        HttpURLConnection conn;
        URL url;
        url = new URL(uri);
        conn = (HttpURLConnection) url.openConnection();
        conn.setDoOutput(true);
        if (token != null) {
            conn.setRequestProperty("Authorization", token);
        }
        conn.setInstanceFollowRedirects(false);
        conn.setRequestMethod(requestMethod.getMethod());
        requestProperties.forEach({ key, value -> conn.setRequestProperty(key, value) });
        conn.setRequestProperty("charset", "utf-8");
        conn.setUseCaches(false);
        conn.setConnectTimeout(connectTmMs);
        conn.setReadTimeout(readTmMs);
        return conn;

    }

    private static String getResponseAsString(InputStream is) throws IOException {
        //read the response
        BufferedReader inBR = new BufferedReader(new InputStreamReader(is));
        String inputLine;
        StringBuilder response = new StringBuilder();

        while ((inputLine = inBR.readLine()) != null) {
            response.append(inputLine);
        }

        inBR.close();

        return response.toString();
    }
}

class Response implements Serializable {
    Data data;
}

class Data implements Serializable {
    String externalId;
    //...
}

//{
//    "data": {
//        "externalId": "35323291",
//        "group": [
//            {
//                "groupLabel": null,
//                "field": [
//                    {
//                        "fieldName": "phoneNumber",
//                        "value": "800032210941"
//                    },
//                    {
//                        "fieldName": "email",
//                        "value": null
//                    },
//                    {
//                        "fieldName": "firstName",
//                        "value": "Cjtestbolteeech Sisil"
//                    },
//                    {
//                        "fieldName": "lastName",
//                        "value": "Approvepre"
//                    },
//                    {
//                        "fieldName": "birthPlace",
//                        "value": null
//                    },
//                    {
//                        "fieldName": "birthDate",
//                        "value": "22/10/1994"
//                    },
//                    {
//                        "fieldName": "gender",
//                        "value": "F"
//                    },
//                    {
//                        "fieldName": "mothersName",
//                        "value": "ibun"
//                    }
//                ]
//            }
//        ]
//    }
//}