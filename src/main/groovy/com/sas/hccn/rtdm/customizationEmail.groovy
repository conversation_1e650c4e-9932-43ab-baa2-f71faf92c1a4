package com.sas.hccn.rtdm

/**************************************************************************************************************************
 Following script is using to gather template information from
 INT_COMMUNICATION.DICT_<CHANNEL_NAME>_TEMPLATE table
 And to personalize field MSG_TEXT from that table.
 It's replacing strings inside MSG_TEXT wich are like ##CUSTOM_INFO<1-10>##.
 Returns specified fields from table and modified field MSG_TEXT
 Configuration point is final String variable CONFIG_FILE(value specified in MessageParametrization class)
 available parameters are:
 1. db.escapeCharacter - specifies escape character.
 Used to determinate what and with which field needs to be replaced.
 Default value is ##.
 2. db.push_dict_table - table which will be used as source for template
 3. db.url - database url in specfic for selected DBMS jdbc format
 4. db.driver = specifes class that will be used as driver for DBMS
 *************************************************************************************************************************/

import org.apache.log4j.Logger

import javax.sql.DataSource
import java.sql.*
import java.util.regex.*;
import com.sas.analytics.ph.RTDMException

class BaseRunnable implements Runnable {
    private static Logger logger = Logger.getLogger('messageParam');
    final String CONFIG_FILE = "/sas/groovy/MessagePersonalization/tool.properties";
    private Properties config = new Properties();
    // Input variables
    String TPL_ID;
    String LANG;
    String CUSTOM_SUBJ_INFO1;
    String CUSTOM_SUBJ_INFO2;
    String CUSTOM_SUBJ_INFO3;
    String CUSTOM_SUBJ_INFO4;
    String CUSTOM_SUBJ_INFO5;
    String CUSTOM_SUBJ_INFO6;
    String CUSTOM_SUBJ_INFO7;
    String CUSTOM_SUBJ_INFO8;
    String CUSTOM_SUBJ_INFO9;
    String CUSTOM_SUBJ_INFO10;
    // Output variables
    String TPL_NAME;
    String PRIORITY_LIST;
    Long LOCAL_TPL_ID;
    String MESSAGE_CODE;
    String USER_NAME;
    String TPL_SUBJECT;
    String FROM_EMAIL;
    String FROM_NAME;
    String TARGET_CARRIER;
    String CUSTOM_INFO1;
    String CUSTOM_INFO2;
    String CUSTOM_INFO3;
    String CUSTOM_INFO4;
    String CUSTOM_INFO5;
    String CUSTOM_INFO6;
    String CUSTOM_INFO7;
    String CUSTOM_INFO8;
    String CUSTOM_INFO9;
    String CUSTOM_INFO10;
    Long DEFAULT_TEMPLATE_FLG;
    Long PEPIPOST_IGNORE_TEXT;
    String TPL_LANGUAGE;
    Long exitCode = 0;
    String errorMessage = "";
    //this method will be called when process will be triggerd in diagramm
    private Map<String, DataSource> mapJDBC = null;
    public void setMapJDBC (Map<String, DataSource> input) { mapJDBC = input }
    private SqlUtils sqlUtils;
    @Override
    void run() {
        logger.info "Execution started.";
        try{
            this.config.load(new FileInputStream(CONFIG_FILE));
            String escapeCharacter = config.getProperty("db.escapeCharacter");
            Map<String, String> customizationData = getCustomizationData();
            logger.info("lang: "+ this.LANG);
            ResultSet template = getTemplate(this.TPL_ID, this.LANG);
            String origMessage = template.getString("TPL_SUBJECT");

            validateEscapeCharacter(origMessage, escapeCharacter);
            List<ForReplacement> listOfReplacements = getPlaceholdersAndFields(origMessage, escapeCharacter);
            TPL_SUBJECT = fillMessage(origMessage, customizationData, listOfReplacements);
            TPL_NAME = template.getString("TPL_NAME");
            PRIORITY_LIST = template.getString("PRIORITY_LIST");
            LOCAL_TPL_ID = (Long) template.getBigDecimal("LOCAL_TPL_ID");
            DEFAULT_TEMPLATE_FLG = (Long) template.getBigDecimal("DEFAULT_TEMPLATE_FLG");
            PEPIPOST_IGNORE_TEXT = (Long) template.getBigDecimal("PEPIPOST_IGNORE_TEXT");
            MESSAGE_CODE = template.getString("MESSAGE_CODE");
            TPL_LANGUAGE = template.getString("TPL_LANGUAGE");
            USER_NAME = template.getString("USER_NAME");
            FROM_EMAIL = template.getString("FROM_EMAIL");
            FROM_NAME = template.getString("FROM_NAME");
            TARGET_CARRIER = template.getString("TARGET_CARRIER");
            CUSTOM_INFO1 = template.getString("CUSTOM_INFO1");
            CUSTOM_INFO2 = template.getString("CUSTOM_INFO2");
            CUSTOM_INFO3 = template.getString("CUSTOM_INFO3");
            CUSTOM_INFO4 = template.getString("CUSTOM_INFO4");
            CUSTOM_INFO5 = template.getString("CUSTOM_INFO5");
            CUSTOM_INFO6 = template.getString("CUSTOM_INFO6");
            CUSTOM_INFO7 = template.getString("CUSTOM_INFO7");
            CUSTOM_INFO8 = template.getString("CUSTOM_INFO8");
            CUSTOM_INFO9 = template.getString("CUSTOM_INFO9");
            CUSTOM_INFO10 = template.getString("CUSTOM_INFO10");
            logger.debug("table metadata:" + template.getMetaData().toString());

        } catch (InternalError internalError) {
            logger.error "InternalError occured. Error is ${internalError.message}";
            this.errorMessage = internalError.message
            this.exitCode = internalError.code
        } catch(Exception ex){
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw)
            ex.printStackTrace(pw);
            logger.error "Error occured. Error is ${ex}";
            logger.error(sw.toString());
            throw new RTDMException(ex);
        } finally {
            sqlUtils.closeConnection();
        }

        logger.info "Execution finished.";
    }
    //This method is used for getting input data as Map<> object.
    private Map<String, String> getCustomizationData(){
        logger.info("Method getCustomizationData started execution");
        Map<String, String> customizationData = new HashMap<String, String>();
        customizationData.put("CUSTOM_SUBJ_INFO1", CUSTOM_SUBJ_INFO1);
        customizationData.put("CUSTOM_SUBJ_INFO2", CUSTOM_SUBJ_INFO2);
        customizationData.put("CUSTOM_SUBJ_INFO3", CUSTOM_SUBJ_INFO3);
        customizationData.put("CUSTOM_SUBJ_INFO4", CUSTOM_SUBJ_INFO4);
        customizationData.put("CUSTOM_SUBJ_INFO5", CUSTOM_SUBJ_INFO5);
        customizationData.put("CUSTOM_SUBJ_INFO6", CUSTOM_SUBJ_INFO6);
        customizationData.put("CUSTOM_SUBJ_INFO7", CUSTOM_SUBJ_INFO7);
        customizationData.put("CUSTOM_SUBJ_INFO8", CUSTOM_SUBJ_INFO8);
        customizationData.put("CUSTOM_SUBJ_INFO9", CUSTOM_SUBJ_INFO9);
        customizationData.put("CUSTOM_SUBJ_INFO10",CUSTOM_SUBJ_INFO10);
        logger.debug("Following fields are available as personal parameter's source: " + customizationData.toString());
        logger.info("Method getCustomizationData finished execution");
        return customizationData;
    }
    //Returns row from dict table for selected TEMPLATE_ID & LANGUAGE
    private ResultSet getTemplate(String tplId, String lang) throws SQLException {
        logger.info("Method getTemplate started execution");
        this.sqlUtils = new SqlUtils(this.mapJDBC);
        String dictQuery;
        String defaultQuery;
        if(!validateString(tplId)){
            ErrorCodes error = ErrorCodes.valueOf("CUSTOMISATION_FIELD_IS_EMPTY");
            error.addExtraText("tplId");
            throw new InternalError(error);
        }

        boolean defaultFlag = false;
        //when lang is null - using default flag = 1
        if(lang == null){
            defaultFlag = true;
        }
        defaultQuery = "select * from %{db.email_dict_table} where TPL_ID = '%{templateId}' and default_template_flg = 1";
        defaultQuery = defaultQuery.replace("%{templateId}", tplId);
        defaultQuery = defaultQuery.replace("%{db.email_dict_table}", config.getProperty("db.email_dict_table"));

        if(defaultFlag){
            dictQuery = defaultQuery;
        } else{
            dictQuery = "select * from %{db.email_dict_table} where TPL_ID = '%{templateId}' and UPPER(TPL_LANGUAGE) = UPPER('%{lang}')";
            dictQuery = dictQuery.replace("%{templateId}", tplId);
            dictQuery = dictQuery.replace("%{lang}", lang);
        }

        dictQuery = dictQuery.replace("%{db.email_dict_table}", config.getProperty("db.email_dict_table"));

        logger.debug("Following query will be used for getting data: " + dictQuery);
        logger.debug("Following query will be used for getting  default data: " + defaultQuery);
        ResultSet resultSet = sqlUtils.executeQuery(dictQuery);

        if(!resultSet.next()){
            if(!defaultFlag){
                resultSet = sqlUtils.executeQuery(defaultQuery);
                if(!resultSet.next()){
                    throw new InternalError(ErrorCodes.valueOf("MISSED_DEFAULT_TEMPLATE"));
                }
            } else {
                throw new InternalError(ErrorCodes.valueOf("MISSED_DEFAULT_TEMPLATE"));
            }
        }
        return resultSet;
        logger.info("Method getTemplate finished execution");
    }
    //this method looks for unbalanced escapeCharacter inside string
    private void validateEscapeCharacter(String stringToCheck, String escapeCharacter){
        logger.info("Method validateEscapeCharacter started execution");
        int lastIndex = 0;
        int count = 0;
        while(lastIndex != -1){
            lastIndex = stringToCheck.indexOf(escapeCharacter, lastIndex);
            if(lastIndex != -1){
                count++;
                lastIndex += escapeCharacter.length();
            }
        }
        logger.debug("count:" + count);
        int balCheck = count%2;
        logger.debug("balCheck:" + balCheck);
        if(balCheck != 0){
            throw new InternalError(ErrorCodes.valueOf("UNBALANCED_ESCAPE_CHARACTERS"));
        }
        logger.info("Method validateEscapeCharacter finished execution");
    }
    //returns list of objects that are like {"stringToReplace":"##CUSTOM_INFO1##", "fieldName":"CUSTOM_INFO1"}
    //will be used to understand which returned rows by method getCustomizationData() must be not empty.
    private List<ForReplacement> getPlaceholdersAndFields(String targetString, String escapeCharacter){
        logger.info("Method getPlaceholdersAndFields started execution");
        int i = 0;
        List<ForReplacement> forReplacements = new ArrayList<>();
        String regExp = "%{escapeCharacter}([^%{escapeCharacter}]+)%{escapeCharacter}";
        regExp = regExp.replaceAll("%\\{escapeCharacter\\}", escapeCharacter);
        Matcher m = Pattern.compile(regExp).matcher(targetString);
        while(m.find()){
            ForReplacement forReplacement = new ForReplacement();
            forReplacement.setFieldName(m.group(1));
            forReplacement.setStringToReplace(escapeCharacter+m.group(1)+escapeCharacter);
            forReplacements.add(forReplacement);
        }
        logger.debug("Following fields will be replaced: " + forReplacements.toString());
        logger.info("Method getPlaceholdersAndFields finished execution");
        return forReplacements;
    }
    //this method processing all object from listOfReplacements in following way:
    // 1. find string customizationData[i].getStringToReplace() inside message
    // 2. searching value inside Map customizationData by correspond for par.1 key
    // 3. Makes replacement
    private String fillMessage(String message, Map<String, String> customizationData, List<ForReplacement> listOfReplacements){
        logger.info("Method fillMessage started execution");
        logger.debug("message is: " + message + "customizationData is: " + customizationData + "listOfReplacements is: " + listOfReplacements);
        String resultingMessage = message;
        listOfReplacements.eachWithIndex{it, i ->
            if(!validateString(customizationData.get(it.getFieldName()))){
                ErrorCodes error = ErrorCodes.valueOf("CUSTOMISATION_FIELD_IS_EMPTY");
                error.addExtraText(it.getFieldName());
                throw new InternalError(error);
            }
            logger.debug("to replace:" + it.getStringToReplace());
            logger.debug("replace with: " + customizationData.get(it.getFieldName()));
            resultingMessage = resultingMessage.replace(it.getStringToReplace(), customizationData.get(it.getFieldName()));
        }
        logger.info("Method fillMessage finished execution");
        return resultingMessage;
    }
    public static boolean validateString(String input){
        if (input?.trim()){
            return true;
        } else {
            return false;
        }
    }

    class ForReplacement{
        private String stringToReplace;
        private String fieldName;
        public String getStringToReplace() { return this.stringToReplace;}
        public String getFieldName() { return this.fieldName;}
        public void setStringToReplace(String string) { this.stringToReplace = string;}
        public void setFieldName(String string) {this.fieldName = string;}
    }
}
//this class is used for execute SELECT in specfied db
class SqlUtils{
    private static Logger logger = Logger.getLogger('messageParam');
    private  Connection connection;
    private  String driver;
    private  Properties config;
    Map<String, DataSource> mapJDBC;
    ResultSet resultSet;
    public SqlUtils(Map<String, DataSource> mapJdbc){
        logger.info("Starting SqlUtils intialization");
        this.mapJDBC = mapJdbc;
        logger.info("SqlUtils is intialized");
    }

    void setConnection() {
        if (this.connection == null) {
            try {
                this.connection = mapJDBC.get('MA_TEMP_JDBC').getConnection();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    void closeConnection() {
        this.connection.close();
    }
    ResultSet executeQuery(String query) throws SQLException {
        try{
            logger.info("method executeQuery started execution");
            setConnection();
            logger.debug("Connection2: " + connection.toString() + "MAp JDBC2: " + this.mapJDBC.toString())
            logger.info("Connection estabilished");
            PreparedStatement statement = this.connection.prepareStatement(query);
            ResultSet resultSet = statement.executeQuery();
            logger.info("method executeQuery started execution. value for reply: "  +resultSet.toString());
            return resultSet;
        } catch (SQLException e){
            logger.error("error during sql query");
        }

    }
}
//Custom error class
public class InternalError extends Exception{
    public int code;
    public InternalError(ErrorCodes error){
        super(error.getText());
        this.code = error.getExitCode();
    }
}
//Enum with error text and codes
public enum ErrorCodes{
    MISSED_DEFAULT_TEMPLATE(1, "There is no template for specfied default template id in dict table"),
    UNBALANCED_ESCAPE_CHARACTERS(2, "There are unbalanced ## inside MSG_TEXT"),
    CUSTOMISATION_FIELD_IS_EMPTY(3, "Following field used for customization is empty %{extraText}"),
    MISSED_TEMPLATE(1, "There is no template for specfied template id and language in dict table");
    private final int exitCode;
    private String errorText;
    ErrorCodes(int exitCode, String errorText){
        this.exitCode = exitCode;
        this.errorText = errorText;
    }
    public String getText(){
        return this.errorText;
    }

    public void addExtraText(String extraText){
        this.errorText = this.errorText.replace("%{extraText}", extraText);
    }
    public int getExitCode(){
        return this.exitCode;
    }

}