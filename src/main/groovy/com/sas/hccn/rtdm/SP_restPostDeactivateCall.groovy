package com.sas.hccn.rtdm

import com.fasterxml.jackson.annotation.JsonInclude.Include
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.ObjectMapper
import com.homecredit.sas.utils.HttpUtils
import com.homecredit.sas.utils.MappingUtils
import com.homecredit.sas.utils.PropertiesUtils
import com.homecredit.sas.utils.enumeration.RequestMethod
import com.homecredit.sas.utils.enumeration.Status
import com.homecredit.sas.utils.model.HttpCallResponse
import com.homecredit.sas.utils.model.PropertiesResponse
import com.sas.analytics.ph.common.RTDMTable
import com.sas.analytics.ph.common.RTDMTable.Row
import com.sas.analytics.ph.common.jaxb.DataTypes
import com.sas.rtdm.implementation.engine.EventInfo
import org.apache.log4j.Logger

class GetDetailForClientList implements Runnable {

    // Input variables
    String id_cuid;
    String il_communication_id;
    String name_call_list;

    // Output variables
    String status = Status.ERROR.getStatus()
    Long httpResponseCode;    // HTTP response code
    String errorMessage;
    RTDMTable ClientDetails;

    // Logger
    private static final Logger log = Logger.getLogger('groovyLog');

    // Event info
    EventInfo evtInfo;

    // Constants
    private final String resourcePrefix = "/blacklist/sas/rtdm/SAS_RTDM";

    // Variables from properties
    private String username;
    private String password;
    private String host;

    private final String CONFIG_FILE = "/sas/groovy/Connections/affinity.properties";

    @Override
    void run() {

        //start log info
        log.info("Starting process PostDeactivateCall...");
        log.info("PostDeactivateCall - cuid: $id_cuid");
        log.info("PostDeactivateCall - il_communication_id: $il_communication_id");
        log.info("PostDeactivateCall - name_call_list: $name_call_list");

        PropertiesResponse propertiesResponse = PropertiesUtils.getProperties(CONFIG_FILE)
        if (propertiesResponse.getProperties() == null) {
            status = propertiesResponse.getStatus().getStatus();
            errorMessage = propertiesResponse.getErrorMessage();
            return;
        }
        Properties config = propertiesResponse.getProperties();

        username = config.getProperty("affinity.username");
        password = config.getProperty("affinity.password");
        host = config.getProperty("affinity.host");

        log.info("PostDeactivateCall - host: $host");

        if(id_cuid != null && !id_cuid.isEmpty()){

            // Create empty table
            ClientDetails = new RTDMTable();
            ClientDetails.columnAdd("code", DataTypes.INT, Collections.emptyList());
            ClientDetails.columnAdd("affectedRecords", DataTypes.INT, Collections.emptyList());
            ClientDetails.columnAdd("message", DataTypes.STRING, Collections.emptyList());

            // Setting API variables

            String uri = host + resourcePrefix;
            log.info("PostDeactivateCall - Endpoint URL: $uri");
            Request requestObject = createRequest()
            if (requestObject == null) {
                return
            }
            String jsonInputString = new ObjectMapper().setSerializationInclusion(Include.NON_NULL).writeValueAsString(requestObject)
            byte[] postData = jsonInputString.getBytes("UTF-8");

            HttpCallResponse httpCallResponse = HttpUtils.runHttpProcess(
                    evtInfo.getEventName(),
                    evtInfo.getSimulationDate().getTime(),
                    uri,
                    HttpUtils.getBasicAuthToken(username, password),
                    [(HttpUtils.CONTENT_TYPE): "application/json"],
                    RequestMethod.POST,
                    postData
            );

            httpResponseCode = httpCallResponse.getHttpResponseCode();
            status = httpCallResponse.getStatus().getStatus();
            errorMessage = httpCallResponse.getErrorMessage();
            String responseString = httpCallResponse.getResponse()
            if (responseString == null) {
                return
            }

            log.trace("PostDeactivateCall - httpResponseCode:" + httpResponseCode);
            log.trace("PostDeactivateCall - response:" + responseString);

            ResponseSingleElement responseObject = MappingUtils.mapToObject(responseString, ResponseSingleElement.class)
            if (responseObject == null) {
                status = Status.ERROR.getStatus();
                errorMessage = "Failed to map response. Wrong response data format."
                return
            }

            if(httpResponseCode == 200){

                Row newRow = ClientDetails.rowAdd();
                newRow.columnDataSet("code", responseObject.code);
                newRow.columnDataSet("affectedRecords", responseObject.affectedRecords);
                newRow.columnDataSet("message", responseObject.message);

                status="OK";
            }
        }
        else {
            log.trace("PostDeactivateCall - No input clients");
        }
    }

    Request createRequest() {
        if ((il_communication_id == null || il_communication_id.isEmpty()) && (name_call_list == null || name_call_list.isEmpty())) {
            status = "ERROR"
            errorMessage = "Exactly 1 of IL_COMMUNICATION_ID, NAME_CALL_LIST must be filled."
            return null;
        }
        if ((il_communication_id != null && !il_communication_id.isEmpty()) && (name_call_list != null && !name_call_list.isEmpty())) {
            status = "ERROR"
            errorMessage = "Exactly 1 of IL_COMMUNICATION_ID, NAME_CALL_LIST must be filled."
            return null;
        }
        return new Request(
                id_cuid: id_cuid,
                il_communication_id: il_communication_id,
                name_call_list: name_call_list
        )
    }
}

class Request implements Serializable {

    @JsonProperty("ID_CUID")
    String id_cuid;

    @JsonProperty("IL_COMMUNICATION_ID")
    String il_communication_id;

    @JsonProperty("NAME_CALL_LIST")
    String name_call_list;
}

class Response implements Serializable {
    Long code;
    Long affectedRecords;
    String message;
}
