package com.sas.hccn.rtdm
/* Loop - version 2 - debug log */


import com.homecredit.sas.utils.HttpUtils
import com.homecredit.sas.utils.PropertiesUtils
import com.homecredit.sas.utils.model.PropertiesResponse

import java.nio.charset.StandardCharsets
import java.text.DateFormat
import java.text.SimpleDateFormat
import org.apache.commons.lang.time.StopWatch
import org.apache.log4j.Logger
import com.sas.analytics.ph.common.RTDMTable
import com.sas.analytics.ph.common.RTDMTable.Row
import com.sas.analytics.ph.common.RTDMTable.Column
import com.sas.analytics.ph.common.jaxb.DataTypes
import groovyx.net.http.*
import groovy.json.JsonOutput
import groovy.json.JsonSlurper
/* Calendar */
import java.time.LocalDateTime;
import java.util.Date;
/**
 * The LoopRest class reads the input data grid and the other input parameters
 * and builds / initiates series of HTTP REST requests for each row in the data grid
 * and stores the response(s) in the output data grid.
 *
 * @param inputDataGrid contains fields to be included in the http request
 * @param baseUrl is the base URL tag
 * @param resource Path is the REST resource
 * @param columnMap is a JSON-format string to rename the columns in the input data grid. Optional!
 * @param outputDataGridDefinition is output data grid built from the JSON reply.
 *
 * @return status message and code based on the results of the HTTP REST request.
 */
class loopRest implements Runnable {
    /*Initializing Logger */
    private static final Logger log= Logger.getLogger("groovyLog")
    private static final Logger requestReplyLog = Logger.getLogger('groovyRequestReply')
    /* Declaring public (input) properties */
    RTDMTable inputDataGrid
    String baseUrl
    String eventId
    //String resourcePath
    String columnMap
    String outputDataGridDefinition
    String inputDataGridDefinition
    String runId
    //Declaring public (output) properties
    RTDMTable outputDataGrid = new RTDMTable();
    Long statusCd = 0
    def statusMsg = "Loop process has been completed successfully."  //Stores default Status message
    //internal private properties (column mapping (old->new))
    def colMapJsonObj = [:]
    //Variable to store possible HTTP error
    Long httpError = 0
    //connection credentials
    String username
    String password
    //Dateformat CONSTANTs
    private static final dateFormatWithShift = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX"
    private static final dateFormatWithoutShift = "yyyy-MM-dd'T'HH:mm:ss.SSS"
    private static final dateFormatWithoutShiftPlusZ = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"
    //CONSTANTs for JSON mandatory fields
    private static final Integer VERSION = 1
    private static final String CORRELATION_ID = ""
    private static final String CLIENT_TIME_ZONE = "Asia/Saigon"
    String resourcePath = "/RTDM/rest/runtime/decisions/"

    //properties file
    private final String RTDM_CONFIG_FILE = "/sas/groovy/Connections/rtdm.properties";


    public String convertToString(Object object) {
        if (object instanceof GregorianCalendar == false) {
            throw new RuntimeException("Unable to convert calendar as it is not a GregorianCalendar");
        }
        GregorianCalendar cal = (GregorianCalendar) object;
        SimpleDateFormat f = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        f.setCalendar(cal);
        String str = f.format(cal.getTime());
        /*return str.substring(0, 26) + ":" + str.substring(26) + "[" + cal.getTimeZone().getID() + "]";*//*original*/
        return str.substring(0, 23) + "Z";
    }
    /**
     * The dataGridRowtoJson method accepts a row from the Data Grid and
     * converts it to a JSON formatted string
     *
     * @param gridRow is the row to convert
     *
     * @return jsonRow is the converted row in JSON formatted string
     */
    def dataGridRowtoJson (Row gridRow) {
        def rowData = [:]
        def value;
        def columnType, columnValue = ""
        List<Column> columns = inputDataGrid.getColumns();
        log.debug(columns)
        //Iterating through the columns of the input data grid and convert each of the values into JSON format
        inputDataGrid.getColumns().size().times {
            columnType = columns[it].type.toString()
            columnValue = gridRow.columnDataGet(columns[it].name)
            value = null;
            if (columnValue != null) {
                switch (columnType)  {
                    case "DATETIME":
                        value = convertToString(columnValue)
                        break
                    default: value = columnValue
                }
            }
            // Putting values into an array (rowData) using optinally renamed column names (defined by colMapJsonObj)
            rowData.put(colMapJsonObj[columns[it].name]  ?: columns[it].name, value)
            log.debug(rowData)
        }
        log.info("funce getColumns probehla")
        //Defining final JSON row
        def jsonData = [
                version:VERSION,
                correlationId:runId,
                clientTimeZone: CLIENT_TIME_ZONE,
                inputs: rowData
        ]
        def jsonRow = new groovy.json.JsonBuilder(jsonData)
        log.info("funkce jsonBuilder probehla")
        return jsonRow
    }
    /**
     * The getValidDate method determines if the a date conforms the given date format
     *
     * @param p_date is the date to check
     * @param p_format is the format to use for checking
     *
     * @return resultDate is the result of the conversion. If it is null, the conversion has failed, otherwise it was successful.
     */
    def getValidDate( String p_date,  String p_format) {
        Date resultDate = null;
        DateFormat dfWithShift = new SimpleDateFormat(p_format, Locale.ENGLISH);
        try {
            resultDate =  dfWithShift.parse(p_date)
        }
        catch (e) {
            println "EXCEPTION:"+e.toString()
        }
        return resultDate
    }

    /**
     * The generateOutputRowFromResponse method adds a row to the output data grid using a response string.
     *
     * @param p_response is the response from the HTTP REST call
     *
     */
    def generateOutputRowFromResponse (HttpResponseDecorator p_response) {
        Row newRow = outputDataGrid.rowAdd();
        List<Column> columns = outputDataGrid.getColumns()
        def outputColumnType, outputColumnValue, outputColumnName,jsonColumnValue = ""
        //def jsonSlurp = new groovy.json.JsonSlurper()
        //def jsonObj = jsonSlurp.parseText(jsonStr)
        def jsonObj = new groovy.json.JsonSlurper().parse(p_response.getData())
        def convJsonValue = null;
        def validDate = null;
        outputDataGrid.getColumns().size().times {
            outputColumnType = columns[it].type.toString()
            outputColumnName = columns[it].name;
            jsonColumnValue = jsonObj."outputs"[outputColumnName];
            convJsonValue = null;
            validDate = null;
            if (jsonColumnValue!= null) {
                switch (outputColumnType) {
                    case "STRING" :  if (jsonColumnValue.getClass() == java.lang.String) {convJsonValue =jsonColumnValue}
                        break
                    case "BOOLEAN" :  if (jsonColumnValue.getClass() == java.lang.Boolean) {convJsonValue =jsonColumnValue}
                        break
                    case "FLOAT":   if (jsonColumnValue.getClass() == java.math.BigDecimal) {convJsonValue = jsonColumnValue.doubleValue()}
                        break
                    case "INT":   if (jsonColumnValue.getClass() == java.lang.Integer|| jsonColumnValue.getClass() == java.lang.Long) {convJsonValue = jsonColumnValue.longValue()}
                        break
                    case "DATETIME": ["yyyy-MM-dd'T'HH:mm:ss.SSSXXX",
                                      "yyyy-MM-dd'T'HH:mm:ss.SSS",
                                      "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"
                    ].each {
                        validDate =  getValidDate(jsonColumnValue,it)
                        if (validDate) {
                            convJsonValue = Calendar.getInstance()
                            convJsonValue.setTime(validDate)
                        }
                    }
                        break
                }
                println "converted json value :"+convJsonValue
                log.debug("converted json value :"+convJsonValue)
                log.debug(jsonColumnValue)
                log.debug(outputColumnType)
                log.debug(jsonColumnValue.getClass()) /*zremovat logy */
                newRow.columnDataSet(outputColumnName, convJsonValue);
            }
            else {
                println outputColumnName+ " no Match or value is null!"
                newRow.columnDataSet(outputColumnName, null);
            }
        }
    }
    /**
     * The restCall method accepts a JSON formatted string and initiates a REST call to
     * the specified baseURL+resourcePath.
     *
     * @param p_JsonToPost is a JSON string to POST using HTTP REST call.
     *
     * @return response is the response from HTTP REST call
     */
    def restCall(p_JsonToPost) {

        log.info ("restCall started");
        log.info ("eventId: "+eventId);

        HttpResponseDecorator response = null
        RESTClient client = new RESTClient(baseUrl+resourcePath+eventId)
        client.encoder.'application/vnd.sas.decision.request+json' = { body ->
            EncoderRegistry registry = new EncoderRegistry();
            return registry.encodeJSON(body, 'application/vnd.sas.decision.request+json; charset=utf-8')
        }
        client.parser.'application/vnd.sas.error+json' = client.parser.'application/json'
        client.parser.'application/vnd.sas.reponse+json' = client.parser.'application/json'
        client.auth.basic(username, password)
        requestReplyLog.info("JsonInto: "+baseUrl+resourcePath+eventId+": "+p_JsonToPost.toString())

        int maxRetries = 3
        int retryDelaySeconds = 5
        boolean requestSuccessful = false

        for (int attempt = 1; attempt <= maxRetries && !requestSuccessful; attempt++) {
            try{
                if (attempt > 1) {
                    log.info("runId="+runId+" Retry attempt ${attempt} of ${maxRetries} after ${retryDelaySeconds} seconds delay")
                }

                //does the HTTP post
                response = client.post(
                        contentType : ContentType.ANY,
                        requestContentType: 'application/vnd.sas.decision.request+json',
                        body : p_JsonToPost.toString()
                )
                statusCd = response.getStatus()
                statusMsg = response.getData()
                requestReplyLog.info("statusCd="+statusCd)
                requestSuccessful = true
            } catch (ex){
                httpError = ex instanceof groovyx.net.http.HttpResponseException ? ex.getResponse()?.getStatus() as Long : -1 as Long
                log.error("runId="+runId+" JsonToPost"+p_JsonToPost.toString())
                if ([400,404].contains(httpError as Integer)){
                    log.error("runId="+runId+" HTTP-ERROR calling REST: "+httpError )
                    statusCd = -1
                    break  // Don't retry for client errors
                } else if ([500, 502, 503, 504].contains(httpError as Integer)){
                    log.error("runId="+runId+" HTTP-ERROR calling REST: "+httpError )
                    statusMsg = ex.getResponse().getData()
                    log.error("runId="+runId+" HTTP-ERROR message:"+httpError  )
                    statusCd = -1
                    break  // Don't retry for server errors
                } else if (httpError == -1){
                    log.error("runId="+runId+" OTHER HTTP-ERROR calling REST: "+httpError + " (attempt ${attempt} of ${maxRetries})")
                    statusMsg = "unknown error" + ex.getLocalizedMessage()
                    log.error("runId="+runId+" HTTP-ERROR message:"+httpError  )

                    if (attempt < maxRetries) {
                        log.info("runId="+runId+" Waiting ${retryDelaySeconds} seconds before retry...")
                        Thread.sleep(retryDelaySeconds * 1000)
                    } else {
                        log.error("runId="+runId+" All retry attempts exhausted for httpError = -1")
                        statusCd = -1
                    }
                }
            }
        }

        client.shutdown()
        return response
    }

    public void run ()  {
        //Generating a random run ID for the process
        /*def runId = Math.abs(new Random().nextInt() % 1000000) + 1*/
        log.info("runId="+runId+" LOOP Rest started...")
        if (log.debugEnabled) {
            log.debug("runId="+runId+" Listing input parameters and the assigned values...")
            log.debug("runId="+runId+" baseUrl:"+baseUrl)
            log.debug("runId="+runId+" resourcePath:"+resourcePath)
            log.debug("runId="+runId+" eventId:"+eventId)
            log.debug("runId="+runId+" columnMap:"+columnMap)
            log.debug("runId="+runId+" outputDataGridDefinition:"+outputDataGridDefinition)
        }
        //Building DEMO data grid
        /*
         RTDMTable newTable1 = new RTDMTable();
        newTable1.columnAdd("in_string", DataTypes.STRING, Collections.emptyList());
        Row newRow1 = newTable1.rowAdd();
        newRow1.columnDataSet("in_string", "KEK");
        Row newRow2 = newTable1.rowAdd();
        newRow2.columnDataSet("in_string", "LOL");
        inputDataGrid=newTable1;
        */
        //If key parameters are missing then exit..
        if (baseUrl == null ||  resourcePath == null || outputDataGridDefinition== null) {
            log.error("runId="+runId+" RTDM RUNTIME ERROR - 100: INPUT PARAMETER ERROR: One or more of the vital parameter values are missing!")
            log.error("runId="+runId+" Listing input parameters...")
            log.error("runId="+runId+" baseUrl:"+baseUrl)
            log.error("runId="+runId+" resourcePath:"+resourcePath)
            log.debug("runId="+runId+" eventId:"+eventId)
            log.error("runId="+runId+" columnMap:"+columnMap)
            log.error("runId="+runId+" outputDataGridDefinition:"+outputDataGridDefinition)
            statusCd=-1
            statusMsg="RTDM RUNTIME ERROR - 100: INPUT PARAMETER ERROR: One or more of the vital parameter values are missing!"
            return
        }

        PropertiesResponse propertiesResponse = PropertiesUtils.getProperties(RTDM_CONFIG_FILE)
        if (propertiesResponse.getProperties() == null) {
            statusCd = -1
            statusMsg = propertiesResponse.getErrorMessage();
            return;
        }
        Properties config = propertiesResponse.getProperties();

        username = config.getProperty("rtdm.username");
        password = config.getProperty("rtdm.password");

        //If input Data Grid is not given or contains zero rows then exit...
        if (inputDataGrid == null || (inputDataGrid.rowCount() == 0)  ) {
            try {
                def jsonSlurp = new groovy.json.JsonSlurper()
                def jsonOutputGridDef = jsonSlurp.parseText(outputDataGridDefinition)
                jsonOutputGridDef.each {k,v ->
                    switch (v) {
                        case "STRING":  outputDataGrid.columnAdd(k, DataTypes.STRING, Collections.emptyList() );
                            break
                        case "INT":  outputDataGrid.columnAdd(k, DataTypes.INT, Collections.emptyList() );
                            break
                        case "DATETIME":outputDataGrid.columnAdd(k, DataTypes.DATETIME, Collections.emptyList() );
                            break
                        case "FLOAT":  outputDataGrid.columnAdd(k, DataTypes.FLOAT, Collections.emptyList() );
                            break
                        case "BOOLEAN": outputDataGrid.columnAdd(k, DataTypes.BOOLEAN, Collections.emptyList() );
                            break
                        default:  statusCd=-1
                    }
                }
                if (statusCd == -1) {
                    log.error("runId="+runId+" RTDM RUNTIME ERROR - 100: INPUT PARAMETER ERROR: Invalid output data grid column type definition! The valid data types are: STRING, INT, FLOAT, DATETIME, BOOLEAN")
                    statusMsg="RTDM RUNTIME ERROR - 100: INPUT PARAMETER ERROR: Invalid output data grid column type definition! The valid data types are: STRING, INT, FLOAT, DATETIME, BOOLEAN"
                    return
                }
            }
            catch (ex) {
                log.error("runId="+runId+" RTDM RUNTIME ERROR - 100: INPUT PARAMETER ERROR: The output data grid definition contains formatting errors.")
                statusCd=-1
                statusMsg="RTDM RUNTIME ERROR - 100: INPUT PARAMETER ERROR: The output data grid definition contains formatting errors."
                return
            }
            log.error("runId="+runId+" RTDM RUNTIME ERROR - 100: INPUT PARAMETER ERROR: The input data grid is missing or contains less than one row.")
            statusCd=-1
            statusMsg="RTDM RUNTIME ERROR - 100: INPUT PARAMETER ERROR: The input data grid is missing or contains less than one row."
            return
        }
        def jsonSlurp = new groovy.json.JsonSlurper()
        //Converting inputDataGrid values as defined in inputDataGridDefinition
        if(inputDataGridDefinition != null) {
            try {
                def jsoninputGridDef = jsonSlurp.parseText(inputDataGridDefinition)
                jsoninputGridDef.each {k,v ->
                    switch (v) {
                        case "STRING":  inputDataGrid.columnDataConvert(k, DataTypes.STRING);
                            break
                        case "INT":  inputDataGrid.columnDataConvert(k, DataTypes.INT);
                            break
                        case "DATETIME":inputDataGrid.columnDataConvert(k, DataTypes.DATETIME);
                            break
                        case "FLOAT":  inputDataGrid.columnDataConvert(k, DataTypes.FLOAT);
                            break
                        case "BOOLEAN": inputDataGrid.columnDataConvert(k, DataTypes.BOOLEAN);
                            break
                        default:  statusCd=-1
                    }
                }
                if (statusCd == -1) {
                    log.error("runId="+runId+" RTDM RUNTIME ERROR - 102: INPUT PARAMETER CONVERSION: Invalid input data grid column type definition! The valid data types are: STRING, INT, FLOAT, DATETIME, BOOLEAN")
                    statusMsg="RTDM RUNTIME ERROR - 102: INPUT PARAMETER ERROR: Invalid input data grid column type definition! The valid data types are: STRING, INT, FLOAT, DATETIME, BOOLEAN"
                    return
                }
            }
            catch(Exception e) {
                log.error("runId="+runId+" RTDM RUNTIME ERROR - 102: INPUT PARAMETER ERROR: The input data grid definition contains formatting errors.")
                log.info(e.getLocalizedMessage())
                statusCd=-1
                statusMsg="RTDM RUNTIME ERROR - 102: INPUT PARAMETER ERROR: The input data grid definition contains formatting errors."
                return
            }
        }
        // Non-empty columnMap parameter means that the original data grid columns have to be renamed to new ones.
        // Creating colMapsJsonObj to store old->new column name mappings
        if (columnMap!= null ) {
            //getting new column names
            colMapJsonObj = jsonSlurp.parseText(columnMap)
        }
        else {
            def colname
            List<Column> gridColumns = inputDataGrid.getColumns();
            int inputNumCols=gridColumns.size()
            inputNumCols.times {
                colname = gridColumns[it].name.toString()
                colMapJsonObj[colname]=colname
            }
        }
        //Creating output data grid based on outputDataGridDefinition parameter
        try {
            def jsonOutputGridDef = jsonSlurp.parseText(outputDataGridDefinition)
            jsonOutputGridDef.each {k,v ->
                switch (v) {
                    case "STRING":  outputDataGrid.columnAdd(k, DataTypes.STRING, Collections.emptyList() );
                        break
                    case "INT":  outputDataGrid.columnAdd(k, DataTypes.INT, Collections.emptyList() );
                        break
                    case "DATETIME":outputDataGrid.columnAdd(k, DataTypes.DATETIME, Collections.emptyList() );
                        break
                    case "FLOAT":  outputDataGrid.columnAdd(k, DataTypes.FLOAT, Collections.emptyList() );
                        break
                    case "BOOLEAN": outputDataGrid.columnAdd(k, DataTypes.BOOLEAN, Collections.emptyList() );
                        break
                    default:  statusCd=-1
                }
            }
            if (statusCd == -1) {
                log.error("runId="+runId+" RTDM RUNTIME ERROR - 100: INPUT PARAMETER ERROR: Invalid output data grid column type definition! The valid data types are: STRING, INT, FLOAT, DATETIME, BOOLEAN")
                statusMsg="RTDM RUNTIME ERROR - 100: INPUT PARAMETER ERROR: Invalid output data grid column type definition! The valid data types are: STRING, INT, FLOAT, DATETIME, BOOLEAN"
                return
            }
        }
        catch (ex) {
            log.error("runId="+runId+" RTDM RUNTIME ERROR - 100: INPUT PARAMETER ERROR: The output data grid definition contains formatting errors.")
            statusCd=-1
            statusMsg="RTDM RUNTIME ERROR - 100: INPUT PARAMETER ERROR: The output data grid definition contains formatting errors."
            return
        }
        //The following section performs three main steps:
        // Iterating through the data grid rows
        // for each iteration:
        //  A Converting data grid row into JSON format
        //  B Calling the REST service using JSON format
        //  C Processing and storing the JSON result in the output grid
        // Iteration over.
        def currJsonRow
        def responseFromRestCall
        def rowCnt = 0
        //Iterating through the data grid rows
        for (Row row:inputDataGrid.iterator()) {
            rowCnt = rowCnt+1
            // (A)  Converting the current row to into JSON
            try {
                currJsonRow = dataGridRowtoJson(row)
            }
            catch(ex) {
                log.error("runId="+runId+" RTDM RUNTIME ERROR - 101: ERROR PROCESSING INPUT PARAMETER: Could not convert input data grid row (row:"+rowCnt+") to JSON.")
                statusCd=-1
                statusMsg="RTDM RUNTIME ERROR - 101: ERROR PROCESSING INPUT PARAMETER: Could not convert input data grid row (row:"+rowCnt+") to JSON."
                return
            }
            // (B) Calling the REST service
            try {
                responseFromRestCall = restCall(currJsonRow)
            }
            catch(ex) {
                log.error("runId="+runId+" RTDM RUNTIME ERROR - 130: Error while preparing the REST call. Invalid baseURL+resourcePath+eventId!")
                log.error("runId="+runId+" RTDM RUNTIME ERROR - 130: "+ex.toString())
                statusMsg="RTDM RUNTIME ERROR - 130: Error while preparing the REST call. Invalid baseURL+resourcePath+eventId!"
                statusCd=-1
                return
            }
            if (statusCd == -1) {
                log.error("runId="+runId+" RTDM RUNTIME ERROR - 140: HTTP error ("+httpError+") while executing REST call.")
                statusMsg="RTDM RUNTIME ERROR - 140: HTTP error ("+httpError+") while executing REST call."
                return
            }
            // (C) Storing the result in the output grid
            log.info("creating output grid from response json")
            try {
                generateOutputRowFromResponse(responseFromRestCall)
            }
            catch (ex) {
                log.error("runId="+runId+" RTDM RUNTIME ERROR - 103: ERROR GENERATING OUTPUT: Could not generate output data grid row (row:"+rowCnt+") from REST call response JSON.")
                log.error("runId="+runId+" JSON string that failed: "+responseFromRestCall)
                statusCd=-1
                statusMsg="RTDM RUNTIME ERROR - 103: ERROR GENERATING OUTPUT: Could not generate output data grid row (row:"+rowCnt+") from REST call response JSON."
                return
            }
        }
        statusCd= 0
        statusMsg="Loop process has been completed successfully."
    }
}