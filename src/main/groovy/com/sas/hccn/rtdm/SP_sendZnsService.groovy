package com.sas.hccn.rtdm;

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.ObjectMapper
import com.homecredit.sas.utils.PropertiesUtils
import com.homecredit.sas.utils.model.PropertiesResponse
import org.apache.log4j.Logger

import java.nio.charset.StandardCharsets
import java.time.format.DateTimeFormatter

/**
 * @version 23/03/15-005
 */
class GetDetailForClientList implements Runnable {

    // Input variables
    String externalId;
    String systemCode;
    String messageCode;
    Object expires;
    String priority;
    String reportLevel;
    String reportContentType;
    String recipient;
    String text;
    Boolean isInteractive;

    String CONTRACT_NUM;
    Integer CUID;
    Integer GATEWAY_TEMPLATE_ID;


    // Output variables
    String status = "ERROR";            // either "OK" or "ERROR"
    String errorMessage;

    // Internal variables
    private static final Logger log = Logger.getLogger('groovyLog');
    private static final Logger requestReplyLog = Logger.getLogger('groovyRequestReply')

    private final String CONFIG_FILE = "/sas/groovy/Connections/rabbitmq.properties";

    @Override
    void run() {
        //start log info
        log.info("Starting process SendZns to RabbitMQ...");
        log.info("SendZns - externalId: $externalId");
        log.info("SendZns - systemCode: $systemCode");
        log.info("SendZns - messageCode: $messageCode");
        log.info("SendZns - expires: $expires");
        log.info("SendZns - priority: $priority");
        log.info("SendZns - reportLevel: $reportLevel");
        log.info("SendZns - reportContentType: $reportContentType");
        log.info("SendZns - recipient: $recipient");
        log.info("SendZns - text: $text");
        log.info("SendZns - isInteractive: $isInteractive");
        log.info("SendZns - CONTRACT_NUM: $CONTRACT_NUM");
        log.info("SendZns - CUID: $CUID");
        log.info("SendZns - GATEWAY_TEMPLATE_ID: $GATEWAY_TEMPLATE_ID");

        List<String> emptyFields = new ArrayList<>()
        if (!externalId) {
            emptyFields.add("externalId")
        }
        if (!systemCode) {
            emptyFields.add("systemCode")
        }
        if (!messageCode) {
            emptyFields.add("messageCode")
        }
        if (!text) {
            emptyFields.add("text")
        }
        if (!recipient) {
            emptyFields.add("recipient")
        }
        if (!reportLevel) {
            emptyFields.add("reportLevel")
        }
        if (!emptyFields.isEmpty()) {
            errorMessage = "Following mandatory fields are null or empty: " + String.join(", ", emptyFields)
            return
        }

        PropertiesResponse propertiesResponse = PropertiesUtils.getProperties(CONFIG_FILE)
        if (propertiesResponse.getProperties() == null) {
            status = propertiesResponse.getStatus().getStatus();
            errorMessage = propertiesResponse.getErrorMessage();
            return;
        }
        Properties config = propertiesResponse.getProperties();

        String host = config.getProperty("rabbitmq.host");
        String routingKey = config.getProperty("rabbitmq.sms-routing-key");
        String username = config.getProperty("rabbitmq.username");
        String password = config.getProperty("rabbitmq.password");
        String virtualHost = config.getProperty("rabbitmq.virtual-host");
        String exchange = config.getProperty("rabbitmq.exchange");

        log.info("SendZns - RabbitMQ configuration: host = $host, routingKey = $routingKey, virtualHost = $virtualHost, exchange = $exchange")
        Message message = new Message()

        message.setExternalId(externalId)
        message.setSystemCode(systemCode)
        message.setMessageCode(messageCode)
        if (expires instanceof GregorianCalendar) {
            message.setExpires(DateTimeFormatter.ISO_OFFSET_DATE_TIME.format(((GregorianCalendar) expires).toZonedDateTime()))
        }
        message.setPriority(priority)
        message.setReportLevel(reportLevel)
        message.setReportContentType(reportContentType)
        message.setRecipient(recipient)
        message.setText(text)
        message.setIsInteractive(isInteractive)
        List<Attribute> attributes = new ArrayList<Attribute>();
        if (CONTRACT_NUM) attributes.add(new Attribute(type: "CONTRACT_NUM", value: CONTRACT_NUM))
        if (CUID) attributes.add(new Attribute(type: "CUID", value: CUID.toString()))
        if (GATEWAY_TEMPLATE_ID) attributes.add(new Attribute(type: "GATEWAY_TEMPLATE_ID", value: GATEWAY_TEMPLATE_ID))
        message.setAttributes(attributes)

        MessageWrapper messageWrapper = new MessageWrapper()
        messageWrapper.setMessage(Arrays.asList(message))

        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        String jsonMessage = mapper.writeValueAsString(messageWrapper)
        log.info("SendZns - Serialized message:" + jsonMessage)

        Map<String, Object> headerMap = new HashMap<String, Object>();
        headerMap.put("SYSTEM_CODE", systemCode);
        headerMap.put("REQUEST_ID", externalId);
        headerMap.put("priority", 0);
        headerMap.put("CorrelationID", externalId)
        headerMap.put("Type", "JMSType")
        BasicProperties messageProperties = new AMQP.BasicProperties.Builder()
                .contentType("application/json")
                .headers(headerMap)
                .build();

        ConnectionFactory factory = new ConnectionFactory();
        factory.setHost(host);
        factory.setUsername(username);
        factory.setPassword(password);
        factory.setVirtualHost(virtualHost);
        Connection connection;
        try {
            connection = factory.newConnection();
            Channel channel = connection.createChannel();
            channel.basicPublish(exchange, routingKey, messageProperties, jsonMessage.getBytes(StandardCharsets.UTF_8));
            log.info("SendZns - Message sent successfully");
            requestReplyLog.info("SendZns - Sent message: $jsonMessage to RabbitMQ (host = $host, routingKey = $routingKey, virtualHost = $virtualHost, exchange = $exchange)")
            status = "OK";
        } catch (Exception e) {
            log.error("SendZns - Failed to send message: " + e.getMessage())
            status = "ERROR";
            throw e;
        } finally {
            if (connection != null) {
                connection.close()
            }
        }
    }
}

class MessageWrapper implements Serializable {
    List<Message> message;
}

class Message implements Serializable {
    String externalId;
    String systemCode;
    String messageCode;
    String expires;
    List<Attribute> attributes;
    String priority;
    String reportLevel;
    String reportContentType;
    String recipient;
    String text;
    Boolean isInteractive;
}

class Attribute implements Serializable {
    String type;
    String value;
}
