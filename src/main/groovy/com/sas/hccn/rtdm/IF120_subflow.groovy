package com.sas.hccn.rtdm

import com.fasterxml.jackson.core.JsonGenerator
import com.fasterxml.jackson.core.JsonParser
import com.fasterxml.jackson.core.JsonProcessingException
import com.fasterxml.jackson.databind.DeserializationContext
import com.fasterxml.jackson.databind.JsonDeserializer
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.JsonSerializer
import com.fasterxml.jackson.databind.SerializerProvider
import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import com.fasterxml.jackson.databind.annotation.JsonSerialize
import com.sas.analytics.ph.common.RTDMTable
import com.sas.analytics.ph.common.RTDMTable.Row
import com.sas.analytics.ph.common.RTDMTable.Column
import com.sas.analytics.ph.common.jaxb.DataTypes
import com.fasterxml.jackson.databind.ObjectMapper
import com.homecredit.sas.utils.HttpUtils
import com.homecredit.sas.utils.MappingUtils
import com.homecredit.sas.utils.enumeration.RequestMethod
import com.homecredit.sas.utils.enumeration.Status
import com.homecredit.sas.utils.model.HttpCallResponse
import com.sas.rtdm.implementation.engine.EventInfo
import org.apache.log4j.Logger

import java.nio.charset.StandardCharsets
import java.text.SimpleDateFormat
import java.time.format.DateTimeFormatter

class CallRTDM implements Runnable {

    // Input variables - CHANGE THIS PART FOR CLIENT INPUTS
    String partyId
    String limitBulkId
    String limitScoringProcessSource
    String calculationSource
    String eventName
    String eventSource
    Long totalRecordNumber
    Object eventTimestamp
    RTDMTable limitList

    String rtdmEventId //IF120_EV_OFSoRBPInteraction
    String rtdmHost //global variable
    String charEventTimestamp
    String correlationId

    // Output variables
    String status = Status.ERROR.getStatus()
    Long httpResponseCode;    // HTTP response code
    String errorMessage;


    // Logger
    private static final Logger log = Logger.getLogger('groovyLog');

    // Event info
    EventInfo evtInfo;

    // Constants
    private final String resourcePrefix = "/RTDM/rest/runtime/decisions/"
    private final Long version = 1;
    private final String clientTimeZone = "Asia/Manila";

    @Override
    void run() {

        //start log info
        log.info("Starting process CallRTDMCampaign...");

        log.info("CallRTDMCampaign - host: $rtdmHost");
        log.info("CallRTDMCampaign - resource prefix: $resourcePrefix");
        log.info("CallRTDMCampaign - partyId: $partyId");
        log.info("CallRTDMCampaign - limitBulkId: $limitBulkId");
        log.info("CallRTDMCampaign - limitScoringProcessSource: $limitScoringProcessSource");
        log.info("CallRTDMCampaign - eventName: $eventName");
        log.info("CallRTDMCampaign - calculationSource: $calculationSource");
        log.info("CallRTDMCampaign - eventSource: $eventSource");
        log.info("CallRTDMCampaign - eventTimestamp: $eventTimestamp");
        log.info("CallRTDMCampaign - limitList: $limitList");

        if (rtdmEventId != null && !rtdmEventId.isEmpty()) {
            if (eventTimestamp instanceof GregorianCalendar) {
                charEventTimestamp = DateTimeFormatter.ISO_OFFSET_DATE_TIME.format(((GregorianCalendar) eventTimestamp).toZonedDateTime())
            }
            log.info("CallRTDMCampaign - charEventTimestamp: $charEventTimestamp");
            // mapping of variables to request object - CHANGE THIS PART FOR MAPPING DIFFERENT CLIENT INPUTS
            Request request = new Request(
                    version: version,
                    correlationId: correlationId,
                    clientTimeZone: clientTimeZone,
                    inputs: new RequestInputs(
                            partyId: partyId,
                            limitBulkId: limitBulkId,
                            limitScoringProcessSource: limitScoringProcessSource,
                            calculationSource: calculationSource,
                            eventName: eventName,
                            eventSource: eventSource,
                            totalRecordNumber: totalRecordNumber,
                            eventTimestamp: charEventTimestamp,
                            limitList: toJsonRtdmTable(limitList)
                    )
            )

            ObjectMapper mapper = new ObjectMapper();
            String jsonMessage = mapper.writeValueAsString(request)
            byte[] data = jsonMessage.getBytes(StandardCharsets.UTF_8)

            // Setting API variables
            String uri = rtdmHost + resourcePrefix + rtdmEventId;
            log.info("CallRTDMCampaign - Endpoint URL: $uri");
            log.info("CallRTDMCampaign - Request: $jsonMessage");

            HttpCallResponse httpCallResponse = HttpUtils.runHttpProcess(
                    evtInfo.getEventName(),
                    evtInfo.getSimulationDate().getTime(),
                    uri,
                    null,
                    [(HttpUtils.CONTENT_TYPE): "application/json"],
                    RequestMethod.POST,
                    data
            );

            httpResponseCode = httpCallResponse.getHttpResponseCode();
            status = httpCallResponse.getStatus().getStatus();
            errorMessage = httpCallResponse.getErrorMessage();
            String responseString = httpCallResponse.getResponse()
            if (responseString == null) {
                return
            }

            log.info("CallRTDMCampaign - httpResponseCode:" + httpResponseCode);
            log.info("CallRTDMCampaign - response:" + responseString);

            MyResponse responseObject = MappingUtils.mapToObject(responseString, MyResponse.class)
            if (responseObject == null) {
                status = Status.ERROR.getStatus();
                errorMessage = "Failed to map response. Wrong response data format."
                return
            }

            if (httpResponseCode == 201) {

                // setting values - CHANGE THIS TO REFLECT OUTPUT FROM RTDM PROCESS
                eventName = responseObject.outputs.eventName;
                partyId = responseObject.outputs.partyId;
                status = responseObject.outputs.status;
//                description = responseObject.outputs.description;
                totalRecordNumber = responseObject.outputs.totalRecordNumber;
                limitList = toRTDMTable(responseObject.outputs.limitList);

            }
        } else {
            log.trace("CallRTDMCampaign - No input clients");
        }
    }

    JsonRtdmTable toJsonRtdmTable(RTDMTable rtdmTable) {
        JsonRtdmTable jsonTable = new JsonRtdmTable()

        for (Column column : rtdmTable.getColumns()) {
            Map<String, String> metadataItem = new HashMap<>();
            String jsonType = toJsonType(column.type.toString())
            metadataItem.put(column.name, jsonType);
            jsonTable.metadata.add(metadataItem);
        }

        for (Row row: rtdmTable.iterator()) {
            List<Object> columnValues = new ArrayList<>();
            for (Column column : rtdmTable.getColumns()) {
                String columnType = column.type.toString()
                String columnName = column.name
                Object columnValue = row.columnDataGet(columnName)
                Object value = null;
                if (columnValue != null) {
                    switch (columnType)  {
                        case "DATETIME":
                            value = convertDateTimeToString(columnValue)
                            break
                        default: value = columnValue
                    }
                }
                columnValues.add(value);
            }
            jsonTable.data.add(columnValues)
        }
        return jsonTable
    }

    static String toJsonType(String rtdmType) {
        switch (rtdmType) {
            case "STRING": return "string"
            case "BOOLEAN": return "boolean"
            case "FLOAT": return "decimal"
            case "INT": return "integer"
            case "DATETIME": return "datetime"
        }
        return "null"
    }

    static String convertDateTimeToString(Object object) {
        if (object instanceof GregorianCalendar == false) {
            throw new RuntimeException("Unable to convert calendar as it is not a GregorianCalendar");
        }
        GregorianCalendar cal = (GregorianCalendar) object;
        SimpleDateFormat f = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        f.setCalendar(cal);
        String str = f.format(cal.getTime());
        /*return str.substring(0, 26) + ":" + str.substring(26) + "[" + cal.getTimeZone().getID() + "]";*//*original*/
        return str.substring(0, 23) + "Z";
    }

    RTDMTable toRTDMTable(JsonRtdmTable jsonRtdmTable) {
        RTDMTable rtdmTable = new RTDMTable();
        List<String> columnNames = new ArrayList<>()
        jsonRtdmTable.metadata.each { metadataItem ->
            metadataItem.each {columnName , jsonType ->
                columnNames.add(columnName);
                rtdmTable.columnAdd(columnName, toDataType(jsonType), Collections.emptyList());
            }
        }

        jsonRtdmTable.data.each { jsonRow ->
            Row newRow = rtdmTable.rowAdd();
            jsonRow.eachWithIndex { Object value, int i ->
                String columnName = columnNames.get(i);
                Object rtdmValue = toRtdmValue(value)
                newRow.columnDataSet(columnName, rtdmValue);
            }
        }

        return rtdmTable
    }

    static DataTypes toDataType(String jsonType) {
        switch (jsonType) {
            case "string": return DataTypes.STRING;
            case "boolean": return DataTypes.BOOLEAN;
            case "decimal": return DataTypes.FLOAT;
            case "integer": return DataTypes.INT;
            case "datetime": return DataTypes.DATETIME;
        }
        return null;
    }

    static Object toRtdmValue(Object value) {
        if (value == null) {
            return null
        }
        if (value.getClass() == java.lang.String || value.getClass() == java.lang.Boolean) {
            return value
        }
        if (value.getClass() == java.math.BigDecimal) {
            return value.doubleValue()
        }
        if (value.getClass() == java.lang.Integer || value.getClass() == java.lang.Long) {
            return value.longValue()
        }
        return null
    }
}

// example of request - CHANGE THIS PART FOR MAPPING DIFFERENT CLIENT INPUTS
class Request implements Serializable {
    Long version
    String correlationId
    String clientTimeZone
    RequestInputs inputs
}

class RequestInputs implements Serializable {
    String partyId
    String limitBulkId
    String limitScoringProcessSource
    String calculationSource
    String eventName
    String eventSource
    Long totalRecordNumber
    String eventTimestamp
    JsonRtdmTable limitList;
}

// example of response - CHANGE THIS TO REFLECT OUTPUT FROM RTDM PROCESS
class MyResponse implements Serializable {
    Long version;
    String correlationId;
    String startTimestamp;
    String endTimestamp;
    ResponseItem outputs;
}

class ResponseItem implements Serializable {
    String eventName;
    String partyId;
    String status;
    String description;
    Long totalRecordNumber;
    JsonRtdmTable limitList;
}

@JsonSerialize(using = JsonRtdmTableSerializer)
@JsonDeserialize(using = JsonRtdmTableDeserializer)
class JsonRtdmTable implements Serializable {
    List<Map<String, String>> metadata = new ArrayList<>();
    List<List<Object>> data = new ArrayList<>();
}

class JsonRtdmTableSerializer extends JsonSerializer<JsonRtdmTable> {
    @Override
    void serialize(JsonRtdmTable value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        gen.writeStartArray()

        gen.writeStartObject()
        gen.writeObjectField("metadata", value.metadata)
        gen.writeEndObject()

        gen.writeStartObject()
        gen.writeObjectField("data", value.data)
        gen.writeEndObject()

        gen.writeEndArray()
    }
}

class JsonRtdmTableDeserializer extends JsonDeserializer<JsonRtdmTable> {
    @Override
    JsonRtdmTable deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JsonProcessingException {
        JsonRtdmTable table = new JsonRtdmTable()
        ObjectMapper mapper = (ObjectMapper) p.getCodec()
        JsonNode node = mapper.readTree(p)
        if (node.isArray()) {
            node.each { JsonNode element ->
                if (element.has("metadata")) {
                    table.metadata = mapper.convertValue(element.get("metadata"), List)
                } else if (element.has("data")) {
                    table.data = mapper.convertValue(element.get("data"), List)
                }
            }
        }
        return table
    }
}
// correct
//{
//   "version":1,
//"correlationId":"ifgbezufbsdjasudfz",
//"clientTimeZone":"Asia/Manila",
//"inputs" :{
//      "partyId": "1234",
//      "limitBulkId": "4567",
//      "limitScoringProcessSource": "ABC",
//      "calculationSource": "DEF",
//	  "eventName": "ONLINE_SCORING",
//	  "eventSource": "RTDM",
//	  "totalRecordNumber": 1,
//	  "eventTimestamp": "2025-01-30T10:01:18",
//      "limitList":       [
//         {"metadata":          [
//            {"limitId": "string"},
//            {"limitValidFrom": "string"},
//            {"limitValidTo": "string"},
//            {"limitStatus": "string"},
//            {"codeRiskGrade": "string"},
//            {"limitPilotCode": "string"},
//            {"limitTypeCode": "string"},
//            {"limitSubTypeCode": "string"},
//            {"acqChannelCodes": "string"},
//            {"relationId": "string"},
//            {"relationCode": "string"},
//            {"accountNumber": "string"},
//            {"limitGuaranteedFlag": "string"},
//            {"commodityCategoryGroup": "string"},
//            {"limitScore": "decimal"},
//            {"amtCreditMax": "integer"},
//            {"amtInstalmentMax": "integer"},
//            {"amtDownPaymentMin": "integer"},
//            {"amtDownPaymentMinRel": "decimal"},
//            {"limitAccuracy": "integer"},
//            {"maxTenor": "integer"},
//            {"amtCreditMin": "integer"},
//            {"amtDownPaymentMaxRel": "decimal"},
//            {"minTenor": "integer"}
//         ]},
//         {"data": [         [
//            "LIMITID1",
//            "2025-01-24T10:01:18.051",
//            "2025-01-24T10:01:18.051",
//            "ACTIVE",
//            "A+",
//            "ABC",
//            "DEF",
//            "GHI",
//            "MNO",
//            "ABC123456",
//            "JKL",
//            "XYZ123456",
//            "TEST",
//            "comm1",
//            123.5,
//            1000,
//            1000,
//            1000,
//            50.5,
//            50,
//            12,
//            500,
//            100.5,
//            12
//         ]]}
//      ]
//   }
//}

//incorrect - this is currently done by mapping tostring via objectmapper
//{
//   "version":1,
//   "correlationId":"12345",
//   "clientTimeZone":"Asia/Manila",
//   "inputs":{
//      "partyId":"1234",
//      "limitBulkId":"1234",
//      "limitScoringProcessSource":"ABC",
//      "calculationSource":"DEF",
//      "eventName":"OFFLINE_SCORING",
//      "eventSource":"RTDM",
//      "totalRecordNumber":1,
//      "eventTimestamp":"2025-01-30T10:01:18+08:00",
//      "limitList":{
//         "columns":[
//            {
//               "name":"limitId",
//               "type":"STRING",
//               "ordinal":0
//            },
//            {
//               "name":"limitValidFrom",
//               "type":"STRING",
//               "ordinal":1
//            },
//            {
//               "name":"limitValidTo",
//               "type":"STRING",
//               "ordinal":2
//            },
//            {
//               "name":"limitStatus",
//               "type":"STRING",
//               "ordinal":3
//            },
//            {
//               "name":"codeRiskGrade",
//               "type":"STRING",
//               "ordinal":4
//            },
//            {
//               "name":"limitPilotCode",
//               "type":"STRING",
//               "ordinal":5
//            },
//            {
//               "name":"limitTypeCode",
//               "type":"STRING",
//               "ordinal":6
//            },
//            {
//               "name":"limitSubTypeCode",
//               "type":"STRING",
//               "ordinal":7
//            },
//            {
//               "name":"acqChannelCodes",
//               "type":"STRING",
//               "ordinal":8
//            },
//            {
//               "name":"relationId",
//               "type":"STRING",
//               "ordinal":9
//            },
//            {
//               "name":"relationCode",
//               "type":"STRING",
//               "ordinal":10
//            },
//            {
//               "name":"accountNumber",
//               "type":"STRING",
//               "ordinal":11
//            },
//            {
//               "name":"limitGuaranteedFlag",
//               "type":"STRING",
//               "ordinal":12
//            },
//            {
//               "name":"commodityCategoryGroup",
//               "type":"STRING",
//               "ordinal":13
//            },
//            {
//               "name":"limitScore",
//               "type":"FLOAT",
//               "ordinal":14
//            },
//            {
//               "name":"amtCreditMax",
//               "type":"INT",
//               "ordinal":15
//            },
//            {
//               "name":"amtInstalmentMax",
//               "type":"INT",
//               "ordinal":16
//            },
//            {
//               "name":"amtDownPaymentMin",
//               "type":"INT",
//               "ordinal":17
//            },
//            {
//               "name":"amtDownPaymentMinRel",
//               "type":"FLOAT",
//               "ordinal":18
//            },
//            {
//               "name":"limitAccuracy",
//               "type":"INT",
//               "ordinal":19
//            },
//            {
//               "name":"maxTenor",
//               "type":"INT",
//               "ordinal":20
//            },
//            {
//               "name":"amtCreditMin",
//               "type":"INT",
//               "ordinal":21
//            },
//            {
//               "name":"amtDownPaymentMaxRel",
//               "type":"FLOAT",
//               "ordinal":22
//            },
//            {
//               "name":"minTenor",
//               "type":"INT",
//               "ordinal":23
//            }
//         ]
//      }
//   }
//}