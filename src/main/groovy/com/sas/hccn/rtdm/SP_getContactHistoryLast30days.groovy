package com.sas.hccn.rtdm


import com.homecredit.sas.utils.enumeration.Status
import com.sas.analytics.ph.common.RTDMTable
import com.sas.analytics.ph.common.RTDMTable.Row
import com.sas.analytics.ph.common.jaxb.DataTypes
import com.sas.rtdm.implementation.engine.EventInfo
import org.apache.log4j.Logger
import javax.sql.DataSource
import java.sql.*

class MyActivity implements Runnable {

    private Map <String, DataSource> mapJDBC = null;
    void setMapJDBC(Map <String, DataSource> input) {
        mapJDBC = input;
    }

    // Input variables
    String cuid;
    String channelCd;

    // Output variables
    String status = Status.ERROR.getStatus()
    Long httpResponseCode;    // HTTP response code
    String errorMessage;
    RTDMTable ContactHistoryDetails;

    // Logger
    private static final Logger log = Logger.getLogger('groovyLog');

    // Event info
    EventInfo evtInfo;

    @Override
    void run() {
        List<String> channelCdList = channelCd.split(';')
                .collect { it.trim() }
                .findAll { it }
        if (cuid == null || channelCdList.isEmpty()) {
            return;
        }

        // Create empty table
        ContactHistoryDetails = new RTDMTable();
        ContactHistoryDetails.columnAdd("GENERIC_RESPONSE", DataTypes.STRING, Collections.emptyList());
        ContactHistoryDetails.columnAdd("DELIVERED_FLG", DataTypes.STRING, Collections.emptyList());
        ContactHistoryDetails.columnAdd("CONTACTED_FLG", DataTypes.STRING, Collections.emptyList());
        ContactHistoryDetails.columnAdd("ID_CUID", DataTypes.STRING, Collections.emptyList());
        ContactHistoryDetails.columnAdd("RESPTRACKING_CD", DataTypes.STRING, Collections.emptyList());
        ContactHistoryDetails.columnAdd("RESPONSE_NM", DataTypes.STRING, Collections.emptyList());
        ContactHistoryDetails.columnAdd("CELL_PACKAGE_SK", DataTypes.STRING, Collections.emptyList());
        ContactHistoryDetails.columnAdd("CH_ID", DataTypes.STRING, Collections.emptyList());
        ContactHistoryDetails.columnAdd("DEACTIVATION_REASON_ID", DataTypes.STRING, Collections.emptyList());
        ContactHistoryDetails.columnAdd("IL_COMMUNICATION_ID", DataTypes.STRING, Collections.emptyList());
        ContactHistoryDetails.columnAdd("TREATMENT_SK", DataTypes.STRING, Collections.emptyList());
        ContactHistoryDetails.columnAdd("CH_ID_CHAR", DataTypes.STRING, Collections.emptyList());
        ContactHistoryDetails.columnAdd("CHANNEL_CD", DataTypes.STRING, Collections.emptyList());
        ContactHistoryDetails.columnAdd("CONTACTED_DTTM", DataTypes.STRING, Collections.emptyList());
        ContactHistoryDetails.columnAdd("CREATED_DTTM", DataTypes.STRING, Collections.emptyList());
        ContactHistoryDetails.columnAdd("UPDATE_DTTM", DataTypes.STRING, Collections.emptyList());

        Connection conn = mapJDBC.get('MA_TEMP_JDBC').getConnection()
        PreparedStatement st = null
        Statement stmt = conn.createStatement();
        try {
            if (conn != null) {
                log.info("SP_getContactHistoryLast30days  - Obtaining data from DB");

                StringBuilder placeholdersBuilder = new StringBuilder();
                for (int i = 0; i < channelCdList.size(); i++) {
                    placeholdersBuilder.append("?");
                    if (i < channelCdList.size() - 1) {
                        placeholdersBuilder.append(", ");
                    }
                }
                String inClausePlaceholders = placeholdersBuilder.toString()

                String query = "select  che.GENERIC_RESPONSE\n" +
                        "        , che.DELIVERED_FLG\n" +
                        "        , che.CONTACTED_FLG\n" +
                        "        , che.ID_CUID\n" +
                        "        , che.RESPTRACKING_CD\n" +
                        "        , che.RESPONSE_NM\n" +
                        "        , che.CELL_PACKAGE_SK\n" +
                        "        , che.CH_ID\n" +
                        "        , che.DEACTIVATION_REASON_ID\n" +
                        "        , che.IL_COMMUNICATION_ID\n" +
                        "        , che.TREATMENT_SK\n" +
                        "        , che.CH_ID_CHAR\n" +
                        "        , cp.CHANNEL_CD\n" +
                        "        , che.CONTACTED_DTTM\n" +
                        "        , che.CREATED_DTTM\n" +
                        "        , che.UPDATE_DTTM\n" +
                        "  from cdm.ci_contact_history_ext che\n" +
                        "  join cdm.ci_cell_package cp on cp.cell_package_sk = che.cell_package_sk\n" +
                        "  where che.CONTACTED_DTTM >= trunc(sysdate-30)\n" +
                        "    and che.ID_CUID = ?\n" +
                        "    and cp.CHANNEL_CD in ($inClausePlaceholders)";
                st = conn.prepareStatement(query);

                log.trace("SP_getContactHistoryLast30days : SQL parameter cuid: 1 = " + cuid);
                st.setString(1, cuid);

                for (int i = 0; i < channelCdList.size(); i++) {
                    int index = i + 2; // st starts with 1, and 1st param is already set
                    log.trace("SP_getContactHistoryLast30days : SQL parameter channelCd: $index = " + channelCdList.get(i));
                    st.setString(index, channelCdList.get(i));
                }

                log.trace("SP_getContactHistoryLast30days : SQL query: " + query);
                ResultSet rs = st.executeQuery();

                while (rs.next()) {
                    Row newRow = ContactHistoryDetails.rowAdd();
                    newRow.columnDataSet("GENERIC_RESPONSE", rs.getString("GENERIC_RESPONSE"));
                    newRow.columnDataSet("DELIVERED_FLG", rs.getString("DELIVERED_FLG"));
                    newRow.columnDataSet("CONTACTED_FLG", rs.getString("CONTACTED_FLG"));
                    newRow.columnDataSet("ID_CUID", rs.getString("ID_CUID"));
                    newRow.columnDataSet("RESPTRACKING_CD", rs.getString("RESPTRACKING_CD"));
                    newRow.columnDataSet("RESPONSE_NM", rs.getString("RESPONSE_NM"));
                    newRow.columnDataSet("CELL_PACKAGE_SK", rs.getString("CELL_PACKAGE_SK"));
                    newRow.columnDataSet("CH_ID", rs.getString("CH_ID"));
                    newRow.columnDataSet("DEACTIVATION_REASON_ID", rs.getString("DEACTIVATION_REASON_ID"));
                    newRow.columnDataSet("IL_COMMUNICATION_ID", rs.getString("IL_COMMUNICATION_ID"));
                    newRow.columnDataSet("TREATMENT_SK", rs.getString("TREATMENT_SK"));
                    newRow.columnDataSet("CH_ID_CHAR", rs.getString("CH_ID_CHAR"));
                    newRow.columnDataSet("CHANNEL_CD", rs.getString("CHANNEL_CD"));
                    newRow.columnDataSet("CONTACTED_DTTM", rs.getString("CONTACTED_DTTM"));
                    newRow.columnDataSet("CREATED_DTTM", rs.getString("CREATED_DTTM"));
                    newRow.columnDataSet("UPDATE_DTTM", rs.getString("UPDATE_DTTM"));
                }
                status = Status.OK.getStatus();
            }
        } catch (SQLException e) {
            errorMessage = e.getLocalizedMessage();
            log.error("SP_getContactHistoryLast30days : oracle error:" + e)
        } finally {
            if (st != null) {
                st.close();
            }
            if (stmt != null) {
                stmt.close();
            }
            if (conn != null) {
                conn.close();
            }
        }

        log.trace("SP_getContactHistoryLast30days - End");
    }
}