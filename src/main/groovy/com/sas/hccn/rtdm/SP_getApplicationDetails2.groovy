package com.sas.hccn.rtdm

import com.homecredit.sas.utils.HttpUtils
import com.homecredit.sas.utils.PropertiesUtils
import com.homecredit.sas.utils.enumeration.Status
import com.homecredit.sas.utils.model.PropertiesResponse
import com.sas.rtdm.implementation.engine.EventInfo
import org.apache.log4j.Logger
import org.w3c.dom.NodeList

/**
 * GetApplicationDetailsID
 * @version 10/1/23-001
 */
class GetDetailForClientList implements Runnable {

    // Input variables
    String applicationCode;

    // Output variables
    String status = Status.ERROR.getStatus()
    Long httpResponseCode;    // HTTP response code
    String errorMessage;

    String clientCuid;
    String offerCode;
    String primaryContactValue;
    String secondaryContactValue;
    String clientFullName;

    // Logger
    private static final Logger log = Logger.getLogger('groovyLog');

    // Event info
    EventInfo evtInfo;

    // Constants
    private final String resourcePrefix = "/ApplicationManagementWSv22.GetApplicationData"

    // Variables from properties
    private String username;
    private String password;
    private String host;

    private final String CONFIG_FILE = "/sas/groovy/Connections/bsl.properties";

    @Override
    void run() {

        //start log info
        log.info("Starting process GetApplicationDetails...");
        log.info("GetApplicationDetails - applicationCode: $applicationCode");

        PropertiesResponse bslPropertiesResponse = PropertiesUtils.getProperties(CONFIG_FILE)
        if (bslPropertiesResponse.getProperties() == null) {
            status = bslPropertiesResponse.getStatus().getStatus();
            errorMessage = bslPropertiesResponse.getErrorMessage();
            return;
        }
        Properties bslConfig = bslPropertiesResponse.getProperties();

        username = bslConfig.getProperty("bsl.username");
        password = bslConfig.getProperty("bsl.password");
        host = bslConfig.getProperty("bsl.host");

        log.info("GetApplicationDetails - host: $host");
        log.info("GetApplicationDetails - resource prefix: $resourcePrefix");

        if (applicationCode != null && !applicationCode.isEmpty()) {

            // Setting API variables
            ApplicationDataResponse response = callBslGetApplicationData()
            if (response == null) {
                return
            }

            clientCuid = response.clientCuid
            offerCode = response.offers?.find { it.chosen }?.code
            primaryContactValue = response.client?.contacts?.find { it.contactTypeCode.equalsIgnoreCase("PRIMARY_MOBILE")}?.value
            secondaryContactValue = response.client?.contacts?.find { it.contactTypeCode.equalsIgnoreCase("SECONDARY_MOBILE")}?.value
            clientFullName = response.client?.fullName

            status = Status.OK.getStatus()
        } else {
            log.trace("GetApplicationDetails - No input clients");
        }
    }

    private ApplicationDataResponse callBslGetApplicationData() {
        log.info("GetApplicationDetails - call BSL GetApplicationData: Endpoint URL: " + host + resourcePrefix);

        SOAPConnectionFactory soapConnectionFactory = SOAPConnectionFactory.newInstance();
        SOAPConnection soapConnection = soapConnectionFactory.createConnection();
        MessageFactory messageFactory = MessageFactory.newInstance();
        SOAPMessage soapMessage = messageFactory.createMessage();
        SOAPPart soapPart = soapMessage.getSOAPPart();
        SOAPEnvelope envelope = soapPart.getEnvelope();
        envelope.addNamespaceDeclaration("v22", "http://homecredit.net/hss/application-management/v22");
        SOAPBody soapBody = envelope.getBody();
        SOAPElement GetApplicationDataRequestElem = soapBody.addChildElement("GetApplicationDataRequest", "v22");
        SOAPElement applicationCodeElem = GetApplicationDataRequestElem.addChildElement("applicationCode", "v22");
        applicationCodeElem.addTextNode(applicationCode);
        SOAPElement dataSetElem = GetApplicationDataRequestElem.addChildElement("dataSet", "v22");
        dataSetElem.addTextNode("OFFER");
        SOAPElement dataSetElem2 = GetApplicationDataRequestElem.addChildElement("dataSet", "v22");
        dataSetElem2.addTextNode("CLIENT");
        SOAPElement dataSetElem3 = GetApplicationDataRequestElem.addChildElement("dataSet", "v22");
        dataSetElem3.addTextNode("CUID");
        MimeHeaders headers = soapMessage.getMimeHeaders();
        String authorization = HttpUtils.getBasicAuthToken(username, password)
        headers.addHeader("Authorization", authorization);
        headers.addHeader("SOAPAction", host + resourcePrefix);
        soapMessage.saveChanges();
        //log message
        ByteArrayOutputStream requestStream = new ByteArrayOutputStream();
        soapMessage.writeTo(requestStream);
        String requestMsg = requestStream.toString("UTF-8")
        log.info("GetApplicationDetails - call BSL GetApplicationData: Message request: " + requestMsg)

        SOAPMessage soapResponse = soapConnection.call(soapMessage, host);
        ByteArrayOutputStream responseStream = new ByteArrayOutputStream();
        soapResponse.writeTo(responseStream);
        String responseMsg = responseStream.toString("UTF-8");
        log.info("GetApplicationDetails - call BSL GetApplicationData: Message response: " + responseMsg)

        ApplicationDataResponse applicationDataResponse = new ApplicationDataResponse()
        SOAPBody body = soapResponse.getSOAPBody()
        List<Offer> offerResult = new ArrayList()
        Client client = new Client()
        try {
            NodeList bodyNodes = body.getChildNodes()
            NodeList getApplicationDataResponse = bodyNodes.item(0).getChildNodes()
            NodeList application = getApplicationDataResponse.item(0).getChildNodes()
            for (j in 0..application.length) {
                if (application.item(j) != null) {
                    if (application.item(j).getLocalName().equalsIgnoreCase("offers")) {
                        NodeList offers = application.item(j).getChildNodes()
                        for (k in 0..offers.length) {
                            if (offers.item(k) != null) {
                                NodeList offerDetails = offers.item(k).getChildNodes()
                                Offer offer = new Offer()
                                for (l in 0..offerDetails.length) {
                                    if (offerDetails.item(l) != null) {
                                        if (offerDetails.item(l).getLocalName().equalsIgnoreCase("code")) {
                                            offer.setCode(offerDetails.item(l).getTextContent())
                                        } else if (offerDetails.item(l).getLocalName().equalsIgnoreCase("type")) {
                                            offer.setType(offerDetails.item(l).getTextContent())
                                        } else if (offerDetails.item(l).getLocalName().equalsIgnoreCase("chosen")) {
                                            offer.setChosen(Boolean.parseBoolean(offerDetails.item(l).getTextContent()))
                                        }
                                    }
                                }
                                offerResult.add(offer)
                            }
                        }
                    } // TODO tohle je potreba vyzkouset
                    else if (application.item(j).getLocalName().equalsIgnoreCase("client")) {
                        client.contacts = new ArrayList<>()
                        NodeList clientDetails = application.item(j).getChildNodes()
                        for (l in 0..clientDetails.length) {
                            if (clientDetails.item(l) != null) {
                                if (clientDetails.item(l).getLocalName().equalsIgnoreCase("contacts")) {
                                    NodeList contactDetails = clientDetails.item(l).getChildNodes()
                                    Contact contact = new Contact();
                                    for (k in 0..contactDetails.length) {
                                        if (contactDetails.item(k) != null) {
                                            if (contactDetails.item(k).getLocalName().equalsIgnoreCase("contactTypeCode")) {
                                                contact.setContactTypeCode(contactDetails.item(k).getTextContent())
                                            }
                                            if (contactDetails.item(k).getLocalName().equalsIgnoreCase("value")) {
                                                contact.setValue(contactDetails.item(k).getTextContent())
                                            }
                                        }
                                    }
                                    client.contacts.add(contact)
                                } else if (clientDetails.item(l).getLocalName().equalsIgnoreCase("name")) {
                                    NodeList nameDetails = clientDetails.item(l).getChildNodes()
                                    for (k in 0..nameDetails.length) {
                                        if (nameDetails.item(k) != null && nameDetails.item(k).getLocalName().equalsIgnoreCase("fullName")) {
                                            client.setFullName(nameDetails.item(k).getTextContent())
                                        }
                                    }
                                }
                            }
                        }
                    }
                    else if (application.item(j).getLocalName().equalsIgnoreCase("code")) {
                        applicationDataResponse.setCode(application.item(j).getTextContent())
                    } else if (application.item(j).getLocalName().equalsIgnoreCase("status")) {
                        applicationDataResponse.setStatus(application.item(j).getTextContent())
                    } else if (application.item(j).getLocalName().equalsIgnoreCase("substatus")) {
                        applicationDataResponse.setSubstatus(application.item(j).getTextContent())
                    } else if (application.item(j).getLocalName().equalsIgnoreCase("clientCuid")) {
                        applicationDataResponse.setClientCuid(application.item(j).getTextContent())
                    }
                }
            }
        } catch (Exception e) {
            log.error("GetApplicationDetails - Failed to map BSL GetApplicationData offer response: " + e.getLocalizedMessage())
            status = Status.ERROR
            errorMessage = "Failed to BSL GetApplicationData offer response: " + e.getLocalizedMessage()
            return null
        }

        applicationDataResponse.setOffers(offerResult)
        applicationDataResponse.setClient(client)
        return applicationDataResponse
    }
}

class ApplicationDataResponse {
    String code;
    String status;
    String substatus;
    String clientCuid;
    List<Offer> offers;
    Client client;
}

class Offer {
    String code;
    String type;
    Boolean chosen;
}

class Client {
    List<Contact> contacts;
    String fullName;
}

class Contact {
    String contactTypeCode;
    String value;
}