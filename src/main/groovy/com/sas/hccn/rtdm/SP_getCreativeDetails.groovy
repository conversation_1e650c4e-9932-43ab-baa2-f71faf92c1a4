package com.sas.hccn.rtdm

import com.homecredit.sas.utils.HttpUtils
import com.homecredit.sas.utils.MappingUtils
import com.homecredit.sas.utils.PropertiesUtils
import com.homecredit.sas.utils.enumeration.RequestMethod
import com.homecredit.sas.utils.enumeration.Status
import com.homecredit.sas.utils.model.HttpCallResponse
import com.homecredit.sas.utils.model.PropertiesResponse
import com.sas.rtdm.implementation.engine.EventInfo
import org.apache.log4j.Logger

/**
 * GetCreativeDetails
 * @version 16/02/23-002
 */
class GetDetailForClientList implements Runnable {

    // Input variables
    String creativeId;

    // Output variables
    String status = Status.ERROR.getStatus()
    Long httpResponseCode;    // HTTP response code
    String errorMessage;

    String id;
    String name;
    String creativeCd;
    Long creativeChannel;
    Long templateId;

    // Logger
    private static final Logger log = Logger.getLogger('groovyLog');

    // Event info
    EventInfo evtInfo;

    // Constants
    private final String resourcePrefix = "/marketingDesign/creatives/"

    // Variables from properties
    private String token;
    private String host;

    private final String CONFIG_FILE = "/sas/groovy/Connections/extapigwservice.properties";

    @Override
    void run() {

        //start log info
        log.info("Starting process GetCreativeDetails...");
        log.info("GetCreativeDetails - creativeId: $creativeId");

        PropertiesResponse propertiesResponse = PropertiesUtils.getProperties(CONFIG_FILE)
        if (propertiesResponse.getProperties() == null) {
            status = propertiesResponse.getStatus().getStatus();
            errorMessage = propertiesResponse.getErrorMessage();
            return;
        }
        Properties config = propertiesResponse.getProperties();

        token = config.getProperty("extapigwservice.token");
        host = config.getProperty("extapigwservice.host");

        log.info("GetCreativeDetails - host: $host");
        log.info("GetCreativeDetails - resource prefix: $resourcePrefix");

        if (creativeId != null && !creativeId.isEmpty()) {

            // Setting API variables
            String uri = host + resourcePrefix + creativeId
            log.info("GetCreativeDetails - Endpoint URL: $uri");

            HttpCallResponse httpCallResponse = HttpUtils.runHttpProcess(
                    evtInfo.getEventName(),
                    evtInfo.getSimulationDate().getTime(),
                    uri,
                    HttpUtils.getBearerAuthToken(token),
                    [(HttpUtils.CONTENT_TYPE): "application/vnd.sas.design.publish.reps.v1.creative+json"],
                    RequestMethod.GET,
                    1000,
                    null
            );

            httpResponseCode = httpCallResponse.getHttpResponseCode();
            status = httpCallResponse.getStatus().getStatus();
            errorMessage = httpCallResponse.getErrorMessage();
            String responseString = httpCallResponse.getResponse()
            if (responseString == null) {
                return
            }

            log.trace("GetCreativeDetails - httpResponseCode:" + httpResponseCode);
            log.trace("GetCreativeDetails - response:" + responseString);

            CreativeResponse responseObject = MappingUtils.mapToObject(responseString, CreativeResponse.class)
            if (responseObject == null) {
                status = Status.ERROR.getStatus();
                errorMessage = "Failed to map response. Wrong response data format."
                return
            }

            if (httpResponseCode == 200) {
                id = responseObject.id;
                name = responseObject.name;
                creativeCd = responseObject.externalCode;
                creativeChannel = responseObject.attributes.find {it.identityCode.equalsIgnoreCase("creative_channel")}?.value as Long;
                templateId = responseObject.attributes.find {it.identityCode.equalsIgnoreCase("template_id")}?.value as Long;

                if (id == null) {
                    status = Status.ERROR.getStatus();
                    errorMessage = "Could not find 'id' in response."
                    return
                }
                if (name == null) {
                    status = Status.ERROR.getStatus();
                    errorMessage = "Could not find 'name' in response."
                    return
                }
                if (creativeCd == null) {
                    status = Status.ERROR.getStatus();
                    errorMessage = "Could not find 'creativeCd' in response."
                    return
                }
                if (creativeChannel == null) {
                    status = Status.ERROR.getStatus();
                    errorMessage = "Could not find 'creativeChannel' in response."
                    return
                }
                if (templateId == null) {
                    status = Status.ERROR.getStatus();
                    errorMessage = "Could not find 'templateId' in response."
                    return
                }
            }
        } else {
            log.trace("GetCreativeDetails - No input clients");
        }
    }
}

class CreativeResponse implements Serializable {
    String id;
    String name;
    String externalCode;
    List<Attribute> attributes;
    //...
}

class Attribute implements Serializable {
    String identityCode;
    String dataType;
    Object value;
}