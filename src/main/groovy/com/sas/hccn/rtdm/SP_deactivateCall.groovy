package com.sas.hccn.rtdm

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.ObjectMapper
import org.apache.kafka.clients.producer.KafkaProducer
import org.apache.kafka.clients.producer.Producer
import org.apache.kafka.clients.producer.ProducerConfig
import org.apache.kafka.clients.producer.ProducerRecord
import org.apache.log4j.Logger

class GetDetailForClientList implements Runnable {

    // Input variables
    String id_cuid;
    String il_communication_id;
    String name_call_list;

    // Output variables
    String status = "ERROR";            // either "OK" or "ERROR"
    String errorMessage;

    // Internal variables
    private static final Logger log = Logger.getLogger('GetDetailForClientList'); //groovyLog
    private static final Logger requestReplyLog = Logger.getLogger('groovyRequestReply')

    private final String CONFIG_FILE = "/sas/groovy/Connections/kafka.properties";
    private Properties config = new Properties();

    @Override
    void run() {
        //start log info
        log.info("Starting process DeactivateCall to Kafka...");
        log.info("DeactivateCall - id_cuid: $id_cuid");
        log.info("DeactivateCall - il_communication_id: $il_communication_id");
        log.info("DeactivateCall - name_call_list: $name_call_list");

        try {
            log.info("DeactivateCall - Loading configuration from path '$CONFIG_FILE'");
            this.config.load(new FileInputStream(CONFIG_FILE));
        } catch (Exception e) {
            log.error("DeactivateCall - Failed to load configuration: " + e.getMessage())
            status = "ERROR";
            throw e;
        }

        String servers = config.getProperty("kafka.servers"); // "kafka01-vn00c1.vn.infra:9092,kafka02-vn00c1.vn.infra:9092,kafka03-vn00c1.vn.infra:9092";
        String topic = config.getProperty("kafka.topic"); // "affinity.rtdm_data";

        log.info("DeactivateCall - Kafka configuration: servers = $servers, topic = $topic")

        if ((il_communication_id && name_call_list) || (!il_communication_id && !name_call_list) ) {
            status = "ERROR"
            errorMessage = "Exactly 1 of IL_COMMUNICATION_ID, NAME_CALL_LIST must be filled."
            return;
        }
        Message message = new Message()
        message.setId_cuid(id_cuid)
        message.setIl_communication_id(il_communication_id)
        message.setName_call_list(name_call_list)

        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        String jsonMessage = mapper.writeValueAsString(message)
          log.info("DeactivateCall - Serialized message:" + jsonMessage)

        Properties props = new Properties();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, servers);
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");

        Producer<String, String> producer = null;
        try {
            producer = new KafkaProducer<>(props);
            producer.send(new ProducerRecord<>(topic, il_communication_id, jsonMessage));
            log.info("DeactivateCall - Message sent successfully");
            requestReplyLog.info("DeactivateCall - Sent message: $jsonMessage to Kafka (servers = $servers, topic = $topic)")
        } catch (Exception e) {
            log.error("DeactivateCall - Failed to send message: " + e.getMessage())
            throw e;
        } finally {
            if (producer != null) {
                producer.close();
            }
        }
    }
}

class Message implements Serializable {

    @JsonProperty("ID_CUID")
    String id_cuid;

    @JsonProperty("IL_COMMUNICATION_ID")
    String il_communication_id;

    @JsonProperty("NAME_CALL_LIST")
    String name_call_list;
}
