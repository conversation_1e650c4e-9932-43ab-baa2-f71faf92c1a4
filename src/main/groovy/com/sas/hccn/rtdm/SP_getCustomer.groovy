package com.sas.hccn.rtdm


import com.homecredit.sas.utils.HttpUtils
import com.homecredit.sas.utils.MappingUtils
import com.homecredit.sas.utils.PropertiesUtils
import com.homecredit.sas.utils.TokenUtils
import com.homecredit.sas.utils.enumeration.RequestMethod
import com.homecredit.sas.utils.enumeration.Status
import com.homecredit.sas.utils.model.HttpCallResponse
import com.homecredit.sas.utils.model.PropertiesResponse
import com.homecredit.sas.utils.model.TokenResponse
import com.sas.rtdm.implementation.engine.EventInfo
import org.apache.log4j.Logger

import javax.sql.DataSource

/**
 * Get Customer
 * @version 05/11/22-002
 */
class GetDetailForClientList implements Runnable {

    private Map <String, DataSource> mapJDBC = null;
    void setMapJDBC(Map <String, DataSource> input) {
        mapJDBC = input;
    }

    // Input variables
    String contractNumber;

    // Output variables
    String status = Status.ERROR.getStatus()
    Long httpResponseCode;    // HTTP response code
    String errorMessage;
    String cuid;

    // Internal variables
    private static final Logger log = Logger.getLogger('groovyLog');

    // Event info
    EventInfo evtInfo;

    // Constants
    private final String resourcePrefix = "/rest/v12/contracts/";
    private final String resourceSuffix = "/customer";

    // Variables from properties
    private String username;
    private String password;
    private String host;
    private String authPath; //= "https://sso.vn00c1.vn.infra/auth/realms/hci/protocol/openid-connect/token";

    private final String CONFIG_FILE = "/sas/groovy/Connections/openid.properties";

    @Override
    void run() {

        //start log info
        log.info("Starting process GetCustomer...");
        log.info("GetCustomer - contractNumber: $contractNumber");

        PropertiesResponse propertiesResponse = PropertiesUtils.getProperties(CONFIG_FILE)
        if (propertiesResponse.getProperties() == null) {
            status = propertiesResponse.getStatus().getStatus();
            errorMessage = propertiesResponse.getErrorMessage();
            return;
        }
        Properties config = propertiesResponse.getProperties();

        username = config.getProperty("openid.username");
        password = config.getProperty("openid.password");
        host = config.getProperty("openid.host");
        authPath = config.getProperty("openid.authpath");

        log.info("GetCustomer - host: $host");
        log.info("GetCustomer - resource prefix: $resourcePrefix");
        log.info("GetCustomer - resource suffix: $resourceSuffix");

        if (contractNumber != null && !contractNumber.isEmpty()) {

            // Setting API variables
            TokenResponse tokenResponse = TokenUtils.getToken(
                    mapJDBC,
                    evtInfo.getEventName(),
                    evtInfo.getSimulationDate().getTime(),
                    username,
                    password,
                    authPath,
                    "MA_TEMP_JDBC",
                    "RTDM_TEMP.SSO_TOKEN"
            )
            if (tokenResponse.getToken() == null) {
                status = tokenResponse.getStatus().getStatus();
                errorMessage = tokenResponse.getErrorMessage();
                return;
            }
            String authToken = tokenResponse.getToken();

            String uri = host + resourcePrefix + contractNumber + resourceSuffix;
            log.info("GetCustomer - Endpoint URL: $uri");

            HttpCallResponse httpCallResponse = HttpUtils.runHttpProcess(
                    evtInfo.getEventName(),
                    evtInfo.getSimulationDate().getTime(),
                    uri,
                    HttpUtils.getBearerAuthToken(authToken),
                    [(HttpUtils.CONTENT_TYPE): "application/json"],
                    RequestMethod.GET,
                    null
            );

            httpResponseCode = httpCallResponse.getHttpResponseCode();
            status = httpCallResponse.getStatus().getStatus();
            errorMessage = httpCallResponse.getErrorMessage();
            String responseString = httpCallResponse.getResponse()
            if (responseString == null) {
                return
            }

            log.trace("GetCustomer - httpResponseCode:" + httpResponseCode);
            log.trace("GetCustomer - response:" + responseString);

            MyResponse responseObject = MappingUtils.mapToObject(responseString, MyResponse.class)
            if (responseObject == null) {
                status = Status.ERROR.getStatus();
                errorMessage = "Failed to map response. Wrong response data format."
                return
            }

            if (httpResponseCode == 200) {
                log.trace("GetCustomer - id:" + responseObject.id);
                cuid = responseObject.id
            }
        } else {
            log.trace("GetCustomer - No input clients");
        }
    }
}

class MyResponse implements Serializable {
    String id;
}