package com.sas.hccn.rtdm

import com.homecredit.sas.utils.HttpUtils
import com.homecredit.sas.utils.PropertiesUtils
import com.homecredit.sas.utils.enumeration.Status
import com.homecredit.sas.utils.model.PropertiesResponse
import com.sas.rtdm.implementation.engine.EventInfo
import org.apache.log4j.Logger
import org.w3c.dom.NodeList

/**
 * GetOfferDetailsID
 * @version 10/1/23-001
 */
class GetDetailForClientList implements Runnable {

    // Input variables
    String offerCode;

    // Output variables
    String status = Status.ERROR.getStatus()
    Long httpResponseCode;    // HTTP response code
    String errorMessage;

    String productCode;

    // Logger
    private static final Logger log = Logger.getLogger('groovyLog');

    // Event info
    EventInfo evtInfo;

    // Constants
    private final String bslResourcePrefix = "/CustomOfferWSv21.getCustomerOfferResponse"

    // Variables from properties
    private String username;
    private String password;
    private String host;

    private final String CONFIG_FILE = "/sas/groovy/Connections/bsl.properties";

    @Override
    void run() {

        //start log info
        log.info("Starting process OfferDetails...");
        log.info("OfferDetails - offerCode: $offerCode");

        PropertiesResponse bslPropertiesResponse = PropertiesUtils.getProperties(CONFIG_FILE)
        if (bslPropertiesResponse.getProperties() == null) {
            status = bslPropertiesResponse.getStatus().getStatus();
            errorMessage = bslPropertiesResponse.getErrorMessage();
            return;
        }
        Properties bslConfig = bslPropertiesResponse.getProperties();

        username = bslConfig.getProperty("bsl.username");
        password = bslConfig.getProperty("bsl.password");
        host = bslConfig.getProperty("bsl.host");

        log.info("OfferDetails - Host: $host");
        log.info("OfferDetails - resource prefix: $bslResourcePrefix");

        if (offerCode != null && !offerCode.isEmpty()) {

            // Setting API variables
            CustomerOfferResponse response = callBslGetCustomerOffer(offerCode)
            if (response == null) {
                return;
            }

            if (response.products.size() > 0) {
                productCode = response.products.get(0).getCode()
            }

            status = Status.OK.toString()
        } else {
            log.trace("OfferDetails - No input clients");
        }
    }

    CustomerOfferResponse callBslGetCustomerOffer(String offerCode) {
        log.info("OfferDetails - call BSL GetCustomerOffer: Endpoint URL: " + host + bslResourcePrefix);

        SOAPConnectionFactory soapConnectionFactory = SOAPConnectionFactory.newInstance();
        SOAPConnection soapConnection = soapConnectionFactory.createConnection();
        MessageFactory messageFactory = MessageFactory.newInstance();
        SOAPMessage soapMessage = messageFactory.createMessage();
        SOAPPart soapPart = soapMessage.getSOAPPart();
        SOAPEnvelope envelope = soapPart.getEnvelope();
        SOAPBody soapBody = envelope.getBody();
        SOAPElement GetCustomerOfferRequestElem = soapBody.addChildElement("GetCustomerOfferRequest", "", "http://homecredit.net/hss/customeroffer/v21");
        SOAPElement offerCodeElem = GetCustomerOfferRequestElem.addChildElement("offerCode", "");
        offerCodeElem.addTextNode(offerCode);
//        SOAPElement dataSetElem = GetCustomerOfferRequestElem.addChildElement("dataSet", "");
//        dataSetElem.addTextNode("VALIDITY");
//        SOAPElement dataSetElem3 = GetCustomerOfferRequestElem.addChildElement("dataSet", "");
//        dataSetElem3.addTextNode("SERVICES");
        MimeHeaders headers = soapMessage.getMimeHeaders();
        String authorization = HttpUtils.getBasicAuthToken(username, password)
        headers.addHeader("Authorization", authorization);
        headers.addHeader("SOAPAction", host + bslResourcePrefix);
        soapMessage.saveChanges();
        //log message
        ByteArrayOutputStream requestStream = new ByteArrayOutputStream();
        soapMessage.writeTo(requestStream);
        String requestMsg = requestStream.toString("UTF-8")
        log.info("OfferDetails - call BSL GetCustomerOffer: Message request: " + requestMsg)

        SOAPMessage soapResponse = soapConnection.call(soapMessage, host);
        ByteArrayOutputStream responseStream = new ByteArrayOutputStream();
        soapResponse.writeTo(responseStream);
        String responseMsg = responseStream.toString("UTF-8");
        log.info("OfferDetails - call BSL GetCustomerOffer: Message response: " + responseMsg)

        SOAPBody body = soapResponse.getSOAPBody()
        CustomerOfferResponse customerOfferResponse = new CustomerOfferResponse()
        customerOfferResponse.products = new ArrayList<>()
        try {
            NodeList bodyNodes = body.getChildNodes()
            NodeList getCustomerOfferResponse = bodyNodes.item(0).getChildNodes()
            NodeList customerOffer = getCustomerOfferResponse.item(0).getChildNodes()
            for (j in 0..customerOffer.length) {
                if (customerOffer.item(j) != null) {
                    if (customerOffer.item(j).getLocalName().equalsIgnoreCase("product")) {
                        NodeList product = customerOffer.item(j).getChildNodes()
                        Product productResponse = new Product()
                        for (i in 0..product.length) {
                            if (product.item(i) != null) {
                                if (product.item(i).getLocalName().equalsIgnoreCase("code")) {
                                    productResponse.setCode(product.item(i).getTextContent())
                                }
                            }
                        }
                        customerOfferResponse.products.add(productResponse)
                    }
                }
            }
        } catch (Exception e) {
            log.error("OfferDetails - Failed to map BSL GetCustomerOffer offer response: " + e.getLocalizedMessage())
            status = Status.ERROR
            errorMessage = "Failed to map BSL GetCustomerOffer offer response: " + e.getLocalizedMessage()
            return null
        }
        return customerOfferResponse
    }
}

class CustomerOfferResponse {
    List<Product> products;
    //...
}

class Product {
    String code;
    //...
}