package com.sas.hccn.rtdm;

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.ObjectMapper
import com.homecredit.sas.utils.PropertiesUtils
import com.homecredit.sas.utils.model.PropertiesResponse
import org.apache.log4j.Logger

import java.nio.charset.StandardCharsets
import java.time.format.DateTimeFormatter

/**
 * @version 22/07/31-004
 */
class GetDetailForClientList implements Runnable {

    // Input variables
    String externalId;
    String systemCode;
    String messageCode;
    Object expires;
    String priority;
    String reportLevel;
    String reportContentType;
    String recipient;
    String text;

    String CHANNEL;
    String SAS_TEMPLATE_ID;
    String CONTRACT_NUM;
    Long CUID;
    String CUSTOM_INFO1;
    String CUSTOM_INFO2;
    String CUSTOM_INFO3;
    String CUSTOM_INFO4;
    String CUSTOM_INFO5;
    String CUSTOM_INFO6;
    String CUSTOM_INFO7;
    String CUSTOM_INFO8;
    String CUSTOM_INFO9;
    String CUSTOM_INFO10;
    String YELLOW_MEDIA_LINK;
    String YELLOW_MEDIA_NAME;
    String WA_LANGUAGE;

    // Output variables
    String status = "ERROR";            // either "OK" or "ERROR"
    String errorMessage;

    // Internal variables
    private static final Logger log = Logger.getLogger('groovyLog');
    private static final Logger requestReplyLog = Logger.getLogger('groovyRequestReply')

    private final String CONFIG_FILE = "/sas/groovy/Connections/rabbitmq.properties";

    @Override
    void run() {
        //start log info
        log.info("Starting process SendWhatsApp to RabbitMQ...");
        log.info("SendWhatsApp - externalId: $externalId");
        log.info("SendWhatsApp - systemCode: $systemCode");
        log.info("SendWhatsApp - messageCode: $messageCode");
        log.info("SendWhatsApp - expires: $expires");
        log.info("SendWhatsApp - priority: $priority");
        log.info("SendWhatsApp - reportLevel: $reportLevel");
        log.info("SendWhatsApp - reportContentType: $reportContentType");
        log.info("SendWhatsApp - recipient: $recipient");
        log.info("SendWhatsApp - text: $text");
        log.info("SendWhatsApp - CHANNEL: $CHANNEL");
        log.info("SendWhatsApp - SAS_TEMPLATE_ID: $SAS_TEMPLATE_ID");
        log.info("SendWhatsApp - CONTRACT_NUM: $CONTRACT_NUM");
        log.info("SendWhatsApp - CUID: $CUID");
        log.info("SendWhatsApp - CUSTOM_INFO1: $CUSTOM_INFO1");
        log.info("SendWhatsApp - CUSTOM_INFO2: $CUSTOM_INFO2");
        log.info("SendWhatsApp - CUSTOM_INFO3: $CUSTOM_INFO3");
        log.info("SendWhatsApp - CUSTOM_INFO4: $CUSTOM_INFO4");
        log.info("SendWhatsApp - CUSTOM_INFO5: $CUSTOM_INFO5");
        log.info("SendWhatsApp - CUSTOM_INFO6: $CUSTOM_INFO6");
        log.info("SendWhatsApp - CUSTOM_INFO7: $CUSTOM_INFO7");
        log.info("SendWhatsApp - CUSTOM_INFO8: $CUSTOM_INFO8");
        log.info("SendWhatsApp - CUSTOM_INFO9: $CUSTOM_INFO9");
        log.info("SendWhatsApp - CUSTOM_INFO10: $CUSTOM_INFO10");
        log.info("SendWhatsApp - YELLOW_MEDIA_LINK: $YELLOW_MEDIA_LINK");
        log.info("SendWhatsApp - YELLOW_MEDIA_NAME: $YELLOW_MEDIA_NAME");
        log.info("SendWhatsApp - WA_LANGUAGE: $WA_LANGUAGE");

        PropertiesResponse propertiesResponse = PropertiesUtils.getProperties(CONFIG_FILE)
        if (propertiesResponse.getProperties() == null) {
            status = propertiesResponse.getStatus().getStatus();
            errorMessage = propertiesResponse.getErrorMessage();
            return;
        }
        Properties config = propertiesResponse.getProperties();

        String host = config.getProperty("rabbitmq.host");
        String routingKey = config.getProperty("rabbitmq.sms-routing-key");
        String username = config.getProperty("rabbitmq.username");
        String password = config.getProperty("rabbitmq.password");
        String virtualHost = config.getProperty("rabbitmq.virtual-host");
        String exchange = config.getProperty("rabbitmq.exchange");

        log.info("SendWhatsApp - RabbitMQ configuration: host = $host, routingKey = $routingKey, virtualHost = $virtualHost, exchange = $exchange")
        Message message = new Message()

        message.setExternalId(externalId)
        message.setSystemCode(systemCode)
        message.setMessageCode(messageCode)
        if (expires instanceof GregorianCalendar) {
            message.setExpires(DateTimeFormatter.ISO_OFFSET_DATE_TIME.format(((GregorianCalendar) expires).toZonedDateTime()))
        }
        message.setPriority(priority)
        message.setReportLevel(reportLevel)
        message.setReportContentType(reportContentType)
        message.setRecipient(recipient)
        message.setText(text)
        List<Attribute> attributes = new ArrayList<Attribute>();
        if (CHANNEL) attributes.add(new Attribute(type: "CHANNEL", value: CHANNEL))
        if (SAS_TEMPLATE_ID) attributes.add(new Attribute(type: "SAS_TEMPLATE_ID", value: SAS_TEMPLATE_ID))
        if (CONTRACT_NUM) attributes.add(new Attribute(type: "CONTRACT_NUM", value: CONTRACT_NUM))
        if (CUID) attributes.add(new Attribute(type: "CUID", value: CUID.toString()))
        if (CUSTOM_INFO1) attributes.add(new Attribute(type: "CUSTOM_INFO1", value: CUSTOM_INFO1))
        if (CUSTOM_INFO2) attributes.add(new Attribute(type: "CUSTOM_INFO2", value: CUSTOM_INFO2))
        if (CUSTOM_INFO3) attributes.add(new Attribute(type: "CUSTOM_INFO3", value: CUSTOM_INFO3))
        if (CUSTOM_INFO4) attributes.add(new Attribute(type: "CUSTOM_INFO4", value: CUSTOM_INFO4))
        if (CUSTOM_INFO5) attributes.add(new Attribute(type: "CUSTOM_INFO5", value: CUSTOM_INFO5))
        if (CUSTOM_INFO6) attributes.add(new Attribute(type: "CUSTOM_INFO6", value: CUSTOM_INFO6))
        if (CUSTOM_INFO7) attributes.add(new Attribute(type: "CUSTOM_INFO7", value: CUSTOM_INFO7))
        if (CUSTOM_INFO8) attributes.add(new Attribute(type: "CUSTOM_INFO8", value: CUSTOM_INFO8))
        if (CUSTOM_INFO9) attributes.add(new Attribute(type: "CUSTOM_INFO9", value: CUSTOM_INFO9))
        if (CUSTOM_INFO10) attributes.add(new Attribute(type: "CUSTOM_INFO10", value: CUSTOM_INFO10))
        if (YELLOW_MEDIA_LINK) attributes.add(new Attribute(type: "YELLOW_MEDIA_LINK", value: YELLOW_MEDIA_LINK))
        if (YELLOW_MEDIA_NAME) attributes.add(new Attribute(type: "YELLOW_MEDIA_NAME", value: YELLOW_MEDIA_NAME))
        if (WA_LANGUAGE) attributes.add(new Attribute(type: "WA_LANGUAGE", value: WA_LANGUAGE))
        message.setAttributes(attributes)

        MessageWrapper messageWrapper = new MessageWrapper()
        messageWrapper.setMessage(Arrays.asList(message))

        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        String jsonMessage = mapper.writeValueAsString(messageWrapper)
        log.info("SendWhatsApp - Serialized message:" + jsonMessage)

        Map<String, Object> headerMap = new HashMap<String, Object>();
        headerMap.put("SYSTEM_CODE", systemCode);
        headerMap.put("REQUEST_ID", externalId);
        headerMap.put("priority", 0);
        headerMap.put("cuid", CUID.toString());
        headerMap.put("CorrelationID", externalId)
        headerMap.put("Type", "JMSType")
        BasicProperties messageProperties = new AMQP.BasicProperties.Builder()
                .contentType("application/json")
                .headers(headerMap)
                .build();

        ConnectionFactory factory = new ConnectionFactory();
        factory.setHost(host);
        factory.setUsername(username);
        factory.setPassword(password);
        factory.setVirtualHost(virtualHost);
        Connection connection;
        try {
            connection = factory.newConnection();
            Channel channel = connection.createChannel();
            channel.basicPublish(exchange, routingKey, messageProperties, jsonMessage.getBytes(StandardCharsets.UTF_8));
            log.info("SendWhatsApp - Message sent successfully");
            requestReplyLog.info("SendWhatsApp - Sent message: $jsonMessage to RabbitMQ (host = $host, routingKey = $routingKey, virtualHost = $virtualHost, exchange = $exchange)")
            status = "OK";
        } catch (Exception e) {
            log.error("SendWhatsApp - Failed to send message: " + e.getMessage())
            status = "ERROR";
            throw e;
        } finally {
            if (connection != null) {
                connection.close()
            }
        }
    }
}

class MessageWrapper implements Serializable {
    List<Message> message;
}

class Message implements Serializable {
    String externalId;
    String systemCode;
    String messageCode;
    String expires;
    List<Attribute> attributes;
    String priority;
    String reportLevel;
    String reportContentType;
    String recipient;
    String text;
}

class Attribute implements Serializable {
    String type;
    String value;
}
