package com.sas.hccn.rtdm

import com.homecredit.sas.utils.HttpUtils
import com.homecredit.sas.utils.MappingUtils
import com.homecredit.sas.utils.PropertiesUtils
import com.homecredit.sas.utils.enumeration.RequestMethod
import com.homecredit.sas.utils.enumeration.Status
import com.homecredit.sas.utils.model.HttpCallResponse
import com.homecredit.sas.utils.model.PropertiesResponse
import com.sas.rtdm.implementation.engine.EventInfo
import org.apache.log4j.Logger

/**
 * GetProductProfileCodeID
 * @version 10/01/23-001
 */
class GetDetailForClientList implements Runnable {

    // Input variables
    String productCode;

    // Output variables
    String status = Status.ERROR.getStatus()
    Long httpResponseCode;    // HTTP response code
    String errorMessage;

    String productProfileCode;

    // Logger
    private static final Logger log = Logger.getLogger('groovyLog');

    // Event info
    EventInfo evtInfo;

    // Constants
    private final String resourcePrefix = "/openapi/v1/products/"
    private final String resourceSuffix = "?deps=PROFILE"

    // Variables from properties
    private String host;
    private String username;
    private String password;

    private final String CONFIG_FILE = "/sas/groovy/Connections/prc.properties";

    @Override
    void run() {

        //start log info
        log.info("Starting process GetProductProfileCode...");
        log.info("GetProductProfileCode - product code: $productCode")

        PropertiesResponse propertiesResponse = PropertiesUtils.getProperties(CONFIG_FILE)
        if (propertiesResponse.getProperties() == null) {
            status = propertiesResponse.getStatus().getStatus();
            errorMessage = propertiesResponse.getErrorMessage();
            return;
        }
        Properties config = propertiesResponse.getProperties();

        host = config.getProperty("prc.host");
        username = config.getProperty("prc.username")
        password = config.getProperty("prc.password")

        log.info("GetProductProfileCode - host: $host");
        log.info("GetProductProfileCode - resource prefix: $resourcePrefix");
        log.info("GetProductProfileCode - resource suffix: $resourceSuffix");

        // Setting API variables
        String uri = host + resourcePrefix + productCode + resourceSuffix;
        log.info("GetProductProfileCode - Endpoint URL: $uri");

        HttpCallResponse httpCallResponse = HttpUtils.runHttpProcess(
                evtInfo.getEventName(),
                evtInfo.getSimulationDate().getTime(),
                uri,
                HttpUtils.getBasicAuthToken(username, password),
                [(HttpUtils.CONTENT_TYPE): "application/json"],
                RequestMethod.GET,
                null
        );

        httpResponseCode = httpCallResponse.getHttpResponseCode();
        status = httpCallResponse.getStatus().getStatus();
        errorMessage = httpCallResponse.getErrorMessage();
        String responseString = httpCallResponse.getResponse()
        if (responseString == null) {
            return
        }

        log.trace("GetProductProfileCode - httpResponseCode:" + httpResponseCode);
        log.trace("GetProductProfileCode - response:" + responseString);

        Response[] responseObject = MappingUtils.mapToObject(responseString, Response[].class)
        if (responseObject == null) {
            status = Status.ERROR.getStatus();
            errorMessage = "Failed to map response. Wrong response data format."
            return
        }

        if (httpResponseCode == 200) {
            if (responseObject.size() == 0) {
                log.info("GetProductProfileCode - the response is empty")
                return;
            }

            log.trace("GetProductProfileCode - record:" + responseObject[0].code);

            productProfileCode = responseObject[0].productProfile.code
            status = Status.OK.getStatus();
        }
    }
}

class Response implements Serializable {
    String code;
    String name;
    ProductProfile productProfile;
    //...
}

class ProductProfile implements Serializable {
    String code;
    // ...
}
