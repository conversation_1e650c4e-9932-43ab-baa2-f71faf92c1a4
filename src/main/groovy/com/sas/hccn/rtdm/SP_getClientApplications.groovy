package com.sas.hccn.rtdm

import com.homecredit.sas.utils.HttpUtils
import com.homecredit.sas.utils.MappingUtils
import com.homecredit.sas.utils.PropertiesUtils
import com.homecredit.sas.utils.TokenUtils
import com.homecredit.sas.utils.enumeration.RequestMethod
import com.homecredit.sas.utils.enumeration.Status
import com.homecredit.sas.utils.model.HttpCallResponse
import com.homecredit.sas.utils.model.PropertiesResponse
import com.homecredit.sas.utils.model.TokenResponse
import com.sas.analytics.ph.common.RTDMTable
import com.sas.analytics.ph.common.RTDMTable.Row
import com.sas.analytics.ph.common.jaxb.DataTypes
import com.sas.rtdm.implementation.engine.EventInfo
import org.apache.log4j.Logger

import javax.sql.DataSource

/**
 * GetClientApplications
 * @version 10/11/22-001
 */
class GetDetailForClientList implements Runnable {

    private Map <String, DataSource> mapJDBC = null;
    void setMapJDBC(Map <String, DataSource> input) {
        mapJDBC = input;
    }

    // Input variables
    String id_cuid;

    // Output variables
    String status = Status.ERROR.getStatus()
    Long httpResponseCode;    // HTTP response code
    String errorMessage;
    RTDMTable ApplicationDetails;

    // Logger
    private static final Logger log = Logger.getLogger('groovyLog');

    // Event info
    EventInfo evtInfo;

    // Constants
    private final String searchPrefix = "/bsl/openapi/v1.0/applications/search?pageNumber=1&pageSize=1&cuid="
    private final String prefix = "/bsl/openapi/v1.0/applications/"
//    private final String suffix = "?projections=DOCUMENT&projections=CLIENT" //TODO projekce

    // Variables from properties
    private String username;
    private String password;
    private String host; //https://bsl.ph00a1.cz.infra/bsl/openapi
    private String authPath //https://sso.ph00a1.cz.infra/auth/realms/hci/protocol/openid-connect/token

    private final String CONFIG_FILE = "/sas/groovy/Connections/bsl.properties";

    @Override
    void run() {

        //start log info
        log.info("Starting process GetClientApplications...");
        log.info("GetClientApplications - id_cuid: $id_cuid");

        PropertiesResponse bslPropertiesResponse = PropertiesUtils.getProperties(CONFIG_FILE)
        if (bslPropertiesResponse.getProperties() == null) {
            status = bslPropertiesResponse.getStatus().getStatus();
            errorMessage = bslPropertiesResponse.getErrorMessage();
            return;
        }
        Properties bslConfig = bslPropertiesResponse.getProperties();

        username = bslConfig.getProperty("bsl.restUsername");
        password = bslConfig.getProperty("bsl.restPassword");
        host = bslConfig.getProperty("bsl.restHost");
        authPath = bslConfig.getProperty("bsl.restAuthPath")

        log.info("GetClientApplications - host: $host");

        if (id_cuid != null && !id_cuid.isEmpty()) {

            // Create empty table
            ApplicationDetails = new RTDMTable();
            ApplicationDetails.columnAdd("applicationCode", DataTypes.STRING, Collections.emptyList());
            ApplicationDetails.columnAdd("status", DataTypes.STRING, Collections.emptyList());
            ApplicationDetails.columnAdd("substatus", DataTypes.STRING, Collections.emptyList());


            // Setting API variables
            TokenResponse tokenResponse = TokenUtils.getToken(
                    mapJDBC,
                    evtInfo.getEventName(),
                    evtInfo.getSimulationDate().getTime(),
                    username,
                    password,
                    authPath,
                    "MA_TEMP_JDBC",
                    "RTDM_TEMP.SSO_TOKEN"
            )
            if (tokenResponse.getToken() == null) {
                status = tokenResponse.getStatus().getStatus();
                errorMessage = tokenResponse.getErrorMessage();
                return;
            }
            String authToken = tokenResponse.getToken();


            // Search for applicationCode
            String searchUri = host + searchPrefix + id_cuid
            log.info("GetClientApplications - Search Endpoint URL: $searchUri");
            HttpCallResponse httpCallResponseSearch = HttpUtils.runHttpProcess(
                    evtInfo.getEventName(),
                    evtInfo.getSimulationDate().getTime(),
                    searchUri,
                    HttpUtils.getBearerAuthToken(authToken),
                    [(HttpUtils.CONTENT_TYPE): "application/json", (HttpUtils.ACCEPT): "application/json"],
                    RequestMethod.GET,
                    null
            );

            httpResponseCode = httpCallResponseSearch.getHttpResponseCode();
            status = httpCallResponseSearch.getStatus().getStatus();
            errorMessage = httpCallResponseSearch.getErrorMessage();
            String searchResponseString = httpCallResponseSearch.getResponse()
            if (searchResponseString == null) {
                return
            }

            log.trace("GetClientApplications - Search httpResponseCode:" + httpResponseCode);
            log.trace("GetClientApplications - Search response:" + searchResponseString);

            SearchApplicationCodeResponse searchResponseObject = MappingUtils.mapToObject(searchResponseString, SearchApplicationCodeResponse.class)
            if (searchResponseObject == null) {
                status = Status.ERROR.getStatus();
                errorMessage = "Failed to map response. Wrong response data format."
                return
            }
            if (searchResponseObject.applicationCode == null || searchResponseObject.applicationCode.isEmpty()) {
                log.trace("GetClientApplications - No applicationCode found for cuid $id_cuid")
                status = Status.OK.getStatus()
                errorMessage = "No applicationCode found for cuid $id_cuid"
                return
            }

            String applicationCode = searchResponseObject.applicationCode.first()

            // Get data by application code
            String uri = host + prefix + applicationCode
            log.info("GetClientApplications - Endpoint URL: $uri");
            HttpCallResponse httpCallResponse = HttpUtils.runHttpProcess(
                    evtInfo.getEventName(),
                    evtInfo.getSimulationDate().getTime(),
                    uri,
                    HttpUtils.getBearerAuthToken(authToken),
                    [(HttpUtils.CONTENT_TYPE): "application/json", (HttpUtils.ACCEPT): "application/json"],
                    RequestMethod.GET,
                    null
            );

            httpResponseCode = httpCallResponse.getHttpResponseCode();
            status = httpCallResponse.getStatus().getStatus();
            errorMessage = httpCallResponse.getErrorMessage();
            String responseString = httpCallResponse.getResponse()
            if (responseString == null) {
                return
            }

            log.trace("GetClientApplications - httpResponseCode:" + httpResponseCode);
            log.trace("GetClientApplications - response:" + responseString);

            ApplicationDataResponse responseObject = MappingUtils.mapToObject(responseString, ApplicationDataResponse.class)
            if (responseObject == null) {
                status = Status.ERROR.getStatus();
                errorMessage = "Failed to map response. Wrong response data format."
                return
            }

            Row newRow = ApplicationDetails.rowAdd();
            newRow.columnDataSet("applicationCode", responseObject.code);
            newRow.columnDataSet("status", responseObject.status);
            newRow.columnDataSet("substatus", responseObject.substatus ?: "")


            status = Status.OK.getStatus()
        } else {
            log.trace("GetClientApplications - No input clients");
        }
    }
}

class SearchApplicationCodeResponse implements Serializable {
    List<String> applicationCode;
}

class ApplicationDataResponse implements Serializable {
    String code;
    String status;
    String substatus;
}