package com.sas.hccn.rtdm

import com.homecredit.sas.utils.HttpUtils
import com.homecredit.sas.utils.PropertiesUtils
import com.homecredit.sas.utils.enumeration.RequestMethod
import com.homecredit.sas.utils.enumeration.Status
import com.homecredit.sas.utils.model.HttpCallResponse
import com.homecredit.sas.utils.model.PropertiesResponse
import com.sas.rtdm.implementation.engine.EventInfo
import org.apache.log4j.Logger

import java.nio.charset.StandardCharsets

/**
 * Set offer status
 * @version 22/09/07-001
 */
class GetDetailForClientList implements Runnable {

    // Input variables
    String partyId;
    String offerStatusName;
    String offerTypeCode;

    // Output variables
    String status = Status.ERROR.getStatus()
    Long httpResponseCode;    // HTTP response code
    String errorMessage;

    // Logger
    private static final Logger log = Logger.getLogger('groovyLog');

    // Event info
    EventInfo evtInfo;

    // Constants
    private final String resourcePrefix = "/ofs/offers/partyId/"
    private final String resourceSuffix = "/status"

    // Variables from properties
    private String host;
    private String username;
    private String password;

    private final String CONFIG_FILE = "/sas/groovy/Connections/ofs.properties";

    @Override
    void run() {

        //start log info
        log.info("Starting process SetOfferStatus...");
        log.info("SetOfferStatus - partyId: $partyId");
        log.info("SetOfferStatus - offerStatusName: $offerStatusName");
        log.info("SetOfferStatus - offerTypeCode: $offerTypeCode");

        PropertiesResponse propertiesResponse = PropertiesUtils.getProperties(CONFIG_FILE)
        if (propertiesResponse.getProperties() == null) {
            status = propertiesResponse.getStatus().getStatus();
            errorMessage = propertiesResponse.getErrorMessage();
            return;
        }
        Properties config = propertiesResponse.getProperties();

        host = config.getProperty("ofs.host");
        username = config.getProperty("ofs.username");
        password = config.getProperty("ofs.password");

        log.info("SetOfferStatus - host: $host");
        log.info("SetOfferStatus - resource prefix: $resourcePrefix");

        if (partyId != null && !partyId.isEmpty()) {

            // Setting API variables
            String uri = host + resourcePrefix + partyId + resourceSuffix;
            if (offerTypeCode) {
                uri = uri + "?offerTypeCode=" + offerTypeCode
            }
            log.info("SetOfferStatus - Endpoint URL: $uri");

            HttpCallResponse httpCallResponse = HttpUtils.runHttpProcess(
                    evtInfo.getEventName(),
                    evtInfo.getSimulationDate().getTime(),
                    uri,
                    HttpUtils.getBasicAuthToken(username, password),
                    [(HttpUtils.CONTENT_TYPE): "application/json"],
                    RequestMethod.POST,
                    offerStatusName.getBytes(StandardCharsets.UTF_8)
            );

            httpResponseCode = httpCallResponse.getHttpResponseCode();
            status = httpCallResponse.getStatus().getStatus();
            errorMessage = httpCallResponse.getErrorMessage();
            log.trace("SetOfferStatus - httpResponseCode:" + httpResponseCode);
        } else {
            log.trace("SetOfferStatus - No input clients");
        }
    }
}
