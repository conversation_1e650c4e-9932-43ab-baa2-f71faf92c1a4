package com.sas.hccn.rtdm
import com.fasterxml.jackson.annotation.JsonFormat
import com.homecredit.sas.utils.HttpUtils
import com.homecredit.sas.utils.MappingUtils
import com.homecredit.sas.utils.PropertiesUtils
import com.homecredit.sas.utils.enumeration.RequestMethod
import com.homecredit.sas.utils.enumeration.Status
import com.homecredit.sas.utils.model.HttpCallResponse
import com.homecredit.sas.utils.model.PropertiesResponse
import com.sas.analytics.ph.common.RTDMTable
import com.sas.analytics.ph.common.RTDMTable.Row
import com.sas.analytics.ph.common.jaxb.DataTypes
import com.sas.rtdm.implementation.engine.EventInfo
import org.apache.log4j.Logger

class GetDetailForClientList implements Runnable {

    // Input variables
    String cuid; // 17028123

    // Output variables
    String status = Status.ERROR.getStatus()
    Long httpResponseCode;    // HTTP response code
    String errorMessage;
    RTDMTable ClientDetails;

    // Logger
    private static final Logger log = Logger.getLogger('groovyLog');

    // Event info
    EventInfo evtInfo;

    // Constants
    private final String resourcePrefix = "/party-web/api/pif/v1/customer/";

    // Variables from properties
    private String username;
    private String password;
    private String host;

    private final String CONFIG_FILE = "/sas/groovy/Connections/pif.properties";

    @Override
    void run() {

        //start log info
        log.info("Starting process GetClientEmailValidation...");
        log.info("GetClientEmailValidation - cuid: $cuid");

        PropertiesResponse propertiesResponse = PropertiesUtils.getProperties(CONFIG_FILE)
        if (propertiesResponse.getProperties() == null) {
            status = propertiesResponse.getStatus().getStatus();
            errorMessage = propertiesResponse.getErrorMessage();
            return;
        }
        Properties config = propertiesResponse.getProperties();

        username = config.getProperty("pif.username");
        password = config.getProperty("pif.password");
        host = config.getProperty("pif.host"); // = "https://pif.vn00c1.vn.infra/";

        log.info("GetClientEmailValidation - host: $host");
        log.info("GetClientEmailValidation - resource prefix: $resourcePrefix");

        if (cuid != null) {

            // Create empty table
            ClientDetails = new RTDMTable();
            ClientDetails.columnAdd("email", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("activeYn", DataTypes.BOOLEAN, Collections.emptyList());
            ClientDetails.columnAdd("id", DataTypes.INT, Collections.emptyList());
            ClientDetails.columnAdd("statusType", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("statusCategory", DataTypes.STRING, Collections.emptyList());

            // Setting API variables
            String uri = host + resourcePrefix + cuid;

            log.info("GetClientEmailValidation - Endpoint URL: $uri");

            HttpCallResponse httpCallResponse = HttpUtils.runHttpProcess(
                    evtInfo.getEventName(),
                    evtInfo.getSimulationDate().getTime(),
                    uri,
                    HttpUtils.getBasicAuthToken(username, password),
                    [(HttpUtils.CONTENT_TYPE): "application/json"],
                    RequestMethod.GET,
                    null
            );

            httpResponseCode = httpCallResponse.getHttpResponseCode();
            status = httpCallResponse.getStatus().getStatus();
            errorMessage = httpCallResponse.getErrorMessage();
            String responseString = httpCallResponse.getResponse()
            if (responseString == null) {
                return
            }

            log.trace("GetClientEmailValidation - httpResponseCode:" + httpResponseCode);
            log.trace("GetClientEmailValidation - response:" + responseString);

            Response responseObject = MappingUtils.mapToObject(responseString, Response.class)
            if (responseObject == null) {
                status = Status.ERROR.getStatus();
                errorMessage = "Failed to map response. Wrong response data format."
                return
            }

            if (httpResponseCode == 200) {
                if (responseObject.resultCode == "FOUND") {
                    for (int i = 0; i < responseObject.data.size(); i++) {
                        ClientData data = responseObject.data.get(i);

                        Email primaryEmail = data.emailAddresses.stream().filter {it.classification.equals("PRIMARY_EMAIL")}.findFirst().orElse(null)
                        if (primaryEmail == null) {
                            log.trace("GetClientEmailValidation - no primary email found");
                            continue
                        }
                        log.trace("GetClientEmailValidation - primary email: " + primaryEmail.email);

                        for (int j = 0; j < primaryEmail.contactStatuses.size(); j++) {

                            ContactStatus status = primaryEmail.contactStatuses.get(j);

                            Row newRow = ClientDetails.rowAdd();
                            newRow.columnDataSet("email", primaryEmail.email)
                            newRow.columnDataSet("activeYn", status.activeYn);
                            newRow.columnDataSet("id", status.id);
                            newRow.columnDataSet("statusType", status.statusType);
                            newRow.columnDataSet("statusCategory", status.statusCategory);
                        }

                        status = Status.OK.getStatus()
                    }
                } else {
                    log.error("GetClientEmailValidation - error: " + responseObject.errorMessage);
                    status = Status.ERROR.getStatus()
                    errorMessage = responseObject.errorMessage
                }
            }
        }
         else {
            log.trace("GetClientEmailValidation - No input clients");
        }
    }
}

class Response implements Serializable {

    @JsonFormat(with = JsonFormat.Feature.ACCEPT_SINGLE_VALUE_AS_ARRAY)
    List<ClientData> data;
    Long externalId;
    String resultCode;
    String errorCode;
    String errorMessage;
    String requestId;
    Object validationErrors;
}

class ClientData implements Serializable {
    List<Email> emailAddresses;
    Long externalId;
    // ...
}

class Email implements Serializable {
    String email;
    String classification;
    List<ContactStatus> contactStatuses;
    // ...
}

class ContactStatus implements Serializable {
    String statusCategory;
    String statusType;
    boolean activeYn;
    Long Id;
    // ...
}

//package com.sas.hccn.rtdm
//import com.fasterxml.jackson.annotation.JsonFormat
//import com.fasterxml.jackson.databind.DeserializationFeature
//import com.fasterxml.jackson.databind.ObjectMapper
//import com.sas.analytics.ph.common.RTDMTable
//import com.sas.analytics.ph.common.RTDMTable.Row
//import com.sas.analytics.ph.common.jaxb.DataTypes
//import com.sas.rtdm.implementation.engine.EventInfo
//import org.apache.log4j.Logger
//
//import java.text.DateFormat
//import java.text.SimpleDateFormat
//
//class GetDetailForClientList implements Runnable {
//
//    // Input variables
//    String cuid; // 17028123
//    String ResourcePrefix = "/party-web/api/pif/v1/customer/";
//
//    // Output variables
//    String status="ERROR";    // either "OK" or "ERROR"
//    Long httpResponseCode;    // HTTP response code
//    String errorMessage;
//    RTDMTable ClientDetails;
//
//    // Internal variables
//    private static final Logger log = Logger.getLogger('groovyLog'); //groovyLog
//    private static final Logger requestReplyLog = Logger.getLogger('groovyRequestReply')
//
//    final DateFormat format = new SimpleDateFormat("dd-MM-yyyy hh:mm:ss");
//    Response responseObject;
//    EventInfo evtInfo;
//
//    // internal timeout limits
//    static final int connectTmMs = 100;        // hardcoded connection timeout                             [millisec]
//    static final int operTmMs    = 200;        // reserved to finalize DB write (error/decision log)       [millisec]
//    static final int minReadTmMs = 10;         // minimal time to response from service GetDetailForClientList
//
//    Long getHttpResponseCode() {
//        return httpResponseCode
//    }
//
//    private final String CONFIG_FILE = "/sas/groovy/Connections/pif.properties";
//    private Properties config = new Properties();
//
//    @Override
//    void run() {
//
//        //start log info
//        log.info("Starting process GetClientContacts...");
//        log.info("GetClientContacts - cuid: $cuid");
//        log.info("GetClientContacts - ResourcePrefix: $ResourcePrefix");
//
//        try {
//            log.info("GetClientContacts - Loading configuration from path '$CONFIG_FILE'");
//            this.config.load(new FileInputStream(CONFIG_FILE));
//        } catch (Exception e) {
//            log.error("GetClientContacts - Failed to load configuration: " + e.getMessage())
//            status = "ERROR";
//            throw e;
//        }
//
//        String username = config.getProperty("pif.username");
//        String password = config.getProperty("pif.password");
//        String ServiceEndpoint = config.getProperty("pif.host"); // = "https://pif.vn00c1.vn.infra/";
//
//        if(cuid != null && !cuid.isEmpty()){
//
//            // Create empty table
//
//            ClientDetails = new RTDMTable();
//
//            ClientDetails.columnAdd("externalId", DataTypes.STRING, Collections.emptyList());
//            ClientDetails.columnAdd("type", DataTypes.STRING, Collections.emptyList());
//            ClientDetails.columnAdd("classification", DataTypes.STRING, Collections.emptyList());
//            ClientDetails.columnAdd("value", DataTypes.STRING, Collections.emptyList());
//
//            // Setting API variables
//            String response="";
//            String authStr = username + ":" + password;
//            String authToken = org.apache.commons.codec.binary.Base64.encodeBase64String(authStr.getBytes("UTF-8"));
//            HttpURLConnection conn = null;
//
//            log.trace("GetClientContacts - data: authToken=" + authToken + ":" + username + ":" + password );
//
//            String GetClientData_serverUri;
//            GetClientData_serverUri = ServiceEndpoint + ResourcePrefix + cuid;
//            log.info("GetClientContacts - Endpoint URL: $GetClientData_serverUri");
//
//            try {
//                //Timeout configuration logic
//                String timeoutMs = System.getProperty("com.sas.hccn.rtdm." + evtInfo.getEventName() + ".timeout.ms");
//                log.trace("GetClientContacts - evtInfo: " + evtInfo.getEventName());
//                log.trace("GetClientContacts - event timeoutMs: " + timeoutMs);
//                if( timeoutMs==null ) timeoutMs = System.getProperty("com.sas.hccn.rtdm.system.timeout.ms");
//                log.trace("GetClientContacts - system timeoutMs: " + timeoutMs);
//                if( timeoutMs==null ) timeoutMs = '10000';
//                log.trace("GetClientContacts - hardcoded timeoutMs: " + timeoutMs);
//                Date currDt = new Date();
//                Date startDt = evtInfo.getSimulationDate().getTime();
//                int readTmMs = currDt.getTime() - startDt.getTime();
//                readTmMs = Integer.parseInt(timeoutMs) - connectTmMs - operTmMs - readTmMs;
//                if (readTmMs<minReadTmMs) {
//                    log.error("GetClientContacts - no time for REST API call finalization; remains " + readTmMs + "ms for read" );
//                    status='TIMEOUT';
//                    errorMessage = "REST timeout underflow. Service not called.";
//
//                    return;
//                }
//                log.trace("GetClientContacts - applying " +evtInfo.getEventName()+ " timeouts: byEvent=" + timeoutMs + " connect=" + connectTmMs + " read=" + readTmMs );
//
//                //create connection & set up headers
//                String bearerAuth = "Basic " + authToken //credentials for Bearer/Token Authorization
//                URL url = new URL(GetClientData_serverUri);
//
//                conn = (HttpURLConnection) url.openConnection();
//
//                conn.setDoOutput(true);
//                conn.setRequestProperty("Authorization", bearerAuth);
//                conn.setInstanceFollowRedirects(false);
//                conn.setRequestMethod("GET");
//                conn.setRequestProperty("Content-Type", "application/json");
//                conn.setRequestProperty("charset", "utf-8");
//                conn.setUseCaches(false);
//                conn.setConnectTimeout(connectTmMs);
//                conn.setReadTimeout(readTmMs);
//
//                requestReplyLog.info("GetClientContacts - request GET $GetClientData_serverUri")
//                httpResponseCode = (long) conn.getResponseCode();
//                switch( conn.getResponseCode() ) {
//                    case HttpURLConnection.HTTP_OK:
//                        response = getResponseAsString(conn.getInputStream());
//                        status = "OK";
//                        break;
//                    case HttpURLConnection.HTTP_CLIENT_TIMEOUT:
//                        status='TIMEOUT';
//                        log.error("GetClientContacts - Response time out2.");
//                        errorMessage = "Timeout on server processing HTTP408";
//                        conn.disconnect();
//                        return;
//                    case HttpURLConnection.HTTP_UNAUTHORIZED:
//                        status='ERROR';
//                        log.error("GetClientContacts - Unauthorized.");
//                        errorMessage = "Unauthorized HTTP 401";
//                        conn.disconnect();
//                        return;
//                    default:
//                        response = getResponseAsString(conn.getErrorStream());
//                        status = "ERROR";
//                        log.error("GetClientContacts - received error reply, httpResponseCode:" + httpResponseCode + ", response: " + response);
//                        errorMessage = response;
//                        conn.disconnect();
//                        return;
//                }
//
//                log.trace("GetClientContacts - data: response=" + response);
//
//                try {
//                    responseObject = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false).configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true).readValue(response, Response.class);
//                }catch(Exception ex){
//                    log.trace("GetClientContacts - Wrong response data format." + response);
//                    status="OK";
//                    conn.disconnect();
//                    return;
//                }
//
//            } catch (UnsupportedEncodingException e) {
//                log.error("GetClientContacts - Encoding exception when converting GetClientContacts request parameters! No data can be returned.");
//                log.error("GetClientContacts - Exception: " + e.toString());
//                e.printStackTrace();
//            } catch (MalformedURLException e) {
//                log.error("GetClientContacts - MalformedURLException occured for GetClientContacts request! No data can be returned.");
//                log.error("GetClientContacts - Exception: " + e.toString());
//                e.printStackTrace();
//            } catch (IOException e) {
//                log.error("GetClientContacts - IOException occured for GetClientContacts request! No data can be returned.");
//                log.error("GetClientContacts - Exception: " + e.toString());
//                e.printStackTrace();
//            } catch (SocketTimeoutException e) {
//                log.error("GetClientContacts - Response time out.");
//                log.error("GetClientContacts - Exception: " + e.toString());
//                e.printStackTrace();
//                status="TIMEOUT";
//            }
//            finally {
//                // Close the connection
//                try {
//                    if (conn != null) {
//                        conn.disconnect();
//                    }
//                }
//                catch (Exception e) {
//                    log.error("GetClientContacts - Cannot release connection to pool: " + e.toString());
//                    e.printStackTrace();
//                }
//            }
//
//            log.trace("GetClientContacts - httpResponseCode:" + httpResponseCode);
//            log.trace("GetClientContacts - response:" + response);
//            requestReplyLog.info("GetClientContacts - response GET $GetClientData_serverUri: (response code: $httpResponseCode) $response")
//
//            if(httpResponseCode == 200){
//                if (responseObject.resultCode == "FOUND") {
//                    List<Email> emailAddresses = responseObject.data.emailAddresses
//                    log.trace("GetClientContacts - record size (emails): " + emailAddresses.size());
//
//                    for (int j = 0; j < emailAddresses.size(); j++) {
//
//                        Email email = emailAddresses.get(j);
//                        log.trace("GetClientContacts - email: " + email.email);
//
//                        Row newRow = ClientDetails.rowAdd();
//                        newRow.columnDataSet("externalId", responseObject.externalId.toString());
//                        newRow.columnDataSet("type", "EMAIL");
//                        newRow.columnDataSet("classification", email.classification);
//                        newRow.columnDataSet("value", email.email);
//
//						List<ContactStatus> contactStatuses = email.contactStatuses;
//						ContactStatus EMLstatus = contactStatuses.get(0);
//
//						newRow.columnDataSet("activeYn", EMLstatus.activeYn);
//						newRow.columnDataSet("statusCategory", EMLstatus.statusCategory);
//						newRow.columnDataSet("statusType", EMLstatus.statusType);
//                    }
//
//                    List<PhoneNumber> phoneNumbers = responseObject.data.phoneNumbers
//                    log.trace("GetClientContacts - record size (phoneNumbers): " + phoneNumbers.size());
//
//                    for (int j = 0; j < phoneNumbers.size(); j++) {
//
//                        PhoneNumber phoneNumber = phoneNumbers.get(j);
//                        log.trace("GetClientContacts - phoneNumber: " + phoneNumber.number);
//
//                        Row newRow = ClientDetails.rowAdd();
//                        newRow.columnDataSet("externalId", responseObject.externalId.toString());
//                        newRow.columnDataSet("type", "PHONE");
//                        newRow.columnDataSet("classification", phoneNumber.classification);
//                        newRow.columnDataSet("value", phoneNumber.number);
//                    }
//                    status="OK";
//
//                    Person person = responseObject.data.person
//                    log.trace("GetClientContacts - personal info: $person.firstName $person.middleName $person.lastName ($person.birthDate, $person.gender.code)");
//
//                    Row firstName = ClientDetails.rowAdd();
//                    firstName.columnDataSet("externalId", responseObject.externalId.toString());
//                    firstName.columnDataSet("type", "PERSONALINFO");
//                    firstName.columnDataSet("classification", "FIRSTNAME");
//                    firstName.columnDataSet("value", person.firstName);
//
//                    Row middleName = ClientDetails.rowAdd();
//                    middleName.columnDataSet("externalId", responseObject.externalId.toString());
//                    middleName.columnDataSet("type", "PERSONALINFO");
//                    middleName.columnDataSet("classification", "MIDDLENAME");
//                    middleName.columnDataSet("value", person.middleName);
//
//                    Row lastName = ClientDetails.rowAdd();
//                    lastName.columnDataSet("externalId", responseObject.externalId.toString());
//                    lastName.columnDataSet("type", "PERSONALINFO");
//                    lastName.columnDataSet("classification", "LASTNAME");
//                    lastName.columnDataSet("value", person.lastName);
//
//                    Row birthDate = ClientDetails.rowAdd();
//                    birthDate.columnDataSet("externalId", responseObject.externalId.toString());
//                    birthDate.columnDataSet("type", "PERSONALINFO");
//                    birthDate.columnDataSet("classification", "BIRTHDATE");
//                    birthDate.columnDataSet("value", person.birthDate);
//
//                    Row gender = ClientDetails.rowAdd();
//                    gender.columnDataSet("externalId", responseObject.externalId.toString());
//                    gender.columnDataSet("type", "PERSONALINFO");
//                    gender.columnDataSet("classification", "GENDER");
//                    gender.columnDataSet("value", person.gender.code);
//
//                    CustomValues customValues = responseObject.data.customValues
//                    String preferredLanguage
//                    if (customValues == null || customValues.preferredLanguage == null) {
//                        preferredLanguage = "Not set"
//                    } else {
//                        preferredLanguage = responseObject.data.customValues.preferredLanguage
//                    }
//                    log.trace("GetClientContacts - custom values: preferred language = $preferredLanguage)");
//
//                    Row language = ClientDetails.rowAdd();
//                    language.columnDataSet("externalId", responseObject.externalId.toString());
//                    language.columnDataSet("type", "PERSONALINFO");
//                    language.columnDataSet("classification", "PREFERREDLANGUAGE");
//                    language.columnDataSet("value", preferredLanguage);
//                } else {
//                    log.error("GetClientContacts - cuid $cuid, error: " + responseObject.errorMessage);
//                    status = "ERROR"
//                    errorMessage = responseObject.errorMessage
//                }
//            }
//        }
//        else {
//            log.trace("GetClientContacts - No input clients");
//        }
//    }
//
//    String getResponseAsString(InputStream is) {
//        //read the response
//        BufferedReader inBR = new BufferedReader(new InputStreamReader(is));
//        String inputLine;
//        StringBuffer response = new StringBuffer();
//
//        while ((inputLine = inBR.readLine()) != null) {
//            response.append(inputLine);
//        }
//
//        inBR.close();
//
//        return response.toString();
//    }
//}
//
//class Response implements Serializable {
//    ClientData data;
//    Long externalId;
//    String resultCode;
//    String errorCode;
//    String errorMessage;
//    String requestId;
//    Object validationErrors;
//}
//
//class ClientData implements Serializable {
//    List<Email> emailAddresses;
//    List<PhoneNumber> phoneNumbers;
//    Long externalId;
//    Person person;
//    CustomValues customValues;
//    // ...
//}
//
//class Email implements Serializable {
//    String email;
//    String classification;
//	List<ContactStatus> contactStatuses;
//    // ...
//}
//
//class ContactStatus implements Serializable {
//	String statusCategory;
//	String statusType;
//	boolean activeYn;
//    // ...
//}
//
//class PhoneNumber implements Serializable {
//    String number;
//    String classification;
//    // ...
//}
//
//class Person implements Serializable {
//    String lastName;
//    String firstName;
//    String middleName;
//    String birthDate;
//    Gender gender;
//    // ...
//}
//
//class Gender implements Serializable {
//    String code;
//    // ...
//}
//
//class CustomValues implements Serializable {
//    String preferredLanguage;
//}