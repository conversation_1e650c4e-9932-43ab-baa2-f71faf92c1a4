package com.sas.hccn.rtdm

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.ObjectMapper
import com.rabbitmq.client.AMQP
import com.rabbitmq.client.BasicProperties
import com.rabbitmq.client.Connection
import com.rabbitmq.client.ConnectionFactory
import org.apache.log4j.Logger

import java.nio.charset.StandardCharsets
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;

/**
 * @version 24/03/17-001
 */
class GetDetailForClientList implements Runnable {

    // Input variables
    String requestPublisher;
    String contractNumber;
    String eventCode;
    String ekycId;
    String cuid;
    String requestId;
    String leadsTrackingId;
    String leadsId;
    String leadsType;
    String name;
    String phoneNumber;
    String offerImages;
    String offerName;
    String offerTextTemplate;
    String productName;
    String offerLimitAmount;
    String priority;
    String offerValidTo;
    String assignee;
    String assigneeRole;
    String prevContract;
    String brandManufacturer;

    // Output variables
    String status = "ERROR";            // either "OK" or "ERROR"
    String errorMessage;
    Long expiredDate;

    // Internal variables
    private static final Logger log = Logger.getLogger('groovyLog');
    private static final Logger requestReplyLog = Logger.getLogger('groovyRequestReply')

    private final String CONFIG_FILE = "/sas/groovy/Connections/rabbitmq.properties";
    private Properties config = new Properties();

    @Override
    void run() {
        //start log info
        log.info("Starting process SendSmartApp to RabbitMQ...");
        log.info("SendSmartApp - requestPublisher: $requestPublisher");
        log.info("SendSmartApp - contractNumber: $contractNumber");
        log.info("SendSmartApp - eventCode: $eventCode");
        log.info("SendSmartApp - ekycId: $ekycId");
        log.info("SendSmartApp - cuid: $cuid");
        log.info("SendSmartApp - requestId: $requestId");
        log.info("SendSmartApp - leadsTrackingId: $leadsTrackingId");
        log.info("SendSmartApp - leadsId: $leadsId");
        log.info("SendSmartApp - leadsType: $leadsType");
        log.info("SendSmartApp - name: $name");
        log.info("SendSmartApp - phoneNumber: $phoneNumber");
        log.info("SendSmartApp - offerImages: $offerImages");
        log.info("SendSmartApp - offerName: $offerName");
        log.info("SendSmartApp - offerTextTemplate: $offerTextTemplate");
        log.info("SendSmartApp - productName: $productName");
        log.info("SendSmartApp - offerLimitAmount: $offerLimitAmount");
        log.info("SendSmartApp - priority: $priority");
        log.info("SendSmartApp - offerValidTo: $offerValidTo");
        log.info("SendSmartApp - assignee: $assignee");
        log.info("SendSmartApp - assigneeRole: $assigneeRole");
        log.info("SendSmartApp - prevContract: $prevContract");
        log.info("SendSmartApp - brandManufacturer: $brandManufacturer");

        try {
            log.info("SendSmartApp - Loading configuration from path '$CONFIG_FILE'");
            this.config.load(new FileInputStream(CONFIG_FILE));
        } catch (Exception e) {
            log.error("SendSmartApp - Failed to load configuration: " + e.getMessage())
            status = "ERROR";
            throw e;
        }

        String host = config.getProperty("rabbitmq.host");
        String routingKey = config.getProperty("rabbitmq.sms-routing-key");
        String username = config.getProperty("rabbitmq.username");
        String password = config.getProperty("rabbitmq.password");
        String virtualHost = config.getProperty("rabbitmq.virtual-host");
        String exchange = config.getProperty("rabbitmq.smartapp.exchange");

        log.info("SendSmartApp - RabbitMQ configuration: host = $host, routingKey = $routingKey, virtualHost = $virtualHost, exchange = $exchange")
        Message message = new Message()

        message.setLeadsTrackingId(leadsTrackingId)
        message.setLeadsId(leadsId)
        message.setLeadsType(leadsType)
        message.setName(name)
        message.setPhoneNumber(phoneNumber)
        message.setOfferImages(offerImages)
        message.setOfferName(offerName)
        message.setOfferTextTemplate(offerTextTemplate)
        message.setProductName(productName)
        message.setOfferLimitAmount(offerLimitAmount)
        message.setPriority(priority)
        if (offerValidTo != null) {
            expiredDate = OffsetDateTime.parse(offerValidTo, DateTimeFormatter.ISO_OFFSET_DATE_TIME).toInstant().toEpochMilli()
            message.setExpiredDate(expiredDate)
        }
        message.setAssignee(assignee)
        message.setAssigneeRole(assigneeRole)
        message.setPrevContract(prevContract)
        message.setToken(cuid)
        message.setBrandManufacturer(brandManufacturer)

        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        String jsonMessage = mapper.writeValueAsString(message)
        log.info("SendSmartApp - Serialized message:" + jsonMessage)

        Map<String, Object> headerMap = new HashMap<String, Object>();
        headerMap.put("requestPublisher", requestPublisher);
        headerMap.put("contractNumber", contractNumber);
        headerMap.put("eventCode", eventCode);
        headerMap.put("ekycId", ekycId);
        headerMap.put("cuid", cuid);
        headerMap.put("requestId", requestId);
        headerMap.put("priority", 0);
        headerMap.put("CorrelationID", requestId)
        headerMap.put("Type", "JMSType")

        BasicProperties messageProperties = new AMQP.BasicProperties.Builder()
                .contentType("application/json")
                .headers(headerMap)
                .build();

        ConnectionFactory factory = new ConnectionFactory();
        factory.setHost(host);
        factory.setUsername(username);
        factory.setPassword(password);
        factory.setVirtualHost(virtualHost);
        Connection connection;
        try {
            connection = factory.newConnection();
            Channel channel = connection.createChannel();
            channel.basicPublish(exchange, routingKey, messageProperties, jsonMessage.getBytes(StandardCharsets.UTF_8));
            log.info("SendSmartApp - Message sent successfully");
            requestReplyLog.info("SendSmartApp - Sent message: $jsonMessage to RabbitMQ (host = $host, routingKey = $routingKey, virtualHost = $virtualHost, exchange = $exchange)")
            status = "OK";
        } catch (Exception e) {
            log.error("SendSmartApp - Failed to send message: " + e.getMessage())
            errorMessage = "Failed to send message: " + e.getMessage()
            status = "ERROR";
            throw e;
        } finally {
            if (connection != null) {
                connection.close()
            }
        }
    }
}

class Message implements Serializable {
    String leadsTrackingId;
    String leadsId;
    String leadsType;
    String name;
    String phoneNumber;
    String offerImages;
    String offerName;
    String offerTextTemplate;
    String productName;
    String offerLimitAmount;
    String priority;
    Long expiredDate;
    String assignee;
    String assigneeRole;
    String prevContract;
    String cuid;
    String brandManufacturer;
}
