package com.sas.hccn.rtdm

import com.homecredit.sas.utils.HttpUtils
import com.homecredit.sas.utils.PropertiesUtils
import com.homecredit.sas.utils.enumeration.RequestMethod
import com.homecredit.sas.utils.enumeration.Status
import com.homecredit.sas.utils.model.HttpCallResponse
import com.homecredit.sas.utils.model.PropertiesResponse
import com.sas.rtdm.implementation.engine.EventInfo
import org.apache.log4j.Logger

import java.nio.charset.StandardCharsets

/**
 * Set offer ID status
 * @version 22/09/07-001
 */
class GetDetailForClientList implements Runnable {

    // Input variables
    String uuid;
    String offerStatusName;

    // Output variables
    String status = Status.ERROR.getStatus()
    Long httpResponseCode;    // HTTP response code
    String errorMessage;

    // Logger
    private static final Logger log = Logger.getLogger('groovyLog');

    // Event info
    EventInfo evtInfo;

    // Constants
    private final String resourcePrefix = "/ofs/offers/"
    private final String resourceSuffix = "/status"

    // Variables from properties
    private String host;
    private String username;
    private String password;

    private final String CONFIG_FILE = "/sas/groovy/Connections/ofs.properties";

    @Override
    void run() {

        //start log info
        log.info("Starting process SetOfferIDStatus...");
        log.info("SetOfferIDStatus - uuid: $uuid");
        log.info("SetOfferIDStatus - offerStatusName: $offerStatusName");

        PropertiesResponse propertiesResponse = PropertiesUtils.getProperties(CONFIG_FILE)
        if (propertiesResponse.getProperties() == null) {
            status = propertiesResponse.getStatus().getStatus();
            errorMessage = propertiesResponse.getErrorMessage();
            return;
        }
        Properties config = propertiesResponse.getProperties();

        host = config.getProperty("ofs.host");
        username = config.getProperty("ofs.username");
        password = config.getProperty("ofs.password");

        log.info("SetOfferIDStatus - host: $host");
        log.info("SetOfferIDStatus - resource prefix: $resourcePrefix");

        if (uuid != null && !uuid.isEmpty()) {

            // Setting API variables
            String uri = host + resourcePrefix + uuid + resourceSuffix;
            log.info("SetOfferIDStatus - Endpoint URL: $uri");

            HttpCallResponse httpCallResponse = HttpUtils.runHttpProcess(
                    evtInfo.getEventName(),
                    evtInfo.getSimulationDate().getTime(),
                    uri,
                    HttpUtils.getBasicAuthToken(username, password),
                    [(HttpUtils.CONTENT_TYPE): "application/json"],
                    RequestMethod.POST,
                    offerStatusName.getBytes(StandardCharsets.UTF_8)
            );

            httpResponseCode = httpCallResponse.getHttpResponseCode();
            status = httpCallResponse.getStatus().getStatus();
            errorMessage = httpCallResponse.getErrorMessage();
            log.trace("SetOfferIDStatus - httpResponseCode:" + httpResponseCode);
        } else {
            log.trace("SetOfferIDStatus - No input clients");
        }
    }
}
