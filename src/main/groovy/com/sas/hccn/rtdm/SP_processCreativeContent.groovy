package com.sas.hccn.rtdm

import com.homecredit.sas.utils.PropertiesUtils
import com.homecredit.sas.utils.model.PropertiesResponse
import com.sas.analytics.ph.common.RTDMTable
import com.sas.analytics.ph.common.RTDMTable.Row
import com.sas.analytics.ph.common.jaxb.DataTypes
import org.apache.log4j.Logger

import java.util.stream.Collectors

class GetDetailForClientList implements Runnable {

    // Input variables
    String creativeContent;
    String channel;

    // Output variables
    String errorMessage = "";
    String resultingMsg;
    RTDMTable attributesTable;

    // Logger
    private static final Logger log = Logger.getLogger('groovyLog');

    //constants
    private static final SEPARATOR_1 = "\\Q||\\E"
    private static final SEPARATOR_2 = ","
    private static final SEPARATOR_3 = "\\Q=\\E"

    private List<String> techAttributeChannels;

    private final String CONFIG_FILE = "/sas/groovy/Connections/creative-content.properties";

    @Override
    void run() {

        //start log info
        log.info("Starting process ProcessCreativeContent...");
        log.info("ProcessCreativeContent - creativeContent: $creativeContent");
        log.info("ProcessCreativeContent - channel: $channel");

        PropertiesResponse propertiesResponse = PropertiesUtils.getProperties(CONFIG_FILE)
        if (propertiesResponse.getProperties() == null) {
            errorMessage = propertiesResponse.getErrorMessage();
            return;
        }
        Properties config = propertiesResponse.getProperties();
        String channels = config.getProperty("techattribute-channels");
        techAttributeChannels = channels.split(SEPARATOR_2).toList().stream().map {it -> it.trim() }.collect(Collectors.toList())

        log.info("ProcessCreativeContent - techAttributeChannels: " + techAttributeChannels.toListString());

        if(creativeContent != null && !creativeContent.isEmpty()){

            String[] groups = creativeContent.split(SEPARATOR_1)
            if (groups.size() == 1) {
                if (techAttributeChannels.contains(channel)) {
                    resultingMsg = groups[0]
                } else {
                    errorMessage = "Failed to parse creativeContent. $creativeContent Should be split in 2 parts by ||. Channel is not in list of allowed techAttributeChannels."
                }
            }
            else if (groups.size() == 2) {
                resultingMsg = groups[0]
                String[] attributes = groups[1].split(SEPARATOR_2)

                // Create empty table
                attributesTable = new RTDMTable();
                Row valueRow = attributesTable.rowAdd();
                for (String attribute : attributes) {
                    String[] nameAndValue = attribute.split(SEPARATOR_3)
                    if (nameAndValue.size() == 2) {
                        String name = nameAndValue[0].trim()
                        String value = nameAndValue[1].trim()
                        attributesTable.columnAdd(name, DataTypes.STRING, Collections.emptyList());
                        valueRow.columnDataSet(name, value);
                    } else if (nameAndValue.size() == 1) {
                        String name = nameAndValue[0].trim()
                        String value = null
                        attributesTable.columnAdd(name, DataTypes.STRING, Collections.emptyList());
                        valueRow.columnDataSet(name, value);
                    } else {
                        errorMessage += "Failed to parse creativeContent attribute. $attribute should be split in 2 parts by =. "
                    }
                }
            } else {
                errorMessage = "Failed to parse creativeContent. $creativeContent Should be split in 2 parts by ||"
            }
        }
        else {
            log.trace("ProcessCreativeContent - No input creativeContent");
        }
    }
}
