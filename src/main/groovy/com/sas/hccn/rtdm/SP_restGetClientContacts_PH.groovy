package com.sas.hccn.rtdm


import com.homecredit.sas.utils.HttpUtils
import com.homecredit.sas.utils.MappingUtils
import com.homecredit.sas.utils.PropertiesUtils
import com.homecredit.sas.utils.enumeration.RequestMethod
import com.homecredit.sas.utils.enumeration.Status
import com.homecredit.sas.utils.model.HttpCallResponse
import com.homecredit.sas.utils.model.PropertiesResponse
import com.sas.analytics.ph.common.RTDMTable
import com.sas.analytics.ph.common.RTDMTable.Row
import com.sas.analytics.ph.common.jaxb.DataTypes
import com.sas.rtdm.implementation.engine.EventInfo
import org.apache.log4j.Logger

class GetDetailForClientList implements Runnable {

    // Input variables
    String cuid; // 17028123

    // Output variables
    String status = Status.ERROR.getStatus()
    Long httpResponseCode;    // HTTP response code
    String errorMessage;
    RTDMTable ClientDetails;

    // Logger
    private static final Logger log = Logger.getLogger('groovyLog');

    // Event info
    EventInfo evtInfo;

    // Constants
    private final String resourcePrefix = "/party-web/api/pif/v1/customer/";

    // Variables from properties
    private String username;
    private String password;
    private String host;

    private final String CONFIG_FILE = "/sas/groovy/Connections/pif.properties";

    @Override
    void run() {

        //start log info
        log.info("Starting process GetClientContacts...");
        log.info("GetClientContacts - cuid: $cuid");

        PropertiesResponse propertiesResponse = PropertiesUtils.getProperties(CONFIG_FILE)
        if (propertiesResponse.getProperties() == null) {
            status = propertiesResponse.getStatus().getStatus();
            errorMessage = propertiesResponse.getErrorMessage();
            return;
        }
        Properties config = propertiesResponse.getProperties();

        username = config.getProperty("pif.username");
        password = config.getProperty("pif.password");
        host = config.getProperty("pif.host"); // = "https://pif.vn00c1.vn.infra/";

        log.info("GetClientContacts - host: $host");
        log.info("GetClientContacts - resource prefix: $resourcePrefix");

        if (cuid != null && !cuid.isEmpty()) {

            // Create empty table
            ClientDetails = new RTDMTable();
            ClientDetails.columnAdd("externalId", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("type", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("classification", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("value", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("consentType", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("consentExpirationDate", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("region", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("district", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("subDistrict", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("locality", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("town", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("postCode", DataTypes.STRING, Collections.emptyList());

            // Setting API variables
            String uri = host + resourcePrefix + cuid;
            log.info("GetClientContacts - Endpoint URL: $uri");

            HttpCallResponse httpCallResponse = HttpUtils.runHttpProcess(
                    evtInfo.getEventName(),
                    evtInfo.getSimulationDate().getTime(),
                    uri,
                    HttpUtils.getBasicAuthToken(username, password),
                    [(HttpUtils.CONTENT_TYPE): "application/json"],
                    RequestMethod.GET,
                    null
            );

            httpResponseCode = httpCallResponse.getHttpResponseCode();
            status = httpCallResponse.getStatus().getStatus();
            errorMessage = httpCallResponse.getErrorMessage();
            String responseString = httpCallResponse.getResponse()
            if (responseString == null) {
                return
            }

            log.trace("GetClientContacts - httpResponseCode:" + httpResponseCode);
            log.trace("GetClientContacts - response:" + responseString);

            Response responseObject = MappingUtils.mapToObject(responseString, Response.class)
            if (responseObject == null) {
                status = Status.ERROR.getStatus();
                errorMessage = "Failed to map response. Wrong response data format."
                return
            }

            if (httpResponseCode == 200) {
                if (responseObject.resultCode == "FOUND") {
                    List<Email> emailAddresses = responseObject.data.emailAddresses
                    log.trace("GetClientContacts - record size (emails): " + emailAddresses.size());

                    for (int j = 0; j < emailAddresses.size(); j++) {

                        Email email = emailAddresses.get(j);
                        log.trace("GetClientContacts - email: " + email.email);

                        Row newRow = ClientDetails.rowAdd();
                        newRow.columnDataSet("externalId", responseObject.externalId.toString());
                        newRow.columnDataSet("type", "EMAIL");
                        newRow.columnDataSet("classification", email.classification);
                        newRow.columnDataSet("value", email.email);
                    }

                    List<PhoneNumber> phoneNumbers = responseObject.data.phoneNumbers
                    PhoneNumber phoneNumber = phoneNumbers.find {it -> (it.classification == "PRIMARY_MOBILE") }
                    if (phoneNumber != null) {
                        log.trace("GetClientContacts - phoneNumber: " + phoneNumber.number);

                        Row newRow = ClientDetails.rowAdd();
                        newRow.columnDataSet("externalId", responseObject.externalId.toString());
                        newRow.columnDataSet("type", "PHONE");
                        newRow.columnDataSet("classification", phoneNumber.classification);
                        newRow.columnDataSet("value", phoneNumber.number);
                    }

                    List<PostalAddress> postalAddresses = responseObject.data.postalAddresses
                    for (int j = 0; j < postalAddresses.size(); j++) {
                        PostalAddress postalAddress = postalAddresses.get(j);
                        log.trace("GetClientContacts - postalAddress: #" + j);
                        Row newRow = ClientDetails.rowAdd();
                        newRow.columnDataSet("region", postalAddress.getRegion()?.getOneValue() ?: "")
                        newRow.columnDataSet("district", postalAddress.getDistrict()?.getOneValue() ?: "")
                        newRow.columnDataSet("subDistrict", postalAddress.getSubDistrict()?.getOneValue() ?: "")
                        newRow.columnDataSet("locality", postalAddress.getLocality()?.getOneValue() ?: "")
                        newRow.columnDataSet("town", postalAddress.getTown()?.getOneValue() ?: "")
                        newRow.columnDataSet("postCode", postalAddress.getPostCode()?.getOneValue() ?: "")
                    }

                    Person person = responseObject.data.person
                    log.trace("GetClientContacts - personal info: $person.firstName $person.middleName $person.lastName ($person.birthDate, $person.gender.code)");

                    Row firstName = ClientDetails.rowAdd();
                    firstName.columnDataSet("externalId", responseObject.externalId.toString());
                    firstName.columnDataSet("type", "PERSONALINFO");
                    firstName.columnDataSet("classification", "FIRSTNAME");
                    firstName.columnDataSet("value", person.firstName);

                    Row middleName = ClientDetails.rowAdd();
                    middleName.columnDataSet("externalId", responseObject.externalId.toString());
                    middleName.columnDataSet("type", "PERSONALINFO");
                    middleName.columnDataSet("classification", "MIDDLENAME");
                    middleName.columnDataSet("value", person.middleName);

                    Row lastName = ClientDetails.rowAdd();
                    lastName.columnDataSet("externalId", responseObject.externalId.toString());
                    lastName.columnDataSet("type", "PERSONALINFO");
                    lastName.columnDataSet("classification", "LASTNAME");
                    lastName.columnDataSet("value", person.lastName);

                    Row birthDate = ClientDetails.rowAdd();
                    birthDate.columnDataSet("externalId", responseObject.externalId.toString());
                    birthDate.columnDataSet("type", "PERSONALINFO");
                    birthDate.columnDataSet("classification", "BIRTHDATE");
                    birthDate.columnDataSet("value", person.birthDate);

                    Row gender = ClientDetails.rowAdd();
                    gender.columnDataSet("externalId", responseObject.externalId.toString());
                    gender.columnDataSet("type", "PERSONALINFO");
                    gender.columnDataSet("classification", "GENDER");
                    gender.columnDataSet("value", person.gender.code);

                    CustomValues customValues = responseObject.data.customValues
                    String preferredLanguage
                    if (customValues == null || customValues.preferredLanguage == null) {
                        preferredLanguage = "Not set"
                    } else {
                        preferredLanguage = responseObject.data.customValues.preferredLanguage
                    }
                    log.trace("GetClientContacts - custom values: preferred language = $preferredLanguage)");

                    Row language = ClientDetails.rowAdd();
                    language.columnDataSet("externalId", responseObject.externalId.toString());
                    language.columnDataSet("type", "PERSONALINFO");
                    language.columnDataSet("classification", "PREFERREDLANGUAGE");
                    language.columnDataSet("value", preferredLanguage);

                    List<Channel> channels = responseObject.data.communicationChannels.get("MSG_APP")
                    if (channels == null) {
                        log.trace("GetClientContacts - record size (communication channels): 0");
                    } else {
                        log.trace("GetClientContacts - record size (communication channels):" + channels.size());

                        for (int j = 0; j < channels.size(); j++) {

                            Channel channel = channels.get(j);
                            log.trace("GetClientContacts - channel: " + channel.id);

                            Row newRow = ClientDetails.rowAdd();
                            newRow.columnDataSet("externalId", responseObject.externalId.toString());
                            newRow.columnDataSet("type", "PUSH");
                            newRow.columnDataSet("value", channel.activeYn.toString());
                            newRow.columnDataSet("classification", channel.channelType);
                        }
                    }

//                    responseObject.data.communicationChannels.forEach { key, channels -> {
//                        log.trace("GetClientContacts - iterating through channels in {}", key);
//                        channels.forEach { channel ->
//                            log.trace("GetClientContacts - channel: " + channel.id);
//                            if (channel.activeYn == false) {
//                                log.trace("GetClientContacts - channel {} is not active. Skipping: " + channel.id);
//                            } else {
//                                log.trace("GetClientContacts - channel {} is active. Adding to table: " + channel.id);
//                                Row newRow = ClientDetails.rowAdd();
//                                newRow.columnDataSet("externalId", responseObject.externalId.toString());
//                                newRow.columnDataSet("type", "PUSH");
//                                newRow.columnDataSet("value", channel.activeYn.toString());
//                                newRow.columnDataSet("classification", channel.channelType);
//                            }
//                        }
//                    }}

                    responseObject.data.consents.forEach {consent ->
                        log.trace("GetClientContacts - iterating through consents");
                        Row newRow = ClientDetails.rowAdd();
                        newRow.columnDataSet("consentType", consent.consentType);
                        newRow.columnDataSet("consentExpirationDate", consent.expirationDate);
                    }

                    status = Status.OK.getStatus()
                } else {
                    log.error("GetClientContacts - cuid $cuid, error: " + responseObject.errorMessage);
                    status = Status.ERROR.getStatus()
                    errorMessage = responseObject.errorMessage
                }
            }
        } else {
            log.trace("GetClientContacts - No input clients");
        }
    }
}

class Response implements Serializable {
    ClientData data;
    Long externalId;
    String resultCode;
    String errorCode;
    String errorMessage;
    String requestId;
    Object validationErrors;
}

class ClientData implements Serializable {
    Long externalId;
    List<Email> emailAddresses;
    List<PhoneNumber> phoneNumbers;
    Person person;
    CustomValues customValues;
    Map<String, List<Channel>> communicationChannels;
    List<PostalAddress> postalAddresses;
    List<Consent> consents;
    // ...
}

class Email implements Serializable {
    String email;
    String classification;
    // ...
}

class PhoneNumber implements Serializable {
    String number;
    String classification;
    // ...
}

class Person implements Serializable {
    String lastName;
    String firstName;
    String middleName;
    String birthDate;
    Gender gender;
    // ...
}

class Gender implements Serializable {
    String code;
    // ...
}

class CustomValues implements Serializable {
    String preferredLanguage;
}

class Channel implements Serializable {
    Boolean activeYn;
    Long id;
    String channelType;
    // ...
}

class PostalAddress implements Serializable {
    PostalAddressField region;
    PostalAddressField district;
    PostalAddressField subDistrict;
    PostalAddressField locality;
    PostalAddressField town;
    PostalAddressField postCode;
    //...
}

class PostalAddressField implements Serializable {
    String code;
    String value;

    String getOneValue() {
        if (code != null) {
            return code
        }
        return value
    }
}

class Consent implements Serializable {
    String consentType;
    String expirationDate;
}