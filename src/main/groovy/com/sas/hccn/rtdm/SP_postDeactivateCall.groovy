package com.sas.hccn.rtdm

import com.fasterxml.jackson.databind.ObjectMapper
import com.homecredit.sas.utils.HttpUtils
import com.homecredit.sas.utils.PropertiesUtils
import com.homecredit.sas.utils.enumeration.RequestMethod
import com.homecredit.sas.utils.enumeration.Status
import com.homecredit.sas.utils.model.HttpCallResponse
import com.homecredit.sas.utils.model.PropertiesResponse
import com.sas.rtdm.implementation.engine.EventInfo
import org.apache.log4j.Logger

/**
 * Deactivate call via POST request
 * @version 23/03/16-002
 */
class GetDetailForClientList implements Runnable {

    // Input variables
    String id_cuid;
    String il_communication_id;
    String name_call_list;
    String contact_info;
    Object validTo;
    String origin;

    // Output variables
    String status = Status.ERROR.getStatus()
    Long httpResponseCode;    // HTTP response code
    String errorMessage;

    // Logger
    private static final Logger log = Logger.getLogger('groovyLog');

    // Event info
    EventInfo evtInfo;

    // Constants
    private final String resourcePrefix = "/blacklist";

    // Variables from properties
    private String username;
    private String password;
    private String host;

    private final String CONFIG_FILE = "/sas/groovy/Connections/affinity.properties";

    @Override
    void run() {

        //start log info
        log.info("Starting process PostDeactivateCall...");
        log.info("PostDeactivateCall - id_cuid: $id_cuid");
        log.info("PostDeactivateCall - il_communication_id: $il_communication_id");
        log.info("PostDeactivateCall - name_call_list: $name_call_list");
        log.info("PostDeactivateCall - contact_info: $contact_info");
        log.info("PostDeactivateCall - validTo: $validTo");
        log.info("PostDeactivateCall - origin: $origin");

        PropertiesResponse propertiesResponse = PropertiesUtils.getProperties(CONFIG_FILE)
        if (propertiesResponse.getProperties() == null) {
            status = propertiesResponse.getStatus().getStatus();
            errorMessage = propertiesResponse.getErrorMessage();
            return;
        }
        Properties config = propertiesResponse.getProperties();

        username = config.getProperty("affinity.username");
        password = config.getProperty("affinity.password");
        host = config.getProperty("affinity.host");

        log.info("PostDeactivateCall - host: $host");

            // Create empty table
//            ClientDetails = new RTDMTable();
//            ClientDetails.columnAdd("fieldName", DataTypes.STRING, Collections.emptyList());
//            ClientDetails.columnAdd("fieldValue", DataTypes.STRING, Collections.emptyList());

            // Setting API variables

            String uri = host + resourcePrefix;
            log.info("PostDeactivateCall - Endpoint URL: $uri");
            Request requestObject = createRequest()
            if (requestObject == null) {
                return
            }
            String jsonInputString = new ObjectMapper().writeValueAsString(requestObject)
            log.info("PostDeactivateCall - Request body: $jsonInputString");
            byte[] postData = jsonInputString.getBytes("UTF-8");

            HttpCallResponse httpCallResponse = HttpUtils.runHttpProcess(
                    evtInfo.getEventName(),
                    evtInfo.getSimulationDate().getTime(),
                    uri,
                    HttpUtils.getBasicAuthToken(username, password),
                    [(HttpUtils.CONTENT_TYPE): "application/json"],
                    RequestMethod.POST,
                    postData
            );

            httpResponseCode = httpCallResponse.getHttpResponseCode();
            status = httpCallResponse.getStatus().getStatus();
            errorMessage = httpCallResponse.getErrorMessage();
//            String responseString = httpCallResponse.getResponse()
//            if (responseString == null) {
//                return
//            }

            log.trace("PostDeactivateCall - httpResponseCode:" + httpResponseCode);
//            log.trace("PostDeactivateCall - response:" + responseString);

//            Map<String, String> responseObject = MappingUtils.mapToObject(responseString, Map.class)
//            if (responseObject == null) {
//                status = Status.ERROR.getStatus();
//                errorMessage = "Failed to map response. Wrong response data format."
//                return
//            }

            if (httpResponseCode == 200) {

//                for (Map.Entry element : responseObject.entrySet()) {
//                    Row newRow = ClientDetails.rowAdd();
//                    newRow.columnDataSet("fieldName", element.key);
//                    newRow.columnDataSet("fieldValue", element.value);
//                }

                status = "OK";
            }
    }

    Request createRequest() {
        String validToString = null;
        if (validTo instanceof GregorianCalendar) {
            validToString = validTo.getTimeInMillis().toString();
        }
        if (il_communication_id) {
            if (id_cuid || contact_info || name_call_list) {
                status = "ERROR"
                errorMessage = "If il_communication_id is filled in, no values from [id_cuid, contact_info, name_call_list] can be filled in."
                return null;
            }
            return new Request(
                    expressions: [
                        new Expression(
                            fieldName: "IL_COMMUNICATION_ID",
                            fieldValue: il_communication_id
                        )],
                    validTo: validToString,
                    origin: origin
            )
        }
        if (id_cuid) {
            if (contact_info) {
                if (name_call_list) {
                    status = "ERROR"
                    errorMessage = "If id_cuid + contact_info is filled in, name_call_list must be null."
                    return null;
                }
                return new Request(
                        expressions: [
                            new Expression(
                                fieldName: "ID_CUID",
                                fieldValue: id_cuid
                            ),
                            new Expression(
                                  fieldName: "CONTACT_INFO",
                                  fieldValue: contact_info
                            )],
                        validTo: validToString,
                        origin: origin
                )
            }
            if (name_call_list) {
                if (contact_info) {
                    status = "ERROR"
                    errorMessage = "If id_cuid + name_call_list is filled in, contact_info must be null."
                    return null;
                }
                return new Request(
                        expressions: [
                                new Expression(
                                        fieldName: "ID_CUID",
                                        fieldValue: id_cuid
                                ),
                                new Expression(
                                        fieldName: "NAME_CALL_LIST",
                                        fieldValue: name_call_list
                                )],
                        validTo: validToString,
                        origin: origin
                )
            }
        }

        status = "ERROR"
        errorMessage = "No valid combination of fields filled in. Valid combinations are: il_communication_id, id_cuid + contact_info, id_cuid + name_call_list."
        return null;
    }
}

class Request implements Serializable {
    List<Expression> expressions;
    String validTo;
    String origin;
}

class Expression implements Serializable {
    String fieldName;
    String fieldValue
}
