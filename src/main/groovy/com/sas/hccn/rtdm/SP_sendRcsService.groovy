package com.sas.hccn.rtdm

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.ObjectMapper
import org.apache.log4j.Logger

import java.nio.charset.StandardCharsets
import java.time.format.DateTimeFormatter

/**
 * @version 24/03/17-001
 */
class GetDetailForClientList implements Runnable {

    // Input variables
    String externalId
    String systemCode
    String messageCode
    Object expires
    String priority
    String reportLevel
    String reportContentType
    String recipient
    String text
    String contentType
    String JMSType
    Long JMSPriority
    String CHANNEL
    String RCS_PAYLOAD_TYPE
    String RCS_TEMPLATE_ID
    String CONTRACT_NUM
    String CUID
    String RCS_PARAM_VALUE_1
    String RCS_PARAM_VALUE_2
    String RCS_PARAM_VALUE_3
    String RCS_PARAM_VALUE_4
    String RCS_PARAM_VALUE_5
    String RCS_PARAM_VALUE_6
    String RCS_PARAM_VALUE_7
    String RCS_PARAM_VALUE_8
    String RCS_PARAM_VALUE_9
    String RCS_PARAM_VALUE_10

    // Output variables
    String status = "ERROR";            // either "OK" or "ERROR"
    String errorMessage;

    // Internal variables
    private static final Logger log = Logger.getLogger('groovyLog');
    private static final Logger requestReplyLog = Logger.getLogger('groovyRequestReply')

    private final String CONFIG_FILE = "/sas/groovy/Connections/rabbitmq.properties";
    private Properties config = new Properties();

    @Override
    void run() {
        //start log info
        log.info("Starting process SendRCS to RabbitMQ...");
        log.info("SendRCS - externalId: $externalId");
        log.info("SendRCS - systemCode: $systemCode");
        log.info("SendRCS - messageCode: $messageCode");
        log.info("SendRCS - expires: $expires");
        log.info("SendRCS - priority: $priority");
        log.info("SendRCS - reportLevel: $reportLevel");
        log.info("SendRCS - reportContentType: $reportContentType");
        log.info("SendRCS - recipient: $recipient");
        log.info("SendRCS - text: $text");
        log.info("SendRCS - CHANNEL: $CHANNEL");
        log.info("SendRCS - RCS_PAYLOAD_TYPE: $RCS_PAYLOAD_TYPE");
        log.info("SendRCS - RCS_TEMPLATE_ID: $RCS_TEMPLATE_ID");
        log.info("SendRCS - CONTRACT_NUM: $CONTRACT_NUM");
        log.info("SendRCS - CUID: $CUID");
        log.info("SendRCS - RCS_PARAM_VALUE_1: $RCS_PARAM_VALUE_1");
        log.info("SendRCS - RCS_PARAM_VALUE_2: $RCS_PARAM_VALUE_2");
        log.info("SendRCS - RCS_PARAM_VALUE_3: $RCS_PARAM_VALUE_3");
        log.info("SendRCS - RCS_PARAM_VALUE_4: $RCS_PARAM_VALUE_4");
        log.info("SendRCS - RCS_PARAM_VALUE_5: $RCS_PARAM_VALUE_5");
        log.info("SendRCS - RCS_PARAM_VALUE_6: $RCS_PARAM_VALUE_6");
        log.info("SendRCS - RCS_PARAM_VALUE_7: $RCS_PARAM_VALUE_7");
        log.info("SendRCS - RCS_PARAM_VALUE_8: $RCS_PARAM_VALUE_8");
        log.info("SendRCS - RCS_PARAM_VALUE_9: $RCS_PARAM_VALUE_9");
        log.info("SendRCS - RCS_PARAM_VALUE_10: $RCS_PARAM_VALUE_10");

        try {
            log.info("SendRCS - Loading configuration from path '$CONFIG_FILE'");
            this.config.load(new FileInputStream(CONFIG_FILE));
        } catch (Exception e) {
            log.error("SendRCS - Failed to load configuration: " + e.getMessage())
            status = "ERROR";
            throw e;
        }

        String host = config.getProperty("rabbitmq.host");
        String routingKey = config.getProperty("rabbitmq.rcs-routing-key");
        String username = config.getProperty("rabbitmq.username");
        String password = config.getProperty("rabbitmq.password");
        String virtualHost = config.getProperty("rabbitmq.virtual-host");
        String exchange = config.getProperty("rabbitmq.exchange");

        log.info("SendRCS - RabbitMQ configuration: host = $host, routingKey = $routingKey, virtualHost = $virtualHost, exchange = $exchange")
        Message message = new Message()
        message.setExternalId(externalId)
        message.setSystemCode(systemCode)
        message.setMessageCode(messageCode)
        if (expires instanceof GregorianCalendar) {
            message.setExpires(DateTimeFormatter.ISO_OFFSET_DATE_TIME.format(((GregorianCalendar) expires).toZonedDateTime()))
        }
        message.setPriority(priority)
        message.setReportLevel(reportLevel)
        message.setReportContentType(reportContentType)
        message.setRecipient(recipient)
        message.setText(text)
        List<Attribute> attributes = new ArrayList<>();
        if (CHANNEL) attributes.add(new Attribute(type: "CHANNEL", value: CHANNEL));
        if (RCS_PAYLOAD_TYPE) attributes.add(new Attribute(type: "RCS_PAYLOAD_TYPE", value: RCS_PAYLOAD_TYPE));
        if (RCS_TEMPLATE_ID) attributes.add(new Attribute(type: "RCS_TEMPLATE_ID", value: RCS_TEMPLATE_ID));
        if (CONTRACT_NUM) attributes.add(new Attribute(type: "CONTRACT_NUM", value: CONTRACT_NUM));
        if (CUID) attributes.add(new Attribute(type: "CUID", value: CUID));
        if (RCS_PARAM_VALUE_1) attributes.add(new Attribute(type: "RCS_PARAM_VALUE_1", value: RCS_PARAM_VALUE_1));
        if (RCS_PARAM_VALUE_2) attributes.add(new Attribute(type: "RCS_PARAM_VALUE_2", value: RCS_PARAM_VALUE_2));
        if (RCS_PARAM_VALUE_3) attributes.add(new Attribute(type: "RCS_PARAM_VALUE_3", value: RCS_PARAM_VALUE_3));
        if (RCS_PARAM_VALUE_4) attributes.add(new Attribute(type: "RCS_PARAM_VALUE_4", value: RCS_PARAM_VALUE_4));
        if (RCS_PARAM_VALUE_5) attributes.add(new Attribute(type: "RCS_PARAM_VALUE_5", value: RCS_PARAM_VALUE_5));
        if (RCS_PARAM_VALUE_6) attributes.add(new Attribute(type: "RCS_PARAM_VALUE_6", value: RCS_PARAM_VALUE_6));
        if (RCS_PARAM_VALUE_7) attributes.add(new Attribute(type: "RCS_PARAM_VALUE_7", value: RCS_PARAM_VALUE_7));
        if (RCS_PARAM_VALUE_8) attributes.add(new Attribute(type: "RCS_PARAM_VALUE_8", value: RCS_PARAM_VALUE_8));
        if (RCS_PARAM_VALUE_9) attributes.add(new Attribute(type: "RCS_PARAM_VALUE_9", value: RCS_PARAM_VALUE_9));
        if (RCS_PARAM_VALUE_10) attributes.add(new Attribute(type: "RCS_PARAM_VALUE_10", value: RCS_PARAM_VALUE_10));
        message.setAttributes(attributes)

        MessageWrapper messageWrapper = new MessageWrapper()
        messageWrapper.setMessage(Arrays.asList(message))

        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        String jsonMessage = mapper.writeValueAsString(messageWrapper)
        log.info("SendRCS - Serialized message:" + jsonMessage)

        Map<String, Object> headerMap = new HashMap<String, Object>();

        headerMap.put("SYSTEM_CODE", systemCode);
        headerMap.put("contentType", contentType);
        headerMap.put("REQUEST_ID", externalId);
        headerMap.put("priority", 0);
        headerMap.put("CorrelationID", externalId)
        headerMap.put("Type", JMSType)

        BasicProperties messageProperties = new AMQP.BasicProperties.Builder()
                .contentType(contentType)
                .priority(JMSPriority.toInteger())
                .headers(headerMap)
                .build();

        ConnectionFactory factory = new ConnectionFactory();
        factory.setHost(host);
        factory.setUsername(username);
        factory.setPassword(password);
        factory.setVirtualHost(virtualHost);
        Connection connection;
        try {
            connection = factory.newConnection();
            Channel channel = connection.createChannel();
            channel.basicPublish(exchange, routingKey, messageProperties, jsonMessage.getBytes(StandardCharsets.UTF_8));
            log.info("SendRCS - Message sent successfully");
            requestReplyLog.info("SendRCS - Sent message: $jsonMessage to RabbitMQ (host = $host, routingKey = $routingKey, virtualHost = $virtualHost, exchange = $exchange)")
            status = "OK";
        } catch (Exception e) {
            log.error("SendRCS - Failed to send message: " + e.getMessage())
            errorMessage = "Failed to send message: " + e.getMessage()
            status = "ERROR";
            throw e;
        } finally {
            if (connection != null) {
                connection.close()
            }
        }
    }
}

class MessageWrapper implements Serializable {
    List<Message> message;
}

class Message implements Serializable {
    String externalId;
    String systemCode;
    String messageCode;
    String expires;
    List<Attribute> attributes;
    String priority;
    String reportLevel;
    String reportContentType;
    String recipient;
    String text;
}

class Attribute implements Serializable {
    String type;
    String value;
}
