package com.sas.hccn.rtdm


import com.homecredit.sas.utils.HttpUtils
import com.homecredit.sas.utils.MappingUtils
import com.homecredit.sas.utils.PropertiesUtils
import com.homecredit.sas.utils.TokenUtils
import com.homecredit.sas.utils.enumeration.RequestMethod
import com.homecredit.sas.utils.enumeration.Status
import com.homecredit.sas.utils.model.HttpCallResponse
import com.homecredit.sas.utils.model.PropertiesResponse
import com.homecredit.sas.utils.model.TokenResponse
import com.sas.rtdm.implementation.engine.EventInfo
import org.apache.log4j.Logger

import javax.sql.DataSource
import java.sql.Connection
import java.sql.PreparedStatement

/**
 * Get Customer Details
 * @version 07/04/23-001
 */
class GetDetailForClientList implements Runnable {

    private Map <String, DataSource> mapJDBC = null;
    void setMapJDBC(Map <String, DataSource> input) {
        mapJDBC = input;
    }

    // Input variables
    String cuid;

    // Output variables
    String status = Status.ERROR.getStatus()
    Long httpResponseCode;    // HTTP response code
    String errorMessage;

    // Internal variables
    private static final Logger log = Logger.getLogger('groovyLog');

    // Event info
    EventInfo evtInfo;

    // Constants
    private final String comaResourcePrefix = "/rest/v12/contracts?customerId=";
    private final String pifResourcePrefix = "/party-web/api/pif/v1/customer/"

    // Variables from properties
    private String comaUsername;
    private String comaPassword;
    private String comaHost;
    private String comaAuthPath; //= "https://sso.vn00c1.vn.infra/auth/realms/hci/protocol/openid-connect/token";

    String pifUsername;
    String pifPassword;
    String pifHost;

    private final String COMA_CONFIG_FILE = "/sas/groovy/Connections/openid.properties";
    private final String PIF_CONFIG_FILE = "/sas/groovy/Connections/pif.properties";

    private Connection connection;

    @Override
    void run() {

        //start log info
        log.info("Starting process GetCustomerDetails...");
        log.info("GetCustomerDetails - cuid: $cuid");

        PropertiesResponse propertiesResponse = PropertiesUtils.getProperties(COMA_CONFIG_FILE)
        if (propertiesResponse.getProperties() == null) {
            status = propertiesResponse.getStatus().getStatus();
            errorMessage = propertiesResponse.getErrorMessage();
            return;
        }
        Properties config = propertiesResponse.getProperties();

        comaUsername = config.getProperty("openid.username");
        comaPassword = config.getProperty("openid.password");
        comaHost = config.getProperty("openid.host");
        comaAuthPath = config.getProperty("openid.authpath");

        log.info("GetCustomerDetails - coma host: $comaHost");
        log.info("GetCustomerDetails - coma resource prefix: $comaResourcePrefix");

        PropertiesResponse pifPropertiesResponse = PropertiesUtils.getProperties(PIF_CONFIG_FILE)
        if (pifPropertiesResponse.getProperties() == null) {
            status = pifPropertiesResponse.getStatus().getStatus();
            errorMessage = pifPropertiesResponse.getErrorMessage();
            return;
        }
        Properties pifConfig = pifPropertiesResponse.getProperties();

        pifUsername = pifConfig.getProperty("pif.username");
        pifPassword = pifConfig.getProperty("pif.password");
        pifHost = pifConfig.getProperty("pif.host"); // = "https://pif.vn00c1.vn.infra/";

        log.info("GetClientContacts - pif host: $pifHost");
        log.info("GetClientContacts - pif resource prefix: $pifResourcePrefix");

        if (cuid != null && !cuid.isEmpty()) {
            try {
                connection = mapJDBC.get('MA_TEMP_JDBC').getConnection()
            } catch (Exception e) {
                log.error("Failed to establish database connection", e)
                errorMessage = "Failed to establish database connection";
                return;
            }
            processComa()
            processPif()

            status = Status.OK.getStatus()
        } else {
            log.trace("GetCustomerDetails - No input clients");
        }
    }

    private void processComa() {
        // Setting API variables
        TokenResponse tokenResponse = TokenUtils.getToken(
                mapJDBC,
                evtInfo.getEventName(),
                evtInfo.getSimulationDate().getTime(),
                comaUsername,
                comaPassword,
                comaAuthPath,
                "MA_TEMP_JDBC",
                "RTDM_TEMP.SSO_TOKEN"
        )
        if (tokenResponse.getToken() == null) {
            status = tokenResponse.getStatus().getStatus();
            errorMessage = tokenResponse.getErrorMessage();
                return;
        }
        String authToken = tokenResponse.getToken();

        String uri = comaHost + comaResourcePrefix + cuid;
        log.info("GetCustomerDetails - coma Endpoint URL: $uri");

        HttpCallResponse httpCallResponse = HttpUtils.runHttpProcess(
                evtInfo.getEventName(),
                evtInfo.getSimulationDate().getTime(),
                uri,
                HttpUtils.getBearerAuthToken(authToken),
                [(HttpUtils.CONTENT_TYPE): "application/json"],
                RequestMethod.GET,
                null
        );

        httpResponseCode = httpCallResponse.getHttpResponseCode();
        status = httpCallResponse.getStatus().getStatus();
        errorMessage = httpCallResponse.getErrorMessage();
        String responseString = httpCallResponse.getResponse()
        if (responseString == null) {
                return
        }

        log.trace("GetCustomerDetails - httpResponseCode:" + httpResponseCode);
        log.trace("GetCustomerDetails - response:" + responseString);

        ComaResponse responseObject = MappingUtils.mapToObject(responseString, ComaResponse.class)
        if (responseObject == null) {
            status = Status.ERROR.getStatus();
            errorMessage = "Failed to map response. Wrong response data format."
                return
        }

        if (httpResponseCode == 200) {
            log.trace("GetCustomerDetails - storing COMA data into database");
            for (content in responseObject.content) {
                try {
                    PreparedStatement insertStatement = connection.prepareStatement(
                            "INSERT INTO RTDM_TEMP.FT_CONTRACT_AD (TEXT_CONTRACT_NUMBER, NAME_CREDIT_STATUS) VALUES (?,?)")
                    insertStatement.setString(1, content.code)
                    insertStatement.setString(2, content.status)
                    insertStatement.executeUpdate()
                    log.trace("GetCustomerDetails - stored COMA data row into database");
                } catch (Exception e) {
                    log.error("GetCustomerDetails - failed to insert row into database", e)
                }
            }
        } else {
            log.info("GetCustomerDetails - Wrong response from COMA. Cannot store data to database")
        }
    }

    private void processPif() {
        String uri = pifHost + pifResourcePrefix + cuid;

        log.info("GetCustomerDetails - pif Endpoint URL: $uri");

        HttpCallResponse httpCallResponse = HttpUtils.runHttpProcess(
                evtInfo.getEventName(),
                evtInfo.getSimulationDate().getTime(),
                uri,
                HttpUtils.getBasicAuthToken(pifUsername, pifPassword),
                [(HttpUtils.CONTENT_TYPE): "application/json"],
                RequestMethod.GET,
                null
        );

        httpResponseCode = httpCallResponse.getHttpResponseCode();
        status = httpCallResponse.getStatus().getStatus();
        errorMessage = httpCallResponse.getErrorMessage();
        String responseString = httpCallResponse.getResponse()
        if (responseString == null) {
            return
        }

        log.trace("GetCustomerDetails - httpResponseCode:" + httpResponseCode);
        log.trace("GetCustomerDetails - response:" + responseString);

        PifResponse responseObject = MappingUtils.mapToObject(responseString, PifResponse.class)
        if (responseObject == null) {
            status = Status.ERROR.getStatus();
            errorMessage = "Failed to map response. Wrong response data format."
            return
        }

        if (httpResponseCode == 200 && responseObject.resultCode == "FOUND") {
            ClientData data = responseObject.data;
            String primaryEmail = data.getEmailAddresses()
                    .find { email -> "PRIMARY_EMAIL" == email.classification }
                    ?.email
            String primaryPhoneNumber = data.getPhoneNumbers()
                    .find { number -> "PRIMARY_MOBILE" == number.classification }
                    ?.number


            log.trace("GetCustomerDetails - storing PIF data into database");
            try {
                PreparedStatement insertStatement = connection.prepareStatement(
                        "INSERT INTO RTDM_TEMP.FT_CLIENT_AD (ID_CUID, TEXT_CONTACT_PRIMARY_EMAIL, TEXT_CONTACT_PRIMARY_MOBILE, NAME_LAST, NAME_FIRST, NAME_MIDDLE, DATE_BIRTH, CODE_GENDER) VALUES (?,?,?,?,?,?,?,?)")
                insertStatement.setLong(1, data.externalId)
                insertStatement.setString(2, primaryEmail)
                insertStatement.setString(3, primaryPhoneNumber)
                insertStatement.setString(4, data.person.lastName)
                insertStatement.setString(5, data.person.firstName)
                insertStatement.setString(6, data.person.middleName)
                insertStatement.setString(7, data.person.birthDate)
                insertStatement.setString(8, data.person.gender.code)
                insertStatement.executeUpdate()
                log.trace("GetCustomerDetails - stored PIF data into database");
            } catch (Exception e) {
                log.error("GetCustomerDetails - failed to insert row into database", e)
            }
        } else {
            log.info("GetCustomerDetails - Wrong response from PIF. Cannot store data to database")
        }
    }
}

class ComaResponse implements Serializable {
     List<Content> content;
     int pageNumber;
     int pageSize;
     int totalPages;
     int totalElements;
}

class Content {
     String code;
     String status;
     String type;
     String paymentMode;
}

class PifResponse implements Serializable {
    ClientData data;
    Long externalId;
    String resultCode;
    String errorCode;
    String errorMessage;
    String requestId;
    Object validationErrors;
}

class ClientData implements Serializable {
    Long externalId;
    List<Email> emailAddresses;
    List<PhoneNumber> phoneNumbers;
    Person person;
    // ...
}

class Email implements Serializable {
    String email;
    String classification;
    // ...
}

class PhoneNumber implements Serializable {
    String number;
    String classification;
    // ...
}

class Person implements Serializable {
    String lastName;
    String firstName;
    String middleName;
    String birthDate;
    Gender gender;
    // ...
}

class Gender implements Serializable {
    String code;
    // ...
}