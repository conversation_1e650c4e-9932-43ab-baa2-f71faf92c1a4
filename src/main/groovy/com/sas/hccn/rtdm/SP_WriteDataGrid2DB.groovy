package com.sas.hccn.rtdm

import com.sas.rtdm.implementation.engine.EventInfo
import com.sas.analytics.ph.common.RTDMTable
import com.sas.analytics.ph.common.RTDMTable.Row
import com.sas.analytics.ph.common.RTDMTable.Column
import org.apache.log4j.Logger

import javax.sql.DataSource
import java.sql.*

class MyActivity implements Runnable {

    Map<String, DataSource> mapJDBC;

    //input
    RTDMTable inputDatagrid;
    String dbTable; //MONITOR.RTDM_LOG_IF136_DPO_DG_IN
    String gridColumns; //crmPilotCode,offerId
    String dbColumns; //CRM_PILOT_CODE, OFFER_ID
    Long logId
    String logIdColumnName

    //output:
    String status
    String statusDescription = ""
    String errorMessage;

    // Event info
    EventInfo evtInfo;
    private boolean useDB = true
    // Logger
    private static final Logger log = Logger.getLogger('groovyLog');

    @Override
    void run() {
        if (dbTable == null || dbTable.isEmpty()) {
            log.error("SP_WriteDataGrid2DB - DB Table is null or empty")
            status = "ERROR"
            errorMessage = "DB Table is null or empty"
            return
        }
        if (gridColumns == null || gridColumns.isEmpty()) {
            log.error("SP_WriteDataGrid2DB - Grid columns is null or empty")
            status = "ERROR"
            errorMessage = "Grid columns is null or empty"
            return
        }
        if (dbColumns == null || dbColumns.isEmpty()) {
            log.error("SP_WriteDataGrid2DB - DB columns is null or empty")
            status = "ERROR"
            errorMessage = "DB columns is null or empty"
            return
        }

        List<String> gridColumnsList = gridColumns.split(",").collect { it.trim() }
        List<String> dbColumnsList = dbColumns.split(",").collect { it.trim() }
        if (gridColumnsList.size() != dbColumnsList.size()) {
            log.error("SP_WriteDataGrid2DB: Number of Grid columns must be equal to number of DB columns. " +
                    "Grid columns = ${gridColumnsList.size()}, DB columns = ${dbColumnsList.size()}")
            status = "ERROR"
            errorMessage = "Number of Grid columns must be equal to number of DB columns.";
            return
        }

        Connection conn;
        if (useDB) {
            try {
                conn = mapJDBC.get("MA_TEMP_JDBC").getConnection()
            } catch (Exception e) {
                log.error("SP_WriteDataGrid2DB: Failed to create connection:" + e)
                status = "ERROR"
                errorMessage = e.getLocalizedMessage();
                return
            }
            log.info("SP_WriteDataGrid2DB - connection URL: " + conn.getMetaData().getURL());
        } else {
            log.info("SP_WriteDataGrid2DB - TEST mode, not using real DB")
        }

        //Mapping of grid data to list of maps.
        //Each item in list represents one row
        //Each item in map is represented by key=column name and value=column value
        List<Map<String, Object>> filteredGridData = new ArrayList<>();
        for (Row row: inputDatagrid.iterator()) {
            Map<String, Object> filteredDataPerRow = new HashMap<>();
            for (Column column : inputDatagrid.getColumns()) {
                String columnType = column.type.toString()
                String columnName = column.name
                if (gridColumnsList.contains(columnName)) {
                    Object columnValue = row.columnDataGet(columnName)
                    log.trace("SP_WriteDataGrid2DB: Column name $columnName should be stored to DB. Adding value $columnValue of type $columnType")
                    filteredDataPerRow.put(columnName, columnValue)
                } else {
                    log.trace("SP_WriteDataGrid2DB: Column name $columnName should NOT be stored to DB. Skipping")
                }
            }
            filteredGridData.add(filteredDataPerRow)
        }

        if (filteredGridData.isEmpty()) {
            log.error("SP_WriteDataGrid2DB: No data filtered from input datagrid. Exiting")
            status = "OK"
            return
        }

        //Transforming the map from grid names to column names, values remaing the same
        List<Map<String, Object>> transformedData = new ArrayList<>();
        for (Map<String, Object> row : filteredGridData) {
            Map<String, Object> transformedRow = new HashMap<>();
            for (int i = 0; i < gridColumnsList.size(); i++) {
                String gridColumn = gridColumnsList.get(i);
                String dbColumn = dbColumnsList.get(i);
                Object value = row.get(gridColumn)
                log.trace("SP_WriteDataGrid2DB: Mapping from grid name to DB name. Grid name: $gridColumn, DB name: $dbColumn, value: $value")
                transformedRow.put(dbColumn, value);
            }
            transformedData.add(transformedRow);
        }

        //Storing data into DB:
        String placeholders = String.join(", ", Collections.nCopies(dbColumnsList.size(), "?"))
        String sql = "INSERT INTO " + dbTable + " (" + dbColumns + ", EVENT_ID, EVENT_NAME, " + logIdColumnName + ", CREATED_DTTM) VALUES (" + placeholders + ", ?, ?, ?, SYSDATE)"

        PreparedStatement st = null
        try {
            if (useDB) {
                st = conn.prepareStatement(sql)
            }
            for (Map<String, Object> row : transformedData) {
                int index = 1;
                List<Object> values = new ArrayList<>(); // To log actual values
                for (String column : dbColumnsList) {
                    Object value = row.get(column);
                    values.add(value);
                    setPreparedStatementValue(st, index++, value);
                }
                setPreparedStatementValue(st, index++, evtInfo.getIdentity())
                values.add(evtInfo.getIdentity())
                setPreparedStatementValue(st, index++, evtInfo.getEventName())
                values.add(evtInfo.getEventName())
                setPreparedStatementValue(st, index++, logId)
                values.add(logId)
                // Log the full query
                String filledQuery = constructQueryWithValues(sql, values);
                log.info("Executing SQL: " + filledQuery);

                if (useDB) {
                    st.addBatch();
                }
            }
            if (useDB) {
                st.executeBatch();
            }
        } catch (Exception e) {
            errorMessage = e.getLocalizedMessage();
            log.error("SP_WriteDataGrid2DB: oracle error:" + e)
        } finally {
            if (st != null) {
                st.close();
            }
            if (conn != null) {
                conn.close();
            }
        }

        status = "OK"
        log.trace("SP_WriteDataGrid2DB - End");
    }

    private static void setPreparedStatementValue(PreparedStatement statement, int index, Object value) throws SQLException {
        if (statement == null) {
            return
        }
        if (value == null) {
            statement.setNull(index, Types.NULL);
        } else if (value instanceof Long) {
            statement.setLong(index, (Long) value);
        } else if (value instanceof Double) {
            statement.setDouble(index, (Double) value);
        } else if (value instanceof String) {
            statement.setString(index, (String) value);
        } else if (value instanceof Boolean) {
            statement.setBoolean(index, (Boolean) value);
        } else if (value instanceof GregorianCalendar) {
            GregorianCalendar calendar = (GregorianCalendar) value;
            statement.setTimestamp(index, new Timestamp(calendar.getTimeInMillis()));
        } else {
            throw new SQLException("Unsupported data type: " + value.getClass().getName());
        }
    }

    private static String constructQueryWithValues(String sql, List<Object> values) {
        StringBuilder query = new StringBuilder(sql);
        for (Object value : values) {
            int placeholderIndex = query.indexOf("?");
            if (placeholderIndex == -1) {
                break
            }

            String replacement = valueToString(value);
            query.replace(placeholderIndex, placeholderIndex + 1, replacement);
        }
        return query.toString()
    }

    private static String valueToString(Object value) {
        if (value == null) {
            return "NULL"
        } else if (value instanceof String) {
            return "'" + value + "'"
        } else if (value instanceof java.util.Date || value instanceof GregorianCalendar) {
            if (value instanceof GregorianCalendar) {
                value = ((GregorianCalendar) value).getTime()
            }
            return "'" + new Timestamp(((java.util.Date) value).getTime()) + "'"
        } else if (value instanceof Boolean) {
            return (Boolean) value ? "TRUE" : "FALSE"
        } else {
            return value.toString()
        }
    }
}