package com.sas.hccn.rtdm


import com.homecredit.sas.utils.HttpUtils
import com.homecredit.sas.utils.PropertiesUtils
import com.homecredit.sas.utils.enumeration.RequestMethod
import com.homecredit.sas.utils.enumeration.Status
import com.homecredit.sas.utils.model.HttpCallResponse
import com.homecredit.sas.utils.model.PropertiesResponse
import com.sas.rtdm.implementation.engine.EventInfo
import org.apache.log4j.Logger
import javax.xml.stream.XMLInputFactory;
import javax.xml.stream.XMLStreamReader;
import javax.xml.stream.events.XMLEvent;

/**
 * Get District Name
 * @version 13/03/24-001
 */
class GetDetailForClientList implements Runnable {

    // Input variables
    String districtCode;

    // Output variables
    String status = Status.ERROR.getStatus()
    Long httpResponseCode;    // HTTP response code
    String errorMessage;
    String districtName;

    // Logger
    private static final Logger log = Logger.getLogger('groovyLog');

    // Event info
    EventInfo evtInfo;

    // Constants
    private final String resourcePrefix = "/csd/api/csd/v1/boundary/search/D?code="; //https://csd.id00c1.id.infra

    // Variables from properties
    private String username;
    private String password;
    private String host;

    private final String CONFIG_FILE = "/sas/groovy/Connections/csd.properties";

    @Override
    void run() {

        //start log info
        log.info("Starting process GetDisctrictName...");
        log.info("GetDisctrictName - districtCode: $districtCode");

        PropertiesResponse propertiesResponse = PropertiesUtils.getProperties(CONFIG_FILE)
        if (propertiesResponse.getProperties() == null) {
            status = propertiesResponse.getStatus().getStatus();
            errorMessage = propertiesResponse.getErrorMessage();
            return;
        }
        Properties config = propertiesResponse.getProperties();

        username = config.getProperty("csd.username");
        password = config.getProperty("csd.password");
        host = config.getProperty("csd.host");

        log.info("GetDisctrictName - host: $host");
        log.info("GetDisctrictName - resource prefix: $resourcePrefix");

        if (districtCode) {

            String uri = host + resourcePrefix + districtCode;
            log.info("GetDisctrictName - Endpoint URL: $uri");

            HttpCallResponse httpCallResponse = HttpUtils.runHttpProcess(
                    evtInfo.getEventName(),
                    evtInfo.getSimulationDate().getTime(),
                    uri,
                    HttpUtils.getBasicAuthToken(username, password),
                    [(HttpUtils.CONTENT_TYPE): "application/json"],
                    RequestMethod.GET,
                    null
            );

            httpResponseCode = httpCallResponse.getHttpResponseCode();
            status = httpCallResponse.getStatus().getStatus();
            errorMessage = httpCallResponse.getErrorMessage();
            String responseString = httpCallResponse.getResponse()
            if (responseString == null) {
                return
            }

            log.trace("GetDisctrictName - httpResponseCode:" + httpResponseCode);
            log.trace("GetDisctrictName - response:" + responseString);

            if (httpResponseCode == 200) {
                XMLInputFactory factory = XMLInputFactory.newInstance();
                XMLStreamReader reader = factory.createXMLStreamReader(new StringReader(responseString));

                while (reader.hasNext()) {
                    if (reader.next() == XMLEvent.START_ELEMENT
                            && "name".equals(reader.getLocalName())
                            && "http://homecredit.net/hss/ws/customer/staticdata/v1".equals(reader.getNamespaceURI())) {
                        reader.next(); // Move to text node
                        districtName = reader.getText();
                        break;
                    }
                }
            }
        } else {
            log.trace("GetDisctrictName - No input clients");
        }
    }
}