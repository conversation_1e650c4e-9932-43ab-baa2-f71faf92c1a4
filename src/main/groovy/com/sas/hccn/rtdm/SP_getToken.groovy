package com.sas.hccn.rtdm

import com.fasterxml.jackson.annotation.JsonProperty
import com.homecredit.sas.utils.HttpUtils
import com.homecredit.sas.utils.MappingUtils
import com.homecredit.sas.utils.enumeration.RequestMethod
import com.homecredit.sas.utils.model.HttpCallResponse
import com.sas.rtdm.implementation.engine.EventInfo
import org.apache.log4j.Logger

import javax.sql.DataSource
import java.sql.*

class MyActivity implements Runnable {

    Map<String, DataSource> mapJDBC;

    //input
    String username;
    String password;
    String authPath; //"https://sso.vn00c1.vn.infra/auth/realms/hci/protocol/openid-connect/token"

    //output:
    String token
    String errorMessage;

    // Event info
    EventInfo evtInfo;
    // Logger
    private static final Logger log = Logger.getLogger('groovyLog');

    @Override
    void run() {
        Connection conn = mapJDBC.get('MA_TEMP_JDBC').getConnection()
        PreparedStatement st = null
        Statement stmt = conn.createStatement();
        try {
            if (conn != null) {
                log.info("SP_GetToken - Obtaining token from DB");

                long now = System.currentTimeSeconds()

                String query = "SELECT ACCESS_TOKEN, REFRESH_TOKEN, EXPIRES_IN, REFRESH_EXPIRES_IN FROM RTDM_TEMP.SSO_TOKEN WHERE USERNAME = ? AND PROVIDER = ?";
                st = conn.prepareStatement(query);

                log.trace("SP_GetToken: SQL parameter username: 1 = " + username);
                st.setString(1, username);
                log.trace("SP_GetToken: SQL parameter provider: 2 = " + authPath);
                st.setString(2, authPath);

                log.trace("SP_GetToken: SQL query: " + query);
                ResultSet rs = st.executeQuery();

                String accessToken = "";
                String refreshToken = "";
                Long expiresIn = 0;
                Long refreshExpiresIn = 0;

                while (rs.next()) {
                    accessToken = rs.getString(1)
                    refreshToken = rs.getString(2)
                    expiresIn = rs.getLong(3)
                    refreshExpiresIn = rs.getLong(4)
                }

                // more than 30 seconds until expiration of token -> token is valid
                if (expiresIn - now > 30) {
                    log.info("SP_GetToken - Valid token stored in DB, returning");
                    token = accessToken
                    return
                }

                Token tokenResponse;
                // more than 30 seconds until expiration of refresh token -> we can call SSO with refresh token
                if (refreshExpiresIn - now > 30) {
                    log.info("SP_GetToken - Valid token not stored in DB, executing request to SSO with refresh token");
                    tokenResponse = getTokenWithRefreshToken(refreshToken)
                } else {
                    log.info("SP_GetToken - Valid token not stored in DB, executing request to SSO with username + password");
                    tokenResponse = getTokenWithUsernameAndPassword()
                }

                if (tokenResponse == null) {
                    return
                }
                token = tokenResponse.accessToken

                log.info("SP_GetToken - Storing new token into db");
                String updateQuery = "MERGE INTO RTDM_TEMP.SSO_TOKEN stored " +
                        "USING (SELECT ? as ACCESS_TOKEN, ? as REFRESH_TOKEN, ? as EXPIRES_IN, ? as REFRESH_EXPIRES_IN, ? as USERNAME, ? as PROVIDER, null as UPDATE_DTTM FROM dual) x " +
                        "ON (x.USERNAME = stored.USERNAME AND x.PROVIDER = stored.PROVIDER) " +
                        "WHEN MATCHED THEN " +
                        "UPDATE SET ACCESS_TOKEN = x.ACCESS_TOKEN, REFRESH_TOKEN = x.REFRESH_TOKEN, EXPIRES_IN = x.EXPIRES_IN, REFRESH_EXPIRES_IN = x.REFRESH_EXPIRES_IN, UPDATE_DTTM = SYSTIMESTAMP " +
                        "WHEN NOT MATCHED THEN " +
                        "INSERT (ACCESS_TOKEN, REFRESH_TOKEN, EXPIRES_IN, REFRESH_EXPIRES_IN, USERNAME, PROVIDER, UPDATE_DTTM) VALUES (x.ACCESS_TOKEN, x.REFRESH_TOKEN, x.EXPIRES_IN, x.REFRESH_EXPIRES_IN, x.USERNAME, x.PROVIDER, SYSTIMESTAMP)"
                st = conn.prepareStatement(updateQuery);
                log.trace("SP_GetToken: SQL parameter access token: 1 = " + tokenResponse.accessToken);
                st.setString(1, tokenResponse.accessToken);
                log.trace("SP_GetToken: SQL parameter refresh token: 2 = " + tokenResponse.refreshToken);
                st.setString(2, tokenResponse.refreshToken);
                log.trace("SP_GetToken: SQL parameter expires_in: 3 = " + (now + tokenResponse.expiresIn));
                st.setLong(3, now + tokenResponse.expiresIn);
                log.trace("SP_GetToken: SQL parameter refresh_expires_in: 4 = " + (now + tokenResponse.refreshExpiresIn));
                st.setLong(4, now + tokenResponse.refreshExpiresIn);
                log.trace("SP_GetToken: SQL parameter username: 5 = " + username);
                st.setString(5, username);
                log.trace("SP_GetToken: SQL parameter authPath: 6 = " + authPath);
                st.setString(6, authPath);
                log.trace("SP_GetToken: SQL query: " + updateQuery);
                int updateRs = st.executeUpdate();
                if (updateRs == 1) {
                    log.info("Token was INSERTED for user " + username + " and provider " + authPath)
                } else {
                    log.info("Token was UPDATED for user " + username + " and provider " + authPath)
                }
            }
        } catch (SQLException e) {
            errorMessage = e.getLocalizedMessage();
            log.error("SP_GetToken: oracle error:" + e)
        } finally {
            if (st != null) {
                st.close();
            }
            if (stmt != null) {
                stmt.close();
            }
            if (conn != null) {
                conn.close();
            }
        }

        log.trace("SP_GetToken - End");
    }

    private Token getTokenWithUsernameAndPassword() {
        log.trace("SP_GetToken - token: getting new auth token");

        String formData = "grant_type=client_credentials&scope=profile email"
        byte[] postData = formData.getBytes("UTF-8");

        HttpCallResponse httpCallResponse = HttpUtils.runHttpProcess(
                evtInfo.getEventName(),
                evtInfo.getSimulationDate().getTime(),
                authPath,
                HttpUtils.getBasicAuthToken(username, password),
                [(HttpUtils.CONTENT_TYPE): "application/x-www-form-urlencoded"],
                RequestMethod.POST,
                postData
        );

        Long httpResponseCode = httpCallResponse.getHttpResponseCode();
        errorMessage = httpCallResponse.getErrorMessage();
        String responseString = httpCallResponse.getResponse()

        log.trace("SP_GetToken - httpResponseCode:" + httpResponseCode);
        log.trace("SP_GetToken - response:" + responseString);

        if (responseString == null) {
            return null
        }

        if (httpResponseCode == 200) {
            Token token = MappingUtils.mapToObject(responseString, Token.class)
            if (token == null) {
                log.error("Failed to map response. Wrong response data format.")
                errorMessage = "Failed to map response. Wrong response data format."
                return null
            }
            return token
        }
        log.error("Failed to receive token from SSO")
        return null
    }

    Token getTokenWithRefreshToken(String refreshToken) {
        log.trace("SP_GetToken - token: getting refreshed auth token");

        String formData = "grant_type=refresh_token&refresh_token=" + refreshToken
        byte[] postData = formData.getBytes("UTF-8");

        HttpCallResponse httpCallResponse = HttpUtils.runHttpProcess(
                evtInfo.getEventName(),
                evtInfo.getSimulationDate().getTime(),
                authPath,
                HttpUtils.getBasicAuthToken(username, password),
                [(HttpUtils.CONTENT_TYPE): "application/x-www-form-urlencoded"],
                RequestMethod.POST,
                postData
        );

        Long httpResponseCode = httpCallResponse.getHttpResponseCode();
        errorMessage = httpCallResponse.getErrorMessage();
        String responseString = httpCallResponse.getResponse()

        log.trace("SP_GetToken - httpResponseCode:" + httpResponseCode);
        log.trace("SP_GetToken - response:" + responseString);

        if (responseString == null) {
            return null
        }

        if (httpResponseCode == 200) {
            Token token = MappingUtils.mapToObject(responseString, Token.class)
            if (token == null) {
                log.error("Failed to map response. Wrong response data format.")
                errorMessage = "Failed to map response. Wrong response data format."
                return null
            }
            return token
        }
        log.error("Failed to receive token from SSO")
        return null
    }
}

class Token implements Serializable {
    @JsonProperty("access_token")
    String accessToken;
    @JsonProperty("expires_in")
    Long expiresIn;
    @JsonProperty("refresh_expires_in")
    Long refreshExpiresIn;
    @JsonProperty("refresh_token")
    String refreshToken;
    // ...
}