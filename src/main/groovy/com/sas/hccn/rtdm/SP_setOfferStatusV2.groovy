package com.sas.hccn.rtdm

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.ObjectMapper
import com.homecredit.sas.utils.HttpUtils
import com.homecredit.sas.utils.PropertiesUtils
import com.homecredit.sas.utils.enumeration.RequestMethod
import com.homecredit.sas.utils.enumeration.Status
import com.homecredit.sas.utils.model.HttpCallResponse
import com.homecredit.sas.utils.model.PropertiesResponse
import com.sas.rtdm.implementation.engine.EventInfo
import org.apache.log4j.Logger

import java.nio.charset.StandardCharsets

/**
 * Set offer status API V2
 * @version 25/04/15
 */
class SP_setOfferStatus implements Runnable {

    // Input variables
    String offerId;
    String newStatus;
    String statusChangeReason;

    // Output variables
    String status = Status.ERROR.getStatus()
    Long httpResponseCode;    // HTTP response code
    String errorMessage;

    // Logger
    private static final Logger log = Logger.getLogger('groovyLog');

    // Event info
    EventInfo evtInfo;

    // Constants
    private final String resourcePrefix = "/rest/v2/offers/"
    private final String resourceSuffix = "/status"

    // Variables from properties
    private String host;
    private String username;
    private String password;

    private final String CONFIG_FILE = "/sas/groovy/Connections/ofs.properties";

    @Override
    void run() {

        //start log info
        log.info("Starting process SetOfferStatus...");
        log.info("SetOfferStatus - offerId: $offerId");
        log.info("SetOfferStatus - newStatus: $newStatus");
        log.info("SetOfferStatus - statusChangeReason: $statusChangeReason");

        PropertiesResponse propertiesResponse = PropertiesUtils.getProperties(CONFIG_FILE)
        if (propertiesResponse.getProperties() == null) {
            status = propertiesResponse.getStatus().getStatus();
            errorMessage = propertiesResponse.getErrorMessage();
            return;
        }
        Properties config = propertiesResponse.getProperties();

        host = config.getProperty("ofs.host");
        username = config.getProperty("ofs.username");
        password = config.getProperty("ofs.password");

        log.info("SetOfferStatus - host: $host");
        log.info("SetOfferStatus - resource prefix: $resourcePrefix");

        if (offerId != null && !offerId.isEmpty()) {

            // Setting API variables
            String uri = host + resourcePrefix + offerId + resourceSuffix;
            log.info("SetOfferStatus - Endpoint URL: $uri");

            SetStatusRequest setStatusRequest = new SetStatusRequest(
                    status: newStatus,
                    manualStatusChangeReason: statusChangeReason
            );
            ObjectMapper mapper = new ObjectMapper();
            mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
            String jsonInputString = mapper.writeValueAsString(setStatusRequest)

            HttpCallResponse httpCallResponse = HttpUtils.runHttpProcess(
                    evtInfo.getEventName(),
                    evtInfo.getSimulationDate().getTime(),
                    uri,
                    HttpUtils.getBasicAuthToken(username, password),
                    [(HttpUtils.CONTENT_TYPE): "application/json"],
                    RequestMethod.POST,
                    jsonInputString.getBytes(StandardCharsets.UTF_8)
            );

            httpResponseCode = httpCallResponse.getHttpResponseCode();
            status = httpCallResponse.getStatus().getStatus();
            errorMessage = httpCallResponse.getErrorMessage();
            log.trace("SetOfferStatus - httpResponseCode:" + httpResponseCode);
        } else {
            log.trace("SetOfferStatus - No input clients");
        }
    }
}

class SetStatusRequest implements Serializable {
    String status;
    String manualStatusChangeReason;
}
