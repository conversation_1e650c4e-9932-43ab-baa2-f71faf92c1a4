package com.sas.hccn.rtdm;

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.ObjectMapper
import com.homecredit.sas.utils.PropertiesUtils
import com.homecredit.sas.utils.model.PropertiesResponse
import org.apache.log4j.Logger

import java.nio.charset.StandardCharsets
import java.time.format.DateTimeFormatter

/**
 * @version 23/03/15-005
 */
class GetDetailForClientList implements Runnable {

    // Input variables
    String externalId;
    String systemCode;
    String messageCode;
    Object expires;
    String priority;
    String reportLevel;
    String reportContentType;
    String recipient;
    String subject;
    String htmlTextCharset;
    String htmlTextValue;

    String CONTRACT_NUM;
    Long CUID;
    String CUSTOM_INFO1;
    String CUSTOM_INFO2;
    String CUSTOM_INFO3;
    String CUSTOM_INFO4;
    String CUSTOM_INFO5;
    String CUSTOM_INFO6;
    String CUSTOM_INFO7;
    String CUSTOM_INFO8;
    String CUSTOM_INFO9;
    String CUSTOM_INFO10;
    String FROM_EMAIL;
    String FROM_NAME;
    Long PEPIPOST_IGNORE_TEXT;
    String SAS_TEMPLATE_ID;

    // Output variables
    String status = "ERROR";            // either "OK" or "ERROR"
    String errorMessage;

    // Internal variables
    private static final Logger log = Logger.getLogger('groovyLog');
    private static final Logger requestReplyLog = Logger.getLogger('groovyRequestReply')

    private final String CONFIG_FILE = "/sas/groovy/Connections/rabbitmq.properties";

    @Override
    void run() {
        //start log info
        log.info("Starting process SendEmail to RabbitMQ...");
        log.info("SendEmail - externalId: $externalId");
        log.info("SendEmail - systemCode: $systemCode");
        log.info("SendEmail - messageCode: $messageCode");
        log.info("SendEmail - expires: $expires");
        log.info("SendEmail - priority: $priority");
        log.info("SendEmail - reportLevel: $reportLevel");
        log.info("SendEmail - reportContentType: $reportContentType");
        log.info("SendEmail - recipient: $recipient");
        log.info("SendEmail - subject: $subject");
        log.info("SendEmail - htmlTextCharset: $htmlTextCharset");
        log.info("SendEmail - htmlTextValue: $htmlTextValue");
        log.info("SendEmail - CONTRACT_NUM: $CONTRACT_NUM");
        log.info("SendEmail - CUID: $CUID");
        log.info("SendEmail - CUSTOM_INFO1: $CUSTOM_INFO1");
        log.info("SendEmail - CUSTOM_INFO2: $CUSTOM_INFO2");
        log.info("SendEmail - CUSTOM_INFO3: $CUSTOM_INFO3");
        log.info("SendEmail - CUSTOM_INFO4: $CUSTOM_INFO4");
        log.info("SendEmail - CUSTOM_INFO5: $CUSTOM_INFO5");
        log.info("SendEmail - CUSTOM_INFO6: $CUSTOM_INFO6");
        log.info("SendEmail - CUSTOM_INFO7: $CUSTOM_INFO7");
        log.info("SendEmail - CUSTOM_INFO8: $CUSTOM_INFO8");
        log.info("SendEmail - CUSTOM_INFO9: $CUSTOM_INFO9");
        log.info("SendEmail - CUSTOM_INFO10: $CUSTOM_INFO10");
        log.info("SendEmail - FROM_EMAIL: $FROM_EMAIL");
        log.info("SendEmail - FROM_NAME: $FROM_NAME");
        log.info("SendEmail - PEPIPOST_IGNORE_TEXT: $PEPIPOST_IGNORE_TEXT");
        log.info("SendEmail - FROM_NAME: $SAS_TEMPLATE_ID");

        List<String> emptyFields = new ArrayList<>()
        if (!externalId) {
            emptyFields.add("externalId")
        }
        if (!systemCode) {
           emptyFields.add("systemCode")
        }
        if (!messageCode) {
           emptyFields.add("messageCode")
        }
        if (!recipient) {
           emptyFields.add("recipient")
        }
        if (!subject) {
           emptyFields.add("subject")
        }
        if (!htmlTextCharset) {
           emptyFields.add("htmlTextCharset")
        }
        if (!htmlTextValue) {
           emptyFields.add("htmlTextValue")
        }
        if (!emptyFields.isEmpty()) {
            errorMessage = "Following mandatory fields are null or empty: " + String.join(", ", emptyFields)
            return
        }

        PropertiesResponse propertiesResponse = PropertiesUtils.getProperties(CONFIG_FILE)
        if (propertiesResponse.getProperties() == null) {
            status = propertiesResponse.getStatus().getStatus();
            errorMessage = propertiesResponse.getErrorMessage();
            return;
        }
        Properties config = propertiesResponse.getProperties();

        String host = config.getProperty("rabbitmq.host");
        String routingKey = config.getProperty("rabbitmq.email-routing-key");
        String username = config.getProperty("rabbitmq.username");
        String password = config.getProperty("rabbitmq.password");
        String virtualHost = config.getProperty("rabbitmq.virtual-host");
        String exchange = config.getProperty("rabbitmq.exchange");

        log.info("SendEmail - RabbitMQ configuration: host = $host, routingKey = $routingKey, virtualHost = $virtualHost, exchange = $exchange")
        Message message = new Message()

        message.setExternalId(externalId)
        message.setSystemCode(systemCode)
        message.setMessageCode(messageCode)
        if (expires instanceof GregorianCalendar) {
            message.setExpires(DateTimeFormatter.ISO_OFFSET_DATE_TIME.format(((GregorianCalendar) expires).toZonedDateTime()))
        }
        message.setPriority(priority)
        message.setReportLevel(reportLevel)
        message.setReportContentType(reportContentType)
        message.setRecipient(recipient)
        message.setSubject(subject)
        message.setHtmlText(new HtmlText(charset: htmlTextCharset, value: htmlTextValue))
        List<Attribute> attributes = new ArrayList<Attribute>();
        if (CONTRACT_NUM) attributes.add(new Attribute(type: "CONTRACT_NUM", value: CONTRACT_NUM))
        if (CUID) attributes.add(new Attribute(type: "CUID", value: CUID.toString()))
        if (CUSTOM_INFO1) attributes.add(new Attribute(type: "CUSTOM_INFO1", value: CUSTOM_INFO1))
        if (CUSTOM_INFO2) attributes.add(new Attribute(type: "CUSTOM_INFO2", value: CUSTOM_INFO2))
        if (CUSTOM_INFO3) attributes.add(new Attribute(type: "CUSTOM_INFO3", value: CUSTOM_INFO3))
        if (CUSTOM_INFO4) attributes.add(new Attribute(type: "CUSTOM_INFO4", value: CUSTOM_INFO4))
        if (CUSTOM_INFO5) attributes.add(new Attribute(type: "CUSTOM_INFO5", value: CUSTOM_INFO5))
        if (CUSTOM_INFO6) attributes.add(new Attribute(type: "CUSTOM_INFO6", value: CUSTOM_INFO6))
        if (CUSTOM_INFO7) attributes.add(new Attribute(type: "CUSTOM_INFO7", value: CUSTOM_INFO7))
        if (CUSTOM_INFO8) attributes.add(new Attribute(type: "CUSTOM_INFO8", value: CUSTOM_INFO8))
        if (CUSTOM_INFO9) attributes.add(new Attribute(type: "CUSTOM_INFO9", value: CUSTOM_INFO9))
        if (CUSTOM_INFO10) attributes.add(new Attribute(type: "CUSTOM_INFO10", value: CUSTOM_INFO10))
        if (FROM_EMAIL) attributes.add(new Attribute(type: "FROM_EMAIL", value: FROM_EMAIL))
        if (FROM_NAME) attributes.add(new Attribute(type: "FROM_NAME", value: FROM_NAME))
        if (PEPIPOST_IGNORE_TEXT) attributes.add(new Attribute(type: "PEPIPOST_IGNORE_TEXT", value: PEPIPOST_IGNORE_TEXT.toString()))
        if (SAS_TEMPLATE_ID) attributes.add(new Attribute(type: "SAS_TEMPLATE_ID", value: SAS_TEMPLATE_ID))
        message.setAttributes(attributes)

        MessageWrapper messageWrapper = new MessageWrapper()
        messageWrapper.setMessage(Arrays.asList(message))

        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        String jsonMessage = mapper.writeValueAsString(messageWrapper)
        log.info("SendEmail - Serialized message:" + jsonMessage)

        Map<String, Object> headerMap = new HashMap<String, Object>();
        headerMap.put("SYSTEM_CODE", systemCode);
        headerMap.put("REQUEST_ID", externalId);
        headerMap.put("priority", 0);
        headerMap.put("cuid", CUID.toString());
        headerMap.put("CorrelationID", externalId)
        headerMap.put("Type", "JMSType")
        BasicProperties messageProperties = new AMQP.BasicProperties.Builder()
                .contentType("application/json")
                .headers(headerMap)
                .build();

        ConnectionFactory factory = new ConnectionFactory();
        factory.setHost(host);
        factory.setUsername(username);
        factory.setPassword(password);
        factory.setVirtualHost(virtualHost);
        Connection connection;
        try {
            connection = factory.newConnection();
            Channel channel = connection.createChannel();
            channel.basicPublish(exchange, routingKey, messageProperties, jsonMessage.getBytes(StandardCharsets.UTF_8));
            log.info("SendEmail - Message sent successfully");
            requestReplyLog.info("SendEmail - Sent message: $jsonMessage to RabbitMQ (host = $host, routingKey = $routingKey, virtualHost = $virtualHost, exchange = $exchange)")
            status = "OK";
        } catch (Exception e) {
            log.error("SendEmail - Failed to send message: " + e.getMessage())
            throw e;
        } finally {
            if (connection != null) {
                connection.close()
            }
        }
    }
}

class MessageWrapper implements Serializable {
    List<Message> message;
}

class Message implements Serializable {
    String externalId;
    String systemCode;
    String messageCode;
    String expires;
    List<Attribute> attributes;
    String priority;
    String reportLevel;
    String reportContentType;
    String recipient;
    String subject;
    HtmlText htmlText;
}

class Attribute implements Serializable {
    String type;
    String value;
}

class HtmlText implements Serializable {
    String charset;
    String value;
}
