package com.sas.hccn.rtdm

import com.fasterxml.jackson.annotation.JsonFormat
import com.homecredit.sas.utils.HttpUtils
import com.homecredit.sas.utils.MappingUtils
import com.homecredit.sas.utils.PropertiesUtils
import com.homecredit.sas.utils.enumeration.RequestMethod
import com.homecredit.sas.utils.enumeration.Status
import com.homecredit.sas.utils.model.HttpCallResponse
import com.homecredit.sas.utils.model.PropertiesResponse
import com.sas.analytics.ph.common.RTDMTable
import com.sas.analytics.ph.common.RTDMTable.Row
import com.sas.analytics.ph.common.jaxb.DataTypes
import com.sas.rtdm.implementation.engine.EventInfo
import org.apache.log4j.Logger

/**
 * Client contacts
 * @version 24/03/08-003
 */
class GetDetailForClientList implements Runnable {

    // Input variables
    String cuid; // 17028123
    String phoneNumber;
    Long relatedPersonPhoneNumberId;

    // Output variables
    String status = Status.ERROR.getStatus()
    Long httpResponseCode;    // HTTP response code
    String errorMessage;
    RTDMTable ClientDetails;

    // Logger
    private static final Logger log = Logger.getLogger('groovyLog');

    // Event info
    EventInfo evtInfo;

    // Constants
    private final String resourcePrefix = "/party-web/api/pif/v1/customer";
    private final String cuidResourcePrefix = "/"
    private final String phoneResourcePrefix = "?filter=phoneNumbers.number.eq("
    private final String phoneResourceSuffix = ")"

    // Variables from properties
    private String username;
    private String password;
    private String host;

    private final String CONFIG_FILE = "/sas/groovy/Connections/pif.properties";

    @Override
    void run() {

        //start log info
        log.info("Starting process GetClientContacts...");
        log.info("GetClientContacts - cuid: $cuid");
        log.info("GetClientContacts - phoneNumber: $phoneNumber");

        PropertiesResponse propertiesResponse = PropertiesUtils.getProperties(CONFIG_FILE)
        if (propertiesResponse.getProperties() == null) {
            status = propertiesResponse.getStatus().getStatus();
            errorMessage = propertiesResponse.getErrorMessage();
            return;
        }
        Properties config = propertiesResponse.getProperties();

        username = config.getProperty("pif.username");
        password = config.getProperty("pif.password");
        host = config.getProperty("pif.host"); // = "https://pif.vn00c1.vn.infra/";

        log.info("GetClientContacts - host: $host");
        log.info("GetClientContacts - resource prefix: $resourcePrefix");

        if ((cuid == null || cuid.isEmpty()) && (phoneNumber == null || phoneNumber.isEmpty())) {
            status = "ERROR"
            errorMessage = "Exactly 1 of CUID or PHONE_NUMBER must be filled."
            return;
        }
        if ((cuid != null && !cuid.isEmpty()) && (phoneNumber != null && !phoneNumber.isEmpty())) {
            status = "ERROR"
            errorMessage = "Exactly 1 of CUID or PHONE_NUMBER must be filled."
            return;
        }
        else {

            // Create empty table
            ClientDetails = new RTDMTable();
            ClientDetails.columnAdd("externalId", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("type", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("classification", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("value", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("id", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("activeYn", DataTypes.BOOLEAN, Collections.emptyList());
            ClientDetails.columnAdd("channelType", DataTypes.STRING, Collections.emptyList());

            // Setting API variables
            String uri;
            if (cuid) {
                uri = host + resourcePrefix + cuidResourcePrefix + cuid;
            } else {
                uri = host + resourcePrefix + phoneResourcePrefix + phoneNumber + phoneResourceSuffix;
            }
            log.info("GetClientContacts - Endpoint URL: $uri");

            HttpCallResponse httpCallResponse = HttpUtils.runHttpProcess(
                    evtInfo.getEventName(),
                    evtInfo.getSimulationDate().getTime(),
                    uri,
                    HttpUtils.getBasicAuthToken(username, password),
                    [(HttpUtils.CONTENT_TYPE): "application/json"],
                    RequestMethod.GET,
                    null
            );

            httpResponseCode = httpCallResponse.getHttpResponseCode();
            status = httpCallResponse.getStatus().getStatus();
            errorMessage = httpCallResponse.getErrorMessage();
            String responseString = httpCallResponse.getResponse()
            if (responseString == null) {
                return
            }

            log.trace("GetClientContacts - httpResponseCode:" + httpResponseCode);
            log.trace("GetClientContacts - response:" + responseString);

            Response responseObject = MappingUtils.mapToObject(responseString, Response.class)
            if (responseObject == null) {
                status = Status.ERROR.getStatus();
                errorMessage = "Failed to map response. Wrong response data format."
                return
            }

            if (httpResponseCode == 200) {
                if (responseObject.resultCode == "FOUND") {
                    for (int i = 0; i < responseObject.data.size(); i++) {
                        ClientData data = responseObject.data.get(i);

                        List<Email> emailAddresses = data.emailAddresses
                        log.trace("GetClientContacts - record size (emails): " + emailAddresses.size());

                        for (int j = 0; j < emailAddresses.size(); j++) {

                            Email email = emailAddresses.get(j);
                            log.trace("GetClientContacts - email: " + email.email);

                            Row newRow = ClientDetails.rowAdd();
                            newRow.columnDataSet("externalId", data.externalId.toString());
                            newRow.columnDataSet("type", "EMAIL");
                            newRow.columnDataSet("classification", email.classification);
                            newRow.columnDataSet("value", email.email);
                        }

                        List<PhoneNumber> phoneNumbers = data.phoneNumbers
                        log.trace("GetClientContacts - record size (phoneNumbers): " + phoneNumbers.size());

                        for (int j = 0; j < phoneNumbers.size(); j++) {

                            PhoneNumber phoneNumber = phoneNumbers.get(j);
                            log.trace("GetClientContacts - phoneNumber: " + phoneNumber.number);

                            Row newRow = ClientDetails.rowAdd();
                            newRow.columnDataSet("externalId", data.externalId.toString());
                            newRow.columnDataSet("type", "PHONE");
                            newRow.columnDataSet("classification", phoneNumber.classification);
                            newRow.columnDataSet("value", phoneNumber.number);
                        }

                        PostalAddress address = data.postalAddresses.find { it.classification == "CONTACT" }
                        if (address != null && address.district != null && address.district.code != null) {

                            log.trace("GetClientContacts - postalAddress: " + address.district.code);

                            Row newRow = ClientDetails.rowAdd();
                            newRow.columnDataSet("externalId", data.externalId.toString());
                            newRow.columnDataSet("type", "ADDRESS");
                            newRow.columnDataSet("classification", address.classification);
                            newRow.columnDataSet("value", address.district.code);
                        }

                        Person person = data.person
                        log.trace("GetClientContacts - personal info: $person.firstName $person.middleName $person.lastName ($person.birthDate, ${person.gender?.code})");

                        Row firstName = ClientDetails.rowAdd();
                        firstName.columnDataSet("externalId", data.externalId.toString());
                        firstName.columnDataSet("type", "PERSONALINFO");
                        firstName.columnDataSet("classification", "FIRSTNAME");
                        firstName.columnDataSet("value", person.firstName);

                        Row middleName = ClientDetails.rowAdd();
                        middleName.columnDataSet("externalId", data.externalId.toString());
                        middleName.columnDataSet("type", "PERSONALINFO");
                        middleName.columnDataSet("classification", "MIDDLENAME");
                        middleName.columnDataSet("value", person.middleName);

                        Row lastName = ClientDetails.rowAdd();
                        lastName.columnDataSet("externalId", data.externalId.toString());
                        lastName.columnDataSet("type", "PERSONALINFO");
                        lastName.columnDataSet("classification", "LASTNAME");
                        lastName.columnDataSet("value", person.lastName);

                        Row birthDate = ClientDetails.rowAdd();
                        birthDate.columnDataSet("externalId", data.externalId.toString());
                        birthDate.columnDataSet("type", "PERSONALINFO");
                        birthDate.columnDataSet("classification", "BIRTHDATE");
                        birthDate.columnDataSet("value", person.birthDate);

                        Row gender = ClientDetails.rowAdd();
                        gender.columnDataSet("externalId", data.externalId.toString());
                        gender.columnDataSet("type", "PERSONALINFO");
                        gender.columnDataSet("classification", "GENDER");
                        gender.columnDataSet("value", person.gender?.code);

                        CustomValues customValues = data.customValues
                        String preferredLanguage
                        if (customValues == null || customValues.preferredLanguage == null) {
                            preferredLanguage = "Not set"
                        } else {
                            preferredLanguage = data.customValues.preferredLanguage
                        }
                        log.trace("GetClientContacts - custom values: preferred language = $preferredLanguage)");

                        Row language = ClientDetails.rowAdd();
                        language.columnDataSet("externalId", data.externalId.toString());
                        language.columnDataSet("type", "PERSONALINFO");
                        language.columnDataSet("classification", "PREFERREDLANGUAGE");
                        language.columnDataSet("value", preferredLanguage);

                        if (relatedPersonPhoneNumberId != null) {
                            PhoneNumber relatedPersonsPhoneNumber = data?.customerPersonRelationship?.relatedPersonInfos?.stream()
                                    ?.flatMap { it.phoneNumbers?.stream() }
                                    ?.filter { it.id == relatedPersonPhoneNumberId}
                                    ?.findFirst()
                                    ?.orElse(null)
                            if (relatedPersonsPhoneNumber != null) {
                                Row relatedPersonPhone = ClientDetails.rowAdd();
                                relatedPersonPhone.columnDataSet("externalId", data.externalId.toString());
                                relatedPersonPhone.columnDataSet("type", "RELATEDPERSONPHONE");
                                relatedPersonPhone.columnDataSet("classification", relatedPersonsPhoneNumber.classification);
                                relatedPersonPhone.columnDataSet("value", relatedPersonsPhoneNumber.number);
                            }
                        }

                        List<Channel> channels = data.communicationChannels.get("MSG_APP")
                        if (channels == null) {
                            log.trace("GetClientContacts - record size: 0");
                        } else {
                            log.trace("GetClientContacts - record size (communication channels):" + channels.size());

                            for (int j = 0; j < channels.size(); j++) {

                                Channel channel = channels.get(j);
                                log.trace("GetClientContacts - channel: " + channel.id);

                                Row newRow = ClientDetails.rowAdd();
                                newRow.columnDataSet("externalId", data.externalId.toString());
                                newRow.columnDataSet("id", channel.id.toString());
                                newRow.columnDataSet("activeYn", channel.activeYn);
                                newRow.columnDataSet("channelType", channel.channelType);
                                newRow.columnDataSet("type", "MSG_APP");
                            }
                        }

                        List<Channel> promotions = data.communicationChannels.get("PROMOTIONS")
                        if (promotions == null) {
                            log.trace("GetClientContacts - record size: 0");
                        } else {
                            log.trace("GetClientContacts - record size (promotions):" + promotions.size());

                            for (int j = 0; j < promotions.size(); j++) {

                                Channel promotion = promotions.get(j);
                                log.trace("GetClientContacts - promotion: " + promotion.id);

                                Row newRow = ClientDetails.rowAdd();
                                newRow.columnDataSet("externalId", data.externalId.toString());
                                newRow.columnDataSet("id", promotion.id.toString());
                                newRow.columnDataSet("activeYn", promotion.activeYn);
                                newRow.columnDataSet("channelType", promotion.channelType);
                                newRow.columnDataSet("type", "PROMOTION");
                            }
                        }

                        status = Status.OK.getStatus()
                    }
                } else {
                    log.error("GetClientContacts - error: " + responseObject.errorMessage);
                    status = Status.ERROR.getStatus()
                    errorMessage = responseObject.errorMessage
                }
            }
        }
    }
}

class Response implements Serializable {

    @JsonFormat(with = JsonFormat.Feature.ACCEPT_SINGLE_VALUE_AS_ARRAY)
    List<ClientData> data;
    Long externalId;
    String resultCode;
    String errorCode;
    String errorMessage;
    String requestId;
    Object validationErrors;
}

class ClientData implements Serializable {
    Long externalId;
    List<Email> emailAddresses;
    List<PhoneNumber> phoneNumbers;
    List<PostalAddress> postalAddresses;
    Person person;
    CustomValues customValues;
    Map<String, List<Channel>> communicationChannels;
    CustomerPersonRelationship customerPersonRelationship;
    // ...
}

class Email implements Serializable {
    String email;
    String classification;
    // ...
}

class PhoneNumber implements Serializable {
    Long id;
    String number;
    String classification;
    // ...
}

class PostalAddress implements Serializable {
    District district;
    String classification;
    // ...
}

class District implements Serializable {
    String code;
}

class Person implements Serializable {
    String lastName;
    String firstName;
    String middleName;
    String birthDate;
    Gender gender;
    // ...
}

class Gender implements Serializable {
    String code;
    // ...
}

class CustomValues implements Serializable {
    String preferredLanguage;
}

class Channel implements Serializable {
    Boolean activeYn;
    Long id;
    String channelType;
    // ...
}

class CustomerPersonRelationship implements Serializable {
    List<RelatedPersonInfo> relatedPersonInfos;
    // ...
}

class RelatedPersonInfo implements Serializable {
    List<PhoneNumber> phoneNumbers;
    // ...
}