package com.sas.hccn.rtdm

import com.homecredit.sas.utils.HttpUtils
import com.homecredit.sas.utils.MappingUtils
import com.homecredit.sas.utils.PropertiesUtils
import com.homecredit.sas.utils.enumeration.RequestMethod
import com.homecredit.sas.utils.enumeration.Status
import com.homecredit.sas.utils.model.HttpCallResponse
import com.homecredit.sas.utils.model.PropertiesResponse
import com.sas.analytics.ph.common.RTDMTable
import com.sas.analytics.ph.common.RTDMTable.Row
import com.sas.analytics.ph.common.jaxb.DataTypes
import com.sas.rtdm.implementation.engine.EventInfo
import org.apache.log4j.Logger

/**
 * Get Contracts v4
 * @version 09/09/23-001
 */
class SP_getContracts_v4 implements Runnable {

    // Input variables
    String cuid;

    // Output variables
    String status = Status.ERROR.getStatus()
    Long httpResponseCode;    // HTTP response code
    String errorMessage;
    RTDMTable ClientDetails;

    // Logger
    private static final Logger log = Logger.getLogger('groovyLog');

    // Event info
    EventInfo evtInfo;

    // Constants
    private final String resourcePrefix = "/bsl/openapi/v9.0/customers/";
    private final String resourceSuffix = "/contracts";

    // Variables from properties
    private String host;
    private String username;
    private String password;

    private final String CONFIG_FILE = "/sas/groovy/Connections/bsl.properties";

    @Override
    void run() {

        //start log info
        log.info("Starting process GetContracts_v4...");
        log.info("GetContracts_v4 - cuid: $cuid")

        PropertiesResponse propertiesResponse = PropertiesUtils.getProperties(CONFIG_FILE)
        if (propertiesResponse.getProperties() == null) {
            status = propertiesResponse.getStatus().getStatus();
            errorMessage = propertiesResponse.getErrorMessage();
            return;
        }
        Properties config = propertiesResponse.getProperties();

        host = config.getProperty("bsl.restHost");
        username = config.getProperty("bsl.restUsername")
        password = config.getProperty("bsl.restPassword")

        log.info("GetContracts_v4 - host: $host");
        log.info("GetContracts_v4 - resource prefix: $resourcePrefix");
        log.info("GetContracts_v4 - resource suffix: $resourceSuffix");

        if (cuid != null && !cuid.isEmpty()) {

            // Create empty table
            ClientDetails = new RTDMTable();
            ClientDetails.columnAdd("contractNumber", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("customerId", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("productCode", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("productVersion", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("productName", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("productType", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("initialTransactionType", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("contractStatus", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("contractSignatureDate", DataTypes.STRING, Collections.emptyList());

            String uri = host + resourcePrefix + cuid + resourceSuffix;
            log.info("GetContracts_v4 - Endpoint URL: $uri");

            HttpCallResponse httpCallResponse = HttpUtils.runHttpProcess(
                    evtInfo.getEventName(),
                    evtInfo.getSimulationDate().getTime(),
                    uri,
                    HttpUtils.getBasicAuthToken(username, password),
                    [(HttpUtils.CONTENT_TYPE): "application/json"],
                    RequestMethod.GET,
                    null
            );

            httpResponseCode = httpCallResponse.getHttpResponseCode();
            status = httpCallResponse.getStatus().getStatus();
            errorMessage = httpCallResponse.getErrorMessage();
            String responseString = httpCallResponse.getResponse()
            if (responseString == null) {
                return
            }

            log.trace("GetContracts_v4 - httpResponseCode:" + httpResponseCode);
            log.trace("GetContracts_v4 - response:" + responseString);

            Response responseObject = MappingUtils.mapToObject(responseString, Response.class)
            if (responseObject == null) {
                status = Status.ERROR.getStatus();
                errorMessage = "Failed to map response. Wrong response data format."
                return
            }

            if (httpResponseCode == 200) {
                log.trace("GetContracts_v4 - record size:" + responseObject.contracts.size());
                for (int j = 0; j < responseObject.contracts.size(); j++) {

                    log.trace("GetContracts_v4 - code: " + responseObject.contracts.get(j).productCode);

                    Row newRow = ClientDetails.rowAdd();
                    newRow.columnDataSet("contractNumber", responseObject.contracts.get(j).contractNumber);
                    newRow.columnDataSet("customerId", responseObject.contracts.get(j).customerId);
                    newRow.columnDataSet("productCode", responseObject.contracts.get(j).productCode);
                    newRow.columnDataSet("productVersion", responseObject.contracts.get(j).productVersion.toString());
                    newRow.columnDataSet("productName", responseObject.contracts.get(j).productName);
                    newRow.columnDataSet("productType", responseObject.contracts.get(j).productType);
                    newRow.columnDataSet("initialTransactionType", responseObject.contracts.get(j).initialTransactionType);
                    newRow.columnDataSet("contractStatus", responseObject.contracts.get(j).contractStatus);
                    newRow.columnDataSet("contractSignatureDate", responseObject.contracts.get(j).contractSignatureDate);

                }
                status = Status.OK.getStatus()
            }
        } else {
            log.trace("GetContracts_v4 - No input clients");
        }
    }
}

class Response implements Serializable {
    List<Contract> contracts;
}

class Contract implements Serializable {
    String contractNumber
    String customerId
    String productCode
    Long productVersion
    String productName
    String productType
    String initialTransactionType
    String contractStatus
    String contractSignatureDate
}
