package com.sas.hccn.rtdm

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.ObjectMapper
import com.homecredit.sas.utils.HttpUtils
import com.homecredit.sas.utils.PropertiesUtils
import com.homecredit.sas.utils.enumeration.RequestMethod
import com.homecredit.sas.utils.enumeration.Status
import com.homecredit.sas.utils.model.HttpCallResponse
import com.homecredit.sas.utils.model.PropertiesResponse
import com.sas.rtdm.implementation.engine.EventInfo
import org.apache.log4j.Logger

import java.time.format.DateTimeFormatter

/**
 * Create affinity call record via POST request
 * @version 23/08/31-003
 */
class GetDetailForClientList implements Runnable {

    // Input variables
    String contactInfo;
    Long recordType;
    Object callbackTime;
    String agentId;
    String callbackCallList;
    Long switchId;
    String clientPhoneNumber;
    String phoneNumber3;
    String phoneNumber4;
    String phoneNumber5;
    Long resptrackingId;
    String cuid;
    Long ilCommunicationId;
    String addressClient;
    String amtAnnuity;
    String amtCreditTotal;
    String callSource;
    String callType;
    Object campaignEnd;
    Object campaignStart;
    String clxAppStatus;
    String cntInstalment;
    String codeCallListType;
    String codeTimezone;
    Object communicationEnd;
    Object communicationStart;
    String contractCode;
    String customInfo1;
    String customInfo2;
    String customInfo3;
    String customInfo4;
    String customInfo5;
    String customInfo6;
    String customInfo7;
    String customInfo8;
    String customInfo9;
    String customInfo10;
    String customerNationalId;
    Long dailyFrom;
    Long dailyTill;
    Object dateAutoCancel;
    Object dateClzVisit;
    Object dateEffective;
    String dateOfBirth;
    Object datePromise;
    String fatherName;
    Object firstDueDate;
    String gender;
    Long groupId;
    String language1;
    String lastFirstName;
    Long maxCreditAmount;
    String maxEmi;
    String nameCallList;
    String nameGoodsPosl;
    String nameSalesroomClx;
    Long priority;
    String productType;
    String scriptType;
    String segmentInfo;
    String shift;
    String sortKey;
    String textLoanPurpose;
    String textNote;

    String key;

    // Output variables
    String status = Status.ERROR.getStatus()
    Long httpResponseCode;    // HTTP response code
    String errorMessage;

    // Logger
    private static final Logger log = Logger.getLogger('groovyLog');

    // Event info
    EventInfo evtInfo;

    // Constants
    private final String resourcePrefix = "/external-system/SAS_RTDM";

    // Variables from properties
    private String username;
    private String password;
    private String host;

    private final String CONFIG_FILE = "/sas/groovy/Connections/affinity.properties";
    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");


    @Override
    void run() {

        //start log info
        log.info("Starting process CreateAffinityCall...");
        log.info("CreateAffinityCall - contactInfo: $contactInfo");
        log.info("CreateAffinityCall - recordType: $recordType");
        log.info("CreateAffinityCall - callbackTime: $callbackTime");
        log.info("CreateAffinityCall - agentId: $agentId");
        log.info("CreateAffinityCall - callbackCallList: $callbackCallList");
        log.info("CreateAffinityCall - switchId: $switchId");
        log.info("CreateAffinityCall - clientPhoneNumber: $clientPhoneNumber");
        log.info("CreateAffinityCall - phoneNumber3: $phoneNumber3");
        log.info("CreateAffinityCall - phoneNumber4: $phoneNumber4");
        log.info("CreateAffinityCall - phoneNumber5: $phoneNumber5");
        log.info("CreateAffinityCall - resptrackingId: $resptrackingId");
        log.info("CreateAffinityCall - cuid: $cuid");
        log.info("CreateAffinityCall - ilCommunicationId: $ilCommunicationId");
        log.info("CreateAffinityCall - addressClient: $addressClient");
        log.info("CreateAffinityCall - amtAnnuity: $amtAnnuity");
        log.info("CreateAffinityCall - amtCreditTotal: $amtCreditTotal");
        log.info("CreateAffinityCall - callSource: $callSource");
        log.info("CreateAffinityCall - callType: $callType");
        log.info("CreateAffinityCall - campaignEnd: $campaignEnd");
        log.info("CreateAffinityCall - campaignStart: $campaignStart");
        log.info("CreateAffinityCall - clxAppStatus: $clxAppStatus");
        log.info("CreateAffinityCall - cntInstalment: $cntInstalment");
        log.info("CreateAffinityCall - codeCallListType: $codeCallListType");
        log.info("CreateAffinityCall - codeTimezone: $codeTimezone");
        log.info("CreateAffinityCall - communicationEnd: $communicationEnd");
        log.info("CreateAffinityCall - communicationStart: $communicationStart");
        log.info("CreateAffinityCall - contractCode: $contractCode");
        log.info("CreateAffinityCall - customInfo1: $customInfo1");
        log.info("CreateAffinityCall - customInfo2: $customInfo2");
        log.info("CreateAffinityCall - customInfo3: $customInfo3");
        log.info("CreateAffinityCall - customInfo4: $customInfo4");
        log.info("CreateAffinityCall - customInfo5: $customInfo5");
        log.info("CreateAffinityCall - customInfo6: $customInfo6");
        log.info("CreateAffinityCall - customInfo7: $customInfo7");
        log.info("CreateAffinityCall - customInfo8: $customInfo8");
        log.info("CreateAffinityCall - customInfo9: $customInfo9");
        log.info("CreateAffinityCall - customInfo10: $customInfo10");
        log.info("CreateAffinityCall - customerNationalId: $customerNationalId");
        log.info("CreateAffinityCall - dailyFrom: $dailyFrom");
        log.info("CreateAffinityCall - dailyTill: $dailyTill");
        log.info("CreateAffinityCall - dateAutoCancel: $dateAutoCancel");
        log.info("CreateAffinityCall - dateClzVisit: $dateClzVisit");
        log.info("CreateAffinityCall - dateEffective: $dateEffective");
        log.info("CreateAffinityCall - dateOfBirth: $dateOfBirth");
        log.info("CreateAffinityCall - datePromise: $datePromise");
        log.info("CreateAffinityCall - fatherName: $fatherName");
        log.info("CreateAffinityCall - firstDueDate: $firstDueDate");
        log.info("CreateAffinityCall - gender: $gender");
        log.info("CreateAffinityCall - groupId: $groupId");
        log.info("CreateAffinityCall - language1: $language1");
        log.info("CreateAffinityCall - lastFirstName: $lastFirstName");
        log.info("CreateAffinityCall - maxCreditAmount: $maxCreditAmount");
        log.info("CreateAffinityCall - maxEmi: $maxEmi");
        log.info("CreateAffinityCall - nameCallList: $nameCallList");
        log.info("CreateAffinityCall - nameGoodsPosl: $nameGoodsPosl");
        log.info("CreateAffinityCall - nameSalesroomClx: $nameSalesroomClx");
        log.info("CreateAffinityCall - priority: $priority");
        log.info("CreateAffinityCall - productType: $productType");
        log.info("CreateAffinityCall - scriptType: $scriptType");
        log.info("CreateAffinityCall - segmentInfo: $segmentInfo");
        log.info("CreateAffinityCall - shift: $shift");
        log.info("CreateAffinityCall - sortKey: $sortKey");
        log.info("CreateAffinityCall - textLoanPurpose: $textLoanPurpose");
        log.info("CreateAffinityCall - textNote: $textNote");

        log.info("CreateAffinityCall - key: $key");

        PropertiesResponse propertiesResponse = PropertiesUtils.getProperties(CONFIG_FILE)
        if (propertiesResponse.getProperties() == null) {
            status = propertiesResponse.getStatus().getStatus();
            errorMessage = propertiesResponse.getErrorMessage();
            return;
        }
        Properties config = propertiesResponse.getProperties();

        username = config.getProperty("affinity.username");
        password = config.getProperty("affinity.password");
        host = config.getProperty("affinity.host");

        log.info("CreateAffinityCall - host: $host");

        // Setting API variables

        String uri = host + resourcePrefix;
        log.info("CreateAffinityCall - Endpoint URL: $uri");
        Request requestObject = createRequest()
        if (requestObject == null) {
            return
        }
        List requestWrapper = [requestObject]

        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        String jsonInputString = mapper.writeValueAsString(requestWrapper)
        log.info("CreateAffinityCall - Request body: $jsonInputString");
        byte[] postData = jsonInputString.getBytes("UTF-8");

        HttpCallResponse httpCallResponse = HttpUtils.runHttpProcess(
                evtInfo.getEventName(),
                evtInfo.getSimulationDate().getTime(),
                uri,
                HttpUtils.getBasicAuthToken(username, password),
                [(HttpUtils.CONTENT_TYPE): "application/json"],
                RequestMethod.POST,
                postData
        );

        httpResponseCode = httpCallResponse.getHttpResponseCode();
        status = httpCallResponse.getStatus().getStatus();
        errorMessage = httpCallResponse.getErrorMessage();

        log.trace("CreateAffinityCall - httpResponseCode:" + httpResponseCode);

        if (httpResponseCode == 200) {
            status = "OK";
        }
    }

    Request createRequest() {
        Record record1 = new Record()

        record1.contactInfo = contactInfo;
        record1.recordType = recordType;
        if (callbackTime instanceof GregorianCalendar) {
            record1.callbackTime = (Long) (((GregorianCalendar) callbackTime).getTimeInMillis() / 1000)
        }
        record1.agentId = agentId;
        record1.callbackCallList = callbackCallList;
        record1.switchId = switchId;
        record1.resptrackingId = resptrackingId;

        Data data = new Data();
        data.records = [record1];

        if (clientPhoneNumber) {
            Record record2 = new Record()
            record2.recordType = recordType
            record2.contactInfo = clientPhoneNumber;
            data.records.add(record2)
        }

        if (phoneNumber3) {
            Record record3 = new Record()
            record3.recordType = recordType
            record3.contactInfo = phoneNumber3
            data.records.add(record3)
        }

        if (phoneNumber4) {
            Record record4 = new Record()
            record4.recordType = recordType
            record4.contactInfo = phoneNumber4
            data.records.add(record4)
        }

        if (phoneNumber5) {
            Record record5 = new Record()
            record5.recordType = recordType
            record5.contactInfo = phoneNumber5
            data.records.add(record5)
        }

        data.cuid = cuid;
        data.ilCommunicationId = ilCommunicationId;
        data.addressClient = addressClient;
        data.amtAnnuity = amtAnnuity;
        data.amtCreditTotal = amtCreditTotal;
        data.callSource = callSource;
        data.callType = callType;
        if (campaignEnd instanceof GregorianCalendar) {
            data.campaignEnd = formatter.format(((GregorianCalendar) campaignEnd).toZonedDateTime())
        }
        if (campaignStart instanceof GregorianCalendar) {
            data.campaignStart = formatter.format(((GregorianCalendar) campaignStart).toZonedDateTime())
        }
        data.clxAppStatus = clxAppStatus;
        data.cntInstalment = cntInstalment;
        data.codeCallListType = codeCallListType;
        data.codeTimezone = codeTimezone;
        if (communicationEnd instanceof GregorianCalendar) {
            data.communicationEnd = formatter.format(((GregorianCalendar) communicationEnd).toZonedDateTime())
        }
        if (communicationStart instanceof GregorianCalendar) {
            data.communicationStart = formatter.format(((GregorianCalendar) communicationStart).toZonedDateTime())
        }
        data.contractCode = contractCode;
        data.customInfo1 = customInfo1;
        data.customInfo2 = customInfo2;
        data.customInfo3 = customInfo3;
        data.customInfo4 = customInfo4;
        data.customInfo5 = customInfo5;
        data.customInfo6 = customInfo6;
        data.customInfo7 = customInfo7;
        data.customInfo8 = customInfo8;
        data.customInfo9 = customInfo9;
        data.customInfo10 = customInfo10;
        data.customerNationalId = customerNationalId;
        data.dailyFrom = dailyFrom;
        data.dailyTill = dailyTill;
        if (dateAutoCancel instanceof GregorianCalendar) {
            data.dateAutoCancel = formatter.format(((GregorianCalendar) dateAutoCancel).toZonedDateTime())
        }
        if (dateClzVisit instanceof GregorianCalendar) {
            data.dateClzVisit = formatter.format(((GregorianCalendar) dateClzVisit).toZonedDateTime())
        }
        if (dateEffective instanceof GregorianCalendar) {
            data.dateEffective = formatter.format(((GregorianCalendar) dateEffective).toZonedDateTime())
        }
        data.dateOfBirth = dateOfBirth;
        if (datePromise instanceof GregorianCalendar) {
            data.datePromise = formatter.format(((GregorianCalendar) datePromise).toZonedDateTime())
        }
        data.fatherName = fatherName;
        if (firstDueDate instanceof GregorianCalendar) {
            data.firstDueDate = formatter.format(((GregorianCalendar) firstDueDate).toZonedDateTime())
        }
        data.gender = gender;
        data.groupId = groupId;
        data.language1 = language1;
        data.lastFirstName = lastFirstName;
        data.maxCreditAmount = maxCreditAmount;
        data.maxEmi = maxEmi;
        data.nameCallList = nameCallList;
        data.nameGoodsPosl = nameGoodsPosl;
        data.nameSalesroomClx = nameSalesroomClx;
        data.priority = priority;
        data.productType = productType;
        data.scriptType = scriptType;
        data.segmentInfo = segmentInfo;
        data.shift = shift;
        data.sortKey = sortKey;
        data.textLoanPurpose = textLoanPurpose;
        data.textNote = textNote;


        Request request = new Request();
        request.key = key;
        request.data = data;

        return request;
    }
}


class Request implements Serializable {
    Data data;
    String key;
}

class Data implements Serializable {
    List<Record> records;

    @JsonProperty("cuid")
    String cuid;
    @JsonProperty("il_communication_id")
    Long ilCommunicationId;
    @JsonProperty("address_client")
    String addressClient;
    @JsonProperty("amt_annuity")
    String amtAnnuity;
    @JsonProperty("amt_credit_total")
    String amtCreditTotal;
    @JsonProperty("call_source")
    String callSource;
    @JsonProperty("call_type")
    String callType;
    @JsonProperty("campaign_end")
    Object campaignEnd;
    @JsonProperty("campaign_start")
    Object campaignStart;
    @JsonProperty("clx_app_status")
    String clxAppStatus;
    @JsonProperty("cnt_instalment")
    String cntInstalment;
    @JsonProperty("code_call_list_type")
    String codeCallListType;
    @JsonProperty("code_timezone")
    String codeTimezone;
    @JsonProperty("communication_end")
    Object communicationEnd;
    @JsonProperty("communication_start")
    Object communicationStart;
    @JsonProperty("contract_code")
    String contractCode;
    @JsonProperty("custom_info1")
    String customInfo1;
    @JsonProperty("custom_info2")
    String customInfo2;
    @JsonProperty("custom_info3")
    String customInfo3;
    @JsonProperty("custom_info4")
    String customInfo4;
    @JsonProperty("custom_info5")
    String customInfo5;
    @JsonProperty("custom_info6")
    String customInfo6;
    @JsonProperty("custom_info7")
    String customInfo7;
    @JsonProperty("custom_info8")
    String customInfo8;
    @JsonProperty("custom_info9")
    String customInfo9;
    @JsonProperty("custom_info10")
    String customInfo10;
    @JsonProperty("customer_national_id")
    String customerNationalId;
    @JsonProperty("daily_from")
    Long dailyFrom;
    @JsonProperty("daily_till")
    Long dailyTill;
    @JsonProperty("date_auto_cancel")
    Object dateAutoCancel;
    @JsonProperty("date_clz_visit")
    Object dateClzVisit;
    @JsonProperty("date_effective")
    Object dateEffective;
    @JsonProperty("date_of_birth")
    String dateOfBirth;
    @JsonProperty("date_promise")
    Object datePromise;
    @JsonProperty("father_name")
    String fatherName;
    @JsonProperty("first_due_date")
    Object firstDueDate;
    @JsonProperty("gender")
    String gender;
    @JsonProperty("group_id")
    Long groupId;
    @JsonProperty("language1")
    String language1;
    @JsonProperty("last_first_name")
    String lastFirstName;
    @JsonProperty("max_credit_amount")
    Long maxCreditAmount;
    @JsonProperty("max_emi")
    String maxEmi;
    @JsonProperty("name_call_list")
    String nameCallList;
    @JsonProperty("name_goods_posl")
    String nameGoodsPosl;
    @JsonProperty("name_salesroom_clx")
    String nameSalesroomClx;
    @JsonProperty("priority")
    Long priority;
    @JsonProperty("product_type")
    String productType;
    @JsonProperty("script_type")
    String scriptType;
    @JsonProperty("segment_info")
    String segmentInfo;
    @JsonProperty("shift")
    String shift;
    @JsonProperty("sort_key")
    String sortKey;
    @JsonProperty("text_loan_purpose")
    String textLoanPurpose;
    @JsonProperty("text_note")
    String textNote;
}

class Record implements Serializable {
    @JsonProperty("contact_info")
    String contactInfo;
    @JsonProperty("record_type")
    Long recordType;
    @JsonProperty("callback_time")
    Object callbackTime;
    @JsonProperty("agent_id")
    String agentId;
    @JsonProperty("callback_call_list")
    String callbackCallList;
    @JsonProperty("switch_id")
    Long switchId;
    @JsonProperty("resptracking_id")
    Long resptrackingId;
}