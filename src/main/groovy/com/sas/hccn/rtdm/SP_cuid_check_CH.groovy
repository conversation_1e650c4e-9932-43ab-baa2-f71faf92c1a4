package com.sas.hccn.rtdm

import com.homecredit.sas.utils.enumeration.Status
import org.apache.log4j.Logger;
import com.sas.rtdm.implementation.engine.EventInfo;

import javax.sql.DataSource
import java.sql.*
import java.util.stream.Collectors

class SP_cuid_check_CH implements Runnable {

    Map<String, DataSource> mapJDBC;

    //input
    List<Long> cuidListIn;

    //output
    List<Long> cuidListFound;
    List<Long> cuidListMissing;
    String status = Status.ERROR.getStatus()
    String errorMessage;

    // Event info
    EventInfo evtInfo;
    // Logger
    private static final Logger log = Logger.getLogger('groovyLog');

    void run() {
        if (cuidListIn == null || cuidListIn.isEmpty()) {
            log.trace("SP_cuid_check_CH: cuidListIn is null, returning empty list of cuidListOut");
            status = Status.OK.getStatus()
            return
        }
        Connection conn = mapJDBC.get('MA_TEMP_JDBC').getConnection()
        PreparedStatement st = null
        Statement stmt = conn.createStatement();
        try {
            if (conn != null) {
                log.trace("SP_cuid_check_CH: Obtaining channel codes from DB");

                String query = String.format("SELECT ID_CUID FROM CDM.CI_CONTACT_HISTORY WHERE ID_CUID IN (%s)",
                        cuidListIn.stream()
                                .map({it -> "?"})
                                .collect(Collectors.joining(", ")));
                st = conn.prepareStatement(query);

                int index = 1;
                for (Long cuid : cuidListIn) {
                    log.trace("SP_cuid_check_CH: SQL parameter cuid: $index = " + cuid);
                    st.setLong(index, cuid);
                    index++;
                }

                log.trace("SP_cuid_check_CH: SQL query: " +  query);
                ResultSet rs = st.executeQuery();

                Set<Long> presentCuids = new HashSet<>();
                while (rs.next()) {
                    presentCuids.add(rs.getLong(1))
                }
                log.trace("SP_cuid_check_CH: found cuids: " + presentCuids.toListString())
                cuidListFound = presentCuids.toList();
                cuidListMissing = (cuidListIn - presentCuids)
                status = Status.OK.getStatus()
            }
        } catch (SQLException e) {
            errorMessage = e.getLocalizedMessage();
            log.error("SP_cuid_check_CH: oracle error:" + e)
        } finally {
            if (st != null) {
                st.close();
            }
            if (stmt != null) {
                stmt.close();
            }
            if (conn != null) {
                conn.close();
            }
        }
        log.trace("SP_cuid_check_CH - End");
    }
}