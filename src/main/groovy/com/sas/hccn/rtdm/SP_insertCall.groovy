package com.sas.hccn.rtdm

import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.ObjectMapper
import org.apache.kafka.clients.producer.KafkaProducer
import org.apache.kafka.clients.producer.Producer
import org.apache.kafka.clients.producer.ProducerConfig
import org.apache.kafka.clients.producer.ProducerRecord
import org.apache.log4j.Logger

import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

class GetDetailForClientList implements Runnable {

    // Input variables
    String id_cuid;
    String il_communication_id;
    String address_client;
    String amt_annuity;
    String amt_credit_total;
    String call_source;
    String call_type;
    String campaign_end;
    String campaign_start;
    String clx_app_status;
    String cnt_instalment;
    String code_call_list_type;
    String code_timezone;
    String communication_end;
    String communication_start;
    String contract_code;
    String custom_info1;
    String custom_info2;
    String custom_info3;
    String custom_info4;
    String custom_info5;
    String custom_info6;
    String custom_info7;
    String custom_info8;
    String custom_info9;
    String custom_info10;
    String customer_national_id;
    String daily_from;
    String daily_till;
    String date_auto_cancel;
    String date_clz_visit;
    String date_effective;
    String date_of_birth;
    String date_promise;
    String father_name;
    String first_due_date;
    String gender;
    String group_id;
    String language1;
    String last_first_name;
    String max_credit_amount;
    String max_emi;
    String name_call_list;
    String name_goods_posl;
    String name_salesroom_clx;
    String priority;
    String product_type;
    String script_type;
    String segment_info;
    String shift;
    String sort_key;
    String text_loan_purpose;
    String text_note;

    String contact_info;
    String record_type;
    String callback_time;
    String agent_id;
    String callback_call_list;
    String switch_id;
    String client_phone_number;
    String phone_number3;
    String phone_number4;
    String phone_number5;

    // Output variables
    String status = "ERROR";            // either "OK" or "ERROR"
    String errorMessage;

    // Internal variables
    private static final Logger log = Logger.getLogger('GetDetailForClientList'); //groovyLog
    private static final Logger requestReplyLog = Logger.getLogger('groovyRequestReply')


    private final String CONFIG_FILE = "/sas/groovy/Connections/kafka.properties";
    private Properties config = new Properties();
    private DateTimeFormatter formatter = DateTimeFormatter.ISO_INSTANT

    @Override
    void run() {

        //start log info
        log.info("Starting process InsertCall to Kafka...");
        log.info("InsertCall - id_cuid: $id_cuid");
        log.info("InsertCall - il_communication_id: $il_communication_id");
        log.info("InsertCall - address_client: $address_client");
        log.info("InsertCall - amt_annuity: $amt_annuity");
        log.info("InsertCall - amt_credit_total: $amt_credit_total");
        log.info("InsertCall - call_source: $call_source");
        log.info("InsertCall - call_type: $call_type");
        log.info("InsertCall - campaign_end: $campaign_end");
        log.info("InsertCall - campaign_start: $campaign_start");
        log.info("InsertCall - clx_app_status: $clx_app_status");
        log.info("InsertCall - cnt_instalment: $cnt_instalment");
        log.info("InsertCall - code_call_list_type: $code_call_list_type");
        log.info("InsertCall - code_timezone: $code_timezone");
        log.info("InsertCall - communication_end: $communication_end");
        log.info("InsertCall - communication_start: $communication_start");
        log.info("InsertCall - contract_code: $contract_code");
        log.info("InsertCall - custom_info1: $custom_info1");
        log.info("InsertCall - custom_info2: $custom_info2");
        log.info("InsertCall - custom_info3: $custom_info3");
        log.info("InsertCall - custom_info4: $custom_info4");
        log.info("InsertCall - custom_info5: $custom_info5");
        log.info("InsertCall - custom_info6: $custom_info6");
        log.info("InsertCall - custom_info7: $custom_info7");
        log.info("InsertCall - custom_info8: $custom_info8");
        log.info("InsertCall - custom_info9: $custom_info9");
        log.info("InsertCall - custom_info10: $custom_info10");
        log.info("InsertCall - customer_national_id: $customer_national_id");
        log.info("InsertCall - daily_from: $daily_from");
        log.info("InsertCall - daily_till: $daily_till");
        log.info("InsertCall - date_auto_cancel: $date_auto_cancel");
        log.info("InsertCall - date_clz_visit: $date_clz_visit");
        log.info("InsertCall - date_effective: $date_effective");
        log.info("InsertCall - date_of_birth: $date_of_birth");
        log.info("InsertCall - date_promise: $date_promise");
        log.info("InsertCall - father_name: $father_name");
        log.info("InsertCall - first_due_date: $first_due_date");
        log.info("InsertCall - gender: $gender");
        log.info("InsertCall - group_id: $group_id");
        log.info("InsertCall - language1: $language1");
        log.info("InsertCall - last_first_name: $last_first_name");
        log.info("InsertCall - max_credit_amount: $max_credit_amount");
        log.info("InsertCall - max_emi: $max_emi");
        log.info("InsertCall - name_call_list: $name_call_list");
        log.info("InsertCall - name_goods_posl: $name_goods_posl");
        log.info("InsertCall - name_salesroom_clx: $name_salesroom_clx");
        log.info("InsertCall - priority: $priority");
        log.info("InsertCall - product_type: $product_type");
        log.info("InsertCall - script_type: $script_type");
        log.info("InsertCall - segment_info: $segment_info");
        log.info("InsertCall - shift: $shift");
        log.info("InsertCall - sort_key: $sort_key");
        log.info("InsertCall - text_loan_purpose: $text_loan_purpose");
        log.info("InsertCall - text_note: $text_note");
        log.info("InsertCall - contact_info: $contact_info");
        log.info("InsertCall - record_type: $record_type");
        log.info("InsertCall - callback_time: $callback_time");
        log.info("InsertCall - agent_id: $agent_id");
        log.info("InsertCall - callback_call_list: $callback_call_list");
        log.info("InsertCall - switch_id: $switch_id");
        log.info("InsertCall - client_phone_number: $client_phone_number");
        log.info("InsertCall - phone_number3: $phone_number3");
        log.info("InsertCall - phone_number4: $phone_number4");
        log.info("InsertCall - phone_number5: $phone_number5");

        try {
            log.info("InsertCall - Loading configuration from path '$CONFIG_FILE'");
            this.config.load(new FileInputStream(CONFIG_FILE));
        } catch (Exception e) {
            log.error("InsertCall - Failed to load configuration: " + e.getMessage())
            status = "ERROR";
            throw e;
        }

        String servers = config.getProperty("kafka.servers"); // "kafka01-vn00c1.vn.infra:9092,kafka02-vn00c1.vn.infra:9092,kafka03-vn00c1.vn.infra:9092";
        String topic = config.getProperty("kafka.topic"); // "affinity.rtdm_data";

        log.info("InsertCall - Kafka configuration: servers = $servers, topic = $topic")

//        if (record_type == "Personal callback" || record_type == "5") {
//            if (!callback_time || !agent_id || !callback_call_list || !switch_id) {
//                log.error("InsertCall - Required attributes null or empty. For this record type following attributes must be set: callback_time, agent_id, callback_call_list, switch_id")
//                status = "ERROR";
//                errorMessage = "Required attributes null or empty. For this record type following attributes must be set: callback_time, agent_id, callback_call_list, switch_id"
//            }
//        }
//        if (record_type == "Campaign callback" || record_type == "6") {
//            if (!callback_time || !callback_call_list) {
//                log.error("InsertCall - Required attributes null or empty. For this record type following attributes must be set: callback_time, agent_id, callback_call_list, switch_id")
//                status = "ERROR";
//                errorMessage = "Required attributes null or empty. For this record type following attributes must be set: callback_time, callback_call_list"
//            }
//        }
        Message message = new Message()
        Record messageRecord = new Record()
        messageRecord.setContact_info(contact_info)
        messageRecord.setRecord_type(record_type)
        if (callback_time) messageRecord.setCallback_time(LocalDateTime.parse(callback_time, formatter))
        messageRecord.setAgent_id(agent_id)
        messageRecord.setCallback_call_list(callback_call_list)
        messageRecord.setSwitch_id(switch_id)
        messageRecord.setClient_phone_number(client_phone_number)
        messageRecord.setPhone_number3(phone_number3)
        messageRecord.setPhone_number4(phone_number4)
        messageRecord.setPhone_number5(phone_number5)

        message.setRecords(Arrays.asList(messageRecord))
        message.setToken(id_cuid)
        message.setIl_communication_id(il_communication_id)
        message.setAddress_client(address_client)
        message.setAmt_annuity(amt_annuity)
        message.setAmt_credit_total(amt_credit_total)
        message.setCall_source(call_source)
        message.setCall_type(call_type)
        if (campaign_end) message.setCampaign_end(LocalDateTime.parse(campaign_end, formatter))
        if (campaign_start) message.setCampaign_start(LocalDateTime.parse(campaign_start, formatter))
        message.setClx_app_status(clx_app_status)
        message.setCnt_instalment(cnt_instalment)
        message.setCode_call_list_type(code_call_list_type)
        message.setCode_timezone(code_timezone)
        if (communication_end) message.setCommunication_end(LocalDateTime.parse(communication_end, formatter))
        if (communication_start) message.setCommunication_start(LocalDateTime.parse(communication_start, formatter))
        message.setContract_code(contract_code)
        message.setCustom_info1(custom_info1)
        message.setCustom_info2(custom_info2)
        message.setCustom_info3(custom_info3)
        message.setCustom_info4(custom_info4)
        message.setCustom_info5(custom_info5)
        message.setCustom_info6(custom_info6)
        message.setCustom_info7(custom_info7)
        message.setCustom_info8(custom_info8)
        message.setCustom_info9(custom_info9)
        message.setCustom_info10(custom_info10)
        message.setCustomer_national_id(customer_national_id)
        message.setDaily_from(daily_from)
        message.setDaily_till(daily_till)
        if (date_auto_cancel) message.setDate_auto_cancel(LocalDateTime.parse(date_auto_cancel, formatter))
        if (date_clz_visit) message.setDate_clz_visit(LocalDateTime.parse(date_clz_visit, formatter))
        if (date_effective) message.setDate_effective(LocalDateTime.parse(date_effective, formatter))
        message.setDate_of_birth(date_of_birth)
        if (date_promise) message.setDate_promise(LocalDateTime.parse(date_promise, formatter))
        message.setFather_name(father_name)
        if (first_due_date) message.setFirst_due_date(LocalDateTime.parse(first_due_date, formatter))
        message.setGender(gender)
        message.setGroup_id(group_id)
        message.setLanguage1(language1)
        message.setLast_first_name(last_first_name)
        message.setMax_credit_amount(max_credit_amount)
        message.setMax_emi(max_emi)
        message.setName_call_list(name_call_list)
        message.setName_goods_posl(name_goods_posl)
        message.setName_salesroom_clx(name_salesroom_clx)
        message.setPriority(priority)
        message.setProduct_type(product_type)
        message.setScript_type(script_type)
        message.setSegment_info(segment_info)
        message.setShift(shift)
        message.setSort_key(sort_key)
        message.setText_loan_purpose(text_loan_purpose)
        message.setText_note(text_note)

        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        String jsonMessage = mapper.writeValueAsString(message)
        log.info("InsertCall - Serialized message:" + jsonMessage)

        Properties props = new Properties();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, servers);
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");

        Producer<String, String> producer = null;
        try {
            producer = new KafkaProducer<>(props);
            producer.send(new ProducerRecord<>(topic, il_communication_id, jsonMessage));
            log.info("InsertCall - Message sent successfully");
            requestReplyLog.info("InsertCall - Sent message: $jsonMessage to Kafka (servers = $servers, topic = $topic)")
        } catch (Exception e) {
            log.error("InsertCall - Failed to send message: " + e.getMessage())
            throw e;
        } finally {
            if (producer != null) {
                producer.close();
            }
        }
    }
}

class Message implements Serializable {

    List<Record> records;
    String cuid;
    String il_communication_id;
    String address_client;
    String amt_annuity;
    String amt_credit_total;
    String call_source;
    String call_type;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy hh:mm:ss", timezone = "UTC")
    LocalDateTime campaign_end;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy hh:mm:ss", timezone = "UTC")
    LocalDateTime campaign_start;
    String clx_app_status;
    String cnt_instalment;
    String code_call_list_type;
    String code_timezone;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy hh:mm:ss", timezone = "UTC")
    LocalDateTime communication_end;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy hh:mm:ss", timezone = "UTC")
    LocalDateTime communication_start;
    String contract_code;
    String custom_info1;
    String custom_info2;
    String custom_info3;
    String custom_info4;
    String custom_info5;
    String custom_info6;
    String custom_info7;
    String custom_info8;
    String custom_info9;
    String custom_info10;
    String customer_national_id;
    String daily_from;
    String daily_till;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy hh:mm:ss", timezone = "UTC")
    LocalDateTime date_auto_cancel;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy hh:mm:ss", timezone = "UTC")
    LocalDateTime date_clz_visit;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy hh:mm:ss", timezone = "UTC")
    LocalDateTime date_effective;
    String date_of_birth;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy hh:mm:ss", timezone = "UTC")
    LocalDateTime date_promise;
    String father_name;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy hh:mm:ss", timezone = "UTC")
    LocalDateTime first_due_date;
    String gender;
    String group_id;
    String language1;
    String last_first_name;
    String max_credit_amount;
    String max_emi;
    String name_call_list;
    String name_goods_posl;
    String name_salesroom_clx;
    String priority;
    String product_type;
    String script_type;
    String segment_info;
    String shift;
    String sort_key;
    String text_loan_purpose;
    String text_note;
}

class Record implements Serializable {

    String contact_info;
    String record_type;

    @JsonFormat(shape = JsonFormat.Shape.NUMBER, timezone = "UTC")
    LocalDateTime callback_time;
    String agent_id;
    String callback_call_list;
    String switch_id;
    String client_phone_number;
    String phone_number3;
    String phone_number4;
    String phone_number5;
}
