package com.sas.hccn.rtdm

import com.homecredit.sas.utils.HttpUtils
import com.homecredit.sas.utils.PropertiesUtils
import com.homecredit.sas.utils.enumeration.Status
import com.homecredit.sas.utils.model.PropertiesResponse
import org.apache.log4j.Logger
import org.w3c.dom.NodeList

import javax.xml.soap.*

/**
 * Get Reject reason
 * @version 22/11/22-001
 */
class GetDetailForClientList implements Runnable {

    // Input variables
    String applicationCode;

    // Output variables
    String status = Status.ERROR.getStatus()
    String errorMessage;
    String rejectReason;
    
    // Logger
    private static final Logger log = Logger.getLogger('groovyLog');

    // Constants
    private final String resourcePrefix = "/ApplicationManagementWSv21.GetApplicationData"

    // Variables from properties
    private String username;
    private String password;
    private String host;

    private final String CONFIG_FILE = "/sas/groovy/Connections/bsl.properties";

    @Override
    void run() {

        //start log info
        log.info("Starting process GetRejectReason...");
        log.info("GetRejectReason - applicationCode: $applicationCode");

        PropertiesResponse bslPropertiesResponse = PropertiesUtils.getProperties(CONFIG_FILE)
        if (bslPropertiesResponse.getProperties() == null) {
            status = bslPropertiesResponse.getStatus().getStatus();
            errorMessage = bslPropertiesResponse.getErrorMessage();
            return;
        }
        Properties bslConfig = bslPropertiesResponse.getProperties();

        username = bslConfig.getProperty("bsl.username");
        password = bslConfig.getProperty("bsl.password");
        host = bslConfig.getProperty("bsl.host");

        log.info("GetRejectReason - host: $host");
        log.info("GetRejectReason - resource prefix: $resourcePrefix");

        if (applicationCode != null && !applicationCode.isEmpty()) {
            // Setting API variables
            rejectReason = callBslGetApplicationData()
            if (rejectReason == null) {
                status = Status.ERROR.getStatus()
                errorMessage = "Failed to map BSL GetApplicationData reject reason"
            } else {
                status = Status.OK.getStatus()
            }
        } else {
            log.trace("GetRejectReason - No input clients")
        }
    }

    private String callBslGetApplicationData() {
        log.info("GetRejectReason - call BSL GetApplicationData: Endpoint URL: " + host + resourcePrefix);

        SOAPConnectionFactory soapConnectionFactory = SOAPConnectionFactory.newInstance();
        SOAPConnection soapConnection = soapConnectionFactory.createConnection();
        MessageFactory messageFactory = MessageFactory.newInstance();
        SOAPMessage soapMessage = messageFactory.createMessage();
        SOAPPart soapPart = soapMessage.getSOAPPart();
        SOAPEnvelope envelope = soapPart.getEnvelope();
        envelope.addNamespaceDeclaration("v21", "http://homecredit.net/hss/application-management/v21");
        SOAPBody soapBody = envelope.getBody();
        SOAPElement GetApplicationDataRequestElem = soapBody.addChildElement("GetApplicationDataRequest", "v21");
        SOAPElement applicationCodeElem = GetApplicationDataRequestElem.addChildElement("applicationCode", "v21");
        applicationCodeElem.addTextNode(applicationCode);
        MimeHeaders headers = soapMessage.getMimeHeaders();
        String authorization = HttpUtils.getBasicAuthToken(username, password)
        headers.addHeader("Authorization", authorization);
        headers.addHeader("SOAPAction", host + resourcePrefix);
        soapMessage.saveChanges();
        //log message
        ByteArrayOutputStream requestStream = new ByteArrayOutputStream();
        soapMessage.writeTo(requestStream);
        String requestMsg = requestStream.toString("UTF-8")
        log.info("GetRejectReason - call BSL GetApplicationData: Message request: " + requestMsg)

        SOAPMessage soapResponse = soapConnection.call(soapMessage, host);
        ByteArrayOutputStream responseStream = new ByteArrayOutputStream();
        soapResponse.writeTo(responseStream);
        String responseMsg = responseStream.toString("UTF-8");
        log.info("GetRejectReason - call BSL GetApplicationData: Message response: " + responseMsg)

        SOAPBody body = soapResponse.getSOAPBody()
        try {
            NodeList bodyNodes = body.getChildNodes()
            NodeList getApplicationDataResponse = bodyNodes.item(0).getChildNodes()
            NodeList application = getApplicationDataResponse.item(0).getChildNodes()
            for (j in 0..application.length) {
                if (application.item(j) != null) {
                    log.info(application.item(j).getLocalName())
                    if (application.item(j).getLocalName().equalsIgnoreCase("statusHistory")) {
                        NodeList statusHistory = application.item(j).getChildNodes()
                        for (l in 0..statusHistory.length) {
                            if (statusHistory.item(l) != null) {
                                log.info(statusHistory.item(l).getLocalName())
                                if (statusHistory.item(l).getLocalName().equalsIgnoreCase("reason")) {
                                    return statusHistory.item(l).getTextContent()
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("GetRejectReason - Failed to map BSL GetApplicationData reject reason: " + e.getLocalizedMessage())
        }
        return null
    }
}