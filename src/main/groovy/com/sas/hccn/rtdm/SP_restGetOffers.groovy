package com.sas.hccn.rtdm

import com.fasterxml.jackson.databind.ObjectMapper
import com.homecredit.sas.utils.HttpUtils
import com.homecredit.sas.utils.MappingUtils
import com.homecredit.sas.utils.PropertiesUtils
import com.homecredit.sas.utils.enumeration.RequestMethod
import com.homecredit.sas.utils.enumeration.Status
import com.homecredit.sas.utils.model.HttpCallResponse
import com.homecredit.sas.utils.model.PropertiesResponse
import com.sas.analytics.ph.common.RTDMTable
import com.sas.analytics.ph.common.RTDMTable.Row
import com.sas.analytics.ph.common.jaxb.DataTypes
import com.sas.rtdm.implementation.engine.EventInfo
import org.apache.log4j.Logger

class GetDetailForClientList implements Runnable {

    // Input variables
    String id_cuid;

    // Output variables
    String status = Status.ERROR.getStatus()
    Long httpResponseCode;    // HTTP response code
    String errorMessage;
    RTDMTable ClientDetails;

    // Logger
    private static final Logger log = Logger.getLogger('groovyLog');

    // Event info
    EventInfo evtInfo;

    // Constants
    private final String resourcePrefix = "/ofs/offers/partyId/"

    // Variables from properties
    private String host;

    private final String CONFIG_FILE = "/sas/groovy/Connections/ofs.properties";

    @Override
    void run() {

        //start log info
        log.info("Starting process GetOffers...");
        log.info("GetOffers - cuid: $id_cuid");

        PropertiesResponse propertiesResponse = PropertiesUtils.getProperties(CONFIG_FILE)
        if (propertiesResponse.getProperties() == null) {
            status = propertiesResponse.getStatus().getStatus();
            errorMessage = propertiesResponse.getErrorMessage();
            return;
        }
        Properties config = propertiesResponse.getProperties();

        host = config.getProperty("ofs.host");

        log.info("GetOffers - host: $host");
        log.info("GetOffers - resource prefix: $resourcePrefix");

        if (id_cuid != null && !id_cuid.isEmpty()) {

            // Create empty table
            ClientDetails = new RTDMTable();
            ClientDetails.columnAdd("offerId", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("limitId", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("offerTypeCode", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("offerTypeName", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("offerCategory", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("partyId", DataTypes.INT, Collections.emptyList());
            ClientDetails.columnAdd("maxCreditAmount", DataTypes.INT, Collections.emptyList());
            ClientDetails.columnAdd("maxMonthlyInst", DataTypes.INT, Collections.emptyList());
            ClientDetails.columnAdd("minCashPayment", DataTypes.INT, Collections.emptyList());
            ClientDetails.columnAdd("relativeMinCashPayment", DataTypes.FLOAT, Collections.emptyList());
            ClientDetails.columnAdd("offerValidTo", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("pricingCategory", DataTypes.INT, Collections.emptyList());
            ClientDetails.columnAdd("crmPilotCode", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("jointLendingPartnerCode", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("offerIdSas", DataTypes.INT, Collections.emptyList());
            ClientDetails.columnAdd("sourceProcessId", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("creationTimestamp", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("modificationTimestamp", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("initTransactionType", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("priority", DataTypes.INT, Collections.emptyList());
            ClientDetails.columnAdd("entryPoint", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("productType", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("lastResponse", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("possibleResponses", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("acqChannelCodes", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("productCodes", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("productSetCode", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("relationId", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("relationCode", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("pricingStrategy", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("minMonthlyInst", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("accountNumber", DataTypes.STRING, Collections.emptyList());


            // Setting API variables
            String uri = host + resourcePrefix + id_cuid;
            log.info("GetOffers - Endpoint URL: $uri");

            HttpCallResponse httpCallResponse = HttpUtils.runHttpProcess(
                    evtInfo.getEventName(),
                    evtInfo.getSimulationDate().getTime(),
                    uri,
                    null,
                    [(HttpUtils.CONTENT_TYPE): "application/json"],
                    RequestMethod.GET,
                    null
            );

            httpResponseCode = httpCallResponse.getHttpResponseCode();
            status = httpCallResponse.getStatus().getStatus();
            errorMessage = httpCallResponse.getErrorMessage();
            String responseString = httpCallResponse.getResponse()
            if (responseString == null) {
                return
            }

            log.trace("GetOffers - httpResponseCode:" + httpResponseCode);
            log.trace("GetOffers - response:" + responseString);

            MyResponse responseObject = MappingUtils.mapToObject(responseString, MyResponse.class)
            if (responseObject == null) {
                status = Status.ERROR.getStatus();
                errorMessage = "Failed to map response. Wrong response data format."
                return
            }

            if (httpResponseCode == 200) {
                log.trace("GetOffers - record size:" + responseObject.records.size());

                for (int j = 0; j < responseObject.records.size(); j++) {

                    String recordId = responseObject.records.get(j).offerId;
                    log.trace("GetOffers - offerId: " + recordId);

                    Row newRow = ClientDetails.rowAdd();
                    newRow.columnDataSet("offerId", responseObject.records.get(j).offerId);
                    newRow.columnDataSet("limitId", responseObject.records.get(j).limitId);
                    newRow.columnDataSet("offerTypeCode", responseObject.records.get(j).offerTypeCode);
                    newRow.columnDataSet("offerTypeName", responseObject.records.get(j).offerTypeName);
                    newRow.columnDataSet("offerCategory", responseObject.records.get(j).offerCategory);
                    newRow.columnDataSet("partyId", Long.parseLong(responseObject.records.get(j).partyId));
                    newRow.columnDataSet("maxCreditAmount", responseObject.records.get(j).maxCreditAmount);
                    newRow.columnDataSet("maxMonthlyInst", responseObject.records.get(j).maxMonthlyInst);
                    newRow.columnDataSet("minCashPayment", responseObject.records.get(j).minCashPayment);
                    newRow.columnDataSet("relativeMinCashPayment", responseObject.records.get(j).relativeMinCashPayment);
                    newRow.columnDataSet("offerValidTo", responseObject.records.get(j).offerValidTo);
                    newRow.columnDataSet("pricingCategory", responseObject.records.get(j).pricingCategory);
                    newRow.columnDataSet("crmPilotCode", responseObject.records.get(j).crmPilotCode);
                    newRow.columnDataSet("jointLendingPartnerCode", responseObject.records.get(j).jointLendingPartnerCode);
                    newRow.columnDataSet("offerIdSas", responseObject.records.get(j).offerIdSas);
                    newRow.columnDataSet("sourceProcessId", responseObject.records.get(j).sourceProcessId);
                    newRow.columnDataSet("creationTimestamp", responseObject.records.get(j).creationTimestamp);
                    newRow.columnDataSet("modificationTimestamp", responseObject.records.get(j).modificationTimestamp);
                    newRow.columnDataSet("initTransactionType", responseObject.records.get(j).initTransactionType);
                    newRow.columnDataSet("priority", responseObject.records.get(j).priority);
                    newRow.columnDataSet("entryPoint", responseObject.records.get(j).entryPoint);
                    newRow.columnDataSet("productType", responseObject.records.get(j).productType);
                    newRow.columnDataSet("lastResponse", responseObject.records.get(j).getLastResponse());
                    newRow.columnDataSet("possibleResponses", responseObject.records.get(j).getPossibleResponses());
                    newRow.columnDataSet("acqChannelCodes", responseObject.records.get(j).getAcqChannelCodes());
                    newRow.columnDataSet("productCodes", responseObject.records.get(j).getProductCodes());
                    newRow.columnDataSet("productSetCode", responseObject.records.get(j).productSetCode);
                    newRow.columnDataSet("relationId", responseObject.records.get(j).relationId);
                    newRow.columnDataSet("relationCode", responseObject.records.get(j).relationCode);
                    newRow.columnDataSet("pricingStrategy", responseObject.records.get(j).pricingStrategy);
                    newRow.columnDataSet("minMonthlyInst", responseObject.records.get(j).minMonthlyInst);
                    newRow.columnDataSet("accountNumber", responseObject.records.get(j).accountNumber);
                }
            }
        } else {
            log.trace("GetOffers - No input clients");
        }
    }
}

class MyResponse implements Serializable {
    List<Records> records;
    Long page;
    Long pages;
    Long totalRecordNumber;
}

class Records implements Serializable {
    String offerId;
    String limitId;
    String offerTypeCode;
    String offerTypeName;
    String offerCategory;
    String partyId;
    String offerStatus;
    Long maxCreditAmount;
    Long maxMonthlyInst;
    Long minCashPayment;
    Double relativeMinCashPayment;
    String offerValidTo;
    Long pricingCategory;
    String crmPilotCode;
    String jointLendingPartnerCode;
    Long offerIdSas;
    String sourceProcessId;
    String creationTimestamp;
    String modificationTimestamp;
    String initTransactionType;
    Long priority;
    String entryPoint;
    String productType;
    LastResponse lastResponse;
    List<PossibleResponse> possibleResponses;
    List<String> acqChannelCodes;
    List<String> productCodes;
    String productSetCode;
    String relationId;
    String relationCode;
    String pricingStrategy;
    String minMonthlyInst;
    String accountNumber;

    String getLastResponse() {
        if (lastResponse == null)
            return null;
        return new ObjectMapper().writeValueAsString(lastResponse)
    }

    String getPossibleResponses() {
        if (possibleResponses == null)
            return null;
        return new ObjectMapper().writeValueAsString(possibleResponses)
    }

    String getAcqChannelCodes() {
        if (acqChannelCodes == null)
            return null;
        return acqChannelCodes.join(",");
    }

    String getProductCodes() {
        if (productCodes == null)
            return null;
        return productCodes.join(",");
    }

}

class PossibleResponse implements Serializable {
    String responseId;
    String acqChannelCode;
    String responseText;
    String creationTimestamp;
    String modificationTimestamp;
}

class LastResponse implements Serializable {
    String id;
    String acqChannelCode;
    String responseText;
    String responseComment;
    String modificationTimestamp;
}
