package com.sas.hccn.rtdm


import java.nio.charset.StandardCharsets;
import com.homecredit.sas.utils.HttpUtils
import com.homecredit.sas.utils.MappingUtils
import com.homecredit.sas.utils.PropertiesUtils
import com.homecredit.sas.utils.enumeration.RequestMethod
import com.homecredit.sas.utils.enumeration.Status
import com.homecredit.sas.utils.model.HttpCallResponse
import com.homecredit.sas.utils.model.PropertiesResponse
import com.sas.rtdm.implementation.engine.EventInfo
import org.apache.log4j.Logger

/**
 * Get limit
 * @version 13/03/24-006
 */
class GetDetailForClientList implements Runnable {

    // Input variables
    String partyId;
    String limitType;
    String limitSubType;
    String limit_id;

    // Output variables
    String status = Status.ERROR.getStatus()
    Long httpResponseCode;    // HTTP response code
    String errorMessage;

    String partyIdOut;
    String limitBulkId;
    String limitId;
    String limitScoringProcessSource;
    String calculationSource;
    String limitValidFrom;
    String limitValidTo;
    String limitStatus;
    String limitScore;
    Long amtCreditMax;
    Long amtInstalmentMax;
    Long amtDownPaymentMin;
    Double amtDownPaymentMinRel;
    String codeRiskGrade;
    Long limitAccuracy;
    String limitPilotCode;
    String limitTypeCode;
    String limitSubTypeCode;
    String acqChannelCodes;
    String relationId;
    String relationCode;
    String accountNumber;
    Long maxTenor;
    Long amtCreditMin;
    Double amtDownPaymentMaxRel;
    Long minTenor;
    String limitGuaranteedFlag;
    String commodityCategoryGroup;

    // Logger
    private static final Logger log = Logger.getLogger('groovyLog');

    // Event info
    EventInfo evtInfo;

    // Constants
    private final String resourcePrefix = "/ofs/limits?"
    private final String partyIdPart = "partyId="
    private final String limitTypePart = "&limitTypeCode="
    private final String limitSubtypePart = "&limitSubTypeCode="
    private final String limitIdPart = "&limitId="

    // Variables from properties
    private String host;
    private String username;
    private String password;

    private final String CONFIG_FILE = "/sas/groovy/Connections/ofs.properties";

    @Override
    void run() {

        //start log info
        log.info("Starting process GetLimits...");
        log.info("GetLimits - partyId: $partyId");
        log.info("GetLimits - limitType: $limitType");
        log.info("GetLimits - limitSubType: $limitSubType");

        PropertiesResponse propertiesResponse = PropertiesUtils.getProperties(CONFIG_FILE)
        if (propertiesResponse.getProperties() == null) {
            status = propertiesResponse.getStatus().getStatus();
            errorMessage = propertiesResponse.getErrorMessage();
            return;
        }
        Properties config = propertiesResponse.getProperties();

        host = config.getProperty("ofs.host");
        username = config.getProperty("ofs.username");
        password = config.getProperty("ofs.password");

        log.info("GetLimits - host: $host");
        log.info("GetLimits - resource prefix: $resourcePrefix");

        if (partyId && ((limitType && limitSubType) || limit_id)) {

            String uri;
            if (partyId && limitType && limitSubType) {
                uri = host + resourcePrefix + partyIdPart + partyId + limitTypePart + URLEncoder.encode(limitType, StandardCharsets.UTF_8.toString()) + limitSubtypePart + URLEncoder.encode(limitSubType, StandardCharsets.UTF_8.toString());
            } else {
                uri = host + resourcePrefix + partyIdPart + partyId + limitIdPart + limit_id;
            }
            log.info("GetLimits - Endpoint URL: $uri");

            HttpCallResponse httpCallResponse = HttpUtils.runHttpProcess(
                    evtInfo.getEventName(),
                    evtInfo.getSimulationDate().getTime(),
                    uri,
                    HttpUtils.getBasicAuthToken(username, password),
                    [(HttpUtils.CONTENT_TYPE): "application/json"],
                    RequestMethod.GET,
                    null
            );

            httpResponseCode = httpCallResponse.getHttpResponseCode();
            status = httpCallResponse.getStatus().getStatus();
            errorMessage = httpCallResponse.getErrorMessage();
            String responseString = httpCallResponse.getResponse()
            if (responseString == null) {
                return
            }

            log.trace("GetLimits - httpResponseCode:" + httpResponseCode);
            log.trace("GetLimits - response:" + responseString);

            LimitResponse limitResponse = MappingUtils.mapToObject(responseString, LimitResponse.class)
            if (limitResponse == null) {
                status = Status.ERROR.getStatus();
                errorMessage = "Failed to map response. Wrong response data format."
                return
            }

            if (httpResponseCode == 200) {
                log.trace("GetLimits - total records: " + limitResponse.totalRecordNumber)

                if (limitResponse.records.size() > 0) {
                    LimitRecord record = limitResponse.records.get(0);

                    partyIdOut = record.partyId;
                    limitBulkId = record.limitBulkId;
                    limitId = record.limitId;
                    limitScoringProcessSource = record.limitScoringProcessSource;
                    calculationSource = record.calculationSource;
                    limitValidFrom = record.limitValidFrom;
                    limitValidTo = record.limitValidTo;
                    limitStatus = record.limitStatus;
                    limitScore = record.limitScore.toString();
                    amtCreditMax = record.amtCreditMax;
                    amtInstalmentMax = record.amtInstalmentMax;
                    amtDownPaymentMin = record.amtDownPaymentMin;
                    amtDownPaymentMinRel = record.amtDownPaymentMinRel;
                    codeRiskGrade = record.codeRiskGrade;
                    limitAccuracy = record.limitAccuracy;
                    limitPilotCode = record.limitPilotCode;
                    limitTypeCode = record.limitTypeCode;
                    limitSubTypeCode = record.limitSubTypeCode;
                    acqChannelCodes = record.getAcqChannelCodes();
                    relationId = record.relationId;
                    relationCode = record.relationCode;
                    accountNumber = record.accountNumber;
                    maxTenor = record.maxTenor;
                    amtCreditMin = record.amtCreditMin;
                    amtDownPaymentMaxRel = record.amtDownPaymentMaxRel;
                    minTenor = record.minTenor;
                    limitGuaranteedFlag = record.limitGuaranteedFlag;
                    commodityCategoryGroup = record.commodityCategoryGroup;
                }
            }
        } else {
            log.trace("GetLimits - No input clients");
        }
    }
}

class LimitResponse implements Serializable {
    List<LimitRecord> records;
    Long page;
    Long pages;
    Long totalRecordNumber;
}

class LimitRecord implements Serializable {

    String partyId;
    String limitBulkId;
    String limitId;
    String limitScoringProcessSource;
    String calculationSource;
    String limitValidFrom;
    String limitValidTo;
    String limitStatus;
    Double limitScore;
    Long amtCreditMax;
    Long amtInstalmentMax;
    Long amtDownPaymentMin;
    Double amtDownPaymentMinRel;
    String codeRiskGrade;
    Long limitAccuracy;
    String limitPilotCode;
    String limitTypeCode;
    String limitSubTypeCode;
    List<String> acqChannelCodes;
    String relationId;
    String relationCode;
    String accountNumber;
    Long maxTenor;
    Long amtCreditMin;
    Double amtDownPaymentMaxRel;
    Long minTenor;
    String limitGuaranteedFlag;
    String commodityCategoryGroup;

    String getAcqChannelCodes() {
        if (acqChannelCodes == null)
            return null;
        return acqChannelCodes.join(",");
    }
}
