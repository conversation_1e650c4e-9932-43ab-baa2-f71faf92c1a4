package com.sas.hccn.rtdm

import com.homecredit.sas.utils.HttpUtils
import com.homecredit.sas.utils.PropertiesUtils
import com.homecredit.sas.utils.enumeration.Status
import com.homecredit.sas.utils.model.PropertiesResponse
import com.sas.analytics.ph.common.RTDMTable
import com.sas.analytics.ph.common.RTDMTable.Row
import com.sas.analytics.ph.common.jaxb.DataTypes
import com.sas.rtdm.implementation.engine.EventInfo
import org.apache.log4j.Logger
import org.w3c.dom.NodeList

/**
 * GetApplicationDetails
 * @version 10/11/22-001
 */
class GetDetailForClientList implements Runnable {

    // Input variables
    String applicationCode;

    // Output variables
    String status = Status.ERROR.getStatus()
    Long httpResponseCode;    // HTTP response code
    String errorMessage;
    RTDMTable ClientDetails;

    // Logger
    private static final Logger log = Logger.getLogger('groovyLog');

    // Event info
    EventInfo evtInfo;

    // Constants
    private final String resourcePrefix = "/ApplicationManagementWSv22.GetApplicationData"

    // Variables from properties
    private String username;
    private String password;
    private String host;

    private final String CONFIG_FILE = "/sas/groovy/Connections/bsl.properties";

    @Override
    void run() {

        //start log info
        log.info("Starting process GetApplicationDetails...");
        log.info("GetApplicationDetails - applicationCode: $applicationCode");

        PropertiesResponse bslPropertiesResponse = PropertiesUtils.getProperties(CONFIG_FILE)
        if (bslPropertiesResponse.getProperties() == null) {
            status = bslPropertiesResponse.getStatus().getStatus();
            errorMessage = bslPropertiesResponse.getErrorMessage();
            return;
        }
        Properties bslConfig = bslPropertiesResponse.getProperties();

        username = bslConfig.getProperty("bsl.username");
        password = bslConfig.getProperty("bsl.password");
        host = bslConfig.getProperty("bsl.host");

        log.info("GetApplicationDetails - host: $host");
        log.info("GetApplicationDetails - resource prefix: $resourcePrefix");

        if (applicationCode != null && !applicationCode.isEmpty()) {

            // Create empty table
            ClientDetails = new RTDMTable();
            ClientDetails.columnAdd("Code", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("Status", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("Substatus", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("CreatedBy", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("CreationDate", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("Salesroom", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("EventType", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("OfferId", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("OfferType", DataTypes.STRING, Collections.emptyList());

            // Setting API variables
            ApplicationDataResponse response = callBslGetApplicationData()
            if (response == null) {
                return
            }

            List<Event> events = response.getEvents()
            List<Offer> offers = response.getOffers()
            Offer chosenOffer = offers.find { it.chosen }
            for (Event event in events) {
                Row newRow = ClientDetails.rowAdd();
                newRow.columnDataSet("Code", response.getCode());
                newRow.columnDataSet("Status", response.getStatus());
                newRow.columnDataSet("Substatus", response.getSubstatus());
                newRow.columnDataSet("CreatedBy", event.getCreatedBy());
                newRow.columnDataSet("CreationDate", event.getCreationDate());
                newRow.columnDataSet("Salesroom", event.getSalesroom());
                newRow.columnDataSet("EventType", event.getEventType());
                newRow.columnDataSet("OfferId", chosenOffer?.code);
                newRow.columnDataSet("OfferType", chosenOffer?.type);
            }

            status = Status.OK.getStatus()
        } else {
            log.trace("GetApplicationDetails - No input clients");
        }
    }

    private ApplicationDataResponse callBslGetApplicationData() {
        log.info("GetApplicationDetails - call BSL GetApplicationData: Endpoint URL: " + host + resourcePrefix);

        SOAPConnectionFactory soapConnectionFactory = SOAPConnectionFactory.newInstance();
        SOAPConnection soapConnection = soapConnectionFactory.createConnection();
        MessageFactory messageFactory = MessageFactory.newInstance();
        SOAPMessage soapMessage = messageFactory.createMessage();
        SOAPPart soapPart = soapMessage.getSOAPPart();
        SOAPEnvelope envelope = soapPart.getEnvelope();
        envelope.addNamespaceDeclaration("v22", "http://homecredit.net/hss/application-management/v22");
        SOAPBody soapBody = envelope.getBody();
        SOAPElement GetApplicationDataRequestElem = soapBody.addChildElement("GetApplicationDataRequest", "v22");
        SOAPElement applicationCodeElem = GetApplicationDataRequestElem.addChildElement("applicationCode", "v22");
        applicationCodeElem.addTextNode(applicationCode);
        SOAPElement dataSetElem = GetApplicationDataRequestElem.addChildElement("dataSet", "v22");
        dataSetElem.addTextNode("OFFER");
        SOAPElement dataSetElem2 = GetApplicationDataRequestElem.addChildElement("dataSet", "v22");
        dataSetElem2.addTextNode("APPLICATION_LOG");
        MimeHeaders headers = soapMessage.getMimeHeaders();
        String authorization = HttpUtils.getBasicAuthToken(username, password)
        headers.addHeader("Authorization", authorization);
        headers.addHeader("SOAPAction", host + resourcePrefix);
        soapMessage.saveChanges();
        //log message
        ByteArrayOutputStream requestStream = new ByteArrayOutputStream();
        soapMessage.writeTo(requestStream);
        String requestMsg = requestStream.toString("UTF-8")
        log.info("GetApplicationDetails - call BSL GetApplicationData: Message request: " + requestMsg)

        SOAPMessage soapResponse = soapConnection.call(soapMessage, host);
        ByteArrayOutputStream responseStream = new ByteArrayOutputStream();
        soapResponse.writeTo(responseStream);
        String responseMsg = responseStream.toString("UTF-8");
        log.info("GetApplicationDetails - call BSL GetApplicationData: Message response: " + responseMsg)

        ApplicationDataResponse applicationDataResponse = new ApplicationDataResponse()
        SOAPBody body = soapResponse.getSOAPBody()
        List<Offer> offerResult = new ArrayList()
        List<Event> eventResult = new ArrayList()
        try {
            NodeList bodyNodes = body.getChildNodes()
            NodeList getApplicationDataResponse = bodyNodes.item(0).getChildNodes()
            NodeList application = getApplicationDataResponse.item(0).getChildNodes()
            for (j in 0..application.length) {
                if (application.item(j) != null && application.item(j).getLocalName() != null) {
                    if (application.item(j).getLocalName().equalsIgnoreCase("offers")) {
                        NodeList offers = application.item(j).getChildNodes()
                        for (k in 0..offers.length) {
                            if (offers.item(k) != null) {
                                NodeList offerDetails = offers.item(k).getChildNodes()
                                Offer offer = new Offer()
                                for (l in 0..offerDetails.length) {
                                    if (offerDetails.item(l) != null && offerDetails.item(l).getLocalName() != null) {
                                        if (offerDetails.item(l).getLocalName().equalsIgnoreCase("code")) {
                                            offer.setCode(offerDetails.item(l).getTextContent())
                                        } else if (offerDetails.item(l).getLocalName().equalsIgnoreCase("type")) {
                                            offer.setType(offerDetails.item(l).getTextContent())
                                        } else if (offerDetails.item(l).getLocalName().equalsIgnoreCase("chosen")) {
                                            offer.setChosen(Boolean.parseBoolean(offerDetails.item(l).getTextContent()))
                                        }
                                    }
                                }
                                offerResult.add(offer)
                            }
                        }
                    }
                    if (application.item(j).getLocalName().equalsIgnoreCase("events")) {
                        NodeList eventDetails = application.item(j).getChildNodes()
                        Event event = new Event()
                        for (l in 0..eventDetails.length) {
                            if (eventDetails.item(l) != null && eventDetails.item(l).getLocalName() != null) {
                                if (eventDetails.item(l).getLocalName().equalsIgnoreCase("createdBy")) {
                                    event.setCreatedBy(eventDetails.item(l).getTextContent())
                                } else if (eventDetails.item(l).getLocalName().equalsIgnoreCase("creationDate")) {
                                    event.setCreationDate(eventDetails.item(l).getTextContent())
                                } else if (eventDetails.item(l).getLocalName().equalsIgnoreCase("salesroom")) {
                                    event.setSalesroom(eventDetails.item(l).getTextContent())
                                } else if (eventDetails.item(l).getLocalName().equalsIgnoreCase("eventType")) {
                                    event.setEventType(eventDetails.item(l).getTextContent())
                                }
                            }
                        }
                        eventResult.add(event)
                    } else if (application.item(j).getLocalName().equalsIgnoreCase("code")) {
                        applicationDataResponse.setCode(application.item(j).getTextContent())
                    } else if (application.item(j).getLocalName().equalsIgnoreCase("status")) {
                        applicationDataResponse.setStatus(application.item(j).getTextContent())
                    } else if (application.item(j).getLocalName().equalsIgnoreCase("substatus")) {
                        applicationDataResponse.setSubstatus(application.item(j).getTextContent())
                    }
                }
            }
        } catch (Exception e) {
            log.error("GetApplicationDetails - Failed to map BSL GetApplicationData offer response: " + e.getLocalizedMessage())
            status = Status.ERROR
            errorMessage = "Failed to BSL GetApplicationData offer response: " + e.getLocalizedMessage()
            return null
        }

        applicationDataResponse.setEvents(eventResult)
        applicationDataResponse.setOffers(offerResult)
        return applicationDataResponse
    }
}

class ApplicationDataResponse {
    String code;
    String status;
    String substatus;
    List<Offer> offers;
    List<Event> events;
}

class Event {
    String createdBy;
    String creationDate;
    String salesroom;
    String eventType;
}

class Offer {
    String code;
    String type;
    Boolean chosen;
}