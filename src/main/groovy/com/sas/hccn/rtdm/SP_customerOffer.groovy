package com.sas.hccn.rtdm

import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.DeserializationFeature
import com.homecredit.sas.utils.HttpUtils
import com.homecredit.sas.utils.PropertiesUtils
import com.homecredit.sas.utils.enumeration.RequestMethod
import com.homecredit.sas.utils.enumeration.Status
import com.homecredit.sas.utils.model.HttpCallResponse
import com.homecredit.sas.utils.model.PropertiesResponse
import com.sas.analytics.ph.common.RTDMTable
import com.sas.analytics.ph.common.RTDMTable.Row
import com.sas.analytics.ph.common.jaxb.DataTypes
import com.sas.rtdm.implementation.engine.EventInfo
import org.apache.log4j.Logger
import org.w3c.dom.NodeList

/**
 * Customer offer
 * @version 20/09/22-003
 */
class GetDetailForClientList implements Runnable {

    // Input variables
    String applicationCode;

    // Output variables
    String status = Status.ERROR.getStatus()
    Long httpResponseCode;    // HTTP response code
    String errorMessage;
    RTDMTable ClientDetails;
    RTDMTable ApplicationEvents;

    // Logger
    private static final Logger log = Logger.getLogger('groovyLog');

    // Event info
    EventInfo evtInfo;

    // Constants
    private final String bslResourcePrefix1 = "/ApplicationManagementWSv22.GetApplicationData"
    private final String bslResourcePrefix2 = "/CustomOfferWSv21.getCustomerOfferResponse"
    private final String pcgResourcePrefix1 = "/openapi/v1/services/"
    private final String pcgResourcePrefix2 = "/openapi/v1/products/"

    // Variables from properties
    private String bslUsername;
    private String bslPassword;
    private String bslHost;
    private String pcgUsername;
    private String pcgPassword;
    private String pcgHost;

    private final String CONFIG_FILE_BSL = "/sas/groovy/Connections/bsl.properties";
    private final String CONFIG_FILE_PCG = "/sas/groovy/Connections/pcg.properties";

    @Override
    void run() {

        //start log info
        log.info("Starting process CustomerOffer...");
        log.info("CustomerOffer - applicationCode: $applicationCode");

        PropertiesResponse bslPropertiesResponse = PropertiesUtils.getProperties(CONFIG_FILE_BSL)
        if (bslPropertiesResponse.getProperties() == null) {
            status = bslPropertiesResponse.getStatus().getStatus();
            errorMessage = bslPropertiesResponse.getErrorMessage();
            return;
        }
        Properties bslConfig = bslPropertiesResponse.getProperties();

        bslUsername = bslConfig.getProperty("bsl.username");
        bslPassword = bslConfig.getProperty("bsl.password");
        bslHost = bslConfig.getProperty("bsl.host");

        log.info("CustomerOffer - BSL Host: $bslHost");
        log.info("CustomerOffer - BSL resource prefix 1: $bslResourcePrefix1");
        log.info("CustomerOffer - BSL resource prefix 2: $bslResourcePrefix2");

        PropertiesResponse pcgPropertiesResponse = PropertiesUtils.getProperties(CONFIG_FILE_PCG)
        if (pcgPropertiesResponse.getProperties() == null) {
            status = pcgPropertiesResponse.getStatus().getStatus();
            errorMessage = pcgPropertiesResponse.getErrorMessage();
            return;
        }
        Properties pcgConfig = pcgPropertiesResponse.getProperties();

        pcgUsername = pcgConfig.getProperty("pcg.username");
        pcgPassword = pcgConfig.getProperty("pcg.password");
        pcgHost = pcgConfig.getProperty("pcg.host");

        log.info("CustomerOffer - PCG Host: $pcgHost");
        log.info("CustomerOffer - PCG resource prefix 1: $pcgResourcePrefix1");
        log.info("CustomerOffer - PCG resource prefix 2: $pcgResourcePrefix2");

        if (applicationCode != null && !applicationCode.isEmpty()) {

            // Create empty table
            ClientDetails = new RTDMTable();
            ClientDetails.columnAdd("Chosen", DataTypes.BOOLEAN, Collections.emptyList());
//            ClientDetails.columnAdd("ProductType", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("FlagInsurance", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("CodeProduct", DataTypes.STRING, Collections.emptyList());
//            ClientDetails.columnAdd("NameProduct", DataTypes.STRING, Collections.emptyList());
//            ClientDetails.columnAdd("ProductProfileCode", DataTypes.STRING, Collections.emptyList());
//            ClientDetails.columnAdd("InitialTransactionType", DataTypes.STRING, Collections.emptyList());

            ApplicationEvents = new RTDMTable();
            ApplicationEvents.columnAdd("CreatedBy", DataTypes.STRING, Collections.emptyList());
            ApplicationEvents.columnAdd("CreationDate", DataTypes.STRING, Collections.emptyList());
            ApplicationEvents.columnAdd("Salesroom", DataTypes.STRING, Collections.emptyList());
            ApplicationEvents.columnAdd("EventType", DataTypes.STRING, Collections.emptyList());
            ApplicationEvents.columnAdd("EventChannel", DataTypes.STRING, Collections.emptyList());

            // Setting API variables
            ApplicationDataResponse adResponse = callBslGetApplicationData()
            if (adResponse == null) {
                return
            }

            List<Event> events = adResponse.getEvents()
            for (Event event in events) {
                Row newRow = ApplicationEvents.rowAdd();
                newRow.columnDataSet("CreatedBy", event.getCreatedBy());
                newRow.columnDataSet("CreationDate", event.getCreationDate());
                newRow.columnDataSet("Salesroom", event.getSalesroom());
                newRow.columnDataSet("EventType", event.getEventType());
                newRow.columnDataSet("EventChannel", event.getEventChannel());
            }

            List<Offer> offers = adResponse.getOffers()
            if (offers == null || offers.isEmpty()) {
                return
            }
            for (j in 0..offers.size() - 1) {
                Offer offer = offers.get(j)

                ObjectMapper mapper = new ObjectMapper()
//                log.info("CustomerOffer - offer " + mapper.writerWithDefaultPrettyPrinter().writeValueAsString(offer))
                CustomerOfferResponse response = callBslGetCustomerOffer(offer)
                if (response == null) {
                    break
                }
//                log.info("CustomerOffer - offer services and products " + mapper.writerWithDefaultPrettyPrinter().writeValueAsString(response))
                List<PcgServicesResponse> servicesResponse = callPcgServices(response)
//                log.info("CustomerOffer - services response " + mapper.writerWithDefaultPrettyPrinter().writeValueAsString(servicesResponse))
//                List<PcgProductsResponse> productsResponse = callPcgProducts(response)
//                log.info("CustomerOffer - products response " + mapper.writerWithDefaultPrettyPrinter().writeValueAsString(productsResponse))

                String flagInsurance = "N";
                for (service in servicesResponse) {
                    if (service.getCategory() != null && service.getCategory().equalsIgnoreCase("INSURANCE")) {
                        flagInsurance = "Y";
                    }
                }
                String productCode = "";
                if (response.products.size() > 0) {
                    productCode = response.products.get(0).getCode()
                }
                Row newRow = ClientDetails.rowAdd();
                newRow.columnDataSet("Chosen", offers.get(j).getChosen());
//                newRow.columnDataSet("ProductType", "");
                newRow.columnDataSet("FlagInsurance", flagInsurance);
                newRow.columnDataSet("CodeProduct", productCode);
//                newRow.columnDataSet("NameProduct", productsResponse.get(0).getName());
//                newRow.columnDataSet("ProductProfileCode", productsResponse.get(0).getProductProfileCode());
//                newRow.columnDataSet("InitialTransactionType", "");

                status = Status.OK.toString()
            }
        } else {
            log.trace("CustomerOffer - No input clients");
        }
    }

    private ApplicationDataResponse callBslGetApplicationData() {
        log.info("CustomerOffer - call BSL GetApplicationData: Endpoint URL: " + bslHost + bslResourcePrefix1);

        SOAPConnectionFactory soapConnectionFactory = SOAPConnectionFactory.newInstance();
        SOAPConnection soapConnection = soapConnectionFactory.createConnection();
        MessageFactory messageFactory = MessageFactory.newInstance();
        SOAPMessage soapMessage = messageFactory.createMessage();
        SOAPPart soapPart = soapMessage.getSOAPPart();
        SOAPEnvelope envelope = soapPart.getEnvelope();
        envelope.addNamespaceDeclaration("v22", "http://homecredit.net/hss/application-management/v22");
        SOAPBody soapBody = envelope.getBody();
        SOAPElement GetApplicationDataRequestElem = soapBody.addChildElement("GetApplicationDataRequest", "v22");
        SOAPElement applicationCodeElem = GetApplicationDataRequestElem.addChildElement("applicationCode", "v22");
        applicationCodeElem.addTextNode(applicationCode);
        SOAPElement dataSetElem = GetApplicationDataRequestElem.addChildElement("dataSet", "v22");
        dataSetElem.addTextNode("OFFER");
        SOAPElement dataSetElem2 = GetApplicationDataRequestElem.addChildElement("dataSet", "v22");
        dataSetElem2.addTextNode("APPLICATION_LOG");
        MimeHeaders headers = soapMessage.getMimeHeaders();
        String authorization = HttpUtils.getBasicAuthToken(bslUsername, bslPassword)
        headers.addHeader("Authorization", authorization);
        headers.addHeader("SOAPAction", bslHost + bslResourcePrefix1);
        soapMessage.saveChanges();
        //log message
        ByteArrayOutputStream requestStream = new ByteArrayOutputStream();
        soapMessage.writeTo(requestStream);
        String requestMsg = requestStream.toString("UTF-8")
        log.info("CustomerOffer - call BSL GetApplicationData: Message request: " + requestMsg)

        SOAPMessage soapResponse = soapConnection.call(soapMessage, bslHost);
        ByteArrayOutputStream responseStream = new ByteArrayOutputStream();
        soapResponse.writeTo(responseStream);
        String responseMsg = responseStream.toString("UTF-8");
        log.info("CustomerOffer - call BSL GetApplicationData: Message response: " + responseMsg)

        SOAPBody body = soapResponse.getSOAPBody()
        List<Offer> offerResult = new ArrayList()
        List<Event> eventResult = new ArrayList()
        try {
            NodeList bodyNodes = body.getChildNodes()
            NodeList getApplicationDataResponse = bodyNodes.item(0).getChildNodes()
            NodeList application = getApplicationDataResponse.item(0).getChildNodes()
            for (j in 0..application.length) {
                if (application.item(j) != null && application.item(j).getLocalName() != null && application.item(j).getLocalName().equalsIgnoreCase("offers")) {
                    NodeList offers = application.item(j).getChildNodes()
                    for (k in 0..offers.length) {
                        if (offers.item(k) != null) {
                            NodeList offerDetails = offers.item(k).getChildNodes()
                            Offer offer = new Offer()
                            for (l in 0..offerDetails.length) {
                                if (offerDetails.item(l) != null && offerDetails.item(l).getLocalName() != null) {
                                    if (offerDetails.item(l).getLocalName().equalsIgnoreCase("code")) {
                                        offer.setCode(offerDetails.item(l).getTextContent())
                                    } else if (offerDetails.item(l).getLocalName().equalsIgnoreCase("type")) {
                                        offer.setType(offerDetails.item(l).getTextContent())
                                    } else if (offerDetails.item(l).getLocalName().equalsIgnoreCase("chosen")) {
                                        offer.setChosen(Boolean.parseBoolean(offerDetails.item(l).getTextContent()))
                                    }
                                }
                            }
                            offerResult.add(offer)
                        }
                    }
                }
                if (application.item(j) != null && application.item(j).getLocalName() != null && application.item(j).getLocalName().equalsIgnoreCase("events")) {
                    NodeList eventDetails = application.item(j).getChildNodes()
                    Event event = new Event()
                    for (l in 0..eventDetails.length) {
                        if (eventDetails.item(l) != null && eventDetails.item(l).getLocalName() != null) {
                            if (eventDetails.item(l).getLocalName().equalsIgnoreCase("createdBy")) {
                                event.setCreatedBy(eventDetails.item(l).getTextContent())
                            } else if (eventDetails.item(l).getLocalName().equalsIgnoreCase("creationDate")) {
                                event.setCreationDate(eventDetails.item(l).getTextContent())
                            } else if (eventDetails.item(l).getLocalName().equalsIgnoreCase("salesroom")) {
                                event.setSalesroom(eventDetails.item(l).getTextContent())
                            } else if (eventDetails.item(l).getLocalName().equalsIgnoreCase("eventType")) {
                                event.setEventType(eventDetails.item(l).getTextContent())
                            } else if (eventDetails.item(l).getLocalName().equalsIgnoreCase("eventChannel")) {
                                event.setEventChannel(eventDetails.item(l).getTextContent())
                            }
                        }
                    }
                    eventResult.add(event)
                }
            }
        } catch (Exception e) {
            log.error("CustomerOffer - Failed to map BSL GetApplicationData offer response: " + e.getLocalizedMessage())
            status = Status.ERROR
            errorMessage = "Failed to BSL GetApplicationData offer response: " + e.getLocalizedMessage()
            return null
        }

        ApplicationDataResponse applicationDataResponse = new ApplicationDataResponse()
        applicationDataResponse.setOffers(offerResult)
        applicationDataResponse.setEvents(eventResult)
        return applicationDataResponse
    }

    CustomerOfferResponse callBslGetCustomerOffer(Offer offer) {
        log.info("CustomerOffer - call BSL GetCustomerOffer: Endpoint URL: " + bslHost + bslResourcePrefix2);

        SOAPConnectionFactory soapConnectionFactory = SOAPConnectionFactory.newInstance();
        SOAPConnection soapConnection = soapConnectionFactory.createConnection();
        MessageFactory messageFactory = MessageFactory.newInstance();
        SOAPMessage soapMessage = messageFactory.createMessage();
        SOAPPart soapPart = soapMessage.getSOAPPart();
        SOAPEnvelope envelope = soapPart.getEnvelope();
        SOAPBody soapBody = envelope.getBody();
        SOAPElement GetCustomerOfferRequestElem = soapBody.addChildElement("GetCustomerOfferRequest", "", "http://homecredit.net/hss/customeroffer/v21");
        SOAPElement offerCode = GetCustomerOfferRequestElem.addChildElement("offerCode", "");
        offerCode.addTextNode(offer.getCode());
        SOAPElement dataSetElem = GetCustomerOfferRequestElem.addChildElement("dataSet", "");
        dataSetElem.addTextNode("VALIDITY");
        SOAPElement dataSetElem3 = GetCustomerOfferRequestElem.addChildElement("dataSet", "");
        dataSetElem3.addTextNode("SERVICES");
        MimeHeaders headers = soapMessage.getMimeHeaders();
        String authorization = HttpUtils.getBasicAuthToken(bslUsername, bslPassword)
        headers.addHeader("Authorization", authorization);
        headers.addHeader("SOAPAction", bslHost + bslResourcePrefix2);
        soapMessage.saveChanges();
        //log message
        ByteArrayOutputStream requestStream = new ByteArrayOutputStream();
        soapMessage.writeTo(requestStream);
        String requestMsg = requestStream.toString("UTF-8")
        log.info("CustomerOffer - call BSL GetCustomerOffer: Message request: " + requestMsg)

        SOAPMessage soapResponse = soapConnection.call(soapMessage, bslHost);
        ByteArrayOutputStream responseStream = new ByteArrayOutputStream();
        soapResponse.writeTo(responseStream);
        String responseMsg = responseStream.toString("UTF-8");
        log.info("CustomerOffer - call BSL GetCustomerOffer: Message response: " + responseMsg)

        SOAPBody body = soapResponse.getSOAPBody()
        CustomerOfferResponse customerOfferResponse = new CustomerOfferResponse()
        customerOfferResponse.services = new ArrayList<>()
        customerOfferResponse.products = new ArrayList<>()
        try {
            NodeList bodyNodes = body.getChildNodes()
            NodeList getCustomerOfferResponse = bodyNodes.item(0).getChildNodes()
            NodeList customerOffer = getCustomerOfferResponse.item(0).getChildNodes()
            for (j in 0..customerOffer.length) {
                if (customerOffer.item(j) != null && customerOffer.item(j).getLocalName() != null) {
                    if (customerOffer.item(j).getLocalName().equalsIgnoreCase("code")) {
                        customerOfferResponse.setCode(customerOffer.item(j).getTextContent())
                    }
                    if (customerOffer.item(j).getLocalName().equalsIgnoreCase("services")) {
                        NodeList service = customerOffer.item(j).getChildNodes()
                        Service serviceResponse = new Service()
                        for (i in 0..service.length) {
                            if (service.item(i) != null && service.item(i).getLocalName() != null) {
                                if (service.item(i).getLocalName().equalsIgnoreCase("code")) {
                                    serviceResponse.setCode(service.item(i).getTextContent())
                                } else if (service.item(i).getLocalName().equalsIgnoreCase("version")) {
                                    serviceResponse.setVersion(service.item(i).getTextContent())
                                }
                            }
                        }
                        customerOfferResponse.services.add(serviceResponse)
                    }
                    if (customerOffer.item(j).getLocalName().equalsIgnoreCase("product")) {
                        NodeList product = customerOffer.item(j).getChildNodes()
                        Product productResponse = new Product()
                        for (i in 0..product.length) {
                            if (product.item(i) != null && product.item(i).getLocalName() != null ) {
                                if (product.item(i).getLocalName().equalsIgnoreCase("code")) {
                                    productResponse.setCode(product.item(i).getTextContent())
                                } else if (product.item(i).getLocalName().equalsIgnoreCase("version")) {
                                    productResponse.setVersion(product.item(i).getTextContent())
                                } else if (product.item(i).getLocalName().equalsIgnoreCase("variantCode")) {
                                    productResponse.setVariantCode(product.item(i).getTextContent())
                                }
                            }
                        }
                        customerOfferResponse.products.add(productResponse)
                    }
                }
            }
        } catch (Exception e) {
            log.error("CustomerOffer - Failed to map BSL GetCustomerOffer offer response: " + e.getLocalizedMessage())
            status = Status.ERROR
            errorMessage = "Failed to map BSL GetCustomerOffer offer response: " + e.getLocalizedMessage()
            return null
        }
        return customerOfferResponse
    }

    List<PcgServicesResponse> callPcgServices(CustomerOfferResponse customerOfferResponse) {
        if (customerOfferResponse.services == null || customerOfferResponse.services.isEmpty()) {
            log.info("CustomerOffer - no services present for customer, skipping call PCG services");
            return new ArrayList<>();
        }
        StringBuilder sb = new StringBuilder()
        for (service in customerOfferResponse.services) {
            sb.append(service.getCode() + ":" + service.getVersion() + ",")
        }
        sb.setLength(sb.length() - 1) // remove last ","

        String uri = pcgHost + pcgResourcePrefix1 + sb.toString();
        log.info("CustomerOffer - call PCG services: " + uri);
        HttpCallResponse httpCallResponse = HttpUtils.runHttpProcess(
                evtInfo.getEventName(),
                evtInfo.getSimulationDate().getTime(),
                uri,
                HttpUtils.getBasicAuthToken(pcgUsername, pcgPassword),
                [(HttpUtils.CONTENT_TYPE): "application/json"],
                RequestMethod.GET,
                null
        );

        httpResponseCode = httpCallResponse.getHttpResponseCode();
        status = httpCallResponse.getStatus().getStatus();
        errorMessage = httpCallResponse.getErrorMessage();
        String responseString = httpCallResponse.getResponse()
        if (responseString == null) {
            return null
        }

        log.trace("CustomerOffer - httpResponseCode:" + httpResponseCode);
        log.trace("CustomerOffer - response:" + responseString);

        ObjectMapper mapper = new ObjectMapper()
        mapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        List<PcgServicesResponse> responseObject = mapper.readValue(responseString, new TypeReference<List<PcgServicesResponse>>() {})
        if (responseObject == null) {
            status = Status.ERROR.getStatus();
            errorMessage = "Failed to map response. Wrong response data format."
            return null
        }

        return responseObject
    }

    List<PcgProductsResponse> callPcgProducts(CustomerOfferResponse customerOfferResponse) {
        if (customerOfferResponse.products == null || customerOfferResponse.products.isEmpty()) {
            log.info("CustomerOffer - no products present for customer, skipping call PCG products");
            return new ArrayList<>();
        }
        StringBuilder sb = new StringBuilder()
        for (product in customerOfferResponse.products) {
            sb.append(product.getCode() + ":" + product.getVersion() + ",")
        }
        sb.setLength(sb.length() - 1) // remove last ","

        String uri = pcgHost + pcgResourcePrefix2 + sb.toString();
        log.info("CustomerOffer - call PCG products: " + uri);
        HttpCallResponse httpCallResponse = HttpUtils.runHttpProcess(
                evtInfo.getEventName(),
                evtInfo.getSimulationDate().getTime(),
                uri,
                HttpUtils.getBasicAuthToken(pcgUsername, pcgPassword),
                [(HttpUtils.CONTENT_TYPE): "application/json"],
                RequestMethod.GET,
                null
        );

        httpResponseCode = httpCallResponse.getHttpResponseCode();
        status = httpCallResponse.getStatus().getStatus();
        errorMessage = httpCallResponse.getErrorMessage();
        String responseString = httpCallResponse.getResponse()
        if (responseString == null) {
            return null
        }

        log.trace("CustomerOffer - httpResponseCode:" + httpResponseCode);
        log.trace("CustomerOffer - response:" + responseString);

        ObjectMapper mapper = new ObjectMapper()
        mapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        List<PcgProductsResponse> responseObject = mapper.readValue(responseString, new TypeReference<List<PcgProductsResponse>>() {})
        if (responseObject == null) {
            status = Status.ERROR.getStatus();
            errorMessage = "Failed to map response. Wrong response data format."
            return null
        }

        return responseObject
    }
}

class ApplicationDataResponse {
    List<Offer> offers;
    List<Event> events;
}

class Offer {
    String code;
    String type;
    Boolean chosen;
}

class Event {
    String createdBy;
    String creationDate;
    String salesroom;
    String eventType;
    String eventChannel;
}

class CustomerOfferResponse {
    String code;
    List<Service> services;
    List<Product> products;
}

class Service {
    String code;
    String version;
}

class Product {
    String code;
    String version;
    String variantCode;
}

class PcgServicesResponse implements Serializable {
    String code;
    String name;
    String category;
}

class PcgProductsResponse implements Serializable {
    String code;
    String name;
    String productProfileCode;
}