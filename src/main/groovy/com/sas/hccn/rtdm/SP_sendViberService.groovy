package com.sas.hccn.rtdm;

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.ObjectMapper
import com.homecredit.sas.utils.PropertiesUtils
import com.homecredit.sas.utils.model.PropertiesResponse
import org.apache.log4j.Logger

import java.nio.charset.StandardCharsets
import java.time.format.DateTimeFormatter

/**
 * @version 22/07/31-004
 */
class GetDetailForClientList implements Runnable {

    // Input variables
    Long cuid;
    String externalId;
    String systemCode;
    String messageCode;
    Object expires;
    Object created;
    String priority;
    String reportLevel;
    String reportContentType;
    String text;
    String logicalApplication;
    String token;

    String VIBER_SERVICEID;
    String VIBER_TYPE;
    String VIBER_IMAGE;
    String VIBER_CAPTION;
    String VIBER_ACTION;
    String SAS_TEMPLATE_ID;
    String SAS_MESSAGE_TYPE;

    // Output variables
    String status = "ERROR";            // either "OK" or "ERROR"
    String errorMessage;

    // Internal variables
    private static final Logger log = Logger.getLogger('groovyLog');
    private static final Logger requestReplyLog = Logger.getLogger('groovyRequestReply')

    private final String CONFIG_FILE = "/sas/groovy/Connections/rabbitmq.properties";

    @Override
    void run() {
        //start log info
        log.info("Starting process SendViber to RabbitMQ...");
        log.info("SendViber - cuid: $cuid");
        log.info("SendViber - externalId: $externalId");
        log.info("SendViber - systemCode: $systemCode");
        log.info("SendViber - messageCode: $messageCode");
        log.info("SendViber - expires: $expires");
        log.info("SendViber - priority: $priority");
        log.info("SendViber - reportLevel: $reportLevel");
        log.info("SendViber - reportContentType: $reportContentType");
        log.info("SendViber - text: $text");
        log.info("SendViber - logicalApplication: $logicalApplication");
        log.info("SendViber - VIBER_SERVICEID: $VIBER_SERVICEID");
        log.info("SendViber - VIBER_TYPE: $VIBER_TYPE");
        log.info("SendViber - VIBER_IMAGE: $VIBER_IMAGE");
        log.info("SendViber - VIBER_CAPTION: $VIBER_CAPTION");
        log.info("SendViber - VIBER_ACTION: $VIBER_ACTION");
        log.info("SendViber - SAS_TEMPLATE_ID: $SAS_TEMPLATE_ID");
        log.info("SendViber - SAS_MESSAGE_TYPE: $SAS_MESSAGE_TYPE");

        PropertiesResponse propertiesResponse = PropertiesUtils.getProperties(CONFIG_FILE)
        if (propertiesResponse.getProperties() == null) {
            status = propertiesResponse.getStatus().getStatus();
            errorMessage = propertiesResponse.getErrorMessage();
            return;
        }
        Properties config = propertiesResponse.getProperties();

        String host = config.getProperty("rabbitmq.host");
        String routingKey = config.getProperty("rabbitmq.push-routing-key");
        String username = config.getProperty("rabbitmq.username");
        String password = config.getProperty("rabbitmq.password");
        String virtualHost = config.getProperty("rabbitmq.virtual-host");
        String exchange = config.getProperty("rabbitmq.exchange");

        log.info("SendViber - RabbitMQ configuration: host = $host, routingKey = $routingKey, virtualHost = $virtualHost, exchange = $exchange")
        Message message = new Message()

        message.setToken(cuid.toString())
        message.setExternalId(externalId)
        message.setSystemCode(systemCode)
        message.setMessageCode(messageCode)
        if (expires instanceof GregorianCalendar) {
            message.setExpires(DateTimeFormatter.ISO_OFFSET_DATE_TIME.format(((GregorianCalendar) expires).toZonedDateTime()))
        }
        message.setPriority(priority)
        message.setReportLevel(reportLevel)
        message.setReportContentType(reportContentType)
        message.setText(text)
        message.setToken(token)
        message.setLogicalApplication(logicalApplication)
        List<Attribute> attributes = new ArrayList<Attribute>();
        if (cuid) attributes.add(new Attribute(type: "CUID", value: cuid.toString()))
        if (SAS_TEMPLATE_ID) attributes.add(new Attribute(type: "SAS_TEMPLATE_ID", value: SAS_TEMPLATE_ID))
        if (SAS_MESSAGE_TYPE) attributes.add(new Attribute(type: "SAS_MESSAGE_TYPE", value: SAS_MESSAGE_TYPE))
        if (VIBER_IMAGE) attributes.add(new Attribute(type: "VIBER_IMAGE", value: VIBER_IMAGE))
        if (VIBER_CAPTION) attributes.add(new Attribute(type: "VIBER_CAPTION", value: VIBER_CAPTION))
        if (VIBER_ACTION) attributes.add(new Attribute(type: "VIBER_ACTION", value: VIBER_ACTION))
        if (VIBER_TYPE) attributes.add(new Attribute(type: "VIBER_TYPE", value: VIBER_TYPE))
        if (VIBER_SERVICEID) attributes.add(new Attribute(type: "VIBER_SERVICEID", value: VIBER_SERVICEID))
        message.setAttributes(attributes)

        MessageWrapper messageWrapper = new MessageWrapper()
        messageWrapper.setMessage(Arrays.asList(message))

        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        String jsonMessage = mapper.writeValueAsString(messageWrapper)
        log.info("SendViber - Serialized message:" + jsonMessage)

        Map<String, Object> headerMap = new HashMap<String, Object>();
        headerMap.put("SYSTEM_CODE", systemCode);
        headerMap.put("REQUEST_ID", externalId);
        headerMap.put("priority", 0);
        headerMap.put("cuid", cuid.toString());
        headerMap.put("CorrelationID", externalId);
        headerMap.put("Type", "JMSType");
        BasicProperties messageProperties = new AMQP.BasicProperties.Builder()
                .contentType("application/json")
                .headers(headerMap)
                .build();

        ConnectionFactory factory = new ConnectionFactory();
        factory.setHost(host);
        factory.setUsername(username);
        factory.setPassword(password);
        factory.setVirtualHost(virtualHost);
        Connection connection;
        try {
            connection = factory.newConnection();
            Channel channel = connection.createChannel();
            channel.basicPublish(exchange, routingKey, messageProperties, jsonMessage.getBytes(StandardCharsets.UTF_8));
            log.info("SendViber - Message sent successfully");
            requestReplyLog.info("SendViber - Sent message: $jsonMessage to RabbitMQ (host = $host, routingKey = $routingKey, virtualHost = $virtualHost, exchange = $exchange)")
            status = "OK";
        } catch (Exception e) {
            log.error("SendViber - Failed to send message: " + e.getMessage())
            status = "ERROR";
            throw e;
        } finally {
            if (connection != null) {
                connection.close()
            }
        }
    }
}

class MessageWrapper implements Serializable {
    List<Message> message;
}

class Message implements Serializable {
    String cuid;
    String externalId;
    String systemCode;
    String messageCode;
    String expires;
    List<Attribute> attributes;
    String priority;
    String reportLevel;
    String reportContentType;
    String text;
    String logicalApplication;
    String token;
}

class Attribute implements Serializable {
    String type;
    String value;
}
