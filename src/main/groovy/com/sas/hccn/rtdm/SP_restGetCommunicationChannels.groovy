package com.sas.hccn.rtdm


import com.homecredit.sas.utils.HttpUtils
import com.homecredit.sas.utils.MappingUtils
import com.homecredit.sas.utils.PropertiesUtils
import com.homecredit.sas.utils.enumeration.RequestMethod
import com.homecredit.sas.utils.enumeration.Status
import com.homecredit.sas.utils.model.HttpCallResponse
import com.homecredit.sas.utils.model.PropertiesResponse
import com.sas.analytics.ph.common.RTDMTable
import com.sas.analytics.ph.common.RTDMTable.Row
import com.sas.analytics.ph.common.jaxb.DataTypes
import com.sas.rtdm.implementation.engine.EventInfo
import org.apache.log4j.Logger

class GetDetailForClientList implements Runnable {

    // Input variables
    String cuid; // 17028123

    // Output variables
    String status = Status.ERROR.getStatus()
    Long httpResponseCode;    // HTTP response code
    String errorMessage;
    RTDMTable ClientDetails;

    // Logger
    private static final Logger log = Logger.getLogger('groovyLog');

    // Event info
    EventInfo evtInfo;

    // Constants
    private final String resourcePrefix = "/party-web/api/pif/v1/customer/"
    private final String resourceSuffix = "?projections=ROLE_COMMUNICATION_CHANNELS"

    // Variables from properties
    private String username;
    private String password;
    private String host; // https://pif.vn00c1.vn.infra/

    private final String CONFIG_FILE = "/sas/groovy/Connections/pif.properties";

    @Override
    void run() {

        //start log info
        log.info("Starting process GetCommunicationChannels...");
        log.info("GetCommunicationChannels - cuid: $cuid");

        PropertiesResponse propertiesResponse = PropertiesUtils.getProperties(CONFIG_FILE)
        if (propertiesResponse.getProperties() == null) {
            status = propertiesResponse.getStatus().getStatus();
            errorMessage = propertiesResponse.getErrorMessage();
            return;
        }
        Properties config = propertiesResponse.getProperties();

        username = config.getProperty("pif.username");
        password = config.getProperty("pif.password");
        host = config.getProperty("pif.host"); // = "https://pif.vn00c1.vn.infra/";

        log.info("GetCommunicationChannels - host: $host");
        log.info("GetCommunicationChannels - resource prefix: $resourcePrefix");
        log.info("GetCommunicationChannels - resource suffix: $resourceSuffix");

        if (cuid != null && !cuid.isEmpty()) {

            // Create empty table
            ClientDetails = new RTDMTable();
            ClientDetails.columnAdd("externalId", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("id", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("activeYn", DataTypes.BOOLEAN, Collections.emptyList());
            ClientDetails.columnAdd("channelType", DataTypes.STRING, Collections.emptyList());

            // Setting API variables
            String uri = host + resourcePrefix + cuid + resourceSuffix;
            log.info("GetCommunicationChannels - Endpoint URL: $uri");

            HttpCallResponse httpCallResponse = HttpUtils.runHttpProcess(
                    evtInfo.getEventName(),
                    evtInfo.getSimulationDate().getTime(),
                    uri,
                    HttpUtils.getBasicAuthToken(username, password),
                    [(HttpUtils.CONTENT_TYPE): "application/json"],
                    RequestMethod.GET,
                    null
            );

            httpResponseCode = httpCallResponse.getHttpResponseCode();
            status = httpCallResponse.getStatus().getStatus();
            errorMessage = httpCallResponse.getErrorMessage();
            String responseString = httpCallResponse.getResponse()
            if (responseString == null) {
                return
            }

            log.trace("GetCommunicationChannels - httpResponseCode:" + httpResponseCode);
            log.trace("GetCommunicationChannels - response:" + responseString);

            Response responseObject = MappingUtils.mapToObject(responseString, Response.class)
            if (responseObject == null) {
                status = Status.ERROR.getStatus();
                errorMessage = "Failed to map response. Wrong response data format."
                return
            }

            if (httpResponseCode == 200) {
                if (responseObject.resultCode == "FOUND") {
                    List<Channel> channels = responseObject.data.communicationChannels.get("MSG_APP")
                    if (channels == null) {
                        log.trace("GetCommunicationChannels - record size: 0");
                    } else {
                        log.trace("GetCommunicationChannels - record size:" + channels.size());

                        for (int j = 0; j < channels.size(); j++) {

                            Channel channel = channels.get(j);
                            log.trace("GetCommunicationChannels - channel: " + channel.id);

                            Row newRow = ClientDetails.rowAdd();
                            newRow.columnDataSet("externalId", responseObject.externalId.toString());
                            newRow.columnDataSet("id", channel.id.toString());
                            newRow.columnDataSet("activeYn", channel.activeYn);
                            newRow.columnDataSet("channelType", channel.channelType);
                        }
                    }
                    status = Status.OK.getStatus()

                } else {
                    log.error("GetCommunicationChannels - cuid $cuid, error: " + responseObject.errorMessage);
                    status = Status.ERROR.getStatus()
                    errorMessage = responseObject.errorMessage
                }
            }
        } else {
            log.trace("GetCommunicationChannels - No input clients");
        }
    }
}

class Response implements Serializable {
    ClientData data;
    Long externalId;
    String resultCode;
    String errorCode;
    String errorMessage;
    String requestId;
    Object validationErrors;
}

class ClientData implements Serializable {
    Long externalId;
    Map<String, List<Channel>> communicationChannels;
    // ...
}

class Channel implements Serializable {
    Boolean activeYn;
    Long id;
    String channelType;
    // ...
}