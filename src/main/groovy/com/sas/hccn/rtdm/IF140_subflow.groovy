package com.sas.hccn.rtdm

import com.fasterxml.jackson.core.JsonGenerator
import com.fasterxml.jackson.core.JsonParser
import com.fasterxml.jackson.core.JsonProcessingException
import com.fasterxml.jackson.databind.*
import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import com.fasterxml.jackson.databind.annotation.JsonSerialize
import com.homecredit.sas.utils.HttpUtils
import com.homecredit.sas.utils.MappingUtils
import com.homecredit.sas.utils.enumeration.RequestMethod
import com.homecredit.sas.utils.enumeration.Status
import com.homecredit.sas.utils.model.HttpCallResponse
import com.sas.analytics.ph.common.RTDMTable
import com.sas.analytics.ph.common.RTDMTable.Column
import com.sas.analytics.ph.common.RTDMTable.Row
import com.sas.analytics.ph.common.jaxb.DataTypes
import com.sas.rtdm.implementation.engine.EventInfo
import org.apache.log4j.Logger

import java.nio.charset.StandardCharsets
import java.text.SimpleDateFormat
import java.time.LocalDateTime
import java.time.OffsetDateTime
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.time.format.DateTimeFormatterBuilder
import java.time.temporal.TemporalAccessor
import java.time.temporal.TemporalQuery

class CallRTDM implements Runnable {

    // Input variables - CHANGE THIS PART FOR CLIENT INPUTS
    String partyID
    String limitID
    Object modification_timestamp
    Object eventTimestamp
    String eventName

    String rtdmEventId
    String rtdmHost
    String charEventTimestamp
    String charModificationTimestamp
    String correlationId

    // Output variables
    String status = Status.ERROR.getStatus()
    String statusDescription
    // + partyID that is already defined in input
    String RTDMHostname
    RTDMTable outOfferDeact
    Long httpResponseCode;
    String errorMessage;

    // Logger
    private static final Logger log = Logger.getLogger('groovyLog');

    // Event info
    EventInfo evtInfo;

    // Constants
    private final String resourcePrefix = "/RTDM/rest/runtime/decisions/"
    private final Long version = 1;
    private final String clientTimeZone = "Asia/Manila";

    @Override
    void run() {

        //start log info
        log.info("Starting process CallRTDMCampaign...");

        log.info("CallRTDMCampaign - host: $rtdmHost");
        log.info("CallRTDMCampaign - resource prefix: $resourcePrefix");
        log.info("CallRTDMCampaign - partyID: $partyID");
        log.info("CallRTDMCampaign - limitID: $limitID");
        log.info("CallRTDMCampaign - eventTimestamp: $eventTimestamp");
        log.info("CallRTDMCampaign - modification_timestamp: $modification_timestamp");
        log.info("CallRTDMCampaign - eventName: $eventName");

        if (rtdmEventId != null && !rtdmEventId.isEmpty()) {
            if (eventTimestamp instanceof GregorianCalendar) {
                charEventTimestamp = DateTimeFormatter.ISO_OFFSET_DATE_TIME.format(((GregorianCalendar) eventTimestamp).toZonedDateTime())
            }
            log.info("CallRTDMCampaign - charEventTimestamp: $charEventTimestamp");
            if (modification_timestamp instanceof GregorianCalendar) {
                charModificationTimestamp = DateTimeFormatter.ISO_OFFSET_DATE_TIME.format(((GregorianCalendar) modification_timestamp).toZonedDateTime())
            }
            log.info("CallRTDMCampaign - charModificationTimestamp: $charModificationTimestamp");

            // mapping of variables to request object - CHANGE THIS PART FOR MAPPING DIFFERENT CLIENT INPUTS
            Request request = new Request(
                    version: version,
                    correlationId: correlationId,
                    clientTimeZone: clientTimeZone,
                    inputs: new RequestInputs(
                            partyID: partyID,
                            limitID: limitID,
                            modification_timestamp: charModificationTimestamp,
                            eventTimestamp: charEventTimestamp,
                            eventName: eventName
                    )
            )

            ObjectMapper mapper = new ObjectMapper();
            String jsonMessage = mapper.writeValueAsString(request)
            byte[] data = jsonMessage.getBytes(StandardCharsets.UTF_8)

            // Setting API variables
            String uri = rtdmHost + resourcePrefix + rtdmEventId;
            log.info("CallRTDMCampaign - Endpoint URL: $uri");
            log.info("CallRTDMCampaign - Request: $jsonMessage");

            HttpCallResponse httpCallResponse = HttpUtils.runHttpProcess(
                    evtInfo.getEventName(),
                    evtInfo.getSimulationDate().getTime(),
                    uri,
                    null,
                    [(HttpUtils.CONTENT_TYPE): "application/json"],
                    RequestMethod.POST,
                    data
            );

            httpResponseCode = httpCallResponse.getHttpResponseCode();
            status = httpCallResponse.getStatus().getStatus();
            errorMessage = httpCallResponse.getErrorMessage();
            String responseString = httpCallResponse.getResponse()
            if (responseString == null) {
                return
            }

            log.info("CallRTDMCampaign - httpResponseCode:" + httpResponseCode);
            log.info("CallRTDMCampaign - response:" + responseString);

            MyResponse responseObject = MappingUtils.mapToObject(responseString, MyResponse.class)
            if (responseObject == null) {
                status = Status.ERROR.getStatus();
                errorMessage = "Failed to map response. Wrong response data format."
                return
            }

            if (httpResponseCode == 201) {

                // setting values - CHANGE THIS TO REFLECT OUTPUT FROM RTDM PROCESS
                partyID = responseObject.outputs.partyID;
                status = responseObject.outputs.status;
                statusDescription = responseObject.outputs.statusDescription
                RTDMHostname = responseObject.outputs.RTDMHostname;
                outOfferDeact = toRTDMTable(responseObject.outputs.outOfferDeact);

            }
        } else {
            log.trace("CallRTDMCampaign - No input clients");
        }
    }

    JsonRtdmTable toJsonRtdmTable(RTDMTable rtdmTable) {
        JsonRtdmTable jsonTable = new JsonRtdmTable()

        for (Column column : rtdmTable.getColumns()) {
            Map<String, String> metadataItem = new HashMap<>();
            String jsonType = toJsonType(column.type.toString())
            metadataItem.put(column.name, jsonType);
            jsonTable.metadata.add(metadataItem);
        }

        for (Row row: rtdmTable.iterator()) {
            List<Object> columnValues = new ArrayList<>();
            for (Column column : rtdmTable.getColumns()) {
                String columnType = column.type.toString()
                String columnName = column.name
                Object columnValue = row.columnDataGet(columnName)
                Object value = null;
                if (columnValue != null) {
                    switch (columnType)  {
                        case "DATETIME":
                            value = convertDateTimeToString(columnValue)
                            break
                        default: value = columnValue
                    }
                }
                columnValues.add(value);
            }
            jsonTable.data.add(columnValues)
        }
        return jsonTable
    }

    static String toJsonType(String rtdmType) {
        switch (rtdmType) {
            case "STRING": return "string"
            case "BOOLEAN": return "boolean"
            case "FLOAT": return "decimal"
            case "INT": return "integer"
            case "DATETIME": return "datetime"
        }
        return "null"
    }

    static String convertDateTimeToString(Object object) {
        if (object instanceof GregorianCalendar == false) {
            throw new RuntimeException("Unable to convert calendar as it is not a GregorianCalendar");
        }
        GregorianCalendar cal = (GregorianCalendar) object;
        SimpleDateFormat f = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        f.setCalendar(cal);
        String str = f.format(cal.getTime());
        /*return str.substring(0, 26) + ":" + str.substring(26) + "[" + cal.getTimeZone().getID() + "]";*//*original*/
        return str.substring(0, 23) + "Z";
    }

    RTDMTable toRTDMTable(JsonRtdmTable jsonRtdmTable) {
        if(jsonRtdmTable == null) {
            return null
        }
        RTDMTable rtdmTable = new RTDMTable();
        List<Map<String, DataTypes>> columnNamesAndTypes = new ArrayList<>();
        jsonRtdmTable.metadata.each { metadataItem ->
            metadataItem.each {columnName , jsonType ->
                DataTypes dataType = toDataType(jsonType);

                Map<String, DataTypes> columnNameAndType = new HashMap<String, DataTypes>();
                columnNameAndType.put(columnName, dataType);
                columnNamesAndTypes.add(columnNameAndType);

                rtdmTable.columnAdd(columnName, dataType, Collections.emptyList());
            }
        }

        jsonRtdmTable.data.each { jsonRow ->
            Row newRow = rtdmTable.rowAdd();
            jsonRow.eachWithIndex { Object value, int i ->
                Map<String, DataTypes> columnNameAndType = columnNamesAndTypes.get(i);
                Map.Entry<String, DataTypes> entry = columnNameAndType.entrySet().iterator().next();
                String columnName = entry.getKey()
                DataTypes columnType = entry.getValue()
                Object rtdmValue = toRtdmValue(value, columnType)
                newRow.columnDataSet(columnName, rtdmValue);
            }
        }

        return rtdmTable
    }

    static DataTypes toDataType(String jsonType) {
        switch (jsonType) {
            case "string": return DataTypes.STRING;
            case "boolean": return DataTypes.BOOLEAN;
            case "decimal": return DataTypes.FLOAT;
            case "integer": return DataTypes.INT;
            case "dateTime": return DataTypes.DATETIME;
        }
        return null;
    }

    static Object toRtdmValue(Object value, DataTypes dataType) {
        if (value == null) {
            return null
        }
        if (value.getClass() == java.lang.Boolean) {
            return value
        }
        if (value.getClass() == java.lang.String) {
            if (dataType == DataTypes.DATETIME) {
                return parseToCalendar(value.toString())
            }
            return value
        }
        if (value.getClass() == java.math.BigDecimal) {
            return value.doubleValue()
        }
        if (value.getClass() == java.lang.Integer || value.getClass() == java.lang.Long) {
            return value.longValue()
        }
        return null
    }

    static Calendar parseToCalendar(String dateStr) {
        DateTimeFormatter formatter = new DateTimeFormatterBuilder()
                .appendPattern("yyyy-MM-dd'T'HH:mm:ss.SSS")
                .optionalStart()
                .appendPattern("XXX")
                .optionalEnd()
                .toFormatter();

        TemporalQuery<OffsetDateTime> offsetQuery = { temporal -> OffsetDateTime.from(temporal) } as TemporalQuery
        TemporalQuery<LocalDateTime> localDateTimeQuery = { temporal -> LocalDateTime.from(temporal) } as TemporalQuery

        TemporalAccessor parsed = formatter.parseBest(dateStr, offsetQuery, localDateTimeQuery)

        ZonedDateTime zonedDateTime;
        if (parsed instanceof OffsetDateTime) {
            zonedDateTime = ((OffsetDateTime) parsed).toZonedDateTime();
        } else {
            LocalDateTime ldt = (LocalDateTime) parsed;
            zonedDateTime = ldt.atZone(ZoneId.systemDefault());
        }

        return GregorianCalendar.from(zonedDateTime);
    }
}

// example of request - CHANGE THIS PART FOR MAPPING DIFFERENT CLIENT INPUTS
class Request implements Serializable {
    Long version
    String correlationId
    String clientTimeZone
    RequestInputs inputs
}

class RequestInputs implements Serializable {
    String partyID
    String limitID
    String modification_timestamp
    String eventTimestamp
    String eventName
}

// example of response - CHANGE THIS TO REFLECT OUTPUT FROM RTDM PROCESS
class MyResponse implements Serializable {
    Long version;
    String correlationId;
    String startTimestamp;
    String endTimestamp;
    ResponseItem outputs;
}

class ResponseItem implements Serializable {
    String partyID;
    String status;
    String statusDescription;
    String RTDMHostname;
    JsonRtdmTable outOfferDeact;
}

@JsonSerialize(using = JsonRtdmTableSerializer)
@JsonDeserialize(using = JsonRtdmTableDeserializer)
class JsonRtdmTable implements Serializable {
    List<Map<String, String>> metadata = new ArrayList<>();
    List<List<Object>> data = new ArrayList<>();
}

class JsonRtdmTableSerializer extends JsonSerializer<JsonRtdmTable> {
    @Override
    void serialize(JsonRtdmTable value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        gen.writeStartArray()

        gen.writeStartObject()
        gen.writeObjectField("metadata", value.metadata)
        gen.writeEndObject()

        gen.writeStartObject()
        gen.writeObjectField("data", value.data)
        gen.writeEndObject()

        gen.writeEndArray()
    }
}

class JsonRtdmTableDeserializer extends JsonDeserializer<JsonRtdmTable> {
    @Override
    JsonRtdmTable deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JsonProcessingException {
        JsonRtdmTable table = new JsonRtdmTable()
        ObjectMapper mapper = (ObjectMapper) p.getCodec()
        JsonNode node = mapper.readTree(p)
        if (node.isArray()) {
            node.each { JsonNode element ->
                if (element.has("metadata")) {
                    table.metadata = mapper.convertValue(element.get("metadata"), List)
                } else if (element.has("data")) {
                    table.data = mapper.convertValue(element.get("data"), List)
                }
            }
        }
        return table
    }
}