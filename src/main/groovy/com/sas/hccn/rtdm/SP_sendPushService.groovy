package com.sas.hccn.rtdm;

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.ObjectMapper
import com.homecredit.sas.utils.PropertiesUtils
import com.homecredit.sas.utils.model.PropertiesResponse
import org.apache.log4j.Logger

import java.nio.charset.StandardCharsets
import java.time.format.DateTimeFormatter

/**
 * @version 23/03/15-005
 */
class GetDetailForClientList implements Runnable {

    // Input variables
    Long cuid_push;
    String externalId;
    String systemCode;
    String messageCode;
    Object expires;
    String priority;
    String reportLevel;
    String reportContentType;
    String text;
    String logicalApplication;

    String CONTRACT_NUM;
    Long CUID;
    String FCM_TITLE;
    String SAS_LINK;
    String SAS_MESSAGE_TYPE;
    String SAS_PICTURE;
    String SAS_TEMPLATE_ID;
    String SCREEN_LABEL;

    //NEW V2 Variables
    def now = new Date();
    def CREATED_TIMESTAMP = now.toTimestamp().getTime();
    String ID_MYHC_EID;
    String ID_MYHC_MESSAGE_SUBTYPE;
    String ID_MYHC_MESSAGE_TYPE;
    String ID_MYHC_SHORT_MESSAGE;
    String FCM_MUTABLE_CONTENT;

    // Output variables
    String status = "ERROR";            // either "OK" or "ERROR"
    String errorMessage;

    // Internal variables
    private static final Logger log = Logger.getLogger('groovyLog');
    private static final Logger requestReplyLog = Logger.getLogger('groovyRequestReply')

    private final String CONFIG_FILE = "/sas/groovy/Connections/rabbitmq.properties";
    private Properties config = new Properties();

    @Override
    void run() {
        //start log info
        log.info("Starting process SendPush to RabbitMQ...");
        log.info("SendPush - cuid_push: $cuid_push");
        log.info("SendPush - externalId: $externalId");
        log.info("SendPush - systemCode: $systemCode");
        log.info("SendPush - messageCode: $messageCode");
        log.info("SendPush - expires: $expires");
        log.info("SendPush - priority: $priority");
        log.info("SendPush - reportLevel: $reportLevel");
        log.info("SendPush - reportContentType: $reportContentType");
        log.info("SendPush - text: $text");
        log.info("SendPush - logicalApplication: $logicalApplication");
        log.info("SendPush - CONTRACT_NUM: $CONTRACT_NUM");
        log.info("SendPush - CUID: $CUID");
        log.info("SendPush - FCM_TITLE: $FCM_TITLE");
        log.info("SendPush - SAS_LINK: $SAS_LINK");
        log.info("SendPush - SAS_MESSAGE_TYPE: $SAS_MESSAGE_TYPE");
        log.info("SendPush - SAS_PICTURE: $SAS_PICTURE");
        log.info("SendPush - SAS_TEMPLATE_ID: $SAS_TEMPLATE_ID");
        log.info("SendPush - SCREEN_LABEL: $SCREEN_LABEL");

        //NEW V2 Log
        log.info("SendPush - CREATED_TIMESTAMP: $CREATED_TIMESTAMP");
        log.info("SendPush - ID_MYHC_EID: $ID_MYHC_EID");
        log.info("SendPush - ID_MYHC_MESSAGE_SUBTYPE: $ID_MYHC_MESSAGE_SUBTYPE");
        log.info("SendPush - ID_MYHC_MESSAGE_TYPE: $ID_MYHC_MESSAGE_TYPE");
        log.info("SendPush - ID_MYHC_SHORT_MESSAGE: $ID_MYHC_SHORT_MESSAGE");
        log.info("SendPush - FCM_MUTABLE_CONTENT: $FCM_MUTABLE_CONTENT");

        List<String> emptyFields = new ArrayList<>()
        if (!externalId) {
            emptyFields.add("externalId")
        }
        if (!systemCode) {
            emptyFields.add("systemCode")
        }
        if (!messageCode) {
            emptyFields.add("messageCode")
        }
        if (!text) {
            emptyFields.add("text")
        }
        if (!logicalApplication) {
            emptyFields.add("logicalApplication")
        }
        if (!emptyFields.isEmpty()) {
            errorMessage = "Following mandatory fields are null or empty: " + String.join(", ", emptyFields)
            return
        }

        PropertiesResponse propertiesResponse = PropertiesUtils.getProperties(CONFIG_FILE)
        if (propertiesResponse.getProperties() == null) {
            status = propertiesResponse.getStatus().getStatus();
            errorMessage = propertiesResponse.getErrorMessage();
            return;
        }
        Properties config = propertiesResponse.getProperties();

        String host = config.getProperty("rabbitmq.host");
        String routingKey = config.getProperty("rabbitmq.push-routing-key");
        String username = config.getProperty("rabbitmq.username");
        String password = config.getProperty("rabbitmq.password");
        String virtualHost = config.getProperty("rabbitmq.virtual-host");
        String exchange = config.getProperty("rabbitmq.exchange");

        log.info("SendPush - RabbitMQ configuration: host = $host, routingKey = $routingKey, virtualHost = $virtualHost, exchange = $exchange")
        Message message = new Message()

        message.setToken(CUID.toString())
        message.setExternalId(externalId)
        message.setSystemCode(systemCode)
        message.setMessageCode(messageCode)
        if (expires instanceof GregorianCalendar) {
            message.setExpires(DateTimeFormatter.ISO_OFFSET_DATE_TIME.format(((GregorianCalendar) expires).toZonedDateTime()))
        }
        message.setPriority(priority)
        message.setReportLevel(reportLevel)
        message.setReportContentType(reportContentType)
        message.setText(text)
        message.setLogicalApplication(logicalApplication)
        List<Attribute> attributes = new ArrayList<Attribute>();
        //NOTIFICATION PAYLOAD
        /*
        if (CONTRACT_NUM) attributes.add(new Attribute(type: "CONTRACT_NUM", value: CONTRACT_NUM))
        if (CUID) attributes.add(new Attribute(type: "CUID", value: CUItextD.toString()))
        if (FCM_TITLE) attributes.add(new Attribute(type: "FCM_TITLE", value: FCM_TITLE))
        if (SAS_LINK) attributes.add(new Attribute(type: "SAS_LINK", value: SAS_LINK))
        if (SAS_MESSAGE_TYPE) attributes.add(new Attribute(type: "SAS_MESSAGE_TYPE", value: SAS_MESSAGE_TYPE))
        if (SAS_PICTURE) attributes.add(new Attribute(type: "FCM_IMAGE", value: SAS_PICTURE))
        if (SAS_TEMPLATE_ID) attributes.add(new Attribute(type: "SAS_TEMPLATE_ID", value: SAS_TEMPLATE_ID))
        if (SCREEN_LABEL) attributes.add(new Attribute(type: "SCREEN_LABEL", value: SCREEN_LABEL))
        */
        //DATA PAYLOAD
        if (CONTRACT_NUM) //MANDATORY, CAN BE FILLED WITH ANY STRING
        {
            attributes.add(new Attribute(type: "CONTRACT_NUM", fullValue: CONTRACT_NUM))
        }
        else
        {
            attributes.add(new Attribute(type: "CONTRACT_NUM", fullValue: "0"))
        }

        if (CUID) //NON MADANTORY FOR MOBILE
        {
            attributes.add(new Attribute(type: "CUID", fullValue: CUID))
        }
        else
        {
            attributes.add(new Attribute(type: "CUID", fullValue: ""))
        }
        //FOR IOS
        if (FCM_TITLE)
        {
            attributes.add(new Attribute(type: "FCM_TITLE", fullValue: FCM_TITLE))
        }
        else
        {
            attributes.add(new Attribute(type: "FCM_TITLE", fullValue: ""))
        }
        if (FCM_MUTABLE_CONTENT)
        {
            attributes.add(new Attribute(type: "FCM_MUTABLE_CONTENT", fullValue: FCM_MUTABLE_CONTENT))
        }
        else
        {
            attributes.add(new Attribute(type: "FCM_MUTABLE_CONTENT", fullValue: "true"))
        }
        if (SAS_LINK)
        {
            attributes.add(new Attribute(type: "SAS_LINK", fullValue: SAS_LINK))
            attributes.add(new Attribute(type: "GMA_DEEPLINK", fullValue: SAS_LINK))
            attributes.add(new Attribute(type: "GMA_CLICK_ACTION", fullValue: "FLUTTER_NOTIFICATION_CLICK"))
            attributes.add(new Attribute(type: "GMA_NOTIFICATION_TYPE", fullValue: "deeplink"))
        }
        else
        {
            attributes.add(new Attribute(type: "SAS_LINK", fullValue: ""))
        }
        /*
        if (SAS_MESSAGE_TYPE)
        {
        	attributes.add(new Attribute(type: "SAS_MESSAGE_TYPE", fullValue: SAS_MESSAGE_TYPE))
        }
        else
        {
        	attributes.add(new Attribute(type: "SAS_MESSAGE_TYPE", fullValue: ""))
        }
        */
        if (SAS_PICTURE)
        {
            attributes.add(new Attribute(type: "FCM_IMAGE", fullValue: SAS_PICTURE))
        }
        else
        {
            attributes.add(new Attribute(type: "FCM_IMAGE", fullValue: ""))
        }
        if (SAS_TEMPLATE_ID)
        {
            attributes.add(new Attribute(type: "SAS_TEMPLATE_ID", fullValue: SAS_TEMPLATE_ID))
        }
        else
        {
            attributes.add(new Attribute(type: "SAS_TEMPLATE_ID", fullValue: ""))
        }
        if (SCREEN_LABEL)
        {
            attributes.add(new Attribute(type: "SCREEN_LABEL", fullValue: SCREEN_LABEL))
        }
        else
        {
            attributes.add(new Attribute(type: "SCREEN_LABEL", fullValue: ""))
        }
        message.setAttributes(attributes)
        MessageWrapper messageWrapper = new MessageWrapper()
        messageWrapper.setMessage(Arrays.asList(message))

        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        String jsonMessage = mapper.writeValueAsString(messageWrapper)
        log.info("SendPush - Serialized message:" + jsonMessage)

        Map<String, Object> headerMap = new HashMap<String, Object>();
        headerMap.put("SYSTEM_CODE", systemCode);
        headerMap.put("REQUEST_ID", externalId);
        headerMap.put("priority", 0);
        headerMap.put("cuid", CUID.toString());
        headerMap.put("CorrelationID", externalId)
        headerMap.put("Type", "JMSType")
        BasicProperties messageProperties = new AMQP.BasicProperties.Builder()
                .contentType("application/json")
                .headers(headerMap)
                .build();

        ConnectionFactory factory = new ConnectionFactory();
        factory.setHost(host);
        factory.setUsername(username);
        factory.setPassword(password);
        factory.setVirtualHost(virtualHost);
        Connection connection;
        try {
            connection = factory.newConnection();
            Channel channel = connection.createChannel();
            channel.basicPublish(exchange, routingKey, messageProperties, jsonMessage.getBytes(StandardCharsets.UTF_8));
            log.info("SendPush - Message sent successfully");
            requestReplyLog.info("SendPush - Sent message: $jsonMessage to RabbitMQ (host = $host, routingKey = $routingKey, virtualHost = $virtualHost, exchange = $exchange)")
            status = "OK";
        } catch (Exception e) {
            log.error("SendPush - Failed to send message: " + e.getMessage())
            status = "ERROR";
            throw e;
        } finally {
            if (connection != null) {
                connection.close()
            }
        }
    }
}

class MessageWrapper implements Serializable {
    List<Message> message;
}

class Message implements Serializable {
    String cuid;
    String externalId;
    String systemCode;
    String messageCode;
    String expires;
    List<Attribute> attributes;
    String priority;
    String reportLevel;
    String reportContentType;
    String text;
    String logicalApplication;
}

class Attribute implements Serializable {
    String type;
    String value;
    String fullValue;
}
