package com.sas.hccn.rtdm

import com.fasterxml.jackson.databind.ObjectMapper
import com.homecredit.sas.utils.HttpUtils
import com.homecredit.sas.utils.MappingUtils
import com.homecredit.sas.utils.PropertiesUtils
import com.homecredit.sas.utils.enumeration.RequestMethod
import com.homecredit.sas.utils.enumeration.Status
import com.homecredit.sas.utils.model.HttpCallResponse
import com.homecredit.sas.utils.model.PropertiesResponse
import com.sas.rtdm.implementation.engine.EventInfo
import org.apache.log4j.Logger

class GetDetailForClientList implements Runnable {

    // Input variables
    String id_cuid;

    // Output variables
    String status = Status.ERROR.getStatus()
    Long httpResponseCode;    // HTTP response code
    String errorMessage;

    String offerId;
    String limitId;
    String offerTypeCode;
    String offerTypeName;
    String offerCategory;
    String partyId;
    String offerStatus;
    Long maxCreditAmount;
    Long maxMonthlyInst;
    Long minCashPayment;
    Long relativeMinCashPayment;
    String offerValidTo;
    Long pricingCategory;
    String crmPilotCode;
    String jointLendingPartnerCode;
    Long offerIdSas;
    String sourceProcessId;
    String creationTimestamp;
    String modificationTimestamp;
    String initTransactionType;
    Long priority;
    String entryPoint;
    String productType;
    String lastResponse;
    String possibleResponses;
    String acqChannelCodes;
    String productCodes;
    String productSetCode;
    String relationId;
    String relationCode;
    String pricingStrategy;
    String minMonthlyInst;
    String accountNumber;
    Long maxTenor;
    Long minCreditAmount;
    Long maxCashPaymentRel;
    Long minTenor;
    String limitCategory;
    String commodityCategoryGroup;

    // Logger
    private static final Logger log = Logger.getLogger('groovyLog');

    // Event info
    EventInfo evtInfo;

    // Constants
    private final String resourcePrefix = "/ofs/offers/"

    // Variables from properties
    private String host;

    private final String CONFIG_FILE = "/sas/groovy/Connections/ofs.properties";

    @Override
    void run() {

        //start log info
        log.info("Starting process GetOffers...");
        log.info("GetOffers - cuid: $id_cuid");

        PropertiesResponse propertiesResponse = PropertiesUtils.getProperties(CONFIG_FILE)
        if (propertiesResponse.getProperties() == null) {
            status = propertiesResponse.getStatus().getStatus();
            errorMessage = propertiesResponse.getErrorMessage();
            return;
        }
        Properties config = propertiesResponse.getProperties();

        host = config.getProperty("ofs.host");

        log.info("GetOffers - host: $host");
        log.info("GetOffers - resource prefix: $resourcePrefix");

        if (id_cuid != null && !id_cuid.isEmpty()) {

            // Setting API variables
            String uri = host + resourcePrefix + id_cuid;
            log.info("GetOffers - Endpoint URL: $uri");

            HttpCallResponse httpCallResponse = HttpUtils.runHttpProcess(
                    evtInfo.getEventName(),
                    evtInfo.getSimulationDate().getTime(),
                    uri,
                    null,
                    [(HttpUtils.CONTENT_TYPE): "application/json"],
                    RequestMethod.GET,
                    null
            );

            httpResponseCode = httpCallResponse.getHttpResponseCode();
            status = httpCallResponse.getStatus().getStatus();
            errorMessage = httpCallResponse.getErrorMessage();
            String responseString = httpCallResponse.getResponse()
            if (responseString == null) {
                return
            }

            log.trace("GetOffers - httpResponseCode:" + httpResponseCode);
            log.trace("GetOffers - response:" + responseString);

            OfferResponse offerResponse = MappingUtils.mapToObject(responseString, OfferResponse.class)
            if (offerResponse == null) {
                status = Status.ERROR.getStatus();
                errorMessage = "Failed to map response. Wrong response data format."
                return
            }

            if (httpResponseCode == 200) {
                offerId = offerResponse.offerId;
                limitId = offerResponse.limitId;
                offerTypeCode = offerResponse.offerTypeCode;
                offerTypeName = offerResponse.offerTypeName;
                offerCategory = offerResponse.offerCategory;
                partyId = offerResponse.partyId;
                offerStatus = offerResponse.offerStatus;
                maxCreditAmount = offerResponse.maxCreditAmount;
                maxMonthlyInst = offerResponse.maxMonthlyInst;
                minCashPayment = offerResponse.minCashPayment;
                relativeMinCashPayment = offerResponse.relativeMinCashPayment;
                offerValidTo = offerResponse.offerValidTo;
                pricingCategory = offerResponse.pricingCategory;
                crmPilotCode = offerResponse.crmPilotCode;
                jointLendingPartnerCode = offerResponse.jointLendingPartnerCode;
                offerIdSas = offerResponse.offerIdSas;
                sourceProcessId = offerResponse.sourceProcessId;
                creationTimestamp = offerResponse.creationTimestamp;
                modificationTimestamp = offerResponse.modificationTimestamp;
                initTransactionType = offerResponse.initTransactionType;
                priority = offerResponse.priority;
                entryPoint = offerResponse.entryPoint;
                productType = offerResponse.productType;
                lastResponse = offerResponse.getLastResponse();
                possibleResponses = offerResponse.getPossibleResponses();
                acqChannelCodes = offerResponse.getAcqChannelCodes();
                productCodes = offerResponse.getProductCodes();
                productSetCode = offerResponse.productSetCode;
                relationId = offerResponse.relationId;
                relationCode = offerResponse.relationCode;
                pricingStrategy = offerResponse.pricingStrategy;
                minMonthlyInst = offerResponse.minMonthlyInst;
                accountNumber = offerResponse.accountNumber;
                maxTenor = offerResponse.maxTenor;
                minCreditAmount = offerResponse.minCreditAmount;
                maxCashPaymentRel = offerResponse.maxCashPaymentRel;
                minTenor = offerResponse.minTenor;
                limitCategory = offerResponse.limitCategory;
                commodityCategoryGroup = offerResponse.commodityCategoryGroup;
            }
        } else {
            log.trace("GetOffers - No input clients");
        }
    }
}

class OfferResponse implements Serializable {
    String offerId;
    String limitId;
    String offerTypeCode;
    String offerTypeName;
    String offerCategory;
    String partyId;
    String offerStatus;
    Long maxCreditAmount;
    Long maxMonthlyInst;
    Long minCashPayment;
    Long relativeMinCashPayment;
    String offerValidTo;
    Long pricingCategory;
    String crmPilotCode;
    String jointLendingPartnerCode;
    Long offerIdSas;
    String sourceProcessId;
    String creationTimestamp;
    String modificationTimestamp;
    String initTransactionType;
    Long priority;
    String entryPoint;
    String productType;
    LastResponse lastResponse;
    List<PossibleResponse> possibleResponses;
    List<String> acqChannelCodes;
    List<String> productCodes;
    String productSetCode;
    String relationId;
    String relationCode;
    String pricingStrategy;
    String minMonthlyInst;
    String accountNumber;
    Long maxTenor;
    Long minCreditAmount;
    Long maxCashPaymentRel;
    Long minTenor;
    String limitCategory;
    String commodityCategoryGroup;

    String getLastResponse() {
        if (lastResponse == null)
            return null;
        return new ObjectMapper().writeValueAsString(lastResponse)
    }

    String getPossibleResponses() {
        if (possibleResponses == null)
            return null;
        return new ObjectMapper().writeValueAsString(possibleResponses)
    }

    String getAcqChannelCodes() {
        if (acqChannelCodes == null)
            return null;
        return acqChannelCodes.join(",");
    }

    String getProductCodes() {
        if (productCodes == null)
            return null;
        return productCodes.join(",");
    }
}

class PossibleResponse implements Serializable {
    String responseId;
    String acqChannelCode;
    String responseText;
    String creationTimestamp;
    String modificationTimestamp;
}

class LastResponse implements Serializable {
    String id;
    String acqChannelCode;
    String responseText;
    String responseComment;
    String modificationTimestamp;
}
