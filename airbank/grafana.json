{"dashboard": {"id": null, "title": "Application Monitoring Dashboard", "panels": [{"title": "CPU Usage", "type": "graph", "targets": [{"expr": "system_cpu_usage", "legendFormat": "CPU Usage"}]}, {"title": "Memory Usage", "type": "graph", "targets": [{"expr": "jvm_memory_used_bytes", "legendFormat": "Used Memory"}, {"expr": "jvm_memory_committed_bytes", "legendFormat": "Committed Memory"}, {"expr": "jvm_memory_max_bytes", "legendFormat": "Max Memory"}]}, {"title": "Disk Usage", "type": "graph", "targets": [{"expr": "disk_free_bytes", "legendFormat": "Free Disk Space"}, {"expr": "disk_total_bytes", "legendFormat": "Total Disk Space"}]}, {"title": "Thread Count", "type": "graph", "targets": [{"expr": "jvm_threads_live_threads", "legendFormat": "Live Threads"}, {"expr": "jvm_threads_daemon_threads", "legendFormat": "<PERSON> Threads"}, {"expr": "jvm_threads_peak_threads", "legendFormat": "Peak Threads"}]}, {"title": "HTTP Server Request Metrics", "type": "graph", "targets": [{"expr": "http_server_requests_seconds_count", "legendFormat": "Request Count"}, {"expr": "http_server_requests_seconds_sum", "legendFormat": "Request Duration"}]}, {"title": "Custom Counters", "type": "graph", "targets": [{"expr": "passed_filter_events_total", "legendFormat": "Passed Filter Events"}, {"expr": "not_passed_filter_events_total", "legendFormat": "Not Passed Filter Events"}, {"expr": "failed_mapping_events_total", "legendFormat": "Failed Mapping Events"}]}], "time": {"from": "now-6h", "to": "now"}}, "folderId": null, "overwrite": false}