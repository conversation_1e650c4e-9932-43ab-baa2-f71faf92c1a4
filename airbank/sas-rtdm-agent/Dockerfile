# Use an official OpenJDK runtime as a base image
FROM openjdk:17-jdk-alpine

# Set the working directory inside the container
WORKDIR /usr/src/app

# Set environment variables (modify these based on your actual setup)
ENV JAVA_HOME=/usr/lib/jvm/java-17-openjdk
ENV APP_HOME=/usr/src/app
ENV AGENT_USER=root
ENV PROXY_HOST=""
ENV PROXY_PORT=""
ENV PROXY_USER=""
ENV PROXY_PASSWORD=""
ENV PROXY_REALM=""
ENV PROXY_ARGS=""
ENV PROXY_USER_ARGS=""

# Copy your application files (including the JAR, libraries, and configuration)
COPY . /usr/src/app

# Ensure that logs directory exists and set proper permissions
RUN mkdir -p /usr/src/app/logs && \
    chown -R $AGENT_USER /usr/src/app/logs

# Ensure startup script is executable if needed
# RUN chmod +x /usr/src/app/bin/startup.sh

# Optional: Create /var/log/upstart.log file if you want to simulate logging (for testing/debugging)
RUN touch /var/log/upstart.log && \
    chown $AGENT_USER /var/log/upstart.log

# Set up proxy arguments if needed (adjust if you're using actual proxy settings)
RUN if [ -n "$PROXY_HOST" ]; then \
    PROXY_ARGS="-Dhttp.proxyHost=$PROXY_HOST -Dhttp.proxyPort=$PROXY_PORT"; \
    fi

RUN if [ -n "$PROXY_USER" ]; then \
    PROXY_USER_ARGS="-Dhttp.proxyUser=$PROXY_USER -Dhttp.proxyPassword=$PROXY_PASSWORD -Dhttp.proxyRealm=$PROXY_REALM"; \
    fi

# Expose necessary ports (replace 8080 with your actual port)
EXPOSE 8080

# The main command to run the Java application (mimicking the upstart exec)
CMD ["sh"]
#CMD exec java -Xmx2226m \
#    -Dagent.shell.enable=false \
#    -Dlogging.config=$APP_HOME/config/logback-spring.xml \
#    -DAPP_HOME=$APP_HOME \
#    $PROXY_ARGS $PROXY_USER_ARGS \
#    -classpath "$APP_HOME/config:$APP_HOME/lib/*:$APP_HOME/libThirdParty/*" \
#    com.sas.mkt.apigw.sdk.streaming.agent.Application >> ${APP_HOME}/logs/console.log 2>&1