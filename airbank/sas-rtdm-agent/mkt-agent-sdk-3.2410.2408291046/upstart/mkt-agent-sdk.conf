#####################################################################
# put this in /etc/init to start the mkt-agent-sdk app as a service
# you can use the following commands
# initctl start mkt-agent-sdk
# initctl stop mkt-agent-sdk
# initctl status mkt-agent-sdk
#####################################################################

description "SAS Customer Intelligence 360 agent"

start on runlevel [2345]

stop on runlevel [06]

script
    #directory where Java is installed
    JAVA_HOME=
    if [ "xx" == "x${JAVA_HOME}x" ]
    then
        echo "[`date`] JAVA_HOME must be set to start SAS Customer Intelligence 360 agent" >> /var/log/upstart.log  
    fi 
    
    
    #directory where Customer Intelligence 360 agent is installed
    APP_HOME=
    if [ "xx" == "x${APP_HOME}x" ]
    then
        echo "[`date`] APP_HOME must be set to start SAS Customer Intelligence 360 agent" >> /var/log/upstart.log  
    fi 
    
    #user that will run the agent process
    AGENT_USER=root
    if [ "xx" == "x${AGENT_USER}x" ]
    then
        echo "[`date`] AGENT_USER must be set to start SAS Customer Intelligence 360 agent" >> /var/log/upstart.log  
    fi 
    
    #optional - set if agent using a proxy server to access Customer Intelligence 360
    PROXY_HOST=
    PROXY_PORT=
    if [ "xx" == "x${PROXY_HOST}x" ]
    then
        PROXY_ARGS=
    else
        PROXY_ARGS="-Dhttp.proxyHost=$PROXY_HOST -Dhttp.proxyPort=$PROXY_PORT"
    fi
    
    #optional - set if proxy server requires a user/password
    PROXY_USER=
    PROXY_PASSWORD=
    PROXY_REALM=       
    if [ "xx" == "x${PROXY_USER}x" ]
    then
        PROXY_USER_ARGS=
    else
        PROXY_USER_ARGS="-Dhttp.proxyUser=$PROXY_USER -Dhttp.proxyPassword=$PROXY_PASSWORD -Dhttp.proxyRealm=$PROXY_REALM"
    fi
    
    if [ ! -d $APP_HOME/logs ]
    then  
    	mkdir $APP_HOME/logs
    	chown -R $AGENT_USER $APP_HOME/logs
    fi
    
    exec su -m $AGENT_USER -c "$JAVA_HOME/bin/java -Xmx2226m -Dagent.shell.enable=false -Dlogging.config=$APP_HOME/config/logback-spring.xml -DAPP_HOME=$APP_HOME $PROXY_ARGS $PROXY_USER_ARGS -classpath $APP_HOME/config:$APP_HOME/lib/*:$APP_HOME/libThirdParty/* com.sas.mkt.apigw.sdk.streaming.agent.Application" >> ${APP_HOME}/logs/console.log 2>&1
    	
end script

respawn
respawn limit 3 180

pre-start script
    echo "[`date`] Starting SAS Customer Intelligence 360 agent" >> /var/log/upstart.log
end script

pre-stop script
    echo "[`date`] Stopping SAS Customer Intelligence 360 agent" >> /var/log/upstart.log
end script
    