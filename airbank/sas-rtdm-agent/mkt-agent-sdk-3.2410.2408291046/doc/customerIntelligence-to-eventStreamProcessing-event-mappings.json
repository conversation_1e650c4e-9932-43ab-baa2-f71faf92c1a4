{"eventStreamProcessingStream": [{"eventName": "load", "eventStreamProcessingWindow": "projectA/queryB/windowC", "eventStreamProcessingMappings": {"ID": "timestamp", "pubStr": "event_channel", "pubDate": "date", "pubDouble": "value", "pubInt64": "amount", "putTime": "time"}}, {"eventName": "click", "eventStreamProcessingWindow": "projectA/queryB/windowC", "eventStreamProcessingMappings": {"ID": "timestamp", "pubStr": "event_channel", "pubDate": "date", "pubDouble": "value", "pubInt64": "amount", "putTime": "time"}}]}