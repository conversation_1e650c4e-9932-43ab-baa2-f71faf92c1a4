#CI360 settings
ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
ci360.jwt=aGVzbG8
ci360.token=aGVzbG8

#Agent, agent.max_threads = maximal opened parallel threads for processing
agent.max_threads=5000

#Event
event.process_event_with_prefix=c_
event.ignore_events=c_activitystart,c_abtestpathassignment
event.direct_event=c_direct

#Database
db.server=TODO
db.service_name=TODO
db.database=TODO
db.user=TODO
db.pwd=aGVzbG8
db.init_connection_pool_size=10
db.max_connection_pool_size=200

#Proxy server
proxy.server=
proxy.port=0

#KAFKA
kafka.server=serverXXXXX:9093
kafka.topic=sentCampaingEmail topic
kafka.SSL_TRUSTSTORE_LOCATION_CONFIG=./cert/kafka-test.truststore.jks
kafka.SSL_TRUSTSTORE_PASSWORD_CONFIG=aGVzbG8
kafka.SSL_KEYSTORE_LOCATION_CONFIG=./cert/kafka-test.keystore.jks
kafka.SSL_KEYSTORE_PASSWORD_CONFIG=aGVzbG8
kafka.SSL_KEY_PASSWORD_CONFIG=aGVzbG8
kafka.SSL_PROTOCOL_CONFIG=SSL
kafka.SSL_ENABLED_PROTOCOLS_CONFIG=TLSv1.2
kafka.SSL_ENDPOINT_IDENTIFICATION_ALGORITHM_CONFIG=