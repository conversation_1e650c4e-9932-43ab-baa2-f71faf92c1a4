package cz.ab.ci360.agent_ch_stream;

import cz.ab.ci360.cache.*;
import cz.ab.ci360.cache.TaskCacheEntry;
import cz.ab.ci360.common.Util;
import org.json.JSONObject;

public class Test {
    public static void test(String payloadFile) throws Exception {
        //String file = "./payload_examples/"+fileName;
        String file = "./"+payloadFile;
        JSONObject payload = Util.loadJSONFile(file);
        String eventPayload=payload.toString();

        //test message processing
        Thread event = new Event(eventPayload);
        event.start();
    }

    public static TaskCacheEntry getTestTaskEntry(String taskId, String taskVersionId) {

        TaskCacheEntry te= new TaskCacheEntry();
        te.taskId=taskId;
        te.taskVersionId=taskVersionId;

        te.tsk_comm_chan_code.value = te.tsk_comm_chan_code.defaultValue;
        te.tsk_comm_camp_name.value = te.tsk_comm_camp_name.defaultValue;
        te.tsk_camp_type.value = te.tsk_camp_type.defaultValue;
        te.tsk_camp_subtype.value = te.tsk_camp_subtype.defaultValue;
        te.tsk_camp_product.value = "Product 1, Product 2, Product 3";
        te.tsk_camp_buss_cause_cd.value = te.tsk_camp_buss_cause_cd.defaultValue;
        te.tsk_camp_comm_type.value = te.tsk_camp_comm_type.defaultValue;
        te.tsk_cp_type.value = te.tsk_cp_type.defaultValue;
        te.tsk_cp_product.value = te.tsk_cp_product.defaultValue;

        return te;
    }

    public static CreativeCacheEntry getTestCreativeEntry(String creativeVersionId,String creativeId){
        CreativeCacheEntry te = new CreativeCacheEntry();
        te.creativeVersionId = creativeVersionId;
        te.creativeId = creativeId;
        te.cre_camp_cp_product.value = te.cre_camp_cp_product.defaultValue;
        return te;
    }

    public static CampaignCacheEntry  getTestCamapignEntry(String campaignCD) {
        CampaignCacheEntry te = new CampaignCacheEntry();
        te.campMessageCD = campaignCD;
        te.camp_name.value = te.camp_name.defaultValue;
        return te;
    }
}
