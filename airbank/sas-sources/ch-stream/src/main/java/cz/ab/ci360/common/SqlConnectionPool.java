package cz.ab.ci360.common;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 * Manage connections to db, implements connection pooling
 */
public class SqlConnectionPool {

    private static final Logger logger = (Logger) LoggerFactory.getLogger(SqlConnectionPool.class);
    static String conStr;
    private static int INITIAL_POOL_SIZE ;
    private static int MAX_POOL_SIZE ;
    private static int MAX_TIMEOUT=10;
    private static List<Connection> connectionPool;		//array of free prepared db connections
    private static List<Connection> usedConnections;	//array of currently used db connections
    private static String user;
    private static String password;
    public static String database;

    public static void init(Config log, boolean testMode) throws SQLException, ClassNotFoundException {

        user = log.loadKeyValue("db.user");
        password = log.loadKeyValueBase64("db.pwd");
        database = log.loadKeyValue("db.database");
        String server = log.loadKeyValue("db.server");
        String serviceName = log.loadKeyValue("db.service_name");

        INITIAL_POOL_SIZE = log.loadKeyValueAsInteger("db.init_connection_pool_size");
        MAX_POOL_SIZE = log.loadKeyValueAsInteger("db.max_connection_pool_size");


        try {
            conStr = "jdbc:oracle:thin:@//" + server + "/" + serviceName;
            if (testMode == false) {
                //create connection pool
                connectionPool = new ArrayList<>(INITIAL_POOL_SIZE);
                for (int i = 0; i < INITIAL_POOL_SIZE; i++) {
                    connectionPool.add(createConnection());
                }

                usedConnections = new ArrayList<>();
            }
        } catch(Exception e) {
            logger.error("ErrorCode:CH_01 - DB - not possible to establish DB connection, error: " + e.getMessage());
            throw e;
        }
    }

    public static Connection getConnection() throws SQLException, InterruptedException, ClassNotFoundException {

        //wait for free connection to db
        while(true)
        {

            synchronized(connectionPool) {
                boolean res=true;

                if (connectionPool.isEmpty()) {
                    if (usedConnections.size() < MAX_POOL_SIZE) {
                        connectionPool.add(createConnection());
                    } else {
                        //Maximum pool size reached, no available connections, wait
                        res=false;
                    }
                }

                if(res) {
                    Connection connection = connectionPool
                            .remove(connectionPool.size() - 1);

                    if(!connection.isValid(MAX_TIMEOUT)){
                        connection = createConnection();
                    }

                    usedConnections.add(connection);
                    return connection;
                }
            }

            Thread.sleep(100);
        }

    }

    public static boolean releaseConnection(Connection connection) {
        synchronized(connectionPool) {
            connectionPool.add(connection);
            return usedConnections.remove(connection);
        }
    }

    public static void stop() throws SQLException {

        synchronized(connectionPool) {
            for (Connection c : usedConnections) {
                releaseConnection(c);
            }
            usedConnections.clear();

            for (Connection c : connectionPool) {
                c.close();
            }
            connectionPool.clear();
        }
    }

    private static Connection createConnection() throws SQLException, ClassNotFoundException {
        Class.forName("oracle.jdbc.driver.OracleDriver");
        return  DriverManager.getConnection(conStr, user, password);
    }

    public static String getSizeInfo() {
        synchronized(connectionPool) {
            return String.format("Free connections: %s, Used connections: %s",connectionPool.size(),usedConnections.size() );
        }
    }

    public static int getPoolSize() {
        return connectionPool.size() ;
    }
    public static int getUsedSize() {
        return  usedConnections.size();
    }

}
