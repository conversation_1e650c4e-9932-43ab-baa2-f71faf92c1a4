package cz.ab.ci360.cache;

public class CacheVariable {
    public String value;
    public String defaultValue;
    public String name;     //db name

    public CacheVariable(String varName, String defValue) {
        value = null;
        defaultValue = defValue;
        name = varName;

    }
    public boolean hasValue() {
        if(value != null && !value.equals(""))
            return true;
        else
            return false;
    }
    public void setValueWithDefault(String newValue) {
        if(newValue != null && !newValue.equals(""))
            value = newValue;
        else
            value = defaultValue;
    }
}
