package cz.ab.ci360.cache;

import cz.ab.ci360.agent_ch_stream.CI360Api;
import cz.ab.ci360.common.Config;
import cz.ab.ci360.common.SqlConnectionPool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.sql.*;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;

public class TaskCache {
    private static final Logger logger =  LoggerFactory.getLogger(TaskCache.class);

    private static HashMap<String,TaskCacheEntry> cache= new HashMap<String,TaskCacheEntry>() ;	//Task CP cache !!!!!!!!!!
    private static HashMap<String, Object> locks = new HashMap<String, Object>();		//used to synchronizace processing each taskVersionId (one thread in one moment)

    private static String db;

    public static void init(Config config)  {

        //log configuration
        db = SqlConnectionPool.database;
    }

    public static TaskCacheEntry getTask(String taskVersionId, String taskId) {

        LocalDateTime start = LocalDateTime.now();
        //add taskVersionId to locks array if missing, used to synchronize processing of taskVersionId (one taskVersionId just in one thread in one moment)
        synchronized(locks) {
            if(locks.get(taskVersionId)==null)
            {
                locks.put(taskVersionId, new Object());
            }
        }

        synchronized(locks.get(taskVersionId)) {

            //get task from cache in memory
            TaskCacheEntry te = cache.get(taskVersionId);

            //task is in application cache
            if(te != null) {
                logger.debug("TaskVersionId=" +taskVersionId + " loaded from application cache, duration[ms]: " + Duration.between(start, LocalDateTime.now()).toMillis()  );
                return te;
            }



            //if not exist load from Task cache db table
            try {
                te = loadCacheTaskCp(taskVersionId,taskId);
                if(te!=null) {
                    logger.debug("TaskVersionId=" +taskVersionId + " loaded from TaskCache table cie_contact_history_stream_task_cp_cache, duration[ms]: " + Duration.between(start, LocalDateTime.now()).toMillis()  );
                    addTaskCacheEntryToCache(te);
                    return te;
                }
            } catch (Exception e) {
                logger.error("Error when loading CP from cie_contact_history_stream_task_cp_cache", e);
            }



            //if not exist read from CI 360 API
            try {
                te = getCI360ApiTaskCp(taskId, taskVersionId);

                if(te != null) {
                    logger.debug("TaskVersionId=" +taskVersionId + " loaded from CI360 API, and saved to TaskCache table, duration[ms]: " + Duration.between(start, LocalDateTime.now()).toMillis()  );
                    saveTaskCpToCache(te);
                    addTaskCacheEntryToCache(te);
                    return te;
                }
            } catch (Exception e) {
                logger.error("Exception()",e);
            }

            return null;		//error
        }

    }

    public static TaskCacheEntry getCI360ApiTaskCp(String taskId, String taskVersionId) {
        TaskCacheEntry te = new TaskCacheEntry();
        te.taskVersionId = taskVersionId;
        te.taskId = taskId;

        //get cp from ci360 api
        List<String> requiredCP = te.getCacheVariableList();
        HashMap<String, String > taskCP =  CI360Api.getTaskCustomProperties(taskId, requiredCP);

        //assign to CP
        te.tsk_comm_chan_code.setValueWithDefault(taskCP.get("tsk_comm_chan_code"));
        te.tsk_comm_camp_name.setValueWithDefault(taskCP.get("tsk_comm_camp_name"));
        te.tsk_camp_type.setValueWithDefault(taskCP.get("tsk_camp_type"));
        te.tsk_camp_subtype.setValueWithDefault(taskCP.get("tsk_camp_subtype"));
        te.tsk_camp_product.setValueWithDefault(taskCP.get("tsk_camp_product"));
        te.tsk_camp_buss_cause_cd.setValueWithDefault(taskCP.get("tsk_camp_buss_cause_cd"));
        te.tsk_camp_comm_type.setValueWithDefault(taskCP.get("tsk_camp_comm_type"));
        te.tsk_cp_type.setValueWithDefault(taskCP.get("tsk_cp_type"));
        te.tsk_cp_product.setValueWithDefault(taskCP.get("tsk_cp_product"));

        return te;
    }

    private static void addTaskCacheEntryToCache(TaskCacheEntry te) {
        cache.put(te.taskVersionId, te);
    }

    public static TaskCacheEntry loadCacheTaskCp( String taskVersionId, String taskId) throws SQLException, InterruptedException, ClassNotFoundException {
        return loadDatabaseTaskCp(db + ".cie_contact_history_stream_task_cp_cache",  taskVersionId,  taskId);
    }

    private static TaskCacheEntry loadDatabaseTaskCp(String dbTable, String taskVersionId, String taskId) throws SQLException, InterruptedException, ClassNotFoundException {

        //save message to DB table queue
        String sql="SELECT  attribute_nm,attribute_val  FROM " + dbTable + " WHERE task_version_id=?";

        Connection con = SqlConnectionPool.getConnection();
        try (
                PreparedStatement st = con.prepareStatement(sql);
        )
        {
            /*input parameter, batch size*/
            st.setString(1,taskVersionId);

            ResultSet rs = st.executeQuery() ;

            if(rs.next()!=false) {	//test if rs is not empty
                TaskCacheEntry te = new TaskCacheEntry();
                te.taskVersionId = taskVersionId;
                te.taskId=taskId;


                do {	//for each custom property
                    String name=rs.getString("attribute_nm");
                    String value=rs.getString("attribute_val");

                    if(name.equalsIgnoreCase(te.tsk_comm_chan_code.name))
                        te.tsk_comm_chan_code.value = value;
                    if(name.equalsIgnoreCase(te.tsk_comm_camp_name.name))
                        te.tsk_comm_camp_name.value = value;
                    if(name.equalsIgnoreCase(te.tsk_camp_type.name))
                        te.tsk_camp_type.value = value;
                    if(name.equalsIgnoreCase(te.tsk_camp_subtype.name))
                        te.tsk_camp_subtype.value = value;
                    if(name.equalsIgnoreCase(te.tsk_camp_product.name))
                        te.tsk_camp_product.value = value;
                    if(name.equalsIgnoreCase(te.tsk_camp_buss_cause_cd.name))
                        te.tsk_camp_buss_cause_cd.value = value;
                    if(name.equalsIgnoreCase(te.tsk_camp_comm_type.name))
                        te.tsk_camp_comm_type.value = value;
                    if(name.equalsIgnoreCase(te.tsk_cp_type.name))
                        te.tsk_cp_type.value = value;
                    if(name.equalsIgnoreCase(te.tsk_cp_product.name))
                        te.tsk_cp_product.value = value;


                    if(
                        te.tsk_comm_chan_code.hasValue() &&
                        te.tsk_comm_camp_name.hasValue() &&
                        te.tsk_camp_type.hasValue() &&
                        te.tsk_camp_subtype.hasValue() &&
                        te.tsk_camp_product.hasValue() &&
                        te.tsk_camp_buss_cause_cd.hasValue() &&
                        te.tsk_camp_comm_type.hasValue() &&
                        te.tsk_cp_type.hasValue() &&
                        te.tsk_cp_product.hasValue()
                    )
                    {
                        //all task custom properties loaded, no need to continue in iteration via remaining rows in table
                        return te;
                    }

                } while (rs.next());

            }
        }
        catch(SQLException e)
        {
            logger.error("Exception()", e);
        }
        finally{
            SqlConnectionPool.releaseConnection(con);
        }

        return null;
    }

    public static void saveTaskCpToCache(TaskCacheEntry te) throws SQLException, InterruptedException, ClassNotFoundException {

        String sql="INSERT INTO " + db + ".cie_contact_history_stream_task_cp_cache  (task_version_id,attribute_nm,attribute_val)    VALUES(?,?,?)";
        Connection con = SqlConnectionPool.getConnection();
        try (
                PreparedStatement st = con.prepareStatement(sql);
        )
        {
            addCPEntryToBatch(st, te.taskVersionId, te.tsk_comm_chan_code.name, te.tsk_comm_chan_code.value);
            addCPEntryToBatch(st, te.taskVersionId, te.tsk_comm_camp_name.name, te.tsk_comm_camp_name.value);
            addCPEntryToBatch(st, te.taskVersionId, te.tsk_camp_type.name, te.tsk_camp_type.value);
            addCPEntryToBatch(st, te.taskVersionId, te.tsk_camp_subtype.name, te.tsk_camp_subtype.value);
            addCPEntryToBatch(st, te.taskVersionId, te.tsk_camp_product.name, te.tsk_camp_product.value);
            addCPEntryToBatch(st, te.taskVersionId, te.tsk_camp_buss_cause_cd.name, te.tsk_camp_buss_cause_cd.value);
            addCPEntryToBatch(st, te.taskVersionId, te.tsk_camp_comm_type.name, te.tsk_camp_comm_type.value);
            addCPEntryToBatch(st, te.taskVersionId, te.tsk_cp_type.name, te.tsk_cp_type.value);
            addCPEntryToBatch(st, te.taskVersionId, te.tsk_cp_product.name, te.tsk_cp_product.value);

            st.executeBatch();
        }
        catch(SQLException e)
        {
            logger.error("Exception()",e);
        }
        finally{
            SqlConnectionPool.releaseConnection(con);
        }
    }


    private static void addCPEntryToBatch(PreparedStatement st , String taskVersionId , String cpName , String cpValue) throws SQLException {

        st.setString(1, taskVersionId);
        st.setString(2, cpName);
        if(cpValue == null) {
            st.setNull(3, Types.VARCHAR);
        }else {
            st.setString(3, cpValue);
        }
        st.addBatch();
    }
}
