package cz.ab.ci360.agent_ch_stream;

import cz.ab.ci360.common.Config;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import cz.ab.ci360.common.Http;

import java.util.HashMap;
import java.util.List;

public class CI360Api {
    private static final Logger logger = (Logger) LoggerFactory.getLogger(CI360Api.class);

    private static String ci360Server;
    private static String ci360Token;

    public static void init(Config config)  {

        //log configuration
        ci360Server = config.loadKeyValue("ci360.gatewayHost");
        ci360Token = config.loadKeyValueBase64("ci360.token");
    }

    public static HashMap<String, String> getTaskCustomProperties(String taskId, List<String> requiredCustProperties) {

        Http http = new Http(0,true);
        String url="https://" + ci360Server  + "/marketingDesign/tasks/"+taskId;
        String[] resp= http.get(url, "application/json", "Bearer "+ ci360Token);

        int httpStatus = Integer.valueOf(resp[0]);
        String httpResponse=resp[1];

        //init TaskEntry and set default values
        HashMap<String, String> custProperties = new HashMap<String, String>();

        try {

            logger.debug("Task API json: " + httpResponse);

            //parse from json TASK cp
            JSONObject task = new JSONObject(httpResponse);

            JSONArray cp = task.getJSONArray("customProperties");

            //for each custom property
            for (int i = 0 ; i < cp.length(); i++) {
                JSONObject obj = cp.getJSONObject(i);
                String cpName = obj.getString("propertyName").toLowerCase();

                if(requiredCustProperties.contains(cpName)) {
                    //required custom properties
                    String value = getCustPropertyFromApiJson(obj);
                    custProperties.put(cpName, value);
                }
            }
        }
        catch(Exception e)
        {
            logger.error("Exception()",e);
        }

        return custProperties;
    }

    public static HashMap<String, String> getCreativeAttributes(String creativeId, List<String> requiredAttributes) {

        Http http = new Http(0,true);
        String url="https://" + ci360Server  + "/marketingDesign/creatives/" + creativeId;
        String[] resp= http.get(url, "application/json", "Bearer "+ ci360Token);

        int httpStatus = Integer.valueOf(resp[0]);
        String httpResponse=resp[1];

        //init TaskEntry and set default values
        HashMap<String, String> attributes = new HashMap<String, String>();

        try {

            logger.debug("Creative API json: " + httpResponse);

            //parse from json Creative Attributes
            JSONObject creative = new JSONObject(httpResponse);

            JSONArray cp = creative.getJSONArray("attributes");

            //for each attribute
            for (int i = 0 ; i < cp.length(); i++) {
                JSONObject obj = cp.getJSONObject(i);
                String attrName = obj.getString("identityCode").toLowerCase();

                if(requiredAttributes.contains(attrName)) {
                    //required attribute
                    String value = obj.getString("value").toString();
                    attributes.put(attrName, value);
                }
            }
        }
        catch(Exception e)
        {
            logger.error("Exception()",e);
        }

        return attributes;
    }

    private static String getCustPropertyFromApiJson(JSONObject obj) {
        JSONArray ar = obj.getJSONArray("propertyValue");
        String value = ar.get(0).toString();
        if(value.equals("null")) value = null;
        return value;
    }

}
