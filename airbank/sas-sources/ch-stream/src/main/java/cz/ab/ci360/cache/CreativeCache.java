package cz.ab.ci360.cache;

import cz.ab.ci360.agent_ch_stream.CI360Api;
import cz.ab.ci360.common.Config;
import cz.ab.ci360.common.SqlConnectionPool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;

public class CreativeCache {
    private static final Logger logger =  LoggerFactory.getLogger(CreativeCache.class);

    private static HashMap<String,CreativeCacheEntry> cache= new HashMap<String, CreativeCacheEntry>();	//Creative CP cache !!!!!!!!!!
    private static HashMap<String, Object> locks = new HashMap<String, Object>();		//used to synchronizace processing each creativeVersionId (one thread in one moment)

    private static String db;

    public static void init(Config config)  {

        //log configuration
        db = SqlConnectionPool.database;
    }

    public static CreativeCacheEntry getCreative(String creativeVersionId, String creativeId) {

        if(creativeId == null || creativeId.equals("")) {
            logger.debug("creativeId not available, skip creative cache...");
            CreativeCacheEntry ce = new CreativeCacheEntry();
            return ce;
        }


        LocalDateTime start = LocalDateTime.now();
        //add creativeVersionId to locks array if missing, used to synchronize processing of creativeVersionId (one creativeVersionId just in one thread in one moment)
        synchronized(locks) {
            if(locks.get(creativeVersionId)==null)
            {
                locks.put(creativeVersionId, new Object());
            }
        }

        synchronized(locks.get(creativeVersionId)) {

            //get creative from cache in memory
            CreativeCacheEntry te = cache.get(creativeVersionId);

            //creative is in application cache
            if(te != null) {
                logger.debug("CreativeVersionId=" + creativeVersionId + " loaded from application cache, duration[ms]: " + Duration.between(start, LocalDateTime.now()).toMillis()  );
                return te;
            }


            //if not exist load from Creative cache db table
            try {
                te = loadCacheCreativeCp(creativeVersionId,creativeId);
                if(te!=null) {
                    logger.debug("CreativeVersionId=" + creativeVersionId + " loaded from CreativeCache table cie_contact_history_stream_CRT_cp_cache, duration[ms]: " + Duration.between(start, LocalDateTime.now()).toMillis()  );
                    addCreativeCacheEntryToCache(te);
                    return te;
                }
            } catch (Exception e) {
                logger.error("Error when loading CP from cie_contact_history_stream_CRT_cp_cache", e);
            }


            //if not exist read from CI 360 API
            try {
                te = getCI360ApiCreativeCp(creativeId, creativeVersionId);

                if(te != null) {
                    logger.debug("CreativeVersionId=" + creativeVersionId + " loaded from CI360 API, and saved to CreativeCache table, duration[ms]: " + Duration.between(start, LocalDateTime.now()).toMillis()  );
                    saveCreativeCpToCache(te);
                    addCreativeCacheEntryToCache(te);
                    return te;
                }
            } catch (Exception e) {
                logger.error("Exception()",e);
            }

            return null;		//error
        }

    }

    public static CreativeCacheEntry getCI360ApiCreativeCp(String creativeId, String creativeVersionId) {
        CreativeCacheEntry te = new CreativeCacheEntry();
        te.creativeVersionId = creativeVersionId;
        te.creativeId = creativeId;

        //get cp from ci360 api
        List<String> requiredCP = te.getCacheVariableList();
        HashMap<String, String > creativeCP =  CI360Api.getCreativeAttributes(creativeId, requiredCP);

        //assign to CP
        te.cre_camp_cp_product.setValueWithDefault(creativeCP.get("cre_camp_cp_product"));

        return te;
    }

    private static void addCreativeCacheEntryToCache(CreativeCacheEntry te) {
        cache.put(te.creativeVersionId, te);
    }

    public static CreativeCacheEntry loadCacheCreativeCp( String creativeVersionId, String creativeId) throws SQLException, InterruptedException, ClassNotFoundException {
        return loadDatabaseCreativeCp(db + ".cie_contact_history_stream_CRT_cp_cache",  creativeVersionId,  creativeId);
    }

    private static CreativeCacheEntry loadDatabaseCreativeCp(String dbTable, String creativeVersionId, String creativeId) throws SQLException, InterruptedException, ClassNotFoundException {

        //save message to DB table queue
        String sql="SELECT attribute_nm,attribute_val  FROM " + dbTable + " WHERE creative_version_id=?";

        Connection con = SqlConnectionPool.getConnection();
        try (
                PreparedStatement st = con.prepareStatement(sql);
        )
        {
            /*input parameter, batch size*/
            st.setString(1,creativeVersionId);

            ResultSet rs = st.executeQuery() ;

            if(rs.next()!=false) {	//test if rs is not empty
                CreativeCacheEntry te = new CreativeCacheEntry();
                te.creativeVersionId = creativeVersionId;
                te.creativeId = creativeId;

                do {	//for each custom property
                    String name=rs.getString("attribute_nm");
                    String value=rs.getString("attribute_val");

                    if(name.equalsIgnoreCase(te.cre_camp_cp_product.name))
                        te.cre_camp_cp_product.value = value;

                    if(
                        te.cre_camp_cp_product.hasValue()
                    )
                    {
                        //all creative custom properties loaded, no need to continue in iteration via remaining rows in table
                        return te;
                    }

                } while (rs.next());

            }
        }
        catch(SQLException e)
        {
            logger.error("Exception()", e);
        }
        finally{
            SqlConnectionPool.releaseConnection(con);
        }

        return null;
    }

    public static void saveCreativeCpToCache(CreativeCacheEntry te) throws SQLException, InterruptedException, ClassNotFoundException {

        String sql="INSERT INTO " + db + ".cie_contact_history_stream_CRT_cp_cache  (creative_version_id,attribute_nm,attribute_val)    VALUES(?,?,?)";
        Connection con = SqlConnectionPool.getConnection();
        try (
                PreparedStatement st = con.prepareStatement(sql);
        )
        {
            addCPEntryToBatch(st, te.creativeVersionId, te.cre_camp_cp_product.name, te.cre_camp_cp_product.value);

            st.executeBatch();
        }
        catch(SQLException e)
        {
            logger.error("Exception()",e);
        }
        finally{
            SqlConnectionPool.releaseConnection(con);
        }
    }


    private static void addCPEntryToBatch(PreparedStatement st , String creativeVersionId , String cpName , String cpValue) throws SQLException {

        st.setString(1, creativeVersionId);
        st.setString(2, cpName);
        if(cpValue == null) {
            st.setNull(3, Types.VARCHAR);
        }else {
            st.setString(3, cpValue);
        }
        st.addBatch();
    }
}
