package cz.ab.ci360.agent_ch_stream;

import com.sas.mkt.agent.sdk.CI360Agent;
import com.sas.mkt.agent.sdk.CI360AgentException;
import com.sas.mkt.agent.sdk.CI360StreamInterface;
import com.sas.mkt.agent.sdk.ErrorCode;
import cz.ab.ci360.cache.CampaignCache;
import cz.ab.ci360.cache.CreativeCache;
import cz.ab.ci360.cache.TaskCache;
import cz.ab.ci360.common.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.Authenticator;
import java.net.PasswordAuthentication;
import java.net.URL;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Enumeration;
import java.util.Properties;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.jar.Manifest;

/**
 * Class for connecting to CI360 and receiving events payload data
 */
public class Agent {
    private static final Logger logger = (Logger) LoggerFactory.getLogger(Agent.class);

    private static CI360Agent agent;
    private static CI360StreamInterface streamListener;

    static private AtomicBoolean alreadySeenStreamClosedCall = new AtomicBoolean(false);
    public static AtomicBoolean Exiting=new AtomicBoolean(false);

    public static boolean testMode = false;
    public static boolean runKafka;

    public static void main(String[] args) throws Exception {

        String gatewayHost = "";
        //String jwt = "";
        String tenantID="";
        String clientSecret="";
        int maxThreads = 0;

        try {
            Runtime.getRuntime().addShutdownHook(new ShutdownHook());	//kill signal handling, stop application

            logger.info("");
            logger.info("----- starting -------------------------------------------------------");

            //log jar path
            String jarPath = Agent.class
                    .getProtectionDomain()
                    .getCodeSource()
                    .getLocation()
                    .toURI()
                    .getPath();
            logger.info("App: " + jarPath);

            //log version from manifest entry
            Enumeration<URL> resources = Agent.class.getClassLoader().getResources("META-INF/MANIFEST.MF");

            Manifest manifest = new Manifest(resources.nextElement().openStream());
            String version = manifest.getMainAttributes().getValue("version");
            logger.info("Version: " + version );

            logger.info("----- configuration --------------------------------------------------");
            //load config
            Config config = new Config();

            //agent properties
            gatewayHost = config.loadKeyValue("ci360.gatewayHost");
            //jwt = config.loadKeyValue("ci360.jwt");
            tenantID = config.loadKeyValue("ci360.tenantID");
            clientSecret = config.loadKeyValueBase64("ci360.clientSecret");
            maxThreads = config.loadKeyValueAsInteger("agent.max_threads");
            String topic = config.loadKeyValue("kafka.topic");
            runKafka = Boolean.valueOf(config.loadKeyValue("agent.kafka"));

            //set proxy server
            ProxySetting.init(config);
            if(!ProxySetting.server.equals("")) {
                System.setProperty("http.proxyHost", ProxySetting.server);
                System.setProperty("http.proxyPort", String.valueOf(ProxySetting.port));

                if(!ProxySetting.realm.equals("")) {
                    System.setProperty("http.proxyRealm", ProxySetting.realm);
                    System.setProperty("http.proxyUser", ProxySetting.user);
                    System.setProperty("http.proxyPassword", ProxySetting.pwd);

                    //!!!!!!!!!!!!  required by Http class
                    Authenticator authenticator = new Authenticator() {
                        public PasswordAuthentication getPasswordAuthentication() {
                            return (new PasswordAuthentication(ProxySetting.user ,
                                    ProxySetting.pwd.toCharArray()));
                        }
                    };
                    Authenticator.setDefault(authenticator);
                    System.setProperty("jdk.http.auth.tunneling.disabledSchemes","");
                    //---

                }

                if(!ProxySetting.nonProxyHosts.equals("")) {
                    System.setProperty("http.nonProxyHosts", ProxySetting.nonProxyHosts);
                }
            }

            //!!! AGENT TEST, USE FOR TESTING, WITHOUT CONNECTING TO CI360
            String testModeProperty = System.getProperty("testMode");
            String testPayloadProperty = System.getProperty("testPayload");
            if(testModeProperty != null) {
                testMode = Boolean.parseBoolean(testModeProperty);
            }
            logger.info("test mode = {}",testMode);
            //eof test inicialization


            //--- init other classes ------------------------
            if(runKafka) {
                Kafka.init(config);
                Kafka.setTopic(topic);
            }

            CI360Api.init(config);
            SqlConnectionPool.init(config, testMode);

            TaskCache.init(config);
            CreativeCache.init(config);
            CampaignCache.init(config);

            Event.init(config);

            logger.info("----- eof configuration --------------------------------------------------");

            if(testMode) {
                Test.test(testPayloadProperty);
                return;
            }
        }
        catch(Exception e) {
            logger.error("Inicialization error. " + e.toString(), e);
            logger.info("Exit code: 1");
            System.exit(1);
        }

        try {

            Properties props = System.getProperties();
            props.setProperty("ci360.gatewayHost", gatewayHost);
            //props.setProperty("ci360.jwt", jwt);
            props.setProperty("ci360.tenantID", tenantID);
            props.setProperty("ci360.clientSecret", clientSecret);

            //start Agent
            agent=new CI360Agent();
            ExecutorService executorService = Executors.newFixedThreadPool(maxThreads);
            streamListener=new CI360StreamInterface() {
                public boolean processEvent(final String event) {

                    Runnable processEvent = new Event(event);
                    executorService.submit(processEvent);
                    return true;
                }

                public void streamClosed(ErrorCode errorCode, String message) {
                    if (Exiting.get() ) {
                        logger.debug("Stream closed");
                        System.out.println("Stream closed");
                    } else {
                        logger.error("Stream to cloud closed, "+ errorCode + ": " + message);
                        System.out.println("Stream closed " + errorCode + ": " + message);
                        if (alreadySeenStreamClosedCall.compareAndSet(false, true)) {
                            logger.debug("Passed compareAndSet test");
                            System.out.println("Passed compareAndSet test");
                            agent.stopStream(true);
                            try {
                                Thread.sleep(15000);
                            } catch (InterruptedException e) {

                            }
                            alreadySeenStreamClosedCall.set(false);
                            try {
                                //Try to reconnect to the event stream.
                                agent.startStream(this, true);
                            } catch (CI360AgentException e) {
                                logger.error("ErrorCode:CH_00 - Not possible to establish connection with tenant, " + e.getErrorCode() + ": " + e.getMessage(), e);
                                System.err.println("ERROR " + e.getErrorCode() + ": " + e.getMessage());
                            }
                        }
                    }
                }
            };
            agent.startStream(streamListener, true);



            if (System.console() != null) {
                //Console mode
                System.console().readLine();
                Exiting.set(true);
                stop("console", 0);
            }else {
                //background service mode
                LocalDateTime lastAlive= LocalDateTime.now();
                while(true) {

                    //each minute log the app is running
                    LocalDateTime now = LocalDateTime.now();
                    if(Duration.between(lastAlive, now).toMinutes()>=1) {
                        logger.info("running");
                        lastAlive= now;
                    }

                    if(Exiting.get()) {
                        executorService.shutdown();
                        stop("signal", 0);
                    }
                    Thread.sleep(1000);
                }
            }


        } catch (Exception e) {
            logger.error("Agent generic error. " + e.toString(), e);
            stop("error", 1);
        }


    }

    public static void stop(String reason, int rc) {
        try {
            logger.info("Stop reason: '{}'", reason);

            //close Sql connection pool
            SqlConnectionPool.stop();
            if(runKafka) {
                Kafka.stop();
            }

            //exiting=true;
            if (agent != null)
                agent.stopStream();
            Thread.sleep(1000);
            logger.info("Exit code: {}", rc);
        } catch (Exception e) {
            logger.error("Error when closing stream to cloud, " + e.getMessage(), e);
            rc = 1;
            logger.info("Exit code: {}", rc);
        } finally {
            System.exit(rc);
        }
    }
}

