package cz.ab.ci360.cache;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

public class CreativeCacheEntry {

    public String creativeVersionId;
    public String creativeId;

    public CacheVariable cre_camp_cp_product = new CacheVariable("cre_camp_cp_product", "");

    public List<String> getCacheVariableList() {
        Field[] fields = CreativeCacheEntry.class.getFields();
        List<String> variables = new ArrayList<>();
        for(Field f : fields) {
            if( f.getType().isAssignableFrom(CacheVariable.class) ) {
                variables.add(f.getName());
            }
        }
        return variables;
    }

}