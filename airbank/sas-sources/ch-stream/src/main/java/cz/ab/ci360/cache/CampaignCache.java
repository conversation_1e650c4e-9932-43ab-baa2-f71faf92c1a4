package cz.ab.ci360.cache;

import cz.ab.ci360.common.Config;
import cz.ab.ci360.common.SqlConnectionPool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.HashMap;

public class CampaignCache {
    private static final Logger logger =  LoggerFactory.getLogger(CampaignCache.class);

    private static HashMap<String,CampaignCacheEntry> cache= new HashMap<String, CampaignCacheEntry>();	//Campaign CP cache !!!!!!!!!!
    private static HashMap<String, Object> locks = new HashMap<String, Object>();		//used to synchronizace processing each campaignCD (one thread in one moment)

    private static final String tableName = "APP_CAMPAIGN_CDM2.CIE_CAMPAIGN_MESSAGE";
    private static String db;

    public static void init(Config config)  {

        //log configuration
        db = SqlConnectionPool.database;
    }

    public static CampaignCacheEntry getCamapign(String campaignCD) {

        LocalDateTime start = LocalDateTime.now();
        //add campaign to locks array if missing, used to synchronize processing of campaign (one campaignCD just in one thread in one moment)
        synchronized(locks) {
            if(locks.get(campaignCD)==null)
            {
                locks.put(campaignCD, new Object());
            }
        }

        synchronized(locks.get(campaignCD)) {

            //get campaign from cache in memory
            CampaignCacheEntry te = cache.get(campaignCD);

            //campaign is in application cache
            if(te != null) {
                logger.debug("CamapignCD=" + campaignCD + " loaded from application cache, duration[ms]: " + Duration.between(start, LocalDateTime.now()).toMillis()  );
                return te;
            }


            //if not exist load from campaign cache db table
            try {
                te = loadDatabaseCampaignCp(campaignCD);
                if(te!=null) {
                    logger.debug("CamapignVersionId=" + campaignCD + " loaded from table " + tableName + ", duration[ms]: " + Duration.between(start, LocalDateTime.now()).toMillis()  );
                    addCampaignCacheEntryToCache(te);
                    return te;
                }
            } catch (Exception e) {
                logger.error("Error when loading CP from " + tableName, e);
            }

            return null;		//error
        }

    }

    private static void addCampaignCacheEntryToCache(CampaignCacheEntry te) {
        cache.put(te.campMessageCD, te);
    }

    private static CampaignCacheEntry loadDatabaseCampaignCp(String campaignCD) throws SQLException, InterruptedException, ClassNotFoundException {

        //save message to DB table queue
        String sql="SELECT CAMP_NAME FROM " + tableName + " WHERE CAMPAIGN_MESSAGE_CD=?";

        Connection con = SqlConnectionPool.getConnection();
        try (
                PreparedStatement st = con.prepareStatement(sql);
        )
        {
            /*input parameter, batch size*/
            st.setString(1,campaignCD);

            ResultSet rs = st.executeQuery() ;

            if(rs.next()!=false) {	//test if rs is not empty
                CampaignCacheEntry te = new CampaignCacheEntry();
                te.campMessageCD = campaignCD;
                te.camp_name.value = rs.getString("CAMP_NAME");
                return te;
            }
        }
        catch(SQLException e)
        {
            logger.error("Exception()", e);
        }
        finally{
            SqlConnectionPool.releaseConnection(con);
        }

        return null;
    }

}
