package cz.ab.ci360.common;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.*;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

public class Http {

    private static final Logger logger = (Logger) LoggerFactory.getLogger(Http.class);

    int timeout;
    boolean logReponse;

    public Http(int timeout, boolean logReponse) {
        this.timeout = timeout;
        this.logReponse = logReponse;
    }

    public String[] post(String url, String msg, String contentType, String authorization)  {

        try {

            URL urlo = new URL (url);
            HttpURLConnection con ;

            if(!ProxySetting.server.equals(""))
            {
                //if proxy
                Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(ProxySetting.server, ProxySetting.port));
                con = (HttpURLConnection)urlo.openConnection(proxy);
            }
            else
            {
                // no proxy
                con = (HttpURLConnection)urlo.openConnection();
            }

            con.setDoOutput(true);

            con.setRequestMethod("POST");
            con.setRequestProperty("Content-Type", contentType);
            if(!authorization.equals(""))
            {
                con.setRequestProperty("Authorization",authorization );
            }
            if(this.timeout != 0) {
                con.setConnectTimeout(this.timeout);
                con.setReadTimeout(this.timeout);
            }

            OutputStream os = con.getOutputStream();
            os.write(msg.getBytes("utf-8"));
            os.flush();
            os.close();

            //retrieve response message
            int code = con.getResponseCode() ;
            String resp = getResponseMessage(con);

            if(!isSuccessResponse(code)) {
                logger.error(String.format("Response, Code: %s , Message: %s",code, resp));
            }
            else {
                String responseToLog;
                if(this.logReponse) {
                    responseToLog = resp;
                } else {
                    responseToLog = "---hidden---";
                }
                logger.debug(String.format("Response, Code: %s , Message: %s", code, responseToLog));
            }

            //result value
            String[] res= new String[2];
            res[0]=String.valueOf(code);	//http code
            res[1]=resp;	        		//http message

            return res;

        }catch(SocketTimeoutException e) {
            logger.error("Http connection timeout: " + e.getMessage(), e);
        }catch(Exception e) {
            logger.error("Exception()" , e);
        }

        return null;
    }

    public String[] get(String url,  String contentType, String authorization)  {

        try {

            URL urlo = new URL (url);
            HttpURLConnection con ;

            if(!ProxySetting.server.equals(""))
            {
                //if proxy
                Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(ProxySetting.server, ProxySetting.port));
                con = (HttpURLConnection)urlo.openConnection(proxy);
            }
            else
            {
                // no proxy
                con = (HttpURLConnection)urlo.openConnection();
            }

            con.setRequestMethod("GET");
            con.setRequestProperty("Content-Type", contentType);
            if(!authorization.equals(""))
            {
                con.setRequestProperty("Authorization",authorization );
            }
            if(this.timeout != 0) {
                con.setConnectTimeout(this.timeout);
                con.setReadTimeout(this.timeout);
            }

            //retrieve response message
            int code = con.getResponseCode() ;
            String resp = getResponseMessage(con);

            if(!isSuccessResponse(code)) {
                logger.error(String.format("Response, Code: %s , Message: %s",code, resp));
            }
            else {
                String responseToLog;
                if(this.logReponse) {
                    responseToLog = resp;
                } else {
                    responseToLog = "---hidden---";
                }
                logger.debug(String.format("Response, Code: %s , Message: %s", code, responseToLog));
            }

            //result/output value
            String[] res= new String[2];
            res[0]=String.valueOf(code);	//http code
            res[1]=resp;	        		//http message

            return res;

        }catch(SocketTimeoutException e) {
            logger.error("Http connection timeout: " + e.getMessage(), e);
        }catch(Exception e) {
            logger.error("Exception()", e);
        }

        return null;
    }

    private static String getResponseMessage(HttpURLConnection con) throws IOException {

        String responseMessage = "";

        try{    // in some cases when response code is 500, there is or there is not any response message
            BufferedReader in = new BufferedReader(new InputStreamReader(con.getInputStream()));
            String inputLine;
            StringBuffer response = new StringBuffer();

            while ((inputLine = in.readLine()) != null) {
                response.append(inputLine);
            }
            in.close();

            // print result
            responseMessage = response.toString();
        } catch (Exception e){
        }
        return responseMessage;
    }

    public static boolean isSuccessResponse(int responseCode) {
        if(String.valueOf(responseCode).startsWith("2")) {
            return true;
        } else {
            return false;
        }
    }

    public static String getAuthorizationBasic(String user, String pwd) {
        String auth = user + ":" + pwd;
        byte[] encodedAuth = Base64.getEncoder().encode(auth.getBytes(StandardCharsets.UTF_8));
        String authString = "Basic " + new String(encodedAuth);
        return authString;
    }
}
