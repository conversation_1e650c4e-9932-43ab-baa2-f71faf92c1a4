<?xml version="1.0" encoding="UTF-8"?>
<!-- scan this file for configuration changes every minute -->
<!-- change the scan period with: <configuration scan="true" scanPeriod="30 seconds" > -->

<configuration scan="true">


  <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <encoder>
      <pattern>%d{ISO8601}  %-10.10t  %-5.5p  %c{16}    %m%n</pattern>
      <charset>UTF-8</charset>
    </encoder>
    <file>./log/agent-ch-stream.log</file>
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
      <!-- daily rollover -->
      <fileNamePattern>./log/agent-ch-stream.log.%d{yyyy-MM-dd}.log</fileNamePattern>
      <!-- keep 30 days' worth of history capped at 3GB total size -->
      <maxHistory>30</maxHistory>
      <totalSizeCap>600MB</totalSizeCap>
    </rollingPolicy>
  </appender>

  <logger name="cz.ab.ci360" level="DEBUG"/>

  <root level="INFO">
    <appender-ref ref="FILE" />
  </root>
</configuration>

