2024-06-20 13:24:17,709  main        INFO   c.a.c.a.Agent    
2024-06-20 13:24:17,712  main        INFO   c.a.c.a.Agent    ----- starting -------------------------------------------------------
2024-06-20 13:24:17,721  main        INFO   c.a.c.a.Agent    App: /opt/agents/ch-stream/agent-ch-stream.jar
2024-06-20 13:24:17,724  main        INFO   c.a.c.a.Agent    Version: 1.0.0
2024-06-20 13:24:17,724  main        INFO   c.a.c.a.Agent    ----- configuration --------------------------------------------------
2024-06-20 13:24:17,727  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-20 13:24:17,728  main        INFO   c.a.c.c.Config    ci360.jwt=f0cb22506600016b1805ee8b
2024-06-20 13:24:17,728  main        INFO   c.a.c.c.Config    agent.max_threads=5000
2024-06-20 13:24:17,728  main        INFO   c.a.c.c.Config    kafka.topic=sentCampaingEmail
2024-06-20 13:24:17,728  main        INFO   c.a.c.c.Config    agent.kafka=false
2024-06-20 13:24:17,729  main        INFO   c.a.c.c.Config    proxy.server=proxy.np.ab
2024-06-20 13:24:17,729  main        INFO   c.a.c.c.Config    proxy.port=3128
2024-06-20 13:24:17,729  main        INFO   c.a.c.c.Config    proxy.realm=proxy
2024-06-20 13:24:17,729  main        INFO   c.a.c.c.Config    proxy.user=sascloud_user
2024-06-20 13:24:17,730  main        INFO   c.a.c.c.Config    proxy.non_proxy_hosts=app-sas-test.np.ab
2024-06-20 13:24:17,731  main        INFO   c.a.c.a.Agent    test mode = false
2024-06-20 13:24:17,731  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-20 13:24:17,731  main        INFO   c.a.c.c.Config    db.user=APP_CAMPAIGN_CDM2
2024-06-20 13:24:17,732  main        INFO   c.a.c.c.Config    db.database=APP_CAMPAIGN_CDM2
2024-06-20 13:24:17,732  main        INFO   c.a.c.c.Config    db.server=DBS01.DE11.NP.AB:1621
2024-06-20 13:24:17,732  main        INFO   c.a.c.c.Config    db.service_name=MNDE11DW.NP.AB
2024-06-20 13:24:17,732  main        INFO   c.a.c.c.Config    db.init_connection_pool_size=10
2024-06-20 13:24:17,732  main        INFO   c.a.c.c.Config    db.max_connection_pool_size=200
2024-06-20 13:24:18,481  main        INFO   c.a.c.c.Config    event.process_event_with_prefix=c_
2024-06-20 13:24:18,482  main        INFO   c.a.c.c.Config    event.direct_event=c_direct
2024-06-20 13:24:18,483  main        INFO   c.a.c.c.Config    event.ignore_events=c_activitystart,c_abtestpathassignment
2024-06-20 13:24:18,483  main        INFO   c.a.c.a.Agent    ----- eof configuration --------------------------------------------------
2024-06-20 13:24:18,487  main        INFO   c.s.m.a.s.CI360Agent    Maximum upstream queue size: 1000
2024-06-20 13:24:18,487  main        INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 13:24:18,541  main        INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-20 13:24:19,003  8348097-22  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-20 13:25:18,705  main        INFO   c.a.c.a.Agent    running
2024-06-20 13:25:29,018  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-20 13:25:29,019  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-20 13:25:29,020  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 13:25:44,020  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 13:25:44,021  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-20 13:25:44,026  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-20 13:25:44,028  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-20 13:25:44,028  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 13:25:44,173  8348097-19  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-20 13:25:59,028  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 13:25:59,031  cheduler-1  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$2.streamClosed(Agent.java:191)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$sendFrame$7(WebSocketCoreSession.java:519)
	at org.eclipse.jetty.util.Callback$3.succeeded(Callback.java:155)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.notifyCallbackSuccess(TransformingFlusher.java:197)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher$Flusher.process(TransformingFlusher.java:154)
	at org.eclipse.jetty.util.IteratingCallback.processing(IteratingCallback.java:243)
	at org.eclipse.jetty.util.IteratingCallback.iterate(IteratingCallback.java:224)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.sendFrame(TransformingFlusher.java:77)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.sendFrame(WebSocketCoreSession.java:522)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.close(WebSocketCoreSession.java:239)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.processHandlerError(WebSocketCoreSession.java:371)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onIdleExpired(WebSocketConnection.java:242)
	at org.eclipse.jetty.io.ssl.SslConnection.onIdleExpired(SslConnection.java:360)
	at org.eclipse.jetty.io.AbstractEndPoint.onIdleExpired(AbstractEndPoint.java:407)
	at org.eclipse.jetty.io.IdleTimeout.checkIdleTimeout(IdleTimeout.java:170)
	at org.eclipse.jetty.io.IdleTimeout.idleCheck(IdleTimeout.java:112)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 13:26:18,716  main        INFO   c.a.c.a.Agent    running
2024-06-20 13:26:54,176  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-20 13:26:54,176  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-20 13:26:54,176  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 13:27:09,176  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 13:27:09,176  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-20 13:27:09,177  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-20 13:27:09,178  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-20 13:27:09,178  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 13:27:09,329  8348097-23  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-20 13:27:18,728  main        INFO   c.a.c.a.Agent    running
2024-06-20 13:27:24,178  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 13:27:24,178  cheduler-1  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$2.streamClosed(Agent.java:191)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$sendFrame$7(WebSocketCoreSession.java:519)
	at org.eclipse.jetty.util.Callback$3.succeeded(Callback.java:155)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.notifyCallbackSuccess(TransformingFlusher.java:197)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher$Flusher.process(TransformingFlusher.java:154)
	at org.eclipse.jetty.util.IteratingCallback.processing(IteratingCallback.java:243)
	at org.eclipse.jetty.util.IteratingCallback.iterate(IteratingCallback.java:224)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.sendFrame(TransformingFlusher.java:77)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.sendFrame(WebSocketCoreSession.java:522)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.close(WebSocketCoreSession.java:239)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.processHandlerError(WebSocketCoreSession.java:371)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onIdleExpired(WebSocketConnection.java:242)
	at org.eclipse.jetty.io.ssl.SslConnection.onIdleExpired(SslConnection.java:360)
	at org.eclipse.jetty.io.AbstractEndPoint.onIdleExpired(AbstractEndPoint.java:407)
	at org.eclipse.jetty.io.IdleTimeout.checkIdleTimeout(IdleTimeout.java:170)
	at org.eclipse.jetty.io.IdleTimeout.idleCheck(IdleTimeout.java:112)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 13:28:18,739  main        INFO   c.a.c.a.Agent    running
2024-06-20 13:28:19,332  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-20 13:28:19,332  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-20 13:28:19,332  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 13:28:34,333  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 13:28:34,333  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-20 13:28:34,333  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-20 13:28:34,333  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-20 13:28:34,334  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 13:28:34,466  8348097-22  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-20 13:28:49,334  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 13:28:49,334  cheduler-1  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$2.streamClosed(Agent.java:191)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$sendFrame$7(WebSocketCoreSession.java:519)
	at org.eclipse.jetty.util.Callback$3.succeeded(Callback.java:155)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.notifyCallbackSuccess(TransformingFlusher.java:197)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher$Flusher.process(TransformingFlusher.java:154)
	at org.eclipse.jetty.util.IteratingCallback.processing(IteratingCallback.java:243)
	at org.eclipse.jetty.util.IteratingCallback.iterate(IteratingCallback.java:224)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.sendFrame(TransformingFlusher.java:77)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.sendFrame(WebSocketCoreSession.java:522)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.close(WebSocketCoreSession.java:239)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.processHandlerError(WebSocketCoreSession.java:371)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onIdleExpired(WebSocketConnection.java:242)
	at org.eclipse.jetty.io.ssl.SslConnection.onIdleExpired(SslConnection.java:360)
	at org.eclipse.jetty.io.AbstractEndPoint.onIdleExpired(AbstractEndPoint.java:407)
	at org.eclipse.jetty.io.IdleTimeout.checkIdleTimeout(IdleTimeout.java:170)
	at org.eclipse.jetty.io.IdleTimeout.idleCheck(IdleTimeout.java:112)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 13:29:18,748  main        INFO   c.a.c.a.Agent    running
2024-06-20 13:29:44,469  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-20 13:29:44,469  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-20 13:29:44,469  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 13:29:59,469  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 13:29:59,470  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-20 13:29:59,470  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-20 13:29:59,470  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-20 13:29:59,471  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 13:29:59,591  8348097-21  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-20 13:30:14,471  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 13:30:14,471  cheduler-1  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$2.streamClosed(Agent.java:191)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$sendFrame$7(WebSocketCoreSession.java:519)
	at org.eclipse.jetty.util.Callback$3.succeeded(Callback.java:155)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.notifyCallbackSuccess(TransformingFlusher.java:197)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher$Flusher.process(TransformingFlusher.java:154)
	at org.eclipse.jetty.util.IteratingCallback.processing(IteratingCallback.java:243)
	at org.eclipse.jetty.util.IteratingCallback.iterate(IteratingCallback.java:224)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.sendFrame(TransformingFlusher.java:77)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.sendFrame(WebSocketCoreSession.java:522)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.close(WebSocketCoreSession.java:239)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.processHandlerError(WebSocketCoreSession.java:371)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onIdleExpired(WebSocketConnection.java:242)
	at org.eclipse.jetty.io.ssl.SslConnection.onIdleExpired(SslConnection.java:360)
	at org.eclipse.jetty.io.AbstractEndPoint.onIdleExpired(AbstractEndPoint.java:407)
	at org.eclipse.jetty.io.IdleTimeout.checkIdleTimeout(IdleTimeout.java:170)
	at org.eclipse.jetty.io.IdleTimeout.idleCheck(IdleTimeout.java:112)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 13:30:18,758  main        INFO   c.a.c.a.Agent    running
2024-06-20 13:31:09,593  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-20 13:31:09,593  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-20 13:31:09,593  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 13:31:18,767  main        INFO   c.a.c.a.Agent    running
2024-06-20 13:31:24,593  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 13:31:24,594  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-20 13:31:24,594  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-20 13:31:24,594  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-20 13:31:24,594  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 13:31:24,740  8348097-25  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-20 13:31:39,595  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 13:31:39,595  cheduler-1  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$2.streamClosed(Agent.java:191)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$sendFrame$7(WebSocketCoreSession.java:519)
	at org.eclipse.jetty.util.Callback$3.succeeded(Callback.java:155)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.notifyCallbackSuccess(TransformingFlusher.java:197)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher$Flusher.process(TransformingFlusher.java:154)
	at org.eclipse.jetty.util.IteratingCallback.processing(IteratingCallback.java:243)
	at org.eclipse.jetty.util.IteratingCallback.iterate(IteratingCallback.java:224)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.sendFrame(TransformingFlusher.java:77)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.sendFrame(WebSocketCoreSession.java:522)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.close(WebSocketCoreSession.java:239)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.processHandlerError(WebSocketCoreSession.java:371)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onIdleExpired(WebSocketConnection.java:242)
	at org.eclipse.jetty.io.ssl.SslConnection.onIdleExpired(SslConnection.java:360)
	at org.eclipse.jetty.io.AbstractEndPoint.onIdleExpired(AbstractEndPoint.java:407)
	at org.eclipse.jetty.io.IdleTimeout.checkIdleTimeout(IdleTimeout.java:170)
	at org.eclipse.jetty.io.IdleTimeout.idleCheck(IdleTimeout.java:112)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 13:32:18,776  main        INFO   c.a.c.a.Agent    running
2024-06-20 13:32:34,742  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-20 13:32:34,742  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-20 13:32:34,742  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 13:32:49,742  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 13:32:49,742  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-20 13:32:49,743  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-20 13:32:49,743  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-20 13:32:49,743  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 13:32:49,908  8348097-24  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-20 13:33:04,744  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 13:33:04,744  cheduler-1  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$2.streamClosed(Agent.java:191)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$sendFrame$7(WebSocketCoreSession.java:519)
	at org.eclipse.jetty.util.Callback$3.succeeded(Callback.java:155)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.notifyCallbackSuccess(TransformingFlusher.java:197)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher$Flusher.process(TransformingFlusher.java:154)
	at org.eclipse.jetty.util.IteratingCallback.processing(IteratingCallback.java:243)
	at org.eclipse.jetty.util.IteratingCallback.iterate(IteratingCallback.java:224)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.sendFrame(TransformingFlusher.java:77)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.sendFrame(WebSocketCoreSession.java:522)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.close(WebSocketCoreSession.java:239)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.processHandlerError(WebSocketCoreSession.java:371)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onIdleExpired(WebSocketConnection.java:242)
	at org.eclipse.jetty.io.ssl.SslConnection.onIdleExpired(SslConnection.java:360)
	at org.eclipse.jetty.io.AbstractEndPoint.onIdleExpired(AbstractEndPoint.java:407)
	at org.eclipse.jetty.io.IdleTimeout.checkIdleTimeout(IdleTimeout.java:170)
	at org.eclipse.jetty.io.IdleTimeout.idleCheck(IdleTimeout.java:112)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 13:33:18,785  main        INFO   c.a.c.a.Agent    running
2024-06-20 13:33:59,911  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-20 13:33:59,911  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-20 13:33:59,911  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 13:34:14,911  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 13:34:14,911  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-20 13:34:14,912  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-20 13:34:14,912  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-20 13:34:14,912  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 13:34:15,045  8348097-20  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-20 13:34:18,794  main        INFO   c.a.c.a.Agent    running
2024-06-20 13:34:29,912  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 13:34:29,913  cheduler-1  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$2.streamClosed(Agent.java:191)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$sendFrame$7(WebSocketCoreSession.java:519)
	at org.eclipse.jetty.util.Callback$3.succeeded(Callback.java:155)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.notifyCallbackSuccess(TransformingFlusher.java:197)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher$Flusher.process(TransformingFlusher.java:154)
	at org.eclipse.jetty.util.IteratingCallback.processing(IteratingCallback.java:243)
	at org.eclipse.jetty.util.IteratingCallback.iterate(IteratingCallback.java:224)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.sendFrame(TransformingFlusher.java:77)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.sendFrame(WebSocketCoreSession.java:522)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.close(WebSocketCoreSession.java:239)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.processHandlerError(WebSocketCoreSession.java:371)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onIdleExpired(WebSocketConnection.java:242)
	at org.eclipse.jetty.io.ssl.SslConnection.onIdleExpired(SslConnection.java:360)
	at org.eclipse.jetty.io.AbstractEndPoint.onIdleExpired(AbstractEndPoint.java:407)
	at org.eclipse.jetty.io.IdleTimeout.checkIdleTimeout(IdleTimeout.java:170)
	at org.eclipse.jetty.io.IdleTimeout.idleCheck(IdleTimeout.java:112)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 13:35:18,803  main        INFO   c.a.c.a.Agent    running
2024-06-20 13:35:25,048  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-20 13:35:25,048  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-20 13:35:25,048  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 13:35:40,048  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 13:35:40,048  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-20 13:35:40,049  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-20 13:35:40,049  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-20 13:35:40,049  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 13:35:40,210  8348097-22  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-20 13:35:55,049  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 13:35:55,050  cheduler-1  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$2.streamClosed(Agent.java:191)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$sendFrame$7(WebSocketCoreSession.java:519)
	at org.eclipse.jetty.util.Callback$3.succeeded(Callback.java:155)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.notifyCallbackSuccess(TransformingFlusher.java:197)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher$Flusher.process(TransformingFlusher.java:154)
	at org.eclipse.jetty.util.IteratingCallback.processing(IteratingCallback.java:243)
	at org.eclipse.jetty.util.IteratingCallback.iterate(IteratingCallback.java:224)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.sendFrame(TransformingFlusher.java:77)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.sendFrame(WebSocketCoreSession.java:522)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.close(WebSocketCoreSession.java:239)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.processHandlerError(WebSocketCoreSession.java:371)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onIdleExpired(WebSocketConnection.java:242)
	at org.eclipse.jetty.io.ssl.SslConnection.onIdleExpired(SslConnection.java:360)
	at org.eclipse.jetty.io.AbstractEndPoint.onIdleExpired(AbstractEndPoint.java:407)
	at org.eclipse.jetty.io.IdleTimeout.checkIdleTimeout(IdleTimeout.java:170)
	at org.eclipse.jetty.io.IdleTimeout.idleCheck(IdleTimeout.java:112)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 13:36:18,812  main        INFO   c.a.c.a.Agent    running
2024-06-20 13:36:50,213  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-20 13:36:50,213  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-20 13:36:50,213  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 13:37:05,213  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 13:37:05,213  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-20 13:37:05,214  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-20 13:37:05,214  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-20 13:37:05,214  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 13:37:05,365  8348097-21  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-20 13:37:18,820  main        INFO   c.a.c.a.Agent    running
2024-06-20 13:37:20,214  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 13:37:20,215  cheduler-1  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$2.streamClosed(Agent.java:191)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$sendFrame$7(WebSocketCoreSession.java:519)
	at org.eclipse.jetty.util.Callback$3.succeeded(Callback.java:155)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.notifyCallbackSuccess(TransformingFlusher.java:197)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher$Flusher.process(TransformingFlusher.java:154)
	at org.eclipse.jetty.util.IteratingCallback.processing(IteratingCallback.java:243)
	at org.eclipse.jetty.util.IteratingCallback.iterate(IteratingCallback.java:224)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.sendFrame(TransformingFlusher.java:77)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.sendFrame(WebSocketCoreSession.java:522)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.close(WebSocketCoreSession.java:239)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.processHandlerError(WebSocketCoreSession.java:371)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onIdleExpired(WebSocketConnection.java:242)
	at org.eclipse.jetty.io.ssl.SslConnection.onIdleExpired(SslConnection.java:360)
	at org.eclipse.jetty.io.AbstractEndPoint.onIdleExpired(AbstractEndPoint.java:407)
	at org.eclipse.jetty.io.IdleTimeout.checkIdleTimeout(IdleTimeout.java:170)
	at org.eclipse.jetty.io.IdleTimeout.idleCheck(IdleTimeout.java:112)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 13:38:15,368  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-20 13:38:15,368  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-20 13:38:15,368  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 13:38:18,828  main        INFO   c.a.c.a.Agent    running
2024-06-20 13:38:30,368  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 13:38:30,368  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-20 13:38:30,369  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-20 13:38:30,369  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-20 13:38:30,369  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 13:38:30,547  8348097-23  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-20 13:38:45,370  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 13:38:45,370  cheduler-1  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$2.streamClosed(Agent.java:191)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$sendFrame$7(WebSocketCoreSession.java:519)
	at org.eclipse.jetty.util.Callback$3.succeeded(Callback.java:155)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.notifyCallbackSuccess(TransformingFlusher.java:197)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher$Flusher.process(TransformingFlusher.java:154)
	at org.eclipse.jetty.util.IteratingCallback.processing(IteratingCallback.java:243)
	at org.eclipse.jetty.util.IteratingCallback.iterate(IteratingCallback.java:224)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.sendFrame(TransformingFlusher.java:77)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.sendFrame(WebSocketCoreSession.java:522)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.close(WebSocketCoreSession.java:239)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.processHandlerError(WebSocketCoreSession.java:371)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onIdleExpired(WebSocketConnection.java:242)
	at org.eclipse.jetty.io.ssl.SslConnection.onIdleExpired(SslConnection.java:360)
	at org.eclipse.jetty.io.AbstractEndPoint.onIdleExpired(AbstractEndPoint.java:407)
	at org.eclipse.jetty.io.IdleTimeout.checkIdleTimeout(IdleTimeout.java:170)
	at org.eclipse.jetty.io.IdleTimeout.idleCheck(IdleTimeout.java:112)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 13:39:18,836  main        INFO   c.a.c.a.Agent    running
2024-06-20 13:39:40,550  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-20 13:39:40,550  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-20 13:39:40,550  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 13:39:55,551  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 13:39:55,551  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-20 13:39:55,551  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-20 13:39:55,551  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-20 13:39:55,552  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 13:39:55,677  8348097-18  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-20 13:40:10,552  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 13:40:10,552  cheduler-1  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$2.streamClosed(Agent.java:191)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$sendFrame$7(WebSocketCoreSession.java:519)
	at org.eclipse.jetty.util.Callback$3.succeeded(Callback.java:155)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.notifyCallbackSuccess(TransformingFlusher.java:197)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher$Flusher.process(TransformingFlusher.java:154)
	at org.eclipse.jetty.util.IteratingCallback.processing(IteratingCallback.java:243)
	at org.eclipse.jetty.util.IteratingCallback.iterate(IteratingCallback.java:224)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.sendFrame(TransformingFlusher.java:77)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.sendFrame(WebSocketCoreSession.java:522)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.close(WebSocketCoreSession.java:239)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.processHandlerError(WebSocketCoreSession.java:371)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onIdleExpired(WebSocketConnection.java:242)
	at org.eclipse.jetty.io.ssl.SslConnection.onIdleExpired(SslConnection.java:360)
	at org.eclipse.jetty.io.AbstractEndPoint.onIdleExpired(AbstractEndPoint.java:407)
	at org.eclipse.jetty.io.IdleTimeout.checkIdleTimeout(IdleTimeout.java:170)
	at org.eclipse.jetty.io.IdleTimeout.idleCheck(IdleTimeout.java:112)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 13:40:18,844  main        INFO   c.a.c.a.Agent    running
2024-06-20 13:41:05,680  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-20 13:41:05,680  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-20 13:41:05,680  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 13:41:18,852  main        INFO   c.a.c.a.Agent    running
2024-06-20 13:41:20,680  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 13:41:20,681  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-20 13:41:20,682  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-20 13:41:20,682  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-20 13:41:20,682  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 13:41:20,796  8348097-24  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-20 13:41:27,853  main        INFO   c.a.c.a.Agent    Stop reason: 'signal'
2024-06-20 13:41:27,860  main        INFO   c.s.m.a.s.CI360Agent    Stopping event stream connection now
2024-06-20 13:41:27,861  main        INFO   o.e.j.w.c.WebSocketClient    Shutdown WebSocketClient@49c648bc[coreClient=WebSocketCoreClient@73173f63{STARTED},openSessions.size=2]
2024-06-20 13:41:27,862  8348097-19  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: null
2024-06-20 13:41:27,862  8348097-19  DEBUG  c.a.c.a.Agent    Stream closed
2024-06-20 13:41:27,863  8348097-19  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1006] Session Closed
2024-06-20 13:41:27,863  8348097-19  DEBUG  c.a.c.a.Agent    Stream closed
2024-06-20 13:41:27,867  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 13:41:27,867  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-20 13:41:27,987  8348097-46  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-20 13:41:28,869  main        INFO   c.a.c.a.Agent    Exit code: 0
2024-06-20 13:42:00,154  main        INFO   c.a.c.a.Agent    
2024-06-20 13:42:00,156  main        INFO   c.a.c.a.Agent    ----- starting -------------------------------------------------------
2024-06-20 13:42:00,160  main        INFO   c.a.c.a.Agent    App: /opt/agents/ch-stream/agent-ch-stream.jar
2024-06-20 13:42:00,163  main        INFO   c.a.c.a.Agent    Version: 1.0.0
2024-06-20 13:42:00,163  main        INFO   c.a.c.a.Agent    ----- configuration --------------------------------------------------
2024-06-20 13:42:00,166  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-20 13:42:00,167  main        INFO   c.a.c.c.Config    ci360.jwt=f0cb22506600016b1805ee8b
2024-06-20 13:42:00,167  main        INFO   c.a.c.c.Config    agent.max_threads=5000
2024-06-20 13:42:00,167  main        INFO   c.a.c.c.Config    kafka.topic=sentCampaingEmail
2024-06-20 13:42:00,167  main        INFO   c.a.c.c.Config    agent.kafka=false
2024-06-20 13:42:00,167  main        INFO   c.a.c.c.Config    proxy.server=proxy.np.ab
2024-06-20 13:42:00,168  main        INFO   c.a.c.c.Config    proxy.port=3128
2024-06-20 13:42:00,168  main        INFO   c.a.c.c.Config    proxy.realm="proxy"
2024-06-20 13:42:00,168  main        INFO   c.a.c.c.Config    proxy.user=sascloud_user
2024-06-20 13:42:00,168  main        INFO   c.a.c.c.Config    proxy.non_proxy_hosts=app-sas-test.np.ab
2024-06-20 13:42:00,169  main        INFO   c.a.c.a.Agent    test mode = false
2024-06-20 13:42:00,169  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-20 13:42:00,170  main        INFO   c.a.c.c.Config    db.user=APP_CAMPAIGN_CDM2
2024-06-20 13:42:00,170  main        INFO   c.a.c.c.Config    db.database=APP_CAMPAIGN_CDM2
2024-06-20 13:42:00,170  main        INFO   c.a.c.c.Config    db.server=DBS01.DE11.NP.AB:1621
2024-06-20 13:42:00,170  main        INFO   c.a.c.c.Config    db.service_name=MNDE11DW.NP.AB
2024-06-20 13:42:00,170  main        INFO   c.a.c.c.Config    db.init_connection_pool_size=10
2024-06-20 13:42:00,170  main        INFO   c.a.c.c.Config    db.max_connection_pool_size=200
2024-06-20 13:42:00,822  main        INFO   c.a.c.c.Config    event.process_event_with_prefix=c_
2024-06-20 13:42:00,822  main        INFO   c.a.c.c.Config    event.direct_event=c_direct
2024-06-20 13:42:00,822  main        INFO   c.a.c.c.Config    event.ignore_events=c_activitystart,c_abtestpathassignment
2024-06-20 13:42:00,822  main        INFO   c.a.c.a.Agent    ----- eof configuration --------------------------------------------------
2024-06-20 13:42:00,825  main        INFO   c.s.m.a.s.CI360Agent    Maximum upstream queue size: 1000
2024-06-20 13:42:00,826  main        INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 13:42:00,874  main        INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-20 13:42:01,130  9329761-24  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-20 13:42:01,131  9329761-24  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-20 13:42:01,131  9329761-24  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 13:42:16,131  9329761-24  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 13:42:16,132  9329761-24  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-20 13:42:16,137  9329761-17  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-20 13:42:16,137  9329761-17  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-20 13:42:16,137  9329761-17  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 13:42:31,137  9329761-17  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 13:42:31,138  9329761-17  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-20 13:42:31,143  9329761-18  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-20 13:42:31,143  9329761-18  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-20 13:42:31,143  9329761-18  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 13:42:46,143  9329761-18  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 13:42:46,144  9329761-18  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-20 13:42:46,151  9329761-23  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-20 13:42:46,151  9329761-23  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-20 13:42:46,151  9329761-23  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 13:43:01,030  main        INFO   c.a.c.a.Agent    running
2024-06-20 13:43:01,152  9329761-23  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 13:43:01,152  9329761-23  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-20 13:43:01,198  9329761-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-20 13:43:01,198  9329761-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-20 13:43:01,199  9329761-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 13:43:08,032  main        INFO   c.a.c.a.Agent    Stop reason: 'signal'
2024-06-20 13:43:09,039  main        INFO   c.a.c.a.Agent    Exit code: 0
2024-06-20 22:02:44,302  main        INFO   c.a.c.a.Agent    
2024-06-20 22:02:44,304  main        INFO   c.a.c.a.Agent    ----- starting -------------------------------------------------------
2024-06-20 22:02:44,309  main        INFO   c.a.c.a.Agent    App: /opt/agents/ch-stream/agent-ch-stream.jar
2024-06-20 22:02:44,311  main        INFO   c.a.c.a.Agent    Version: 1.0.0
2024-06-20 22:02:44,311  main        INFO   c.a.c.a.Agent    ----- configuration --------------------------------------------------
2024-06-20 22:02:44,314  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-20 22:02:44,315  main        INFO   c.a.c.c.Config    ci360.jwt=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJjbGllbnRJRCI6ImYwY2IyMjUwNjYwMDAxNmIxODA1ZWU4YiJ9.SS0UpNFT-uQolcHVKHHiyh4AjMLfxbOvph1BMtWlYQA
2024-06-20 22:02:44,316  main        INFO   c.a.c.c.Config    agent.max_threads=5000
2024-06-20 22:02:44,316  main        INFO   c.a.c.c.Config    kafka.topic=sentCampaingEmail
2024-06-20 22:02:44,316  main        INFO   c.a.c.c.Config    agent.kafka=false
2024-06-20 22:02:44,316  main        INFO   c.a.c.c.Config    proxy.server=proxy.np.ab
2024-06-20 22:02:44,316  main        INFO   c.a.c.c.Config    proxy.port=3128
2024-06-20 22:02:44,316  main        INFO   c.a.c.c.Config    proxy.realm="proxy"
2024-06-20 22:02:44,316  main        INFO   c.a.c.c.Config    proxy.user=sascloud_user
2024-06-20 22:02:44,317  main        INFO   c.a.c.c.Config    proxy.non_proxy_hosts=app-sas-test.np.ab
2024-06-20 22:02:44,317  main        INFO   c.a.c.a.Agent    test mode = false
2024-06-20 22:02:44,318  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-20 22:02:44,320  main        ERROR  c.a.c.a.Agent    Inicialization error. java.lang.IllegalArgumentException: Illegal base64 character 2e
java.lang.IllegalArgumentException: Illegal base64 character 2e
	at java.base/java.util.Base64$Decoder.decode0(Base64.java:746)
	at java.base/java.util.Base64$Decoder.decode(Base64.java:538)
	at java.base/java.util.Base64$Decoder.decode(Base64.java:561)
	at cz.ab.ci360.common.Util.base64Decode(Util.java:30)
	at cz.ab.ci360.common.Config.loadKeyValueBase64(Config.java:38)
	at cz.ab.ci360.agent_ch_stream.CI360Api.init(CI360Api.java:23)
	at cz.ab.ci360.agent_ch_stream.Agent.main(Agent.java:133)
2024-06-20 22:02:44,320  main        INFO   c.a.c.a.Agent    Exit code: 1
2024-06-20 22:03:50,072  main        INFO   c.a.c.a.Agent    
2024-06-20 22:03:50,073  main        INFO   c.a.c.a.Agent    ----- starting -------------------------------------------------------
2024-06-20 22:03:50,078  main        INFO   c.a.c.a.Agent    App: /opt/agents/ch-stream/agent-ch-stream.jar
2024-06-20 22:03:50,080  main        INFO   c.a.c.a.Agent    Version: 1.0.0
2024-06-20 22:03:50,080  main        INFO   c.a.c.a.Agent    ----- configuration --------------------------------------------------
2024-06-20 22:03:50,083  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-20 22:03:50,085  main        INFO   c.a.c.c.Config    ci360.jwt=ZXlKMGVYQWlPaUpLVjFRaUxDSmhiR2NpT2lKSVV6STFOaUo5LmV5SmpiR2xsYm5SSlJDSTZJbVl3WTJJeU1qVXdOall3TURBeE5tSXhPREExWldVNFlpSjkuU1MwVXBORlQtdVFvbGNIVktISGl5aDRBak1MZnhiT3ZwaDFCTXRXbFlRQQo=
2024-06-20 22:03:50,085  main        INFO   c.a.c.c.Config    agent.max_threads=5000
2024-06-20 22:03:50,085  main        INFO   c.a.c.c.Config    kafka.topic=sentCampaingEmail
2024-06-20 22:03:50,085  main        INFO   c.a.c.c.Config    agent.kafka=false
2024-06-20 22:03:50,085  main        INFO   c.a.c.c.Config    proxy.server=proxy.np.ab
2024-06-20 22:03:50,085  main        INFO   c.a.c.c.Config    proxy.port=3128
2024-06-20 22:03:50,085  main        INFO   c.a.c.c.Config    proxy.realm="proxy"
2024-06-20 22:03:50,085  main        INFO   c.a.c.c.Config    proxy.user=sascloud_user
2024-06-20 22:03:50,086  main        INFO   c.a.c.c.Config    proxy.non_proxy_hosts=app-sas-test.np.ab
2024-06-20 22:03:50,087  main        INFO   c.a.c.a.Agent    test mode = false
2024-06-20 22:03:50,087  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-20 22:03:50,088  main        INFO   c.a.c.c.Config    db.user=APP_CAMPAIGN_CDM2
2024-06-20 22:03:50,088  main        INFO   c.a.c.c.Config    db.database=APP_CAMPAIGN_CDM2
2024-06-20 22:03:50,088  main        INFO   c.a.c.c.Config    db.server=DBS01.DE11.NP.AB:1621
2024-06-20 22:03:50,088  main        INFO   c.a.c.c.Config    db.service_name=MNDE11DW.NP.AB
2024-06-20 22:03:50,088  main        INFO   c.a.c.c.Config    db.init_connection_pool_size=10
2024-06-20 22:03:50,088  main        INFO   c.a.c.c.Config    db.max_connection_pool_size=200
2024-06-20 22:03:51,355  main        INFO   c.a.c.c.Config    event.process_event_with_prefix=c_
2024-06-20 22:03:51,355  main        INFO   c.a.c.c.Config    event.direct_event=c_direct
2024-06-20 22:03:51,355  main        INFO   c.a.c.c.Config    event.ignore_events=c_activitystart,c_abtestpathassignment
2024-06-20 22:03:51,355  main        INFO   c.a.c.a.Agent    ----- eof configuration --------------------------------------------------
2024-06-20 22:03:51,359  main        INFO   c.s.m.a.s.CI360Agent    Maximum upstream queue size: 1000
2024-06-20 22:03:51,359  main        INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 22:03:51,409  main        INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-20 22:03:51,671  4818087-23  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-20 22:03:51,672  4818087-23  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-20 22:03:51,673  4818087-23  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 22:04:06,673  4818087-23  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 22:04:06,673  4818087-23  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-20 22:04:06,679  4818087-17  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-20 22:04:06,679  4818087-17  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-20 22:04:06,679  4818087-17  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 22:04:12,570  main        INFO   c.a.c.a.Agent    Stop reason: 'signal'
2024-06-20 22:04:13,580  main        INFO   c.a.c.a.Agent    Exit code: 0
2024-06-20 22:04:38,942  main        INFO   c.a.c.a.Agent    
2024-06-20 22:04:38,943  main        INFO   c.a.c.a.Agent    ----- starting -------------------------------------------------------
2024-06-20 22:04:38,948  main        INFO   c.a.c.a.Agent    App: /opt/agents/ch-stream/agent-ch-stream.jar
2024-06-20 22:04:38,951  main        INFO   c.a.c.a.Agent    Version: 1.0.0
2024-06-20 22:04:38,951  main        INFO   c.a.c.a.Agent    ----- configuration --------------------------------------------------
2024-06-20 22:04:38,954  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-20 22:04:38,956  main        INFO   c.a.c.c.Config    ci360.jwt=ZXlKMGVYQWlPaUpLVjFRaUxDSmhiR2NpT2lKSVV6STFOaUo5LmV5SmpiR2xsYm5SSlJDSTZJbVl3WTJJeU1qVXdOall3TURBeE5tSXhPREExWldVNFlpSjkuU1MwVXBORlQtdVFvbGNIVktISGl5aDRBak1MZnhiT3ZwaDFCTXRXbFlRQQo=
2024-06-20 22:04:38,956  main        INFO   c.a.c.c.Config    agent.max_threads=5000
2024-06-20 22:04:38,956  main        INFO   c.a.c.c.Config    kafka.topic=sentCampaingEmail
2024-06-20 22:04:38,956  main        INFO   c.a.c.c.Config    agent.kafka=false
2024-06-20 22:04:38,956  main        INFO   c.a.c.c.Config    proxy.server=proxy.np.ab
2024-06-20 22:04:38,956  main        INFO   c.a.c.c.Config    proxy.port=3128
2024-06-20 22:04:38,956  main        INFO   c.a.c.c.Config    proxy.realm=proxy
2024-06-20 22:04:38,956  main        INFO   c.a.c.c.Config    proxy.user=sascloud_user
2024-06-20 22:04:38,957  main        INFO   c.a.c.c.Config    proxy.non_proxy_hosts=app-sas-test.np.ab
2024-06-20 22:04:38,958  main        INFO   c.a.c.a.Agent    test mode = false
2024-06-20 22:04:38,958  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-20 22:04:38,959  main        INFO   c.a.c.c.Config    db.user=APP_CAMPAIGN_CDM2
2024-06-20 22:04:38,959  main        INFO   c.a.c.c.Config    db.database=APP_CAMPAIGN_CDM2
2024-06-20 22:04:38,959  main        INFO   c.a.c.c.Config    db.server=DBS01.DE11.NP.AB:1621
2024-06-20 22:04:38,959  main        INFO   c.a.c.c.Config    db.service_name=MNDE11DW.NP.AB
2024-06-20 22:04:38,959  main        INFO   c.a.c.c.Config    db.init_connection_pool_size=10
2024-06-20 22:04:38,959  main        INFO   c.a.c.c.Config    db.max_connection_pool_size=200
2024-06-20 22:04:39,902  main        INFO   c.a.c.c.Config    event.process_event_with_prefix=c_
2024-06-20 22:04:39,902  main        INFO   c.a.c.c.Config    event.direct_event=c_direct
2024-06-20 22:04:39,903  main        INFO   c.a.c.c.Config    event.ignore_events=c_activitystart,c_abtestpathassignment
2024-06-20 22:04:39,903  main        INFO   c.a.c.a.Agent    ----- eof configuration --------------------------------------------------
2024-06-20 22:04:39,907  main        INFO   c.s.m.a.s.CI360Agent    Maximum upstream queue size: 1000
2024-06-20 22:04:39,907  main        INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 22:04:39,972  main        INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-20 22:04:40,433  5043985-21  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-20 22:05:40,149  main        INFO   c.a.c.a.Agent    running
2024-06-20 22:05:50,447  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-20 22:05:50,448  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-20 22:05:50,449  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 22:06:05,449  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 22:06:05,449  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-20 22:06:05,454  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-20 22:06:05,455  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-20 22:06:05,456  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 22:06:05,643  5043985-22  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-20 22:06:20,456  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 22:06:20,459  cheduler-1  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$2.streamClosed(Agent.java:191)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$sendFrame$7(WebSocketCoreSession.java:519)
	at org.eclipse.jetty.util.Callback$3.succeeded(Callback.java:155)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.notifyCallbackSuccess(TransformingFlusher.java:197)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher$Flusher.process(TransformingFlusher.java:154)
	at org.eclipse.jetty.util.IteratingCallback.processing(IteratingCallback.java:243)
	at org.eclipse.jetty.util.IteratingCallback.iterate(IteratingCallback.java:224)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.sendFrame(TransformingFlusher.java:77)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.sendFrame(WebSocketCoreSession.java:522)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.close(WebSocketCoreSession.java:239)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.processHandlerError(WebSocketCoreSession.java:371)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onIdleExpired(WebSocketConnection.java:242)
	at org.eclipse.jetty.io.ssl.SslConnection.onIdleExpired(SslConnection.java:360)
	at org.eclipse.jetty.io.AbstractEndPoint.onIdleExpired(AbstractEndPoint.java:407)
	at org.eclipse.jetty.io.IdleTimeout.checkIdleTimeout(IdleTimeout.java:170)
	at org.eclipse.jetty.io.IdleTimeout.idleCheck(IdleTimeout.java:112)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 22:06:40,160  main        INFO   c.a.c.a.Agent    running
2024-06-20 22:07:15,646  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-20 22:07:15,646  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-20 22:07:15,646  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 22:07:30,646  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 22:07:30,647  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-20 22:07:30,647  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-20 22:07:30,647  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-20 22:07:30,648  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 22:07:30,754  5043985-23  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-20 22:07:40,171  main        INFO   c.a.c.a.Agent    running
2024-06-20 22:07:45,648  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 22:07:45,648  cheduler-1  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$2.streamClosed(Agent.java:191)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$sendFrame$7(WebSocketCoreSession.java:519)
	at org.eclipse.jetty.util.Callback$3.succeeded(Callback.java:155)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.notifyCallbackSuccess(TransformingFlusher.java:197)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher$Flusher.process(TransformingFlusher.java:154)
	at org.eclipse.jetty.util.IteratingCallback.processing(IteratingCallback.java:243)
	at org.eclipse.jetty.util.IteratingCallback.iterate(IteratingCallback.java:224)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.sendFrame(TransformingFlusher.java:77)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.sendFrame(WebSocketCoreSession.java:522)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.close(WebSocketCoreSession.java:239)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.processHandlerError(WebSocketCoreSession.java:371)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onIdleExpired(WebSocketConnection.java:242)
	at org.eclipse.jetty.io.ssl.SslConnection.onIdleExpired(SslConnection.java:360)
	at org.eclipse.jetty.io.AbstractEndPoint.onIdleExpired(AbstractEndPoint.java:407)
	at org.eclipse.jetty.io.IdleTimeout.checkIdleTimeout(IdleTimeout.java:170)
	at org.eclipse.jetty.io.IdleTimeout.idleCheck(IdleTimeout.java:112)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 22:08:40,181  main        INFO   c.a.c.a.Agent    running
2024-06-20 22:08:40,756  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-20 22:08:40,756  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-20 22:08:40,757  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 22:08:55,757  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 22:08:55,757  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-20 22:08:55,758  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-20 22:08:55,758  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-20 22:08:55,758  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 22:08:55,905  5043985-20  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-20 22:09:10,758  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 22:09:10,759  cheduler-1  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$2.streamClosed(Agent.java:191)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$sendFrame$7(WebSocketCoreSession.java:519)
	at org.eclipse.jetty.util.Callback$3.succeeded(Callback.java:155)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.notifyCallbackSuccess(TransformingFlusher.java:197)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher$Flusher.process(TransformingFlusher.java:154)
	at org.eclipse.jetty.util.IteratingCallback.processing(IteratingCallback.java:243)
	at org.eclipse.jetty.util.IteratingCallback.iterate(IteratingCallback.java:224)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.sendFrame(TransformingFlusher.java:77)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.sendFrame(WebSocketCoreSession.java:522)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.close(WebSocketCoreSession.java:239)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.processHandlerError(WebSocketCoreSession.java:371)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onIdleExpired(WebSocketConnection.java:242)
	at org.eclipse.jetty.io.ssl.SslConnection.onIdleExpired(SslConnection.java:360)
	at org.eclipse.jetty.io.AbstractEndPoint.onIdleExpired(AbstractEndPoint.java:407)
	at org.eclipse.jetty.io.IdleTimeout.checkIdleTimeout(IdleTimeout.java:170)
	at org.eclipse.jetty.io.IdleTimeout.idleCheck(IdleTimeout.java:112)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 22:09:40,190  main        INFO   c.a.c.a.Agent    running
2024-06-20 22:10:05,909  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-20 22:10:05,909  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-20 22:10:05,909  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 22:10:20,909  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 22:10:20,910  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-20 22:10:20,910  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-20 22:10:20,910  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-20 22:10:20,910  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 22:10:21,043  5043985-19  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-20 22:10:35,911  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 22:10:35,911  cheduler-1  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$2.streamClosed(Agent.java:191)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$sendFrame$7(WebSocketCoreSession.java:519)
	at org.eclipse.jetty.util.Callback$3.succeeded(Callback.java:155)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.notifyCallbackSuccess(TransformingFlusher.java:197)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher$Flusher.process(TransformingFlusher.java:154)
	at org.eclipse.jetty.util.IteratingCallback.processing(IteratingCallback.java:243)
	at org.eclipse.jetty.util.IteratingCallback.iterate(IteratingCallback.java:224)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.sendFrame(TransformingFlusher.java:77)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.sendFrame(WebSocketCoreSession.java:522)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.close(WebSocketCoreSession.java:239)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.processHandlerError(WebSocketCoreSession.java:371)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onIdleExpired(WebSocketConnection.java:242)
	at org.eclipse.jetty.io.ssl.SslConnection.onIdleExpired(SslConnection.java:360)
	at org.eclipse.jetty.io.AbstractEndPoint.onIdleExpired(AbstractEndPoint.java:407)
	at org.eclipse.jetty.io.IdleTimeout.checkIdleTimeout(IdleTimeout.java:170)
	at org.eclipse.jetty.io.IdleTimeout.idleCheck(IdleTimeout.java:112)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 22:10:40,199  main        INFO   c.a.c.a.Agent    running
2024-06-20 22:11:31,046  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-20 22:11:31,046  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-20 22:11:31,046  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 22:11:40,209  main        INFO   c.a.c.a.Agent    running
2024-06-20 22:11:46,046  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 22:11:46,046  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-20 22:11:46,047  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-20 22:11:46,047  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-20 22:11:46,047  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 22:11:46,200  5043985-18  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-20 22:12:01,047  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 22:12:01,048  cheduler-1  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$2.streamClosed(Agent.java:191)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$sendFrame$7(WebSocketCoreSession.java:519)
	at org.eclipse.jetty.util.Callback$3.succeeded(Callback.java:155)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.notifyCallbackSuccess(TransformingFlusher.java:197)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher$Flusher.process(TransformingFlusher.java:154)
	at org.eclipse.jetty.util.IteratingCallback.processing(IteratingCallback.java:243)
	at org.eclipse.jetty.util.IteratingCallback.iterate(IteratingCallback.java:224)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.sendFrame(TransformingFlusher.java:77)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.sendFrame(WebSocketCoreSession.java:522)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.close(WebSocketCoreSession.java:239)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.processHandlerError(WebSocketCoreSession.java:371)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onIdleExpired(WebSocketConnection.java:242)
	at org.eclipse.jetty.io.ssl.SslConnection.onIdleExpired(SslConnection.java:360)
	at org.eclipse.jetty.io.AbstractEndPoint.onIdleExpired(AbstractEndPoint.java:407)
	at org.eclipse.jetty.io.IdleTimeout.checkIdleTimeout(IdleTimeout.java:170)
	at org.eclipse.jetty.io.IdleTimeout.idleCheck(IdleTimeout.java:112)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 22:12:40,217  main        INFO   c.a.c.a.Agent    running
2024-06-20 22:12:56,203  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-20 22:12:56,203  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-20 22:12:56,203  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 22:13:11,203  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 22:13:11,204  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-20 22:13:11,204  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-20 22:13:11,204  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-20 22:13:11,205  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 22:13:11,322  5043985-21  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-20 22:13:26,205  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 22:13:26,205  cheduler-1  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$2.streamClosed(Agent.java:191)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$sendFrame$7(WebSocketCoreSession.java:519)
	at org.eclipse.jetty.util.Callback$3.succeeded(Callback.java:155)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.notifyCallbackSuccess(TransformingFlusher.java:197)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher$Flusher.process(TransformingFlusher.java:154)
	at org.eclipse.jetty.util.IteratingCallback.processing(IteratingCallback.java:243)
	at org.eclipse.jetty.util.IteratingCallback.iterate(IteratingCallback.java:224)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.sendFrame(TransformingFlusher.java:77)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.sendFrame(WebSocketCoreSession.java:522)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.close(WebSocketCoreSession.java:239)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.processHandlerError(WebSocketCoreSession.java:371)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onIdleExpired(WebSocketConnection.java:242)
	at org.eclipse.jetty.io.ssl.SslConnection.onIdleExpired(SslConnection.java:360)
	at org.eclipse.jetty.io.AbstractEndPoint.onIdleExpired(AbstractEndPoint.java:407)
	at org.eclipse.jetty.io.IdleTimeout.checkIdleTimeout(IdleTimeout.java:170)
	at org.eclipse.jetty.io.IdleTimeout.idleCheck(IdleTimeout.java:112)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 22:13:40,225  main        INFO   c.a.c.a.Agent    running
2024-06-20 22:14:21,325  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-20 22:14:21,325  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-20 22:14:21,326  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 22:14:36,326  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 22:14:36,326  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-20 22:14:36,327  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-20 22:14:36,327  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-20 22:14:36,327  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 22:14:36,453  5043985-23  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-20 22:14:40,232  main        INFO   c.a.c.a.Agent    running
2024-06-20 22:14:51,327  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 22:14:51,328  cheduler-1  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$2.streamClosed(Agent.java:191)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$sendFrame$7(WebSocketCoreSession.java:519)
	at org.eclipse.jetty.util.Callback$3.succeeded(Callback.java:155)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.notifyCallbackSuccess(TransformingFlusher.java:197)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher$Flusher.process(TransformingFlusher.java:154)
	at org.eclipse.jetty.util.IteratingCallback.processing(IteratingCallback.java:243)
	at org.eclipse.jetty.util.IteratingCallback.iterate(IteratingCallback.java:224)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.sendFrame(TransformingFlusher.java:77)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.sendFrame(WebSocketCoreSession.java:522)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.close(WebSocketCoreSession.java:239)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.processHandlerError(WebSocketCoreSession.java:371)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onIdleExpired(WebSocketConnection.java:242)
	at org.eclipse.jetty.io.ssl.SslConnection.onIdleExpired(SslConnection.java:360)
	at org.eclipse.jetty.io.AbstractEndPoint.onIdleExpired(AbstractEndPoint.java:407)
	at org.eclipse.jetty.io.IdleTimeout.checkIdleTimeout(IdleTimeout.java:170)
	at org.eclipse.jetty.io.IdleTimeout.idleCheck(IdleTimeout.java:112)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 22:15:40,239  main        INFO   c.a.c.a.Agent    running
2024-06-20 22:15:46,456  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-20 22:15:46,456  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-20 22:15:46,456  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 22:16:01,456  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 22:16:01,457  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-20 22:16:01,457  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-20 22:16:01,457  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-20 22:16:01,458  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 22:16:01,588  5043985-20  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-20 22:16:16,458  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 22:16:16,458  cheduler-1  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$2.streamClosed(Agent.java:191)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$sendFrame$7(WebSocketCoreSession.java:519)
	at org.eclipse.jetty.util.Callback$3.succeeded(Callback.java:155)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.notifyCallbackSuccess(TransformingFlusher.java:197)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher$Flusher.process(TransformingFlusher.java:154)
	at org.eclipse.jetty.util.IteratingCallback.processing(IteratingCallback.java:243)
	at org.eclipse.jetty.util.IteratingCallback.iterate(IteratingCallback.java:224)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.sendFrame(TransformingFlusher.java:77)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.sendFrame(WebSocketCoreSession.java:522)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.close(WebSocketCoreSession.java:239)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.processHandlerError(WebSocketCoreSession.java:371)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onIdleExpired(WebSocketConnection.java:242)
	at org.eclipse.jetty.io.ssl.SslConnection.onIdleExpired(SslConnection.java:360)
	at org.eclipse.jetty.io.AbstractEndPoint.onIdleExpired(AbstractEndPoint.java:407)
	at org.eclipse.jetty.io.IdleTimeout.checkIdleTimeout(IdleTimeout.java:170)
	at org.eclipse.jetty.io.IdleTimeout.idleCheck(IdleTimeout.java:112)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 22:16:40,246  main        INFO   c.a.c.a.Agent    running
2024-06-20 22:17:11,590  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-20 22:17:11,590  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-20 22:17:11,590  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 22:17:26,590  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 22:17:26,591  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-20 22:17:26,592  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-20 22:17:26,592  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-20 22:17:26,592  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 22:17:26,719  5043985-22  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-20 22:17:40,254  main        INFO   c.a.c.a.Agent    running
2024-06-20 22:17:41,592  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 22:17:41,592  cheduler-1  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$2.streamClosed(Agent.java:191)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$sendFrame$7(WebSocketCoreSession.java:519)
	at org.eclipse.jetty.util.Callback$3.succeeded(Callback.java:155)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.notifyCallbackSuccess(TransformingFlusher.java:197)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher$Flusher.process(TransformingFlusher.java:154)
	at org.eclipse.jetty.util.IteratingCallback.processing(IteratingCallback.java:243)
	at org.eclipse.jetty.util.IteratingCallback.iterate(IteratingCallback.java:224)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.sendFrame(TransformingFlusher.java:77)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.sendFrame(WebSocketCoreSession.java:522)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.close(WebSocketCoreSession.java:239)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.processHandlerError(WebSocketCoreSession.java:371)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onIdleExpired(WebSocketConnection.java:242)
	at org.eclipse.jetty.io.ssl.SslConnection.onIdleExpired(SslConnection.java:360)
	at org.eclipse.jetty.io.AbstractEndPoint.onIdleExpired(AbstractEndPoint.java:407)
	at org.eclipse.jetty.io.IdleTimeout.checkIdleTimeout(IdleTimeout.java:170)
	at org.eclipse.jetty.io.IdleTimeout.idleCheck(IdleTimeout.java:112)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 22:18:36,722  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-20 22:18:36,722  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-20 22:18:36,722  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 22:18:40,261  main        INFO   c.a.c.a.Agent    running
2024-06-20 22:18:51,722  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 22:18:51,723  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-20 22:18:51,724  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-20 22:18:51,724  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-20 22:18:51,724  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 22:18:51,864  5043985-17  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-20 22:19:06,724  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 22:19:06,724  cheduler-1  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$2.streamClosed(Agent.java:191)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$sendFrame$7(WebSocketCoreSession.java:519)
	at org.eclipse.jetty.util.Callback$3.succeeded(Callback.java:155)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.notifyCallbackSuccess(TransformingFlusher.java:197)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher$Flusher.process(TransformingFlusher.java:154)
	at org.eclipse.jetty.util.IteratingCallback.processing(IteratingCallback.java:243)
	at org.eclipse.jetty.util.IteratingCallback.iterate(IteratingCallback.java:224)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.sendFrame(TransformingFlusher.java:77)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.sendFrame(WebSocketCoreSession.java:522)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.close(WebSocketCoreSession.java:239)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.processHandlerError(WebSocketCoreSession.java:371)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onIdleExpired(WebSocketConnection.java:242)
	at org.eclipse.jetty.io.ssl.SslConnection.onIdleExpired(SslConnection.java:360)
	at org.eclipse.jetty.io.AbstractEndPoint.onIdleExpired(AbstractEndPoint.java:407)
	at org.eclipse.jetty.io.IdleTimeout.checkIdleTimeout(IdleTimeout.java:170)
	at org.eclipse.jetty.io.IdleTimeout.idleCheck(IdleTimeout.java:112)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 22:19:40,269  main        INFO   c.a.c.a.Agent    running
2024-06-20 22:20:01,867  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-20 22:20:01,867  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-20 22:20:01,867  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 22:20:16,867  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 22:20:16,868  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-20 22:20:16,868  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-20 22:20:16,868  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-20 22:20:16,869  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 22:20:17,009  5043985-19  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-20 22:20:31,869  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 22:20:31,869  cheduler-1  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$2.streamClosed(Agent.java:191)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$sendFrame$7(WebSocketCoreSession.java:519)
	at org.eclipse.jetty.util.Callback$3.succeeded(Callback.java:155)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.notifyCallbackSuccess(TransformingFlusher.java:197)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher$Flusher.process(TransformingFlusher.java:154)
	at org.eclipse.jetty.util.IteratingCallback.processing(IteratingCallback.java:243)
	at org.eclipse.jetty.util.IteratingCallback.iterate(IteratingCallback.java:224)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.sendFrame(TransformingFlusher.java:77)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.sendFrame(WebSocketCoreSession.java:522)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.close(WebSocketCoreSession.java:239)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.processHandlerError(WebSocketCoreSession.java:371)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onIdleExpired(WebSocketConnection.java:242)
	at org.eclipse.jetty.io.ssl.SslConnection.onIdleExpired(SslConnection.java:360)
	at org.eclipse.jetty.io.AbstractEndPoint.onIdleExpired(AbstractEndPoint.java:407)
	at org.eclipse.jetty.io.IdleTimeout.checkIdleTimeout(IdleTimeout.java:170)
	at org.eclipse.jetty.io.IdleTimeout.idleCheck(IdleTimeout.java:112)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 22:20:40,277  main        INFO   c.a.c.a.Agent    running
2024-06-20 22:21:27,012  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-20 22:21:27,012  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-20 22:21:27,012  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 22:21:40,284  main        INFO   c.a.c.a.Agent    running
2024-06-20 22:21:42,012  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 22:21:42,013  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-20 22:21:42,014  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-20 22:21:42,014  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-20 22:21:42,014  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 22:21:42,129  5043985-24  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-20 22:21:57,014  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 22:21:57,015  cheduler-1  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$2.streamClosed(Agent.java:191)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$sendFrame$7(WebSocketCoreSession.java:519)
	at org.eclipse.jetty.util.Callback$3.succeeded(Callback.java:155)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.notifyCallbackSuccess(TransformingFlusher.java:197)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher$Flusher.process(TransformingFlusher.java:154)
	at org.eclipse.jetty.util.IteratingCallback.processing(IteratingCallback.java:243)
	at org.eclipse.jetty.util.IteratingCallback.iterate(IteratingCallback.java:224)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.sendFrame(TransformingFlusher.java:77)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.sendFrame(WebSocketCoreSession.java:522)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.close(WebSocketCoreSession.java:239)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.processHandlerError(WebSocketCoreSession.java:371)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onIdleExpired(WebSocketConnection.java:242)
	at org.eclipse.jetty.io.ssl.SslConnection.onIdleExpired(SslConnection.java:360)
	at org.eclipse.jetty.io.AbstractEndPoint.onIdleExpired(AbstractEndPoint.java:407)
	at org.eclipse.jetty.io.IdleTimeout.checkIdleTimeout(IdleTimeout.java:170)
	at org.eclipse.jetty.io.IdleTimeout.idleCheck(IdleTimeout.java:112)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 22:22:40,292  main        INFO   c.a.c.a.Agent    running
2024-06-20 22:22:52,131  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-20 22:22:52,132  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-20 22:22:52,132  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 22:23:07,132  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 22:23:07,132  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-20 22:23:07,133  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-20 22:23:07,133  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-20 22:23:07,133  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 22:23:07,264  5043985-18  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-20 22:23:22,133  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 22:23:22,134  cheduler-1  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$2.streamClosed(Agent.java:191)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$sendFrame$7(WebSocketCoreSession.java:519)
	at org.eclipse.jetty.util.Callback$3.succeeded(Callback.java:155)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.notifyCallbackSuccess(TransformingFlusher.java:197)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher$Flusher.process(TransformingFlusher.java:154)
	at org.eclipse.jetty.util.IteratingCallback.processing(IteratingCallback.java:243)
	at org.eclipse.jetty.util.IteratingCallback.iterate(IteratingCallback.java:224)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.sendFrame(TransformingFlusher.java:77)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.sendFrame(WebSocketCoreSession.java:522)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.close(WebSocketCoreSession.java:239)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.processHandlerError(WebSocketCoreSession.java:371)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onIdleExpired(WebSocketConnection.java:242)
	at org.eclipse.jetty.io.ssl.SslConnection.onIdleExpired(SslConnection.java:360)
	at org.eclipse.jetty.io.AbstractEndPoint.onIdleExpired(AbstractEndPoint.java:407)
	at org.eclipse.jetty.io.IdleTimeout.checkIdleTimeout(IdleTimeout.java:170)
	at org.eclipse.jetty.io.IdleTimeout.idleCheck(IdleTimeout.java:112)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 22:23:40,299  main        INFO   c.a.c.a.Agent    running
2024-06-20 22:24:17,266  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-20 22:24:17,266  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-20 22:24:17,267  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 22:24:32,267  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 22:24:32,267  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-20 22:24:32,268  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-20 22:24:32,268  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-20 22:24:32,268  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 22:24:32,413  5043985-21  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-20 22:24:40,307  main        INFO   c.a.c.a.Agent    running
2024-06-20 22:24:47,268  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 22:24:47,269  cheduler-1  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$2.streamClosed(Agent.java:191)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$sendFrame$7(WebSocketCoreSession.java:519)
	at org.eclipse.jetty.util.Callback$3.succeeded(Callback.java:155)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.notifyCallbackSuccess(TransformingFlusher.java:197)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher$Flusher.process(TransformingFlusher.java:154)
	at org.eclipse.jetty.util.IteratingCallback.processing(IteratingCallback.java:243)
	at org.eclipse.jetty.util.IteratingCallback.iterate(IteratingCallback.java:224)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.sendFrame(TransformingFlusher.java:77)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.sendFrame(WebSocketCoreSession.java:522)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.close(WebSocketCoreSession.java:239)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.processHandlerError(WebSocketCoreSession.java:371)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onIdleExpired(WebSocketConnection.java:242)
	at org.eclipse.jetty.io.ssl.SslConnection.onIdleExpired(SslConnection.java:360)
	at org.eclipse.jetty.io.AbstractEndPoint.onIdleExpired(AbstractEndPoint.java:407)
	at org.eclipse.jetty.io.IdleTimeout.checkIdleTimeout(IdleTimeout.java:170)
	at org.eclipse.jetty.io.IdleTimeout.idleCheck(IdleTimeout.java:112)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 22:25:40,316  main        INFO   c.a.c.a.Agent    running
2024-06-20 22:25:42,415  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-20 22:25:42,416  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-20 22:25:42,416  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 22:25:57,416  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 22:25:57,416  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-20 22:25:57,417  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-20 22:25:57,417  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-20 22:25:57,417  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 22:25:57,546  5043985-22  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-20 22:26:12,417  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 22:26:12,418  cheduler-1  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$2.streamClosed(Agent.java:191)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$sendFrame$7(WebSocketCoreSession.java:519)
	at org.eclipse.jetty.util.Callback$3.succeeded(Callback.java:155)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.notifyCallbackSuccess(TransformingFlusher.java:197)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher$Flusher.process(TransformingFlusher.java:154)
	at org.eclipse.jetty.util.IteratingCallback.processing(IteratingCallback.java:243)
	at org.eclipse.jetty.util.IteratingCallback.iterate(IteratingCallback.java:224)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.sendFrame(TransformingFlusher.java:77)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.sendFrame(WebSocketCoreSession.java:522)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.close(WebSocketCoreSession.java:239)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.processHandlerError(WebSocketCoreSession.java:371)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onIdleExpired(WebSocketConnection.java:242)
	at org.eclipse.jetty.io.ssl.SslConnection.onIdleExpired(SslConnection.java:360)
	at org.eclipse.jetty.io.AbstractEndPoint.onIdleExpired(AbstractEndPoint.java:407)
	at org.eclipse.jetty.io.IdleTimeout.checkIdleTimeout(IdleTimeout.java:170)
	at org.eclipse.jetty.io.IdleTimeout.idleCheck(IdleTimeout.java:112)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 22:26:40,325  main        INFO   c.a.c.a.Agent    running
2024-06-20 22:27:07,549  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-20 22:27:07,549  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-20 22:27:07,549  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 22:27:22,549  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 22:27:22,549  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-20 22:27:22,550  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-20 22:27:22,550  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-20 22:27:22,550  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 22:27:22,677  5043985-20  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-20 22:27:37,551  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 22:27:37,552  cheduler-1  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$2.streamClosed(Agent.java:191)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$sendFrame$7(WebSocketCoreSession.java:519)
	at org.eclipse.jetty.util.Callback$3.succeeded(Callback.java:155)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.notifyCallbackSuccess(TransformingFlusher.java:197)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher$Flusher.process(TransformingFlusher.java:154)
	at org.eclipse.jetty.util.IteratingCallback.processing(IteratingCallback.java:243)
	at org.eclipse.jetty.util.IteratingCallback.iterate(IteratingCallback.java:224)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.sendFrame(TransformingFlusher.java:77)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.sendFrame(WebSocketCoreSession.java:522)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.close(WebSocketCoreSession.java:239)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.processHandlerError(WebSocketCoreSession.java:371)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onIdleExpired(WebSocketConnection.java:242)
	at org.eclipse.jetty.io.ssl.SslConnection.onIdleExpired(SslConnection.java:360)
	at org.eclipse.jetty.io.AbstractEndPoint.onIdleExpired(AbstractEndPoint.java:407)
	at org.eclipse.jetty.io.IdleTimeout.checkIdleTimeout(IdleTimeout.java:170)
	at org.eclipse.jetty.io.IdleTimeout.idleCheck(IdleTimeout.java:112)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 22:27:40,333  main        INFO   c.a.c.a.Agent    running
2024-06-20 22:28:32,680  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-20 22:28:32,680  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-20 22:28:32,680  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 22:28:40,341  main        INFO   c.a.c.a.Agent    running
2024-06-20 22:28:47,680  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 22:28:47,680  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-20 22:28:47,681  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-20 22:28:47,681  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-20 22:28:47,681  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 22:28:47,824  5043985-17  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-20 22:29:02,682  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 22:29:02,682  cheduler-1  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$2.streamClosed(Agent.java:191)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$sendFrame$7(WebSocketCoreSession.java:519)
	at org.eclipse.jetty.util.Callback$3.succeeded(Callback.java:155)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.notifyCallbackSuccess(TransformingFlusher.java:197)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher$Flusher.process(TransformingFlusher.java:154)
	at org.eclipse.jetty.util.IteratingCallback.processing(IteratingCallback.java:243)
	at org.eclipse.jetty.util.IteratingCallback.iterate(IteratingCallback.java:224)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.sendFrame(TransformingFlusher.java:77)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.sendFrame(WebSocketCoreSession.java:522)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.close(WebSocketCoreSession.java:239)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.processHandlerError(WebSocketCoreSession.java:371)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onIdleExpired(WebSocketConnection.java:242)
	at org.eclipse.jetty.io.ssl.SslConnection.onIdleExpired(SslConnection.java:360)
	at org.eclipse.jetty.io.AbstractEndPoint.onIdleExpired(AbstractEndPoint.java:407)
	at org.eclipse.jetty.io.IdleTimeout.checkIdleTimeout(IdleTimeout.java:170)
	at org.eclipse.jetty.io.IdleTimeout.idleCheck(IdleTimeout.java:112)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 22:29:40,349  main        INFO   c.a.c.a.Agent    running
2024-06-20 22:29:57,827  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-20 22:29:57,827  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-20 22:29:57,827  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 22:30:12,827  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 22:30:12,827  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-20 22:30:12,828  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-20 22:30:12,828  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-20 22:30:12,828  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 22:30:12,982  5043985-23  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-20 22:30:27,828  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 22:30:27,829  cheduler-1  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$2.streamClosed(Agent.java:191)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$sendFrame$7(WebSocketCoreSession.java:519)
	at org.eclipse.jetty.util.Callback$3.succeeded(Callback.java:155)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.notifyCallbackSuccess(TransformingFlusher.java:197)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher$Flusher.process(TransformingFlusher.java:154)
	at org.eclipse.jetty.util.IteratingCallback.processing(IteratingCallback.java:243)
	at org.eclipse.jetty.util.IteratingCallback.iterate(IteratingCallback.java:224)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.sendFrame(TransformingFlusher.java:77)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.sendFrame(WebSocketCoreSession.java:522)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.close(WebSocketCoreSession.java:239)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.processHandlerError(WebSocketCoreSession.java:371)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onIdleExpired(WebSocketConnection.java:242)
	at org.eclipse.jetty.io.ssl.SslConnection.onIdleExpired(SslConnection.java:360)
	at org.eclipse.jetty.io.AbstractEndPoint.onIdleExpired(AbstractEndPoint.java:407)
	at org.eclipse.jetty.io.IdleTimeout.checkIdleTimeout(IdleTimeout.java:170)
	at org.eclipse.jetty.io.IdleTimeout.idleCheck(IdleTimeout.java:112)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 22:30:40,358  main        INFO   c.a.c.a.Agent    running
2024-06-20 22:31:22,984  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-20 22:31:22,985  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-20 22:31:22,985  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 22:31:37,985  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 22:31:37,985  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-20 22:31:37,986  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-20 22:31:37,986  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-20 22:31:37,986  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-20 22:31:38,119  5043985-19  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-20 22:31:40,366  main        INFO   c.a.c.a.Agent    running
2024-06-20 22:31:52,986  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 22:31:52,987  cheduler-1  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$2.streamClosed(Agent.java:191)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$sendFrame$7(WebSocketCoreSession.java:519)
	at org.eclipse.jetty.util.Callback$3.succeeded(Callback.java:155)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.notifyCallbackSuccess(TransformingFlusher.java:197)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher$Flusher.process(TransformingFlusher.java:154)
	at org.eclipse.jetty.util.IteratingCallback.processing(IteratingCallback.java:243)
	at org.eclipse.jetty.util.IteratingCallback.iterate(IteratingCallback.java:224)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.sendFrame(TransformingFlusher.java:77)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.sendFrame(WebSocketCoreSession.java:522)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.close(WebSocketCoreSession.java:239)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.processHandlerError(WebSocketCoreSession.java:371)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onIdleExpired(WebSocketConnection.java:242)
	at org.eclipse.jetty.io.ssl.SslConnection.onIdleExpired(SslConnection.java:360)
	at org.eclipse.jetty.io.AbstractEndPoint.onIdleExpired(AbstractEndPoint.java:407)
	at org.eclipse.jetty.io.IdleTimeout.checkIdleTimeout(IdleTimeout.java:170)
	at org.eclipse.jetty.io.IdleTimeout.idleCheck(IdleTimeout.java:112)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 22:32:01,369  main        INFO   c.a.c.a.Agent    Stop reason: 'signal'
2024-06-20 22:32:01,385  main        INFO   c.s.m.a.s.CI360Agent    Stopping event stream connection now
2024-06-20 22:32:01,386  main        INFO   o.e.j.w.c.WebSocketClient    Shutdown WebSocketClient@b6e2c88e[coreClient=WebSocketCoreClient@3d9f6567{STARTED},openSessions.size=1]
2024-06-20 22:32:01,387  5043985-23  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: null
2024-06-20 22:32:01,387  5043985-23  DEBUG  c.a.c.a.Agent    Stream closed
2024-06-20 22:32:01,387  5043985-23  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1006] Session Closed
2024-06-20 22:32:01,387  5043985-23  DEBUG  c.a.c.a.Agent    Stream closed
2024-06-20 22:32:02,394  main        INFO   c.a.c.a.Agent    Exit code: 0
2024-06-20 22:58:24,812  main        INFO   c.a.c.a.Agent    
2024-06-20 22:58:24,813  main        INFO   c.a.c.a.Agent    ----- starting -------------------------------------------------------
2024-06-20 22:58:24,820  main        INFO   c.a.c.a.Agent    App: /opt/agents/ch-stream/agent-ch-stream.jar
2024-06-20 22:58:24,822  main        INFO   c.a.c.a.Agent    Version: 1.0.0
2024-06-20 22:58:24,823  main        INFO   c.a.c.a.Agent    ----- configuration --------------------------------------------------
2024-06-20 22:58:24,826  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-20 22:58:24,827  main        INFO   c.a.c.c.Config    ci360.jwt=null
2024-06-20 22:58:24,827  main        INFO   c.a.c.c.Config    agent.max_threads=5000
2024-06-20 22:58:24,827  main        INFO   c.a.c.c.Config    kafka.topic=sentCampaingEmail
2024-06-20 22:58:24,827  main        INFO   c.a.c.c.Config    agent.kafka=false
2024-06-20 22:58:24,828  main        INFO   c.a.c.c.Config    proxy.server=proxy.np.ab
2024-06-20 22:58:24,828  main        INFO   c.a.c.c.Config    proxy.port=3128
2024-06-20 22:58:24,828  main        INFO   c.a.c.c.Config    proxy.realm=proxy
2024-06-20 22:58:24,828  main        INFO   c.a.c.c.Config    proxy.user=sascloud_user
2024-06-20 22:58:24,828  main        INFO   c.a.c.c.Config    proxy.non_proxy_hosts=app-sas-test.np.ab
2024-06-20 22:58:24,829  main        INFO   c.a.c.a.Agent    test mode = false
2024-06-20 22:58:24,830  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-20 22:58:24,830  main        INFO   c.a.c.c.Config    db.user=APP_CAMPAIGN_CDM2
2024-06-20 22:58:24,830  main        INFO   c.a.c.c.Config    db.database=APP_CAMPAIGN_CDM2
2024-06-20 22:58:24,830  main        INFO   c.a.c.c.Config    db.server=DBS01.DE11.NP.AB:1621
2024-06-20 22:58:24,830  main        INFO   c.a.c.c.Config    db.service_name=MNDE11DW.NP.AB
2024-06-20 22:58:24,830  main        INFO   c.a.c.c.Config    db.init_connection_pool_size=10
2024-06-20 22:58:24,830  main        INFO   c.a.c.c.Config    db.max_connection_pool_size=200
2024-06-20 22:58:25,496  main        INFO   c.a.c.c.Config    event.process_event_with_prefix=c_
2024-06-20 22:58:25,496  main        INFO   c.a.c.c.Config    event.direct_event=c_direct
2024-06-20 22:58:25,496  main        INFO   c.a.c.c.Config    event.ignore_events=c_activitystart,c_abtestpathassignment
2024-06-20 22:58:25,496  main        INFO   c.a.c.a.Agent    ----- eof configuration --------------------------------------------------
2024-06-20 22:58:25,498  main        ERROR  c.a.c.a.Agent    Agent generic error. java.lang.NullPointerException
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.putVal(ConcurrentHashMap.java:1011)
	at java.base/java.util.concurrent.ConcurrentHashMap.put(ConcurrentHashMap.java:1006)
	at java.base/java.util.Properties.put(Properties.java:1340)
	at java.base/java.util.Properties.setProperty(Properties.java:228)
	at cz.ab.ci360.agent_ch_stream.Agent.main(Agent.java:159)
2024-06-20 22:58:25,499  main        INFO   c.a.c.a.Agent    Stop reason: 'error'
2024-06-20 22:58:26,506  main        INFO   c.a.c.a.Agent    Exit code: 1
2024-06-20 22:59:46,793  main        INFO   c.a.c.a.Agent    
2024-06-20 22:59:46,795  main        INFO   c.a.c.a.Agent    ----- starting -------------------------------------------------------
2024-06-20 22:59:46,799  main        INFO   c.a.c.a.Agent    App: /opt/agents/ch-stream/agent-ch-stream.jar
2024-06-20 22:59:46,806  main        INFO   c.a.c.a.Agent    Version: 1.0.0
2024-06-20 22:59:46,806  main        INFO   c.a.c.a.Agent    ----- configuration --------------------------------------------------
2024-06-20 22:59:46,809  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-20 22:59:46,810  main        INFO   c.a.c.c.Config    ci360.jwt=
2024-06-20 22:59:46,811  main        INFO   c.a.c.c.Config    agent.max_threads=5000
2024-06-20 22:59:46,811  main        INFO   c.a.c.c.Config    kafka.topic=sentCampaingEmail
2024-06-20 22:59:46,811  main        INFO   c.a.c.c.Config    agent.kafka=false
2024-06-20 22:59:46,811  main        INFO   c.a.c.c.Config    proxy.server=proxy.np.ab
2024-06-20 22:59:46,811  main        INFO   c.a.c.c.Config    proxy.port=3128
2024-06-20 22:59:46,811  main        INFO   c.a.c.c.Config    proxy.realm=proxy
2024-06-20 22:59:46,811  main        INFO   c.a.c.c.Config    proxy.user=sascloud_user
2024-06-20 22:59:46,812  main        INFO   c.a.c.c.Config    proxy.non_proxy_hosts=app-sas-test.np.ab
2024-06-20 22:59:46,813  main        INFO   c.a.c.a.Agent    test mode = false
2024-06-20 22:59:46,813  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-20 22:59:46,814  main        INFO   c.a.c.c.Config    db.user=APP_CAMPAIGN_CDM2
2024-06-20 22:59:46,814  main        INFO   c.a.c.c.Config    db.database=APP_CAMPAIGN_CDM2
2024-06-20 22:59:46,814  main        INFO   c.a.c.c.Config    db.server=DBS01.DE11.NP.AB:1621
2024-06-20 22:59:46,814  main        INFO   c.a.c.c.Config    db.service_name=MNDE11DW.NP.AB
2024-06-20 22:59:46,814  main        INFO   c.a.c.c.Config    db.init_connection_pool_size=10
2024-06-20 22:59:46,814  main        INFO   c.a.c.c.Config    db.max_connection_pool_size=200
2024-06-20 22:59:47,545  main        INFO   c.a.c.c.Config    event.process_event_with_prefix=c_
2024-06-20 22:59:47,546  main        INFO   c.a.c.c.Config    event.direct_event=c_direct
2024-06-20 22:59:47,546  main        INFO   c.a.c.c.Config    event.ignore_events=c_activitystart,c_abtestpathassignment
2024-06-20 22:59:47,546  main        INFO   c.a.c.a.Agent    ----- eof configuration --------------------------------------------------
2024-06-20 22:59:47,549  main        INFO   c.s.m.a.s.CI360Agent    Maximum upstream queue size: 1000
2024-06-20 22:59:47,549  main        INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 22:59:47,551  main        ERROR  c.a.c.a.Agent    Agent generic error. com.sas.mkt.agent.sdk.CI360AgentException
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.processConnectionSettings(CI360Agent.java:644)
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:273)
	at cz.ab.ci360.agent_ch_stream.Agent.main(Agent.java:200)
2024-06-20 22:59:47,552  main        INFO   c.a.c.a.Agent    Stop reason: 'error'
2024-06-20 22:59:48,559  main        INFO   c.a.c.a.Agent    Exit code: 1
2024-06-20 23:01:01,210  main        INFO   c.a.c.a.Agent    
2024-06-20 23:01:01,212  main        INFO   c.a.c.a.Agent    ----- starting -------------------------------------------------------
2024-06-20 23:01:01,216  main        INFO   c.a.c.a.Agent    App: /opt/agents/ch-stream/agent-ch-stream.jar
2024-06-20 23:01:01,219  main        INFO   c.a.c.a.Agent    Version: 1.0.0
2024-06-20 23:01:01,219  main        INFO   c.a.c.a.Agent    ----- configuration --------------------------------------------------
2024-06-20 23:01:01,223  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-20 23:01:01,224  main        INFO   c.a.c.c.Config    ci360.tenantID=f0cb22506600016b1805ee8b
2024-06-20 23:01:01,225  main        INFO   c.a.c.c.Config    agent.max_threads=5000
2024-06-20 23:01:01,225  main        INFO   c.a.c.c.Config    kafka.topic=sentCampaingEmail
2024-06-20 23:01:01,225  main        INFO   c.a.c.c.Config    agent.kafka=false
2024-06-20 23:01:01,225  main        INFO   c.a.c.c.Config    proxy.server=proxy.np.ab
2024-06-20 23:01:01,225  main        INFO   c.a.c.c.Config    proxy.port=3128
2024-06-20 23:01:01,225  main        INFO   c.a.c.c.Config    proxy.realm=proxy
2024-06-20 23:01:01,225  main        INFO   c.a.c.c.Config    proxy.user=sascloud_user
2024-06-20 23:01:01,225  main        INFO   c.a.c.c.Config    proxy.non_proxy_hosts=app-sas-test.np.ab
2024-06-20 23:01:01,225  main        INFO   c.a.c.a.Agent    test mode = false
2024-06-20 23:01:01,226  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-20 23:01:01,229  main        INFO   c.a.c.c.Config    event.process_event_with_prefix=c_
2024-06-20 23:01:01,229  main        INFO   c.a.c.c.Config    event.direct_event=c_direct
2024-06-20 23:01:01,229  main        INFO   c.a.c.c.Config    event.ignore_events=c_activitystart,c_abtestpathassignment
2024-06-20 23:01:01,229  main        INFO   c.a.c.a.Agent    ----- eof configuration --------------------------------------------------
2024-06-20 23:01:01,235  main        INFO   c.s.m.a.s.CI360Agent    Maximum upstream queue size: 1000
2024-06-20 23:01:01,235  main        INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 23:01:01,307  main        INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-20 23:01:02,095  8912029-17  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-20 23:01:02,163  8912029-20  INFO   c.s.m.a.s.StreamWebSocket    Agent version: v2402
2024-06-20 23:01:02,183  8912029-20  INFO   c.s.m.a.s.StreamWebSocket    Gateway version: 1.0.125
2024-06-20 23:01:02,184  8912029-20  INFO   c.s.m.a.s.StreamWebSocket    Gateway supported versions: v2406,v2405,v2404,v2403,v2402,v2401,v2312
2024-06-20 23:01:02,184  8912029-20  INFO   c.s.m.a.s.StreamWebSocket    Gateway warning versions: v2311,v2310,v2309
2024-06-20 23:02:01,515  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:03:01,526  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:04:01,537  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:05:01,548  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:05:01,579  2-thread-1  INFO   c.a.c.a.Event    Event received, event: c_DCH, will be processed...
2024-06-20 23:05:01,580  2-thread-1  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"463860","externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"463860","session":"463860","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"subject_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"448e1dd2-6e51-3023-a56e-ba36e24be8af","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"463860","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","Text":"Test CH","message_id":"0","sessionId":"463860","event_category":"unifiedAndEngage","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"448e1dd2-6e51-3023-a56e-ba36e24be8af","guid":"1b9f0af3-cd2b-42a0-8c87-a03606409319","applicationId":"SAS360Proxy","customer_id":"33a1c5ca4227efd37dfd2b0bf6f645ffd189c07d1912be75ebad71e9988add2f453ae735584c8c9fbb3ad32b0850a22c","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"1b9f0af3-cd2b-42a0-8c87-a03606409319"}
2024-06-20 23:05:01,582  2-thread-1  ERROR  c.a.c.c.TaskCache    Error when loading CP from cie_contact_history_stream_task_cp_cache
java.lang.NullPointerException: null
	at cz.ab.ci360.common.SqlConnectionPool.getConnection(SqlConnectionPool.java:63)
	at cz.ab.ci360.cache.TaskCache.loadDatabaseTaskCp(TaskCache.java:121)
	at cz.ab.ci360.cache.TaskCache.loadCacheTaskCp(TaskCache.java:113)
	at cz.ab.ci360.cache.TaskCache.getTask(TaskCache.java:54)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:99)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 23:05:01,591  2-thread-1  ERROR  c.a.c.c.Http    Exception()
java.lang.IllegalArgumentException: Illegal character(s) in message header value: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJjbGllbnRJRCI6ImYwY2IyMjUwNjYwMDAxNmIxODA1ZWU4YiJ9.SS0UpNFT-uQolcHVKHHiyh4AjMLfxbOvph1BMtWlYQA

	at java.base/sun.net.www.protocol.http.HttpURLConnection.checkMessageHeader(HttpURLConnection.java:559)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.isExternalMessageHeaderAllowed(HttpURLConnection.java:494)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.setRequestProperty(HttpURLConnection.java:3193)
	at java.base/sun.net.www.protocol.https.HttpsURLConnectionImpl.setRequestProperty(HttpsURLConnectionImpl.java:312)
	at cz.ab.ci360.common.Http.get(Http.java:119)
	at cz.ab.ci360.agent_ch_stream.CI360Api.getTaskCustomProperties(CI360Api.java:30)
	at cz.ab.ci360.cache.TaskCache.getCI360ApiTaskCp(TaskCache.java:92)
	at cz.ab.ci360.cache.TaskCache.getTask(TaskCache.java:68)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:99)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 23:05:01,591  2-thread-1  ERROR  c.a.c.c.TaskCache    Exception()
java.lang.NullPointerException: null
	at cz.ab.ci360.agent_ch_stream.CI360Api.getTaskCustomProperties(CI360Api.java:32)
	at cz.ab.ci360.cache.TaskCache.getCI360ApiTaskCp(TaskCache.java:92)
	at cz.ab.ci360.cache.TaskCache.getTask(TaskCache.java:68)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:99)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 23:05:01,592  2-thread-1  ERROR  c.a.c.c.CreativeCache    Error when loading CP from cie_contact_history_stream_CRT_cp_cache
java.lang.NullPointerException: null
	at cz.ab.ci360.common.SqlConnectionPool.getConnection(SqlConnectionPool.java:63)
	at cz.ab.ci360.cache.CreativeCache.loadDatabaseCreativeCp(CreativeCache.java:112)
	at cz.ab.ci360.cache.CreativeCache.loadCacheCreativeCp(CreativeCache.java:104)
	at cz.ab.ci360.cache.CreativeCache.getCreative(CreativeCache.java:54)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:100)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 23:05:01,593  2-thread-1  ERROR  c.a.c.c.Http    Exception()
java.lang.IllegalArgumentException: Illegal character(s) in message header value: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJjbGllbnRJRCI6ImYwY2IyMjUwNjYwMDAxNmIxODA1ZWU4YiJ9.SS0UpNFT-uQolcHVKHHiyh4AjMLfxbOvph1BMtWlYQA

	at java.base/sun.net.www.protocol.http.HttpURLConnection.checkMessageHeader(HttpURLConnection.java:559)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.isExternalMessageHeaderAllowed(HttpURLConnection.java:494)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.setRequestProperty(HttpURLConnection.java:3193)
	at java.base/sun.net.www.protocol.https.HttpsURLConnectionImpl.setRequestProperty(HttpsURLConnectionImpl.java:312)
	at cz.ab.ci360.common.Http.get(Http.java:119)
	at cz.ab.ci360.agent_ch_stream.CI360Api.getCreativeAttributes(CI360Api.java:71)
	at cz.ab.ci360.cache.CreativeCache.getCI360ApiCreativeCp(CreativeCache.java:91)
	at cz.ab.ci360.cache.CreativeCache.getCreative(CreativeCache.java:67)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:100)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 23:05:01,593  2-thread-1  ERROR  c.a.c.c.CreativeCache    Exception()
java.lang.NullPointerException: null
	at cz.ab.ci360.agent_ch_stream.CI360Api.getCreativeAttributes(CI360Api.java:73)
	at cz.ab.ci360.cache.CreativeCache.getCI360ApiCreativeCp(CreativeCache.java:91)
	at cz.ab.ci360.cache.CreativeCache.getCreative(CreativeCache.java:67)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:100)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 23:05:01,593  2-thread-1  ERROR  c.a.c.a.Event    Error when processing event.
java.lang.NullPointerException: null
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:101)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 23:05:01,593  2-thread-1  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"463860","externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"463860","session":"463860","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"subject_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"448e1dd2-6e51-3023-a56e-ba36e24be8af","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"463860","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","Text":"Test CH","message_id":"0","sessionId":"463860","event_category":"unifiedAndEngage","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"448e1dd2-6e51-3023-a56e-ba36e24be8af","guid":"1b9f0af3-cd2b-42a0-8c87-a03606409319","applicationId":"SAS360Proxy","customer_id":"33a1c5ca4227efd37dfd2b0bf6f645ffd189c07d1912be75ebad71e9988add2f453ae735584c8c9fbb3ad32b0850a22c","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"1b9f0af3-cd2b-42a0-8c87-a03606409319"}
2024-06-20 23:05:16,246  2-thread-2  INFO   c.a.c.a.Event    Event received, event: c_DCH, will be processed...
2024-06-20 23:05:16,246  2-thread-2  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"463860","externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"463860","session":"463860","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"subject_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"448e1dd2-6e51-3023-a56e-ba36e24be8af","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"463860","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","Text":"Test CH","message_id":"0","sessionId":"463860","event_category":"unifiedAndEngage","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"448e1dd2-6e51-3023-a56e-ba36e24be8af","guid":"18eaddb2-bbfa-4388-9a5a-26826bc38d76","applicationId":"SAS360Proxy","customer_id":"33a1c5ca4227efd37dfd2b0bf6f645ffd189c07d1912be75ebad71e9988add2f453ae735584c8c9fbb3ad32b0850a22c","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"18eaddb2-bbfa-4388-9a5a-26826bc38d76"}
2024-06-20 23:05:16,247  2-thread-2  ERROR  c.a.c.c.TaskCache    Error when loading CP from cie_contact_history_stream_task_cp_cache
java.lang.NullPointerException: null
	at cz.ab.ci360.common.SqlConnectionPool.getConnection(SqlConnectionPool.java:63)
	at cz.ab.ci360.cache.TaskCache.loadDatabaseTaskCp(TaskCache.java:121)
	at cz.ab.ci360.cache.TaskCache.loadCacheTaskCp(TaskCache.java:113)
	at cz.ab.ci360.cache.TaskCache.getTask(TaskCache.java:54)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:99)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 23:05:16,247  2-thread-2  ERROR  c.a.c.c.Http    Exception()
java.lang.IllegalArgumentException: Illegal character(s) in message header value: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJjbGllbnRJRCI6ImYwY2IyMjUwNjYwMDAxNmIxODA1ZWU4YiJ9.SS0UpNFT-uQolcHVKHHiyh4AjMLfxbOvph1BMtWlYQA

	at java.base/sun.net.www.protocol.http.HttpURLConnection.checkMessageHeader(HttpURLConnection.java:559)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.isExternalMessageHeaderAllowed(HttpURLConnection.java:494)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.setRequestProperty(HttpURLConnection.java:3193)
	at java.base/sun.net.www.protocol.https.HttpsURLConnectionImpl.setRequestProperty(HttpsURLConnectionImpl.java:312)
	at cz.ab.ci360.common.Http.get(Http.java:119)
	at cz.ab.ci360.agent_ch_stream.CI360Api.getTaskCustomProperties(CI360Api.java:30)
	at cz.ab.ci360.cache.TaskCache.getCI360ApiTaskCp(TaskCache.java:92)
	at cz.ab.ci360.cache.TaskCache.getTask(TaskCache.java:68)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:99)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 23:05:16,248  2-thread-2  ERROR  c.a.c.c.TaskCache    Exception()
java.lang.NullPointerException: null
	at cz.ab.ci360.agent_ch_stream.CI360Api.getTaskCustomProperties(CI360Api.java:32)
	at cz.ab.ci360.cache.TaskCache.getCI360ApiTaskCp(TaskCache.java:92)
	at cz.ab.ci360.cache.TaskCache.getTask(TaskCache.java:68)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:99)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 23:05:16,248  2-thread-2  ERROR  c.a.c.c.CreativeCache    Error when loading CP from cie_contact_history_stream_CRT_cp_cache
java.lang.NullPointerException: null
	at cz.ab.ci360.common.SqlConnectionPool.getConnection(SqlConnectionPool.java:63)
	at cz.ab.ci360.cache.CreativeCache.loadDatabaseCreativeCp(CreativeCache.java:112)
	at cz.ab.ci360.cache.CreativeCache.loadCacheCreativeCp(CreativeCache.java:104)
	at cz.ab.ci360.cache.CreativeCache.getCreative(CreativeCache.java:54)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:100)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 23:05:16,248  2-thread-2  ERROR  c.a.c.c.Http    Exception()
java.lang.IllegalArgumentException: Illegal character(s) in message header value: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJjbGllbnRJRCI6ImYwY2IyMjUwNjYwMDAxNmIxODA1ZWU4YiJ9.SS0UpNFT-uQolcHVKHHiyh4AjMLfxbOvph1BMtWlYQA

	at java.base/sun.net.www.protocol.http.HttpURLConnection.checkMessageHeader(HttpURLConnection.java:559)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.isExternalMessageHeaderAllowed(HttpURLConnection.java:494)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.setRequestProperty(HttpURLConnection.java:3193)
	at java.base/sun.net.www.protocol.https.HttpsURLConnectionImpl.setRequestProperty(HttpsURLConnectionImpl.java:312)
	at cz.ab.ci360.common.Http.get(Http.java:119)
	at cz.ab.ci360.agent_ch_stream.CI360Api.getCreativeAttributes(CI360Api.java:71)
	at cz.ab.ci360.cache.CreativeCache.getCI360ApiCreativeCp(CreativeCache.java:91)
	at cz.ab.ci360.cache.CreativeCache.getCreative(CreativeCache.java:67)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:100)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 23:05:16,248  2-thread-2  ERROR  c.a.c.c.CreativeCache    Exception()
java.lang.NullPointerException: null
	at cz.ab.ci360.agent_ch_stream.CI360Api.getCreativeAttributes(CI360Api.java:73)
	at cz.ab.ci360.cache.CreativeCache.getCI360ApiCreativeCp(CreativeCache.java:91)
	at cz.ab.ci360.cache.CreativeCache.getCreative(CreativeCache.java:67)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:100)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 23:05:16,248  2-thread-2  ERROR  c.a.c.a.Event    Error when processing event.
java.lang.NullPointerException: null
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:101)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 23:05:16,248  2-thread-2  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"463860","externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"463860","session":"463860","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"subject_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"448e1dd2-6e51-3023-a56e-ba36e24be8af","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"463860","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","Text":"Test CH","message_id":"0","sessionId":"463860","event_category":"unifiedAndEngage","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"448e1dd2-6e51-3023-a56e-ba36e24be8af","guid":"18eaddb2-bbfa-4388-9a5a-26826bc38d76","applicationId":"SAS360Proxy","customer_id":"33a1c5ca4227efd37dfd2b0bf6f645ffd189c07d1912be75ebad71e9988add2f453ae735584c8c9fbb3ad32b0850a22c","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"18eaddb2-bbfa-4388-9a5a-26826bc38d76"}
2024-06-20 23:06:01,558  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:07:01,567  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:08:01,576  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:09:01,584  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:10:01,592  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:11:01,600  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:12:01,608  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:12:56,616  main        INFO   c.a.c.a.Agent    Stop reason: 'signal'
2024-06-20 23:12:56,618  main        ERROR  c.a.c.a.Agent    Error when closing stream to cloud, null
java.lang.NullPointerException: null
	at cz.ab.ci360.common.SqlConnectionPool.stop(SqlConnectionPool.java:102)
	at cz.ab.ci360.agent_ch_stream.Agent.stop(Agent.java:232)
	at cz.ab.ci360.agent_ch_stream.Agent.main(Agent.java:212)
2024-06-20 23:12:56,618  main        INFO   c.a.c.a.Agent    Exit code: 1
2024-06-20 23:12:59,390  main        INFO   c.a.c.a.Agent    
2024-06-20 23:12:59,392  main        INFO   c.a.c.a.Agent    ----- starting -------------------------------------------------------
2024-06-20 23:12:59,396  main        INFO   c.a.c.a.Agent    App: /opt/agents/ch-stream/agent-ch-stream.jar
2024-06-20 23:12:59,398  main        INFO   c.a.c.a.Agent    Version: 1.0.0
2024-06-20 23:12:59,398  main        INFO   c.a.c.a.Agent    ----- configuration --------------------------------------------------
2024-06-20 23:12:59,401  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-20 23:12:59,402  main        INFO   c.a.c.c.Config    ci360.tenantID=f0cb22506600016b1805ee8b
2024-06-20 23:12:59,403  main        INFO   c.a.c.c.Config    agent.max_threads=5000
2024-06-20 23:12:59,403  main        INFO   c.a.c.c.Config    kafka.topic=sentCampaingEmail
2024-06-20 23:12:59,403  main        INFO   c.a.c.c.Config    agent.kafka=false
2024-06-20 23:12:59,403  main        INFO   c.a.c.c.Config    proxy.server=proxy.np.ab
2024-06-20 23:12:59,404  main        INFO   c.a.c.c.Config    proxy.port=3128
2024-06-20 23:12:59,404  main        INFO   c.a.c.c.Config    proxy.realm=proxy
2024-06-20 23:12:59,404  main        INFO   c.a.c.c.Config    proxy.user=sascloud_user
2024-06-20 23:12:59,404  main        INFO   c.a.c.c.Config    proxy.non_proxy_hosts=app-sas-test.np.ab
2024-06-20 23:12:59,404  main        INFO   c.a.c.a.Agent    test mode = false
2024-06-20 23:12:59,404  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-20 23:12:59,407  main        INFO   c.a.c.c.Config    event.process_event_with_prefix=c_
2024-06-20 23:12:59,407  main        INFO   c.a.c.c.Config    event.direct_event=c_direct
2024-06-20 23:12:59,407  main        INFO   c.a.c.c.Config    event.ignore_events=c_activitystart,c_abtestpathassignment
2024-06-20 23:12:59,407  main        INFO   c.a.c.a.Agent    ----- eof configuration --------------------------------------------------
2024-06-20 23:12:59,413  main        INFO   c.s.m.a.s.CI360Agent    Maximum upstream queue size: 1000
2024-06-20 23:12:59,413  main        INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-20 23:12:59,482  main        INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-20 23:13:00,198  8912029-17  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-20 23:13:00,259  8912029-19  INFO   c.s.m.a.s.StreamWebSocket    Agent version: v2402
2024-06-20 23:13:00,275  8912029-19  INFO   c.s.m.a.s.StreamWebSocket    Gateway version: 1.0.125
2024-06-20 23:13:00,275  8912029-19  INFO   c.s.m.a.s.StreamWebSocket    Gateway supported versions: v2406,v2405,v2404,v2403,v2402,v2401,v2312
2024-06-20 23:13:00,275  8912029-19  INFO   c.s.m.a.s.StreamWebSocket    Gateway warning versions: v2311,v2310,v2309
2024-06-20 23:13:20,997  2-thread-1  INFO   c.a.c.a.Event    Event received, event: c_DCH, will be processed...
2024-06-20 23:13:20,997  2-thread-1  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"463860","externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"463860","session":"463860","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"subject_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"448e1dd2-6e51-3023-a56e-ba36e24be8af","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"463860","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","Text":"Test CH","message_id":"0","sessionId":"463860","event_category":"unifiedAndEngage","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"448e1dd2-6e51-3023-a56e-ba36e24be8af","guid":"7bfbb6d9-3645-4dda-8336-5f9e4a0d28ed","applicationId":"SAS360Proxy","customer_id":"33a1c5ca4227efd37dfd2b0bf6f645ffd189c07d1912be75ebad71e9988add2f453ae735584c8c9fbb3ad32b0850a22c","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"7bfbb6d9-3645-4dda-8336-5f9e4a0d28ed"}
2024-06-20 23:13:21,000  2-thread-1  ERROR  c.a.c.c.TaskCache    Error when loading CP from cie_contact_history_stream_task_cp_cache
java.lang.NullPointerException: null
	at cz.ab.ci360.common.SqlConnectionPool.getConnection(SqlConnectionPool.java:63)
	at cz.ab.ci360.cache.TaskCache.loadDatabaseTaskCp(TaskCache.java:121)
	at cz.ab.ci360.cache.TaskCache.loadCacheTaskCp(TaskCache.java:113)
	at cz.ab.ci360.cache.TaskCache.getTask(TaskCache.java:54)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:99)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 23:13:21,016  2-thread-1  ERROR  c.a.c.c.Http    Response, Code: 407 , Message: 
2024-06-20 23:13:21,017  2-thread-1  DEBUG  c.a.c.a.CI360Api    Task API json: 
2024-06-20 23:13:21,017  2-thread-1  ERROR  c.a.c.a.CI360Api    Exception()
org.json.JSONException: A JSONObject text must begin with '{' at 0 [character 1 line 1]
	at org.json.JSONTokener.syntaxError(JSONTokener.java:503)
	at org.json.JSONObject.<init>(JSONObject.java:213)
	at org.json.JSONObject.<init>(JSONObject.java:430)
	at cz.ab.ci360.agent_ch_stream.CI360Api.getTaskCustomProperties(CI360Api.java:43)
	at cz.ab.ci360.cache.TaskCache.getCI360ApiTaskCp(TaskCache.java:92)
	at cz.ab.ci360.cache.TaskCache.getTask(TaskCache.java:68)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:99)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 23:13:21,020  2-thread-1  ERROR  c.a.c.c.TaskCache    Exception()
java.lang.NullPointerException: null
	at cz.ab.ci360.common.SqlConnectionPool.getConnection(SqlConnectionPool.java:63)
	at cz.ab.ci360.cache.TaskCache.saveTaskCpToCache(TaskCache.java:195)
	at cz.ab.ci360.cache.TaskCache.getTask(TaskCache.java:72)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:99)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 23:13:21,020  2-thread-1  ERROR  c.a.c.c.CreativeCache    Error when loading CP from cie_contact_history_stream_CRT_cp_cache
java.lang.NullPointerException: null
	at cz.ab.ci360.common.SqlConnectionPool.getConnection(SqlConnectionPool.java:63)
	at cz.ab.ci360.cache.CreativeCache.loadDatabaseCreativeCp(CreativeCache.java:112)
	at cz.ab.ci360.cache.CreativeCache.loadCacheCreativeCp(CreativeCache.java:104)
	at cz.ab.ci360.cache.CreativeCache.getCreative(CreativeCache.java:54)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:100)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 23:13:21,023  2-thread-1  ERROR  c.a.c.c.Http    Response, Code: 407 , Message: 
2024-06-20 23:13:21,023  2-thread-1  DEBUG  c.a.c.a.CI360Api    Creative API json: 
2024-06-20 23:13:21,023  2-thread-1  ERROR  c.a.c.a.CI360Api    Exception()
org.json.JSONException: A JSONObject text must begin with '{' at 0 [character 1 line 1]
	at org.json.JSONTokener.syntaxError(JSONTokener.java:503)
	at org.json.JSONObject.<init>(JSONObject.java:213)
	at org.json.JSONObject.<init>(JSONObject.java:430)
	at cz.ab.ci360.agent_ch_stream.CI360Api.getCreativeAttributes(CI360Api.java:84)
	at cz.ab.ci360.cache.CreativeCache.getCI360ApiCreativeCp(CreativeCache.java:91)
	at cz.ab.ci360.cache.CreativeCache.getCreative(CreativeCache.java:67)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:100)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 23:13:21,023  2-thread-1  ERROR  c.a.c.c.CreativeCache    Exception()
java.lang.NullPointerException: null
	at cz.ab.ci360.common.SqlConnectionPool.getConnection(SqlConnectionPool.java:63)
	at cz.ab.ci360.cache.CreativeCache.saveCreativeCpToCache(CreativeCache.java:160)
	at cz.ab.ci360.cache.CreativeCache.getCreative(CreativeCache.java:71)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:100)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 23:13:21,024  2-thread-1  ERROR  c.a.c.a.Event    Error when processing event.
java.lang.NullPointerException: null
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:101)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-20 23:13:21,024  2-thread-1  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"463860","externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"463860","session":"463860","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"subject_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"448e1dd2-6e51-3023-a56e-ba36e24be8af","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"463860","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","Text":"Test CH","message_id":"0","sessionId":"463860","event_category":"unifiedAndEngage","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"448e1dd2-6e51-3023-a56e-ba36e24be8af","guid":"7bfbb6d9-3645-4dda-8336-5f9e4a0d28ed","applicationId":"SAS360Proxy","customer_id":"33a1c5ca4227efd37dfd2b0bf6f645ffd189c07d1912be75ebad71e9988add2f453ae735584c8c9fbb3ad32b0850a22c","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"7bfbb6d9-3645-4dda-8336-5f9e4a0d28ed"}
2024-06-20 23:13:59,691  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:14:59,703  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:15:59,713  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:16:59,724  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:17:59,733  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:18:59,742  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:19:59,751  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:20:59,760  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:21:59,769  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:22:59,778  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:23:59,786  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:24:59,795  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:25:59,803  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:26:59,812  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:27:59,820  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:28:59,828  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:29:59,836  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:30:59,844  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:31:59,852  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:32:59,859  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:33:59,867  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:34:59,875  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:35:59,884  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:36:59,893  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:37:59,901  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:38:59,909  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:39:59,917  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:40:59,925  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:41:59,933  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:42:59,940  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:43:59,949  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:44:59,957  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:45:59,964  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:46:59,972  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:47:59,981  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:48:59,989  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:49:59,997  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:51:00,005  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:52:00,013  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:53:00,021  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:54:00,029  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:55:00,037  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:56:00,045  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:57:00,053  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:58:00,061  main        INFO   c.a.c.a.Agent    running
2024-06-20 23:59:00,070  main        INFO   c.a.c.a.Agent    running
