2024-06-19 16:20:04,200  main        INFO   c.a.c.a.Agent    
2024-06-19 16:20:04,204  main        INFO   c.a.c.a.Agent    ----- starting -------------------------------------------------------
2024-06-19 16:20:04,213  main        INFO   c.a.c.a.Agent    App: /opt/agents/ch-stream/agent-ch-stream.jar
2024-06-19 16:20:04,218  main        INFO   c.a.c.a.Agent    Version: 1.0.0
2024-06-19 16:20:04,218  main        INFO   c.a.c.a.Agent    ----- configuration --------------------------------------------------
2024-06-19 16:20:04,223  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-19 16:20:04,229  main        INFO   c.a.c.c.Config    ci360.jwt=f0cb22506600016b1805ee8b
2024-06-19 16:20:04,229  main        INFO   c.a.c.c.Config    agent.max_threads=5000
2024-06-19 16:20:04,230  main        INFO   c.a.c.c.Config    kafka.topic=sentCampaingEmail
2024-06-19 16:20:04,230  main        INFO   c.a.c.c.Config    agent.kafka=false
2024-06-19 16:20:04,230  main        INFO   c.a.c.c.Config    proxy.server=proxy.np.ab
2024-06-19 16:20:04,230  main        INFO   c.a.c.c.Config    proxy.port=3128
2024-06-19 16:20:04,230  main        INFO   c.a.c.c.Config    proxy.realm=proxy
2024-06-19 16:20:04,230  main        INFO   c.a.c.c.Config    proxy.user=sascloud_user
2024-06-19 16:20:04,231  main        INFO   c.a.c.c.Config    proxy.non_proxy_hosts=app-sas-test.np.ab
2024-06-19 16:20:04,233  main        INFO   c.a.c.a.Agent    test mode = false
2024-06-19 16:20:04,233  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-19 16:20:04,234  main        INFO   c.a.c.c.Config    db.user=APP_CAMPAIGN_CDM2
2024-06-19 16:20:04,234  main        INFO   c.a.c.c.Config    db.database=APP_CAMPAIGN_CDM2
2024-06-19 16:20:04,234  main        INFO   c.a.c.c.Config    db.server=DBS01.DE11.NP.AB:1621
2024-06-19 16:20:04,234  main        INFO   c.a.c.c.Config    db.service_name=MNDE11DW.NP.AB
2024-06-19 16:20:04,234  main        INFO   c.a.c.c.Config    db.init_connection_pool_size=10
2024-06-19 16:20:04,234  main        INFO   c.a.c.c.Config    db.max_connection_pool_size=200
2024-06-19 16:20:06,598  main        INFO   c.a.c.c.Config    event.process_event_with_prefix=c_
2024-06-19 16:20:06,599  main        INFO   c.a.c.c.Config    event.direct_event=c_direct
2024-06-19 16:20:06,599  main        INFO   c.a.c.c.Config    event.ignore_events=c_activitystart,c_abtestpathassignment
2024-06-19 16:20:06,599  main        INFO   c.a.c.a.Agent    ----- eof configuration --------------------------------------------------
2024-06-19 16:20:06,603  main        INFO   c.s.m.a.s.CI360Agent    Maximum upstream queue size: 1000
2024-06-19 16:20:06,604  main        INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-19 16:20:06,672  main        INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-19 16:20:07,297  8924571-22  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-19 16:21:06,861  main        INFO   c.a.c.a.Agent    running
2024-06-19 16:21:17,320  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-19 16:21:17,321  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-19 16:21:17,322  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-19 16:21:32,322  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-19 16:21:32,323  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-19 16:21:32,330  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-19 16:21:32,333  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-19 16:21:32,333  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-19 16:21:32,458  8924571-19  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-19 16:21:47,333  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-19 16:21:47,337  cheduler-1  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$2.streamClosed(Agent.java:191)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$sendFrame$7(WebSocketCoreSession.java:519)
	at org.eclipse.jetty.util.Callback$3.succeeded(Callback.java:155)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.notifyCallbackSuccess(TransformingFlusher.java:197)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher$Flusher.process(TransformingFlusher.java:154)
	at org.eclipse.jetty.util.IteratingCallback.processing(IteratingCallback.java:243)
	at org.eclipse.jetty.util.IteratingCallback.iterate(IteratingCallback.java:224)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.sendFrame(TransformingFlusher.java:77)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.sendFrame(WebSocketCoreSession.java:522)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.close(WebSocketCoreSession.java:239)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.processHandlerError(WebSocketCoreSession.java:371)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onIdleExpired(WebSocketConnection.java:242)
	at org.eclipse.jetty.io.ssl.SslConnection.onIdleExpired(SslConnection.java:360)
	at org.eclipse.jetty.io.AbstractEndPoint.onIdleExpired(AbstractEndPoint.java:407)
	at org.eclipse.jetty.io.IdleTimeout.checkIdleTimeout(IdleTimeout.java:170)
	at org.eclipse.jetty.io.IdleTimeout.idleCheck(IdleTimeout.java:112)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-19 16:22:06,876  main        INFO   c.a.c.a.Agent    running
2024-06-19 16:22:42,462  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-19 16:22:42,462  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-19 16:22:42,463  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-19 16:22:57,463  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-19 16:22:57,463  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-19 16:22:57,464  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-19 16:22:57,464  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-19 16:22:57,464  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-19 16:22:57,595  8924571-25  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-19 16:23:06,892  main        INFO   c.a.c.a.Agent    running
2024-06-19 16:23:12,465  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-19 16:23:12,465  cheduler-1  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$2.streamClosed(Agent.java:191)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$sendFrame$7(WebSocketCoreSession.java:519)
	at org.eclipse.jetty.util.Callback$3.succeeded(Callback.java:155)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.notifyCallbackSuccess(TransformingFlusher.java:197)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher$Flusher.process(TransformingFlusher.java:154)
	at org.eclipse.jetty.util.IteratingCallback.processing(IteratingCallback.java:243)
	at org.eclipse.jetty.util.IteratingCallback.iterate(IteratingCallback.java:224)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.sendFrame(TransformingFlusher.java:77)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.sendFrame(WebSocketCoreSession.java:522)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.close(WebSocketCoreSession.java:239)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.processHandlerError(WebSocketCoreSession.java:371)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onIdleExpired(WebSocketConnection.java:242)
	at org.eclipse.jetty.io.ssl.SslConnection.onIdleExpired(SslConnection.java:360)
	at org.eclipse.jetty.io.AbstractEndPoint.onIdleExpired(AbstractEndPoint.java:407)
	at org.eclipse.jetty.io.IdleTimeout.checkIdleTimeout(IdleTimeout.java:170)
	at org.eclipse.jetty.io.IdleTimeout.idleCheck(IdleTimeout.java:112)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-19 16:24:06,906  main        INFO   c.a.c.a.Agent    running
2024-06-19 16:24:07,599  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-19 16:24:07,599  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-19 16:24:07,599  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-19 16:24:22,599  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-19 16:24:22,599  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-19 16:24:22,600  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-19 16:24:22,600  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-19 16:24:22,601  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-19 16:24:22,739  8924571-22  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-19 16:24:37,601  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-19 16:24:37,601  cheduler-1  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$2.streamClosed(Agent.java:191)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$sendFrame$7(WebSocketCoreSession.java:519)
	at org.eclipse.jetty.util.Callback$3.succeeded(Callback.java:155)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.notifyCallbackSuccess(TransformingFlusher.java:197)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher$Flusher.process(TransformingFlusher.java:154)
	at org.eclipse.jetty.util.IteratingCallback.processing(IteratingCallback.java:243)
	at org.eclipse.jetty.util.IteratingCallback.iterate(IteratingCallback.java:224)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.sendFrame(TransformingFlusher.java:77)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.sendFrame(WebSocketCoreSession.java:522)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.close(WebSocketCoreSession.java:239)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.processHandlerError(WebSocketCoreSession.java:371)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onIdleExpired(WebSocketConnection.java:242)
	at org.eclipse.jetty.io.ssl.SslConnection.onIdleExpired(SslConnection.java:360)
	at org.eclipse.jetty.io.AbstractEndPoint.onIdleExpired(AbstractEndPoint.java:407)
	at org.eclipse.jetty.io.IdleTimeout.checkIdleTimeout(IdleTimeout.java:170)
	at org.eclipse.jetty.io.IdleTimeout.idleCheck(IdleTimeout.java:112)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-19 16:24:45,914  main        INFO   c.a.c.a.Agent    Stop reason: 'signal'
2024-06-19 16:24:45,928  main        INFO   c.s.m.a.s.CI360Agent    Stopping event stream connection now
2024-06-19 16:24:45,929  main        INFO   o.e.j.w.c.WebSocketClient    Shutdown WebSocketClient@b7430b45[coreClient=WebSocketCoreClient@2cc3ad05{STARTED},openSessions.size=1]
2024-06-19 16:24:45,931  8924571-23  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: null
2024-06-19 16:24:45,931  8924571-23  DEBUG  c.a.c.a.Agent    Stream closed
2024-06-19 16:24:45,931  8924571-23  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1006] Session Closed
2024-06-19 16:24:45,931  8924571-23  DEBUG  c.a.c.a.Agent    Stream closed
2024-06-19 16:24:46,938  main        INFO   c.a.c.a.Agent    Exit code: 0
2024-06-19 16:25:16,238  main        INFO   c.a.c.a.Agent    
2024-06-19 16:25:16,240  main        INFO   c.a.c.a.Agent    ----- starting -------------------------------------------------------
2024-06-19 16:25:16,245  main        INFO   c.a.c.a.Agent    App: /opt/agents/ch-stream/agent-ch-stream.jar
2024-06-19 16:25:16,249  main        INFO   c.a.c.a.Agent    Version: 1.0.0
2024-06-19 16:25:16,249  main        INFO   c.a.c.a.Agent    ----- configuration --------------------------------------------------
2024-06-19 16:25:16,252  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-19 16:25:16,254  main        INFO   c.a.c.c.Config    ci360.jwt=f0cb22506600016b1805ee8b
2024-06-19 16:25:16,254  main        INFO   c.a.c.c.Config    agent.max_threads=5000
2024-06-19 16:25:16,254  main        INFO   c.a.c.c.Config    kafka.topic=sentCampaingEmail
2024-06-19 16:25:16,254  main        INFO   c.a.c.c.Config    agent.kafka=false
2024-06-19 16:25:16,255  main        INFO   c.a.c.c.Config    proxy.server=proxy.np.ab
2024-06-19 16:25:16,255  main        INFO   c.a.c.c.Config    proxy.port=3128
2024-06-19 16:25:16,255  main        INFO   c.a.c.c.Config    proxy.realm=proxy
2024-06-19 16:25:16,255  main        INFO   c.a.c.c.Config    proxy.user=sascloud_user
2024-06-19 16:25:16,256  main        INFO   c.a.c.c.Config    proxy.non_proxy_hosts=app-sas-test.np.ab
2024-06-19 16:25:16,257  main        INFO   c.a.c.a.Agent    test mode = false
2024-06-19 16:25:16,257  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-19 16:25:16,258  main        INFO   c.a.c.c.Config    db.user=APP_CAMPAIGN_CDM2
2024-06-19 16:25:16,258  main        INFO   c.a.c.c.Config    db.database=APP_CAMPAIGN_CDM2
2024-06-19 16:25:16,258  main        INFO   c.a.c.c.Config    db.server=DBS01.DE11.NP.AB:1621
2024-06-19 16:25:16,258  main        INFO   c.a.c.c.Config    db.service_name=MNDE11DW.NP.AB
2024-06-19 16:25:16,258  main        INFO   c.a.c.c.Config    db.init_connection_pool_size=10
2024-06-19 16:25:16,258  main        INFO   c.a.c.c.Config    db.max_connection_pool_size=200
2024-06-19 16:25:17,095  main        INFO   c.a.c.c.Config    event.process_event_with_prefix=c_
2024-06-19 16:25:17,095  main        INFO   c.a.c.c.Config    event.direct_event=c_direct
2024-06-19 16:25:17,095  main        INFO   c.a.c.c.Config    event.ignore_events=c_activitystart,c_abtestpathassignment
2024-06-19 16:25:17,095  main        INFO   c.a.c.a.Agent    ----- eof configuration --------------------------------------------------
2024-06-19 16:25:17,099  main        INFO   c.s.m.a.s.CI360Agent    Maximum upstream queue size: 1000
2024-06-19 16:25:17,099  main        INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-19 16:25:17,157  main        INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-19 16:25:17,710  4818087-21  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-19 16:25:54,330  main        INFO   c.a.c.a.Agent    Stop reason: 'signal'
2024-06-19 16:25:54,342  main        INFO   c.s.m.a.s.CI360Agent    Stopping event stream connection now
2024-06-19 16:25:54,344  main        INFO   o.e.j.w.c.WebSocketClient    Shutdown WebSocketClient@aa85d01c[coreClient=WebSocketCoreClient@c055c54{STARTED},openSessions.size=1]
2024-06-19 16:25:54,349  4818087-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: null
2024-06-19 16:25:54,350  4818087-22  DEBUG  c.a.c.a.Agent    Stream closed
2024-06-19 16:25:54,356  4818087-22  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1006] Session Closed
2024-06-19 16:25:54,359  4818087-22  DEBUG  c.a.c.a.Agent    Stream closed
2024-06-19 16:25:55,361  main        INFO   c.a.c.a.Agent    Exit code: 0
