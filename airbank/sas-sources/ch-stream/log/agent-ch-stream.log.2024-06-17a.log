2024-06-17 08:41:05,452  main        INFO   c.a.c.a.Agent    
2024-06-17 08:41:05,454  main        INFO   c.a.c.a.Agent    ----- starting -------------------------------------------------------
2024-06-17 08:41:05,460  main        INFO   c.a.c.a.Agent    App: /opt/agents/ch-stream/agent-ch-stream.jar
2024-06-17 08:41:05,462  main        INFO   c.a.c.a.Agent    Version: 1.0.0
2024-06-17 08:41:05,462  main        INFO   c.a.c.a.Agent    ----- configuration --------------------------------------------------
2024-06-17 08:41:05,466  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-17 08:41:05,467  main        INFO   c.a.c.c.Config    ci360.jwt=f0cb22506600016b1805ee8b
2024-06-17 08:41:05,467  main        INFO   c.a.c.c.Config    proxy.server=proxy.np.ab
2024-06-17 08:41:05,467  main        INFO   c.a.c.c.Config    proxy.port=3128
2024-06-17 08:41:05,467  main        INFO   c.a.c.c.Config    agent.max_threads=5000
2024-06-17 08:41:05,467  main        INFO   c.a.c.c.Config    kafka.topic=sentCampaingEmail topic
2024-06-17 08:41:05,467  main        INFO   c.a.c.a.Agent    test mode = false
2024-06-17 08:41:05,468  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-17 08:41:05,468  main        INFO   c.a.c.c.Config    proxy.server=proxy.np.ab
2024-06-17 08:41:05,468  main        INFO   c.a.c.c.Config    proxy.port=3128
2024-06-17 08:41:05,469  main        INFO   c.a.c.c.Config    db.user=APP_CAMPAIGN_CDM2
2024-06-17 08:41:05,469  main        INFO   c.a.c.c.Config    db.database=APP_CAMPAIGN_CDM2
2024-06-17 08:41:05,469  main        INFO   c.a.c.c.Config    db.server=dbs02.te00.np.ab:1621
2024-06-17 08:41:05,469  main        INFO   c.a.c.c.Config    db.service_name=MNTE00DW.np.ab
2024-06-17 08:41:05,469  main        INFO   c.a.c.c.Config    db.init_connection_pool_size=10
2024-06-17 08:41:05,469  main        INFO   c.a.c.c.Config    db.max_connection_pool_size=200
2024-06-17 08:41:06,562  main        INFO   c.a.c.c.Config    event.process_event_with_prefix=c_
2024-06-17 08:41:06,562  main        INFO   c.a.c.c.Config    event.direct_event=c_direct
2024-06-17 08:41:06,562  main        INFO   c.a.c.c.Config    event.ignore_events=c_activitystart,c_abtestpathassignment
2024-06-17 08:41:06,562  main        INFO   c.a.c.a.Agent    ----- eof configuration --------------------------------------------------
2024-06-17 08:41:06,566  main        INFO   c.s.m.a.s.CI360Agent    Maximum upstream queue size: 1000
2024-06-17 08:41:06,567  main        INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-17 08:41:06,620  main        INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-17 08:41:06,813  1764601-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-17 08:41:06,814  1764601-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-17 08:41:06,815  1764601-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-17 08:41:21,815  1764601-22  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-17 08:41:21,815  1764601-22  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-17 08:41:21,820  1764601-17  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-17 08:41:21,820  1764601-17  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-17 08:41:21,821  1764601-17  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-17 08:41:36,821  1764601-17  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-17 08:41:36,821  1764601-17  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-17 08:41:36,877  1764601-18  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-17 08:41:36,877  1764601-18  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-17 08:41:36,877  1764601-18  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-17 08:41:51,759  main        INFO   c.a.c.a.Agent    Stop reason: 'signal'
2024-06-17 08:41:51,778  main        ERROR  c.a.c.a.Agent    Error when closing stream to cloud, null
java.lang.NullPointerException: null
	at cz.ab.ci360.common.Kafka.stop(Kafka.java:61)
	at cz.ab.ci360.agent_ch_stream.Agent.stop(Agent.java:212)
	at cz.ab.ci360.agent_ch_stream.Agent.main(Agent.java:190)
2024-06-17 08:41:51,778  main        INFO   c.a.c.a.Agent    Exit code: 1
2024-06-17 08:41:51,877  1764601-18  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-17 08:41:51,878  1764601-18  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-17 08:41:51,883  1764601-21  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-17 08:41:51,883  1764601-21  DEBUG  c.a.c.a.Agent    Stream closed
2024-06-17 08:42:08,311  main        INFO   c.a.c.a.Agent    
2024-06-17 08:42:08,313  main        INFO   c.a.c.a.Agent    ----- starting -------------------------------------------------------
2024-06-17 08:42:08,318  main        INFO   c.a.c.a.Agent    App: /opt/agents/ch-stream/agent-ch-stream.jar
2024-06-17 08:42:08,321  main        INFO   c.a.c.a.Agent    Version: 1.0.0
2024-06-17 08:42:08,321  main        INFO   c.a.c.a.Agent    ----- configuration --------------------------------------------------
2024-06-17 08:42:08,324  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-17 08:42:08,325  main        INFO   c.a.c.c.Config    ci360.jwt=f0cb22506600016b1805ee8b
2024-06-17 08:42:08,326  main        INFO   c.a.c.c.Config    agent.max_threads=5000
2024-06-17 08:42:08,326  main        INFO   c.a.c.c.Config    kafka.topic=sentCampaingEmail topic
2024-06-17 08:42:08,326  main        INFO   c.a.c.c.Config    proxy.server=proxy.np.ab
2024-06-17 08:42:08,326  main        INFO   c.a.c.c.Config    proxy.port=3128
2024-06-17 08:42:08,326  main        INFO   c.a.c.c.Config    proxy.realm=proxy
2024-06-17 08:42:08,326  main        INFO   c.a.c.c.Config    proxy.user=sascloud_user
2024-06-17 08:42:08,327  main        INFO   c.a.c.c.Config    proxy.non_proxy_hosts=app-sas-test.np.ab
2024-06-17 08:42:08,327  main        INFO   c.a.c.a.Agent    test mode = false
2024-06-17 08:42:08,328  main        INFO   c.a.c.c.Config    kafka.server=serverXXXXX:9093
2024-06-17 08:42:08,328  main        INFO   c.a.c.c.Config    kafka.SSL_TRUSTSTORE_LOCATION_CONFIG=./cert/kafka-test.truststore.jks
2024-06-17 08:42:08,328  main        INFO   c.a.c.c.Config    kafka.SSL_KEYSTORE_LOCATION_CONFIG=./cert/kafka-test.keystore.jks
2024-06-17 08:42:08,328  main        INFO   c.a.c.c.Config    kafka.SSL_PROTOCOL_CONFIG=SSL
2024-06-17 08:42:08,328  main        INFO   c.a.c.c.Config    kafka.SSL_ENABLED_PROTOCOLS_CONFIG=TLSv1.2
2024-06-17 08:42:08,328  main        INFO   c.a.c.c.Config    kafka.SSL_ENDPOINT_IDENTIFICATION_ALGORITHM_CONFIG=
2024-06-17 08:42:08,349  main        INFO   o.a.k.c.p.ProducerConfig    ProducerConfig values: 
	acks = -1
	batch.size = 16384
	bootstrap.servers = [serverXXXXX:9093]
	buffer.memory = 33554432
	client.dns.lookup = use_all_dns_ips
	client.id = producer-1
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = true
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 0
	max.block.ms = 60000
	max.in.flight.requests.per.connection = 5
	max.request.size = 1048576
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.class = class org.apache.kafka.clients.producer.internals.DefaultPartitioner
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retries = 2147483647
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = SSL
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2]
	ssl.endpoint.identification.algorithm = 
	ssl.engine.factory.class = null
	ssl.key.password = [hidden]
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = ./cert/kafka-test.keystore.jks
	ssl.keystore.password = [hidden]
	ssl.keystore.type = JKS
	ssl.protocol = SSL
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = ./cert/kafka-test.truststore.jks
	ssl.truststore.password = [hidden]
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.StringSerializer

2024-06-17 08:42:08,459  main        INFO   o.a.k.c.p.KafkaProducer    [Producer clientId=producer-1] Instantiated an idempotent producer.
2024-06-17 08:42:08,504  main        WARN   o.a.k.c.ClientUtils    Couldn't resolve server serverXXXXX:9093 from bootstrap.servers as DNS resolution failed for serverXXXXX
2024-06-17 08:42:08,504  main        INFO   o.a.k.c.p.KafkaProducer    [Producer clientId=producer-1] Closing the Kafka producer with timeoutMillis = 0 ms.
2024-06-17 08:42:08,505  main        INFO   o.a.k.c.m.Metrics    Metrics scheduler closed
2024-06-17 08:42:08,505  main        INFO   o.a.k.c.m.Metrics    Closing reporter org.apache.kafka.common.metrics.JmxReporter
2024-06-17 08:42:08,505  main        INFO   o.a.k.c.m.Metrics    Metrics reporters closed
2024-06-17 08:42:08,506  main        INFO   o.a.k.c.u.AppInfoParser    App info kafka.producer for producer-1 unregistered
2024-06-17 08:42:08,509  main        ERROR  c.a.c.a.Agent    Inicialization error. org.apache.kafka.common.KafkaException: Failed to construct kafka producer
org.apache.kafka.common.KafkaException: Failed to construct kafka producer
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:439)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:290)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:317)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:302)
	at cz.ab.ci360.common.Kafka.init(Kafka.java:56)
	at cz.ab.ci360.agent_ch_stream.Agent.main(Agent.java:107)
Caused by: org.apache.kafka.common.config.ConfigException: No resolvable bootstrap urls given in bootstrap.servers
	at org.apache.kafka.clients.ClientUtils.parseAndValidateAddresses(ClientUtils.java:89)
	at org.apache.kafka.clients.ClientUtils.parseAndValidateAddresses(ClientUtils.java:48)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:413)
	... 5 common frames omitted
2024-06-17 08:42:08,509  main        INFO   c.a.c.a.Agent    Exit code: 1
2024-06-17 09:00:51,720  main        INFO   c.a.c.a.Agent    
2024-06-17 09:00:51,722  main        INFO   c.a.c.a.Agent    ----- starting -------------------------------------------------------
2024-06-17 09:00:51,727  main        INFO   c.a.c.a.Agent    App: /opt/agents/ch-stream/agent-ch-stream.jar
2024-06-17 09:00:51,730  main        INFO   c.a.c.a.Agent    Version: 1.0.0
2024-06-17 09:00:51,730  main        INFO   c.a.c.a.Agent    ----- configuration --------------------------------------------------
2024-06-17 09:00:51,734  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-17 09:00:51,735  main        INFO   c.a.c.c.Config    ci360.jwt=f0cb22506600016b1805ee8b
2024-06-17 09:00:51,735  main        INFO   c.a.c.c.Config    agent.max_threads=5000
2024-06-17 09:00:51,735  main        INFO   c.a.c.c.Config    kafka.topic=sentCampaingEmail topic
2024-06-17 09:00:51,735  main        INFO   c.a.c.c.Config    proxy.server=proxy.np.ab
2024-06-17 09:00:51,736  main        INFO   c.a.c.c.Config    proxy.port=3128
2024-06-17 09:00:51,736  main        INFO   c.a.c.c.Config    proxy.realm=proxy
2024-06-17 09:00:51,736  main        INFO   c.a.c.c.Config    proxy.user=sascloud_user
2024-06-17 09:00:51,736  main        INFO   c.a.c.c.Config    proxy.non_proxy_hosts=app-sas-test.np.ab
2024-06-17 09:00:51,736  main        INFO   c.a.c.a.Agent    test mode = false
2024-06-17 09:00:51,737  main        INFO   c.a.c.c.Config    kafka.server=null
2024-06-17 09:00:51,738  main        INFO   c.a.c.c.Config    kafka.SSL_TRUSTSTORE_LOCATION_CONFIG=./cert/kafka-test.truststore.jks
2024-06-17 09:00:51,738  main        INFO   c.a.c.c.Config    kafka.SSL_KEYSTORE_LOCATION_CONFIG=./cert/kafka-test.keystore.jks
2024-06-17 09:00:51,738  main        INFO   c.a.c.c.Config    kafka.SSL_PROTOCOL_CONFIG=SSL
2024-06-17 09:00:51,738  main        INFO   c.a.c.c.Config    kafka.SSL_ENABLED_PROTOCOLS_CONFIG=TLSv1.2
2024-06-17 09:00:51,738  main        INFO   c.a.c.c.Config    kafka.SSL_ENDPOINT_IDENTIFICATION_ALGORITHM_CONFIG=
2024-06-17 09:00:51,740  main        ERROR  c.a.c.a.Agent    Inicialization error. java.lang.NullPointerException
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.putVal(ConcurrentHashMap.java:1011)
	at java.base/java.util.concurrent.ConcurrentHashMap.put(ConcurrentHashMap.java:1006)
	at java.base/java.util.Properties.put(Properties.java:1340)
	at cz.ab.ci360.common.Kafka.init(Kafka.java:38)
	at cz.ab.ci360.agent_ch_stream.Agent.main(Agent.java:107)
2024-06-17 09:00:51,741  main        INFO   c.a.c.a.Agent    Exit code: 1
2024-06-17 15:54:02,505  main        INFO   c.a.c.a.Agent    
2024-06-17 15:54:02,506  main        INFO   c.a.c.a.Agent    ----- starting -------------------------------------------------------
2024-06-17 15:54:02,511  main        INFO   c.a.c.a.Agent    App: /opt/agents/ch-stream/agent-ch-stream.jar
2024-06-17 15:54:02,513  main        INFO   c.a.c.a.Agent    Version: 1.0.0
2024-06-17 15:54:02,514  main        INFO   c.a.c.a.Agent    ----- configuration --------------------------------------------------
2024-06-17 15:54:02,517  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-17 15:54:02,518  main        INFO   c.a.c.c.Config    ci360.jwt=f0cb22506600016b1805ee8b
2024-06-17 15:54:02,518  main        INFO   c.a.c.c.Config    agent.max_threads=5000
2024-06-17 15:54:02,518  main        INFO   c.a.c.c.Config    kafka.topic=sentCampaingEmail
2024-06-17 15:54:02,518  main        INFO   c.a.c.c.Config    proxy.server=proxy.np.ab
2024-06-17 15:54:02,519  main        INFO   c.a.c.c.Config    proxy.port=3128
2024-06-17 15:54:02,519  main        INFO   c.a.c.c.Config    proxy.realm=proxy
2024-06-17 15:54:02,519  main        INFO   c.a.c.c.Config    proxy.user=sascloud_user
2024-06-17 15:54:02,519  main        INFO   c.a.c.c.Config    proxy.non_proxy_hosts=app-sas-test.np.ab
2024-06-17 15:54:02,519  main        INFO   c.a.c.a.Agent    test mode = false
2024-06-17 15:54:02,520  main        INFO   c.a.c.c.Config    kafka.server=null
2024-06-17 15:54:02,520  main        INFO   c.a.c.c.Config    kafka.SSL_TRUSTSTORE_LOCATION_CONFIG=./cert/kafka-test.truststore.jks
2024-06-17 15:54:02,520  main        INFO   c.a.c.c.Config    kafka.SSL_KEYSTORE_LOCATION_CONFIG=./cert/kafka-test.keystore.jks
2024-06-17 15:54:02,521  main        INFO   c.a.c.c.Config    kafka.SSL_PROTOCOL_CONFIG=SSL
2024-06-17 15:54:02,521  main        INFO   c.a.c.c.Config    kafka.SSL_ENABLED_PROTOCOLS_CONFIG=TLSv1.2
2024-06-17 15:54:02,521  main        INFO   c.a.c.c.Config    kafka.SSL_ENDPOINT_IDENTIFICATION_ALGORITHM_CONFIG=
2024-06-17 15:54:02,523  main        ERROR  c.a.c.a.Agent    Inicialization error. java.lang.NullPointerException
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.putVal(ConcurrentHashMap.java:1011)
	at java.base/java.util.concurrent.ConcurrentHashMap.put(ConcurrentHashMap.java:1006)
	at java.base/java.util.Properties.put(Properties.java:1340)
	at cz.ab.ci360.common.Kafka.init(Kafka.java:38)
	at cz.ab.ci360.agent_ch_stream.Agent.main(Agent.java:107)
2024-06-17 15:54:02,523  main        INFO   c.a.c.a.Agent    Exit code: 1
2024-06-17 15:54:50,900  main        INFO   c.a.c.a.Agent    
2024-06-17 15:54:50,902  main        INFO   c.a.c.a.Agent    ----- starting -------------------------------------------------------
2024-06-17 15:54:50,906  main        INFO   c.a.c.a.Agent    App: /opt/agents/ch-stream/agent-ch-stream.jar
2024-06-17 15:54:50,909  main        INFO   c.a.c.a.Agent    Version: 1.0.0
2024-06-17 15:54:50,909  main        INFO   c.a.c.a.Agent    ----- configuration --------------------------------------------------
2024-06-17 15:54:50,912  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-17 15:54:50,913  main        INFO   c.a.c.c.Config    ci360.jwt=f0cb22506600016b1805ee8b
2024-06-17 15:54:50,914  main        INFO   c.a.c.c.Config    agent.max_threads=5000
2024-06-17 15:54:50,914  main        INFO   c.a.c.c.Config    kafka.topic=sentCampaingEmail
2024-06-17 15:54:50,914  main        INFO   c.a.c.c.Config    proxy.server=proxy.np.ab
2024-06-17 15:54:50,914  main        INFO   c.a.c.c.Config    proxy.port=3128
2024-06-17 15:54:50,914  main        INFO   c.a.c.c.Config    proxy.realm=proxy
2024-06-17 15:54:50,914  main        INFO   c.a.c.c.Config    proxy.user=sascloud_user
2024-06-17 15:54:50,915  main        INFO   c.a.c.c.Config    proxy.non_proxy_hosts=app-sas-test.np.ab
2024-06-17 15:54:50,915  main        INFO   c.a.c.a.Agent    test mode = false
2024-06-17 15:54:50,916  main        INFO   c.a.c.c.Config    kafka.server=kafka.np.ab:9093
2024-06-17 15:54:50,916  main        INFO   c.a.c.c.Config    kafka.SSL_TRUSTSTORE_LOCATION_CONFIG=./cert/kafka-test.truststore.jks
2024-06-17 15:54:50,916  main        INFO   c.a.c.c.Config    kafka.SSL_KEYSTORE_LOCATION_CONFIG=./cert/kafka-test.keystore.jks
2024-06-17 15:54:50,916  main        INFO   c.a.c.c.Config    kafka.SSL_PROTOCOL_CONFIG=SSL
2024-06-17 15:54:50,916  main        INFO   c.a.c.c.Config    kafka.SSL_ENABLED_PROTOCOLS_CONFIG=TLSv1.2
2024-06-17 15:54:50,916  main        INFO   c.a.c.c.Config    kafka.SSL_ENDPOINT_IDENTIFICATION_ALGORITHM_CONFIG=
2024-06-17 15:54:50,938  main        INFO   o.a.k.c.p.ProducerConfig    ProducerConfig values: 
	acks = -1
	batch.size = 16384
	bootstrap.servers = [kafka.np.ab:9093]
	buffer.memory = 33554432
	client.dns.lookup = use_all_dns_ips
	client.id = producer-1
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = true
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 0
	max.block.ms = 60000
	max.in.flight.requests.per.connection = 5
	max.request.size = 1048576
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.class = class org.apache.kafka.clients.producer.internals.DefaultPartitioner
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retries = 2147483647
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = SSL
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2]
	ssl.endpoint.identification.algorithm = 
	ssl.engine.factory.class = null
	ssl.key.password = [hidden]
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = ./cert/kafka-test.keystore.jks
	ssl.keystore.password = [hidden]
	ssl.keystore.type = JKS
	ssl.protocol = SSL
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = ./cert/kafka-test.truststore.jks
	ssl.truststore.password = [hidden]
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.StringSerializer

2024-06-17 15:54:51,060  main        INFO   o.a.k.c.p.KafkaProducer    [Producer clientId=producer-1] Instantiated an idempotent producer.
2024-06-17 15:54:51,088  main        INFO   o.a.k.c.p.KafkaProducer    [Producer clientId=producer-1] Closing the Kafka producer with timeoutMillis = 0 ms.
2024-06-17 15:54:51,088  main        INFO   o.a.k.c.m.Metrics    Metrics scheduler closed
2024-06-17 15:54:51,088  main        INFO   o.a.k.c.m.Metrics    Closing reporter org.apache.kafka.common.metrics.JmxReporter
2024-06-17 15:54:51,088  main        INFO   o.a.k.c.m.Metrics    Metrics reporters closed
2024-06-17 15:54:51,090  main        INFO   o.a.k.c.u.AppInfoParser    App info kafka.producer for producer-1 unregistered
2024-06-17 15:54:51,092  main        ERROR  c.a.c.a.Agent    Inicialization error. org.apache.kafka.common.KafkaException: Failed to construct kafka producer
org.apache.kafka.common.KafkaException: Failed to construct kafka producer
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:439)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:290)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:317)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:302)
	at cz.ab.ci360.common.Kafka.init(Kafka.java:56)
	at cz.ab.ci360.agent_ch_stream.Agent.main(Agent.java:107)
Caused by: org.apache.kafka.common.KafkaException: Failed to load SSL keystore ./cert/kafka-test.keystore.jks of type JKS
	at org.apache.kafka.common.security.ssl.DefaultSslEngineFactory$FileBasedStore.load(DefaultSslEngineFactory.java:377)
	at org.apache.kafka.common.security.ssl.DefaultSslEngineFactory$FileBasedStore.<init>(DefaultSslEngineFactory.java:349)
	at org.apache.kafka.common.security.ssl.DefaultSslEngineFactory.createKeystore(DefaultSslEngineFactory.java:299)
	at org.apache.kafka.common.security.ssl.DefaultSslEngineFactory.configure(DefaultSslEngineFactory.java:161)
	at org.apache.kafka.common.security.ssl.SslFactory.instantiateSslEngineFactory(SslFactory.java:140)
	at org.apache.kafka.common.security.ssl.SslFactory.configure(SslFactory.java:97)
	at org.apache.kafka.common.network.SslChannelBuilder.configure(SslChannelBuilder.java:73)
	at org.apache.kafka.common.network.ChannelBuilders.create(ChannelBuilders.java:192)
	at org.apache.kafka.common.network.ChannelBuilders.clientChannelBuilder(ChannelBuilders.java:81)
	at org.apache.kafka.clients.ClientUtils.createChannelBuilder(ClientUtils.java:105)
	at org.apache.kafka.clients.producer.KafkaProducer.newSender(KafkaProducer.java:447)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:428)
	... 5 common frames omitted
Caused by: java.io.EOFException: null
	at java.base/java.io.DataInputStream.readInt(DataInputStream.java:397)
	at java.base/sun.security.provider.JavaKeyStore.engineLoad(JavaKeyStore.java:665)
	at java.base/sun.security.util.KeyStoreDelegator.engineLoad(KeyStoreDelegator.java:222)
	at java.base/java.security.KeyStore.load(KeyStore.java:1479)
	at org.apache.kafka.common.security.ssl.DefaultSslEngineFactory$FileBasedStore.load(DefaultSslEngineFactory.java:374)
	... 16 common frames omitted
2024-06-17 15:54:51,093  main        INFO   c.a.c.a.Agent    Exit code: 1
2024-06-17 16:13:20,583  main        INFO   c.a.c.a.Agent    
2024-06-17 16:13:20,585  main        INFO   c.a.c.a.Agent    ----- starting -------------------------------------------------------
2024-06-17 16:13:20,589  main        INFO   c.a.c.a.Agent    App: /opt/agents/ch-stream/agent-ch-stream.jar
2024-06-17 16:13:20,592  main        INFO   c.a.c.a.Agent    Version: 1.0.0
2024-06-17 16:13:20,592  main        INFO   c.a.c.a.Agent    ----- configuration --------------------------------------------------
2024-06-17 16:13:20,595  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-17 16:13:20,596  main        INFO   c.a.c.c.Config    ci360.jwt=f0cb22506600016b1805ee8b
2024-06-17 16:13:20,596  main        INFO   c.a.c.c.Config    agent.max_threads=5000
2024-06-17 16:13:20,596  main        INFO   c.a.c.c.Config    kafka.topic=sentCampaingEmail
2024-06-17 16:13:20,597  main        INFO   c.a.c.c.Config    proxy.server=proxy.np.ab
2024-06-17 16:13:20,597  main        INFO   c.a.c.c.Config    proxy.port=3128
2024-06-17 16:13:20,597  main        INFO   c.a.c.c.Config    proxy.realm=proxy
2024-06-17 16:13:20,597  main        INFO   c.a.c.c.Config    proxy.user=sascloud_user
2024-06-17 16:13:20,597  main        INFO   c.a.c.c.Config    proxy.non_proxy_hosts=app-sas-test.np.ab
2024-06-17 16:13:20,598  main        INFO   c.a.c.a.Agent    test mode = false
2024-06-17 16:13:20,598  main        INFO   c.a.c.c.Config    kafka.server=kafka.np.ab:9092
2024-06-17 16:13:20,599  main        INFO   c.a.c.c.Config    kafka.SSL_TRUSTSTORE_LOCATION_CONFIG=./cert/kafka-test.truststore.jks
2024-06-17 16:13:20,599  main        INFO   c.a.c.c.Config    kafka.SSL_KEYSTORE_LOCATION_CONFIG=./cert/kafka-test.keystore.jks
2024-06-17 16:13:20,599  main        INFO   c.a.c.c.Config    kafka.SSL_PROTOCOL_CONFIG=SSL
2024-06-17 16:13:20,599  main        INFO   c.a.c.c.Config    kafka.SSL_ENABLED_PROTOCOLS_CONFIG=TLSv1.2
2024-06-17 16:13:20,599  main        INFO   c.a.c.c.Config    kafka.SSL_ENDPOINT_IDENTIFICATION_ALGORITHM_CONFIG=
2024-06-17 16:13:20,620  main        INFO   o.a.k.c.p.ProducerConfig    ProducerConfig values: 
	acks = -1
	batch.size = 16384
	bootstrap.servers = [kafka.np.ab:9092]
	buffer.memory = 33554432
	client.dns.lookup = use_all_dns_ips
	client.id = producer-1
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = true
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 0
	max.block.ms = 60000
	max.in.flight.requests.per.connection = 5
	max.request.size = 1048576
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.class = class org.apache.kafka.clients.producer.internals.DefaultPartitioner
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retries = 2147483647
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = SSL
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2]
	ssl.endpoint.identification.algorithm = 
	ssl.engine.factory.class = null
	ssl.key.password = [hidden]
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = ./cert/kafka-test.keystore.jks
	ssl.keystore.password = [hidden]
	ssl.keystore.type = JKS
	ssl.protocol = SSL
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = ./cert/kafka-test.truststore.jks
	ssl.truststore.password = [hidden]
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.StringSerializer

2024-06-17 16:13:20,729  main        INFO   o.a.k.c.p.KafkaProducer    [Producer clientId=producer-1] Instantiated an idempotent producer.
2024-06-17 16:13:20,757  main        INFO   o.a.k.c.p.KafkaProducer    [Producer clientId=producer-1] Closing the Kafka producer with timeoutMillis = 0 ms.
2024-06-17 16:13:20,757  main        INFO   o.a.k.c.m.Metrics    Metrics scheduler closed
2024-06-17 16:13:20,757  main        INFO   o.a.k.c.m.Metrics    Closing reporter org.apache.kafka.common.metrics.JmxReporter
2024-06-17 16:13:20,757  main        INFO   o.a.k.c.m.Metrics    Metrics reporters closed
2024-06-17 16:13:20,759  main        INFO   o.a.k.c.u.AppInfoParser    App info kafka.producer for producer-1 unregistered
2024-06-17 16:13:20,761  main        ERROR  c.a.c.a.Agent    Inicialization error. org.apache.kafka.common.KafkaException: Failed to construct kafka producer
org.apache.kafka.common.KafkaException: Failed to construct kafka producer
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:439)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:290)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:317)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:302)
	at cz.ab.ci360.common.Kafka.init(Kafka.java:56)
	at cz.ab.ci360.agent_ch_stream.Agent.main(Agent.java:107)
Caused by: org.apache.kafka.common.KafkaException: Failed to load SSL keystore ./cert/kafka-test.keystore.jks of type JKS
	at org.apache.kafka.common.security.ssl.DefaultSslEngineFactory$FileBasedStore.load(DefaultSslEngineFactory.java:377)
	at org.apache.kafka.common.security.ssl.DefaultSslEngineFactory$FileBasedStore.<init>(DefaultSslEngineFactory.java:349)
	at org.apache.kafka.common.security.ssl.DefaultSslEngineFactory.createKeystore(DefaultSslEngineFactory.java:299)
	at org.apache.kafka.common.security.ssl.DefaultSslEngineFactory.configure(DefaultSslEngineFactory.java:161)
	at org.apache.kafka.common.security.ssl.SslFactory.instantiateSslEngineFactory(SslFactory.java:140)
	at org.apache.kafka.common.security.ssl.SslFactory.configure(SslFactory.java:97)
	at org.apache.kafka.common.network.SslChannelBuilder.configure(SslChannelBuilder.java:73)
	at org.apache.kafka.common.network.ChannelBuilders.create(ChannelBuilders.java:192)
	at org.apache.kafka.common.network.ChannelBuilders.clientChannelBuilder(ChannelBuilders.java:81)
	at org.apache.kafka.clients.ClientUtils.createChannelBuilder(ClientUtils.java:105)
	at org.apache.kafka.clients.producer.KafkaProducer.newSender(KafkaProducer.java:447)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:428)
	... 5 common frames omitted
Caused by: java.io.EOFException: null
	at java.base/java.io.DataInputStream.readInt(DataInputStream.java:397)
	at java.base/sun.security.provider.JavaKeyStore.engineLoad(JavaKeyStore.java:665)
	at java.base/sun.security.util.KeyStoreDelegator.engineLoad(KeyStoreDelegator.java:222)
	at java.base/java.security.KeyStore.load(KeyStore.java:1479)
	at org.apache.kafka.common.security.ssl.DefaultSslEngineFactory$FileBasedStore.load(DefaultSslEngineFactory.java:374)
	... 16 common frames omitted
2024-06-17 16:13:20,761  main        INFO   c.a.c.a.Agent    Exit code: 1
2024-06-17 17:31:52,537  main        INFO   c.a.c.a.Agent    
2024-06-17 17:31:52,538  main        INFO   c.a.c.a.Agent    ----- starting -------------------------------------------------------
2024-06-17 17:31:52,543  main        INFO   c.a.c.a.Agent    App: /opt/agents/ch-stream/agent-ch-stream.jar
2024-06-17 17:31:52,546  main        INFO   c.a.c.a.Agent    Version: 1.0.0
2024-06-17 17:31:52,546  main        INFO   c.a.c.a.Agent    ----- configuration --------------------------------------------------
2024-06-17 17:31:52,549  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-17 17:31:52,550  main        INFO   c.a.c.c.Config    ci360.jwt=f0cb22506600016b1805ee8b
2024-06-17 17:31:52,550  main        INFO   c.a.c.c.Config    agent.max_threads=5000
2024-06-17 17:31:52,550  main        INFO   c.a.c.c.Config    kafka.topic=sentCampaingEmail
2024-06-17 17:31:52,550  main        INFO   c.a.c.c.Config    proxy.server=proxy.np.ab
2024-06-17 17:31:52,550  main        INFO   c.a.c.c.Config    proxy.port=3128
2024-06-17 17:31:52,551  main        INFO   c.a.c.c.Config    proxy.realm=proxy
2024-06-17 17:31:52,551  main        INFO   c.a.c.c.Config    proxy.user=sascloud_user
2024-06-17 17:31:52,551  main        INFO   c.a.c.c.Config    proxy.non_proxy_hosts=app-sas-test.np.ab
2024-06-17 17:31:52,551  main        INFO   c.a.c.a.Agent    test mode = true
2024-06-17 17:31:52,552  main        INFO   c.a.c.c.Config    kafka.server=kafka.np.ab:9092
2024-06-17 17:31:52,552  main        INFO   c.a.c.c.Config    kafka.SSL_TRUSTSTORE_LOCATION_CONFIG=./cert/kafka-test.truststore.jks
2024-06-17 17:31:52,552  main        INFO   c.a.c.c.Config    kafka.SSL_KEYSTORE_LOCATION_CONFIG=./cert/kafka-test.keystore.jks
2024-06-17 17:31:52,552  main        INFO   c.a.c.c.Config    kafka.SSL_PROTOCOL_CONFIG=SSL
2024-06-17 17:31:52,553  main        INFO   c.a.c.c.Config    kafka.SSL_ENABLED_PROTOCOLS_CONFIG=TLSv1.2
2024-06-17 17:31:52,553  main        INFO   c.a.c.c.Config    kafka.SSL_ENDPOINT_IDENTIFICATION_ALGORITHM_CONFIG=
2024-06-17 17:31:52,577  main        INFO   o.a.k.c.p.ProducerConfig    ProducerConfig values: 
	acks = -1
	batch.size = 16384
	bootstrap.servers = [kafka.np.ab:9092]
	buffer.memory = 33554432
	client.dns.lookup = use_all_dns_ips
	client.id = producer-1
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = true
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 0
	max.block.ms = 60000
	max.in.flight.requests.per.connection = 5
	max.request.size = 1048576
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.class = class org.apache.kafka.clients.producer.internals.DefaultPartitioner
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retries = 2147483647
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = SSL
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2]
	ssl.endpoint.identification.algorithm = 
	ssl.engine.factory.class = null
	ssl.key.password = [hidden]
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = ./cert/kafka-test.keystore.jks
	ssl.keystore.password = [hidden]
	ssl.keystore.type = JKS
	ssl.protocol = SSL
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = ./cert/kafka-test.truststore.jks
	ssl.truststore.password = [hidden]
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.StringSerializer

2024-06-17 17:31:52,690  main        INFO   o.a.k.c.p.KafkaProducer    [Producer clientId=producer-1] Instantiated an idempotent producer.
2024-06-17 17:31:52,725  main        INFO   o.a.k.c.p.KafkaProducer    [Producer clientId=producer-1] Closing the Kafka producer with timeoutMillis = 0 ms.
2024-06-17 17:31:52,725  main        INFO   o.a.k.c.m.Metrics    Metrics scheduler closed
2024-06-17 17:31:52,725  main        INFO   o.a.k.c.m.Metrics    Closing reporter org.apache.kafka.common.metrics.JmxReporter
2024-06-17 17:31:52,725  main        INFO   o.a.k.c.m.Metrics    Metrics reporters closed
2024-06-17 17:31:52,727  main        INFO   o.a.k.c.u.AppInfoParser    App info kafka.producer for producer-1 unregistered
2024-06-17 17:31:52,729  main        ERROR  c.a.c.a.Agent    Inicialization error. org.apache.kafka.common.KafkaException: Failed to construct kafka producer
org.apache.kafka.common.KafkaException: Failed to construct kafka producer
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:439)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:290)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:317)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:302)
	at cz.ab.ci360.common.Kafka.init(Kafka.java:56)
	at cz.ab.ci360.agent_ch_stream.Agent.main(Agent.java:107)
Caused by: org.apache.kafka.common.KafkaException: Failed to load SSL keystore ./cert/kafka-test.keystore.jks of type JKS
	at org.apache.kafka.common.security.ssl.DefaultSslEngineFactory$FileBasedStore.load(DefaultSslEngineFactory.java:377)
	at org.apache.kafka.common.security.ssl.DefaultSslEngineFactory$FileBasedStore.<init>(DefaultSslEngineFactory.java:349)
	at org.apache.kafka.common.security.ssl.DefaultSslEngineFactory.createKeystore(DefaultSslEngineFactory.java:299)
	at org.apache.kafka.common.security.ssl.DefaultSslEngineFactory.configure(DefaultSslEngineFactory.java:161)
	at org.apache.kafka.common.security.ssl.SslFactory.instantiateSslEngineFactory(SslFactory.java:140)
	at org.apache.kafka.common.security.ssl.SslFactory.configure(SslFactory.java:97)
	at org.apache.kafka.common.network.SslChannelBuilder.configure(SslChannelBuilder.java:73)
	at org.apache.kafka.common.network.ChannelBuilders.create(ChannelBuilders.java:192)
	at org.apache.kafka.common.network.ChannelBuilders.clientChannelBuilder(ChannelBuilders.java:81)
	at org.apache.kafka.clients.ClientUtils.createChannelBuilder(ClientUtils.java:105)
	at org.apache.kafka.clients.producer.KafkaProducer.newSender(KafkaProducer.java:447)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:428)
	... 5 common frames omitted
Caused by: java.io.EOFException: null
	at java.base/java.io.DataInputStream.readInt(DataInputStream.java:397)
	at java.base/sun.security.provider.JavaKeyStore.engineLoad(JavaKeyStore.java:665)
	at java.base/sun.security.util.KeyStoreDelegator.engineLoad(KeyStoreDelegator.java:222)
	at java.base/java.security.KeyStore.load(KeyStore.java:1479)
	at org.apache.kafka.common.security.ssl.DefaultSslEngineFactory$FileBasedStore.load(DefaultSslEngineFactory.java:374)
	... 16 common frames omitted
2024-06-17 17:31:52,730  main        INFO   c.a.c.a.Agent    Exit code: 1
2024-06-17 21:12:24,930  main        INFO   c.a.c.a.Agent    
2024-06-17 21:12:24,932  main        INFO   c.a.c.a.Agent    ----- starting -------------------------------------------------------
2024-06-17 21:12:24,936  main        INFO   c.a.c.a.Agent    App: /opt/agents/ch-stream/agent-ch-stream.jar
2024-06-17 21:12:24,940  main        INFO   c.a.c.a.Agent    Version: 1.0.0
2024-06-17 21:12:24,940  main        INFO   c.a.c.a.Agent    ----- configuration --------------------------------------------------
2024-06-17 21:12:24,943  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-17 21:12:24,944  main        INFO   c.a.c.c.Config    ci360.jwt=f0cb22506600016b1805ee8b
2024-06-17 21:12:24,945  main        INFO   c.a.c.c.Config    agent.max_threads=5000
2024-06-17 21:12:24,945  main        INFO   c.a.c.c.Config    kafka.topic=sentCampaingEmail
2024-06-17 21:12:24,945  main        INFO   c.a.c.c.Config    agent.kafka=false
2024-06-17 21:12:24,945  main        INFO   c.a.c.c.Config    proxy.server=proxy.np.ab
2024-06-17 21:12:24,945  main        INFO   c.a.c.c.Config    proxy.port=3128
2024-06-17 21:12:24,945  main        INFO   c.a.c.c.Config    proxy.realm=proxy
2024-06-17 21:12:24,945  main        INFO   c.a.c.c.Config    proxy.user=sascloud_user
2024-06-17 21:12:24,946  main        INFO   c.a.c.c.Config    proxy.non_proxy_hosts=app-sas-test.np.ab
2024-06-17 21:12:24,946  main        INFO   c.a.c.a.Agent    test mode = false
2024-06-17 21:12:24,946  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-17 21:12:24,947  main        INFO   c.a.c.c.Config    db.user=APP_CAMPAIGN_CDM2
2024-06-17 21:12:24,947  main        INFO   c.a.c.c.Config    db.database=APP_CAMPAIGN_CDM2
2024-06-17 21:12:24,947  main        INFO   c.a.c.c.Config    db.server=dbs02.te00.np.ab:1621
2024-06-17 21:12:24,947  main        INFO   c.a.c.c.Config    db.service_name=MNTE00DW.np.ab
2024-06-17 21:12:24,947  main        INFO   c.a.c.c.Config    db.init_connection_pool_size=10
2024-06-17 21:12:24,947  main        INFO   c.a.c.c.Config    db.max_connection_pool_size=200
2024-06-17 21:12:25,595  main        INFO   c.a.c.c.Config    event.process_event_with_prefix=c_
2024-06-17 21:12:25,595  main        INFO   c.a.c.c.Config    event.direct_event=c_direct
2024-06-17 21:12:25,596  main        INFO   c.a.c.c.Config    event.ignore_events=c_activitystart,c_abtestpathassignment
2024-06-17 21:12:25,596  main        INFO   c.a.c.a.Agent    ----- eof configuration --------------------------------------------------
2024-06-17 21:12:25,599  main        INFO   c.s.m.a.s.CI360Agent    Maximum upstream queue size: 1000
2024-06-17 21:12:25,599  main        INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-17 21:12:25,649  main        INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-17 21:12:26,117  4155890-21  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-17 21:13:25,790  main        INFO   c.a.c.a.Agent    running
2024-06-17 21:13:36,132  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-17 21:13:36,132  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-17 21:13:36,133  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-17 21:13:51,133  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-17 21:13:51,133  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-17 21:13:51,138  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-17 21:13:51,140  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-17 21:13:51,140  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-17 21:13:51,279  4155890-18  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-17 21:14:06,140  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-17 21:14:06,143  cheduler-1  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$1.streamClosed(Agent.java:172)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$sendFrame$7(WebSocketCoreSession.java:519)
	at org.eclipse.jetty.util.Callback$3.succeeded(Callback.java:155)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.notifyCallbackSuccess(TransformingFlusher.java:197)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher$Flusher.process(TransformingFlusher.java:154)
	at org.eclipse.jetty.util.IteratingCallback.processing(IteratingCallback.java:243)
	at org.eclipse.jetty.util.IteratingCallback.iterate(IteratingCallback.java:224)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.sendFrame(TransformingFlusher.java:77)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.sendFrame(WebSocketCoreSession.java:522)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.close(WebSocketCoreSession.java:239)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.processHandlerError(WebSocketCoreSession.java:371)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onIdleExpired(WebSocketConnection.java:242)
	at org.eclipse.jetty.io.ssl.SslConnection.onIdleExpired(SslConnection.java:360)
	at org.eclipse.jetty.io.AbstractEndPoint.onIdleExpired(AbstractEndPoint.java:407)
	at org.eclipse.jetty.io.IdleTimeout.checkIdleTimeout(IdleTimeout.java:170)
	at org.eclipse.jetty.io.IdleTimeout.idleCheck(IdleTimeout.java:112)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-17 21:14:25,801  main        INFO   c.a.c.a.Agent    running
2024-06-17 21:15:01,282  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-17 21:15:01,282  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-17 21:15:01,283  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-17 21:15:16,283  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-17 21:15:16,283  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-17 21:15:16,284  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-17 21:15:16,284  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-17 21:15:16,285  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-17 21:15:16,423  4155890-23  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-17 21:15:25,812  main        INFO   c.a.c.a.Agent    running
2024-06-17 21:15:29,814  main        INFO   c.a.c.a.Agent    Stop reason: 'signal'
2024-06-17 21:15:29,822  main        INFO   c.s.m.a.s.CI360Agent    Stopping event stream connection now
2024-06-17 21:15:29,823  main        INFO   o.e.j.w.c.WebSocketClient    Shutdown WebSocketClient@edfd230a[coreClient=WebSocketCoreClient@5c08c46a{STARTED},openSessions.size=2]
2024-06-17 21:15:29,824  4155890-20  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: null
2024-06-17 21:15:29,824  4155890-20  DEBUG  c.a.c.a.Agent    Stream closed
2024-06-17 21:15:29,824  4155890-20  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1006] Session Closed
2024-06-17 21:15:29,824  4155890-20  DEBUG  c.a.c.a.Agent    Stream closed
2024-06-17 21:15:29,829  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-17 21:15:29,829  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-17 21:15:29,954  4155890-38  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-17 21:15:30,830  main        INFO   c.a.c.a.Agent    Exit code: 0
