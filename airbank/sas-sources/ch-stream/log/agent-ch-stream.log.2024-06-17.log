2024-06-17 21:18:15,814  main        INFO   c.a.c.a.Agent    
2024-06-17 21:18:15,815  main        INFO   c.a.c.a.Agent    ----- starting -------------------------------------------------------
2024-06-17 21:18:15,820  main        INFO   c.a.c.a.Agent    App: /opt/agents/ch-stream/agent-ch-stream.jar
2024-06-17 21:18:15,822  main        INFO   c.a.c.a.Agent    Version: 1.0.0
2024-06-17 21:18:15,823  main        INFO   c.a.c.a.Agent    ----- configuration --------------------------------------------------
2024-06-17 21:18:15,826  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-17 21:18:15,827  main        INFO   c.a.c.c.Config    ci360.jwt=f0cb22506600016b1805ee8b
2024-06-17 21:18:15,827  main        INFO   c.a.c.c.Config    agent.max_threads=5000
2024-06-17 21:18:15,827  main        INFO   c.a.c.c.Config    kafka.topic=sentCampaingEmail
2024-06-17 21:18:15,827  main        INFO   c.a.c.c.Config    agent.kafka=false
2024-06-17 21:18:15,827  main        INFO   c.a.c.c.Config    proxy.server=proxy.np.ab
2024-06-17 21:18:15,827  main        INFO   c.a.c.c.Config    proxy.port=3128
2024-06-17 21:18:15,827  main        INFO   c.a.c.c.Config    proxy.realm=proxy
2024-06-17 21:18:15,827  main        INFO   c.a.c.c.Config    proxy.user=sascloud_user
2024-06-17 21:18:15,828  main        INFO   c.a.c.c.Config    proxy.non_proxy_hosts=app-sas-test.np.ab
2024-06-17 21:18:15,828  main        INFO   c.a.c.a.Agent    test mode = false
2024-06-17 21:18:15,829  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-17 21:18:15,829  main        INFO   c.a.c.c.Config    db.user=APP_CAMPAIGN_CDM2
2024-06-17 21:18:15,829  main        INFO   c.a.c.c.Config    db.database=APP_CAMPAIGN_CDM2
2024-06-17 21:18:15,829  main        INFO   c.a.c.c.Config    db.server=dbs02.te00.np.ab:1621
2024-06-17 21:18:15,829  main        INFO   c.a.c.c.Config    db.service_name=MNTE00DW.np.ab
2024-06-17 21:18:15,829  main        INFO   c.a.c.c.Config    db.init_connection_pool_size=10
2024-06-17 21:18:15,829  main        INFO   c.a.c.c.Config    db.max_connection_pool_size=200
2024-06-17 21:18:16,490  main        INFO   c.a.c.c.Config    event.process_event_with_prefix=c_
2024-06-17 21:18:16,491  main        INFO   c.a.c.c.Config    event.direct_event=c_direct
2024-06-17 21:18:16,491  main        INFO   c.a.c.c.Config    event.ignore_events=c_activitystart,c_abtestpathassignment
2024-06-17 21:18:16,491  main        INFO   c.a.c.a.Agent    ----- eof configuration --------------------------------------------------
2024-06-17 21:18:16,495  main        INFO   c.s.m.a.s.CI360Agent    Maximum upstream queue size: 1000
2024-06-17 21:18:16,495  main        INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-17 21:18:16,542  main        INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-17 21:18:16,968  5580595-21  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-17 21:19:16,691  main        INFO   c.a.c.a.Agent    running
2024-06-17 21:19:26,982  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-17 21:19:26,983  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-17 21:19:26,983  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-17 21:19:41,984  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-17 21:19:41,984  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-17 21:19:41,989  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-17 21:19:41,990  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-17 21:19:41,991  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-17 21:19:42,177  5580595-22  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-17 21:19:56,991  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-17 21:19:56,995  cheduler-1  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$1.streamClosed(Agent.java:172)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$sendFrame$7(WebSocketCoreSession.java:519)
	at org.eclipse.jetty.util.Callback$3.succeeded(Callback.java:155)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.notifyCallbackSuccess(TransformingFlusher.java:197)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher$Flusher.process(TransformingFlusher.java:154)
	at org.eclipse.jetty.util.IteratingCallback.processing(IteratingCallback.java:243)
	at org.eclipse.jetty.util.IteratingCallback.iterate(IteratingCallback.java:224)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.sendFrame(TransformingFlusher.java:77)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.sendFrame(WebSocketCoreSession.java:522)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.close(WebSocketCoreSession.java:239)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.processHandlerError(WebSocketCoreSession.java:371)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onIdleExpired(WebSocketConnection.java:242)
	at org.eclipse.jetty.io.ssl.SslConnection.onIdleExpired(SslConnection.java:360)
	at org.eclipse.jetty.io.AbstractEndPoint.onIdleExpired(AbstractEndPoint.java:407)
	at org.eclipse.jetty.io.IdleTimeout.checkIdleTimeout(IdleTimeout.java:170)
	at org.eclipse.jetty.io.IdleTimeout.idleCheck(IdleTimeout.java:112)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-17 21:20:16,702  main        INFO   c.a.c.a.Agent    running
2024-06-17 21:20:52,179  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-17 21:20:52,179  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-17 21:20:52,180  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-17 21:21:07,180  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-17 21:21:07,180  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-17 21:21:07,181  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-17 21:21:07,181  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-17 21:21:07,181  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-17 21:21:07,299  5580595-23  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-17 21:21:16,713  main        INFO   c.a.c.a.Agent    running
2024-06-17 21:21:22,181  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-17 21:21:22,182  cheduler-1  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$1.streamClosed(Agent.java:172)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$sendFrame$7(WebSocketCoreSession.java:519)
	at org.eclipse.jetty.util.Callback$3.succeeded(Callback.java:155)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.notifyCallbackSuccess(TransformingFlusher.java:197)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher$Flusher.process(TransformingFlusher.java:154)
	at org.eclipse.jetty.util.IteratingCallback.processing(IteratingCallback.java:243)
	at org.eclipse.jetty.util.IteratingCallback.iterate(IteratingCallback.java:224)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.sendFrame(TransformingFlusher.java:77)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.sendFrame(WebSocketCoreSession.java:522)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.close(WebSocketCoreSession.java:239)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.processHandlerError(WebSocketCoreSession.java:371)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onIdleExpired(WebSocketConnection.java:242)
	at org.eclipse.jetty.io.ssl.SslConnection.onIdleExpired(SslConnection.java:360)
	at org.eclipse.jetty.io.AbstractEndPoint.onIdleExpired(AbstractEndPoint.java:407)
	at org.eclipse.jetty.io.IdleTimeout.checkIdleTimeout(IdleTimeout.java:170)
	at org.eclipse.jetty.io.IdleTimeout.idleCheck(IdleTimeout.java:112)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-17 21:22:16,725  main        INFO   c.a.c.a.Agent    running
2024-06-17 21:22:17,302  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-17 21:22:17,302  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-17 21:22:17,302  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-17 21:22:32,302  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-17 21:22:32,303  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-17 21:22:32,303  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-17 21:22:32,303  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-17 21:22:32,304  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-17 21:22:32,433  5580595-21  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-17 21:22:47,304  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-17 21:22:47,304  cheduler-1  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$1.streamClosed(Agent.java:172)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$sendFrame$7(WebSocketCoreSession.java:519)
	at org.eclipse.jetty.util.Callback$3.succeeded(Callback.java:155)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.notifyCallbackSuccess(TransformingFlusher.java:197)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher$Flusher.process(TransformingFlusher.java:154)
	at org.eclipse.jetty.util.IteratingCallback.processing(IteratingCallback.java:243)
	at org.eclipse.jetty.util.IteratingCallback.iterate(IteratingCallback.java:224)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.sendFrame(TransformingFlusher.java:77)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.sendFrame(WebSocketCoreSession.java:522)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.close(WebSocketCoreSession.java:239)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.processHandlerError(WebSocketCoreSession.java:371)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onIdleExpired(WebSocketConnection.java:242)
	at org.eclipse.jetty.io.ssl.SslConnection.onIdleExpired(SslConnection.java:360)
	at org.eclipse.jetty.io.AbstractEndPoint.onIdleExpired(AbstractEndPoint.java:407)
	at org.eclipse.jetty.io.IdleTimeout.checkIdleTimeout(IdleTimeout.java:170)
	at org.eclipse.jetty.io.IdleTimeout.idleCheck(IdleTimeout.java:112)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-17 21:23:04,734  main        INFO   c.a.c.a.Agent    Stop reason: 'signal'
2024-06-17 21:23:04,744  main        INFO   c.s.m.a.s.CI360Agent    Stopping event stream connection now
2024-06-17 21:23:04,745  main        INFO   o.e.j.w.c.WebSocketClient    Shutdown WebSocketClient@c58e2804[coreClient=WebSocketCoreClient@2a5b3fee{STARTED},openSessions.size=1]
2024-06-17 21:23:04,746  5580595-23  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: null
2024-06-17 21:23:04,747  5580595-23  DEBUG  c.a.c.a.Agent    Stream closed
2024-06-17 21:23:04,747  5580595-23  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1006] Session Closed
2024-06-17 21:23:04,747  5580595-23  DEBUG  c.a.c.a.Agent    Stream closed
2024-06-17 21:23:05,753  main        INFO   c.a.c.a.Agent    Exit code: 0
2024-06-17 21:23:42,604  main        INFO   c.a.c.a.Agent    
2024-06-17 21:23:42,605  main        INFO   c.a.c.a.Agent    ----- starting -------------------------------------------------------
2024-06-17 21:23:42,610  main        INFO   c.a.c.a.Agent    App: /opt/agents/ch-stream/agent-ch-stream.jar
2024-06-17 21:23:42,613  main        INFO   c.a.c.a.Agent    Version: 1.0.0
2024-06-17 21:23:42,613  main        INFO   c.a.c.a.Agent    ----- configuration --------------------------------------------------
2024-06-17 21:23:42,616  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-17 21:23:42,617  main        INFO   c.a.c.c.Config    ci360.jwt=f0cb22506600016b1805ee8b
2024-06-17 21:23:42,617  main        INFO   c.a.c.c.Config    agent.max_threads=5000
2024-06-17 21:23:42,617  main        INFO   c.a.c.c.Config    kafka.topic=sentCampaingEmail
2024-06-17 21:23:42,617  main        INFO   c.a.c.c.Config    agent.kafka=false
2024-06-17 21:23:42,617  main        INFO   c.a.c.c.Config    proxy.server=proxy.np.ab
2024-06-17 21:23:42,618  main        INFO   c.a.c.c.Config    proxy.port=3128
2024-06-17 21:23:42,618  main        INFO   c.a.c.c.Config    proxy.realm="proxy"
2024-06-17 21:23:42,618  main        INFO   c.a.c.c.Config    proxy.user=sascloud_user
2024-06-17 21:23:42,618  main        INFO   c.a.c.c.Config    proxy.non_proxy_hosts=app-sas-test.np.ab
2024-06-17 21:23:42,618  main        INFO   c.a.c.a.Agent    test mode = false
2024-06-17 21:23:42,619  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-17 21:23:42,619  main        INFO   c.a.c.c.Config    db.user=APP_CAMPAIGN_CDM2
2024-06-17 21:23:42,619  main        INFO   c.a.c.c.Config    db.database=APP_CAMPAIGN_CDM2
2024-06-17 21:23:42,619  main        INFO   c.a.c.c.Config    db.server=dbs02.te00.np.ab:1621
2024-06-17 21:23:42,619  main        INFO   c.a.c.c.Config    db.service_name=MNTE00DW.np.ab
2024-06-17 21:23:42,619  main        INFO   c.a.c.c.Config    db.init_connection_pool_size=10
2024-06-17 21:23:42,619  main        INFO   c.a.c.c.Config    db.max_connection_pool_size=200
2024-06-17 21:23:43,294  main        INFO   c.a.c.c.Config    event.process_event_with_prefix=c_
2024-06-17 21:23:43,295  main        INFO   c.a.c.c.Config    event.direct_event=c_direct
2024-06-17 21:23:43,295  main        INFO   c.a.c.c.Config    event.ignore_events=c_activitystart,c_abtestpathassignment
2024-06-17 21:23:43,295  main        INFO   c.a.c.a.Agent    ----- eof configuration --------------------------------------------------
2024-06-17 21:23:43,298  main        INFO   c.s.m.a.s.CI360Agent    Maximum upstream queue size: 1000
2024-06-17 21:23:43,299  main        INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-17 21:23:43,351  main        INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-17 21:23:43,591  0651474-23  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-17 21:23:43,593  0651474-23  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-17 21:23:43,593  0651474-23  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-17 21:23:58,593  0651474-23  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-17 21:23:58,593  0651474-23  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-17 21:23:58,601  0651474-17  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-17 21:23:58,601  0651474-17  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-17 21:23:58,601  0651474-17  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-17 21:24:13,601  0651474-17  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-17 21:24:13,601  0651474-17  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-17 21:24:13,607  0651474-18  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-17 21:24:13,608  0651474-18  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-17 21:24:13,608  0651474-18  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-17 21:24:28,608  0651474-18  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-17 21:24:28,608  0651474-18  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-17 21:24:28,614  0651474-21  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-17 21:24:28,614  0651474-21  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-17 21:24:28,614  0651474-21  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-17 21:24:43,510  main        INFO   c.a.c.a.Agent    running
2024-06-17 21:24:43,614  0651474-21  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-17 21:24:43,615  0651474-21  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-17 21:24:43,676  0651474-20  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-17 21:24:43,676  0651474-20  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-17 21:24:43,676  0651474-20  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-17 21:24:55,513  main        INFO   c.a.c.a.Agent    Stop reason: 'signal'
2024-06-17 21:24:56,520  main        INFO   c.a.c.a.Agent    Exit code: 0
2024-06-17 21:25:29,591  main        INFO   c.a.c.a.Agent    
2024-06-17 21:25:29,593  main        INFO   c.a.c.a.Agent    ----- starting -------------------------------------------------------
2024-06-17 21:25:29,597  main        INFO   c.a.c.a.Agent    App: /opt/agents/ch-stream/agent-ch-stream.jar
2024-06-17 21:25:29,600  main        INFO   c.a.c.a.Agent    Version: 1.0.0
2024-06-17 21:25:29,600  main        INFO   c.a.c.a.Agent    ----- configuration --------------------------------------------------
2024-06-17 21:25:29,603  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-17 21:25:29,604  main        INFO   c.a.c.c.Config    ci360.jwt=f0cb22506600016b1805ee8b
2024-06-17 21:25:29,604  main        INFO   c.a.c.c.Config    agent.max_threads=5000
2024-06-17 21:25:29,604  main        INFO   c.a.c.c.Config    kafka.topic=sentCampaingEmail
2024-06-17 21:25:29,604  main        INFO   c.a.c.c.Config    agent.kafka=false
2024-06-17 21:25:29,605  main        INFO   c.a.c.c.Config    proxy.server=proxy.np.ab
2024-06-17 21:25:29,605  main        INFO   c.a.c.c.Config    proxy.port=3128
2024-06-17 21:25:29,605  main        INFO   c.a.c.c.Config    proxy.realm=/"proxy/"
2024-06-17 21:25:29,605  main        INFO   c.a.c.c.Config    proxy.user=sascloud_user
2024-06-17 21:25:29,605  main        INFO   c.a.c.c.Config    proxy.non_proxy_hosts=app-sas-test.np.ab
2024-06-17 21:25:29,605  main        INFO   c.a.c.a.Agent    test mode = false
2024-06-17 21:25:29,606  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-17 21:25:29,606  main        INFO   c.a.c.c.Config    db.user=APP_CAMPAIGN_CDM2
2024-06-17 21:25:29,606  main        INFO   c.a.c.c.Config    db.database=APP_CAMPAIGN_CDM2
2024-06-17 21:25:29,606  main        INFO   c.a.c.c.Config    db.server=dbs02.te00.np.ab:1621
2024-06-17 21:25:29,607  main        INFO   c.a.c.c.Config    db.service_name=MNTE00DW.np.ab
2024-06-17 21:25:29,607  main        INFO   c.a.c.c.Config    db.init_connection_pool_size=10
2024-06-17 21:25:29,607  main        INFO   c.a.c.c.Config    db.max_connection_pool_size=200
2024-06-17 21:25:30,360  main        INFO   c.a.c.c.Config    event.process_event_with_prefix=c_
2024-06-17 21:25:30,361  main        INFO   c.a.c.c.Config    event.direct_event=c_direct
2024-06-17 21:25:30,361  main        INFO   c.a.c.c.Config    event.ignore_events=c_activitystart,c_abtestpathassignment
2024-06-17 21:25:30,361  main        INFO   c.a.c.a.Agent    ----- eof configuration --------------------------------------------------
2024-06-17 21:25:30,364  main        INFO   c.s.m.a.s.CI360Agent    Maximum upstream queue size: 1000
2024-06-17 21:25:30,364  main        INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-17 21:25:30,415  main        INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-17 21:25:30,629  1829978-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-17 21:25:30,630  1829978-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-17 21:25:30,631  1829978-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-17 21:25:43,555  main        INFO   c.a.c.a.Agent    Stop reason: 'signal'
2024-06-17 21:25:44,577  main        INFO   c.a.c.a.Agent    Exit code: 0
2024-06-17 21:25:45,631  1829978-22  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-17 21:25:45,632  1829978-22  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-17 21:25:45,689  1829978-17  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-17 21:25:45,689  1829978-17  DEBUG  c.a.c.a.Agent    Stream closed
2024-06-17 21:27:01,629  main        INFO   c.a.c.a.Agent    
2024-06-17 21:27:01,631  main        INFO   c.a.c.a.Agent    ----- starting -------------------------------------------------------
2024-06-17 21:27:01,635  main        INFO   c.a.c.a.Agent    App: /opt/agents/ch-stream/agent-ch-stream.jar
2024-06-17 21:27:01,638  main        INFO   c.a.c.a.Agent    Version: 1.0.0
2024-06-17 21:27:01,638  main        INFO   c.a.c.a.Agent    ----- configuration --------------------------------------------------
2024-06-17 21:27:01,641  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-17 21:27:01,642  main        INFO   c.a.c.c.Config    ci360.jwt=f0cb22506600016b1805ee8b
2024-06-17 21:27:01,642  main        INFO   c.a.c.c.Config    agent.max_threads=5000
2024-06-17 21:27:01,642  main        INFO   c.a.c.c.Config    kafka.topic=sentCampaingEmail
2024-06-17 21:27:01,643  main        INFO   c.a.c.c.Config    agent.kafka=false
2024-06-17 21:27:01,643  main        INFO   c.a.c.c.Config    proxy.server=proxy.np.ab
2024-06-17 21:27:01,643  main        INFO   c.a.c.c.Config    proxy.port=3128
2024-06-17 21:27:01,643  main        INFO   c.a.c.c.Config    proxy.realm=proxy
2024-06-17 21:27:01,643  main        INFO   c.a.c.c.Config    proxy.user=sascloud_user
2024-06-17 21:27:01,644  main        INFO   c.a.c.c.Config    proxy.non_proxy_hosts=app-sas-test.np.ab
2024-06-17 21:27:01,644  main        INFO   c.a.c.a.Agent    test mode = false
2024-06-17 21:27:01,644  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-17 21:27:01,644  main        INFO   c.a.c.c.Config    db.user=APP_CAMPAIGN_CDM2
2024-06-17 21:27:01,645  main        INFO   c.a.c.c.Config    db.database=APP_CAMPAIGN_CDM2
2024-06-17 21:27:01,645  main        INFO   c.a.c.c.Config    db.server=dbs02.te00.np.ab:1621
2024-06-17 21:27:01,645  main        INFO   c.a.c.c.Config    db.service_name=MNTE00DW.np.ab
2024-06-17 21:27:01,645  main        INFO   c.a.c.c.Config    db.init_connection_pool_size=10
2024-06-17 21:27:01,645  main        INFO   c.a.c.c.Config    db.max_connection_pool_size=200
2024-06-17 21:27:02,350  main        INFO   c.a.c.c.Config    event.process_event_with_prefix=c_
2024-06-17 21:27:02,351  main        INFO   c.a.c.c.Config    event.direct_event=c_direct
2024-06-17 21:27:02,351  main        INFO   c.a.c.c.Config    event.ignore_events=c_activitystart,c_abtestpathassignment
2024-06-17 21:27:02,351  main        INFO   c.a.c.a.Agent    ----- eof configuration --------------------------------------------------
2024-06-17 21:27:02,354  main        INFO   c.s.m.a.s.CI360Agent    Maximum upstream queue size: 1000
2024-06-17 21:27:02,354  main        INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-17 21:27:02,408  main        INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-17 21:27:02,875  5580595-22  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-17 21:28:02,562  main        INFO   c.a.c.a.Agent    running
2024-06-17 21:28:12,889  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-17 21:28:12,890  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-17 21:28:12,890  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-17 21:28:27,890  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-17 21:28:27,891  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-17 21:28:27,899  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-17 21:28:27,901  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-17 21:28:27,901  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-17 21:28:28,050  5580595-19  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-17 21:28:42,901  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-17 21:28:42,904  cheduler-1  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$1.streamClosed(Agent.java:172)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$sendFrame$7(WebSocketCoreSession.java:519)
	at org.eclipse.jetty.util.Callback$3.succeeded(Callback.java:155)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.notifyCallbackSuccess(TransformingFlusher.java:197)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher$Flusher.process(TransformingFlusher.java:154)
	at org.eclipse.jetty.util.IteratingCallback.processing(IteratingCallback.java:243)
	at org.eclipse.jetty.util.IteratingCallback.iterate(IteratingCallback.java:224)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.sendFrame(TransformingFlusher.java:77)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.sendFrame(WebSocketCoreSession.java:522)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.close(WebSocketCoreSession.java:239)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.processHandlerError(WebSocketCoreSession.java:371)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onIdleExpired(WebSocketConnection.java:242)
	at org.eclipse.jetty.io.ssl.SslConnection.onIdleExpired(SslConnection.java:360)
	at org.eclipse.jetty.io.AbstractEndPoint.onIdleExpired(AbstractEndPoint.java:407)
	at org.eclipse.jetty.io.IdleTimeout.checkIdleTimeout(IdleTimeout.java:170)
	at org.eclipse.jetty.io.IdleTimeout.idleCheck(IdleTimeout.java:112)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-17 21:29:02,573  main        INFO   c.a.c.a.Agent    running
2024-06-17 21:29:38,052  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-17 21:29:38,053  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-17 21:29:38,053  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-17 21:29:53,053  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-17 21:29:53,053  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-17 21:29:53,054  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-17 21:29:53,054  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-17 21:29:53,054  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-17 21:29:53,200  5580595-24  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-17 21:30:01,584  main        INFO   c.a.c.a.Agent    Stop reason: 'signal'
2024-06-17 21:30:01,600  main        INFO   c.s.m.a.s.CI360Agent    Stopping event stream connection now
2024-06-17 21:30:01,602  main        INFO   o.e.j.w.c.WebSocketClient    Shutdown WebSocketClient@b8062627[coreClient=WebSocketCoreClient@2a5b3fee{STARTED},openSessions.size=2]
2024-06-17 21:30:01,603  5580595-19  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: null
2024-06-17 21:30:01,603  5580595-19  DEBUG  c.a.c.a.Agent    Stream closed
2024-06-17 21:30:01,603  5580595-19  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1006] Session Closed
2024-06-17 21:30:01,603  5580595-19  DEBUG  c.a.c.a.Agent    Stream closed
2024-06-17 21:30:01,609  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-17 21:30:01,609  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-17 21:30:01,740  5580595-32  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-17 21:30:02,611  main        INFO   c.a.c.a.Agent    Exit code: 0
