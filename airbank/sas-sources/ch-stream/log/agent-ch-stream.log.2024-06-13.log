2024-06-13 16:00:23,970  main        INFO   c.a.c.a.Agent    
2024-06-13 16:00:23,973  main        INFO   c.a.c.a.Agent    ----- starting -------------------------------------------------------
2024-06-13 16:00:23,978  main        INFO   c.a.c.a.Agent    App: /opt/agents/ch-stream/agent-ch-stream.jar
2024-06-13 16:00:23,981  main        INFO   c.a.c.a.Agent    Version: 1.0.0
2024-06-13 16:00:23,982  main        INFO   c.a.c.a.Agent    ----- configuration --------------------------------------------------
2024-06-13 16:00:23,985  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com:443
2024-06-13 16:00:23,986  main        INFO   c.a.c.c.Config    ci360.jwt=f0cb22506600016b1805ee8b
2024-06-13 16:00:23,986  main        INFO   c.a.c.c.Config    proxy.server=proxy.np.ab
2024-06-13 16:00:23,986  main        INFO   c.a.c.c.Config    proxy.port=3128
2024-06-13 16:00:23,987  main        INFO   c.a.c.c.Config    agent.max_threads=5000
2024-06-13 16:00:23,987  main        INFO   c.a.c.c.Config    kafka.topic=sentCampaingEmail topic
2024-06-13 16:00:23,987  main        INFO   c.a.c.a.Agent    test mode = false
2024-06-13 16:00:23,988  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com:443
2024-06-13 16:00:23,988  main        INFO   c.a.c.c.Config    proxy.server=proxy.np.ab
2024-06-13 16:00:23,988  main        INFO   c.a.c.c.Config    proxy.port=3128
2024-06-13 16:00:23,989  main        INFO   c.a.c.c.Config    db.user=APP_CAMPAIGN_CDM2
2024-06-13 16:00:23,989  main        INFO   c.a.c.c.Config    db.database=APP_CAMPAIGN_CDM2
2024-06-13 16:00:23,989  main        INFO   c.a.c.c.Config    db.server=dbs02.te00.np.ab:1621
2024-06-13 16:00:23,990  main        INFO   c.a.c.c.Config    db.service_name=MNTE00DW.np.ab
2024-06-13 16:00:23,992  main        INFO   c.a.c.c.Config    db.init_connection_pool_size=10
2024-06-13 16:00:23,992  main        INFO   c.a.c.c.Config    db.max_connection_pool_size=200
2024-06-13 16:00:24,810  main        INFO   c.a.c.c.Config    event.process_event_with_prefix=c_
2024-06-13 16:00:24,811  main        INFO   c.a.c.c.Config    event.direct_event=c_direct
2024-06-13 16:00:24,811  main        INFO   c.a.c.c.Config    event.ignore_events=c_activitystart,c_abtestpathassignment
2024-06-13 16:00:24,811  main        INFO   c.a.c.a.Agent    ----- eof configuration --------------------------------------------------
2024-06-13 16:00:24,814  main        INFO   c.s.m.a.s.CI360Agent    Maximum upstream queue size: 1000
2024-06-13 16:00:24,814  main        INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 16:00:24,868  main        INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 16:00:25,072  4404955-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:00:25,073  4404955-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:00:25,073  4404955-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 16:00:40,074  4404955-22  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 16:00:40,074  4404955-22  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 16:00:40,078  4404955-18  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:00:40,078  4404955-18  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:00:40,078  4404955-18  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 16:00:55,078  4404955-18  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 16:00:55,079  4404955-18  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 16:00:55,085  4404955-19  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:00:55,086  4404955-19  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:00:55,086  4404955-19  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 16:01:10,086  4404955-19  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 16:01:10,086  4404955-19  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 16:01:10,110  4404955-23  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:01:10,110  4404955-23  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:01:10,110  4404955-23  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 16:01:25,012  main        INFO   c.a.c.a.Agent    running
2024-06-13 16:01:25,111  4404955-23  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 16:01:25,111  4404955-23  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 16:01:25,117  4404955-21  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:01:25,117  4404955-21  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:01:25,117  4404955-21  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 16:01:31,013  main        INFO   c.a.c.a.Agent    Stop reason: 'signal'
2024-06-13 16:01:31,025  main        ERROR  c.a.c.a.Agent    Error when closing stream to cloud, null
java.lang.NullPointerException: null
	at cz.ab.ci360.common.Kafka.stop(Kafka.java:61)
	at cz.ab.ci360.agent_ch_stream.Agent.stop(Agent.java:212)
	at cz.ab.ci360.agent_ch_stream.Agent.main(Agent.java:190)
2024-06-13 16:01:31,025  main        INFO   c.a.c.a.Agent    Exit code: 1
2024-06-13 16:01:47,464  main        INFO   c.a.c.a.Agent    
2024-06-13 16:01:47,465  main        INFO   c.a.c.a.Agent    ----- starting -------------------------------------------------------
2024-06-13 16:01:47,470  main        INFO   c.a.c.a.Agent    App: /opt/agents/ch-stream/agent-ch-stream.jar
2024-06-13 16:01:47,476  main        INFO   c.a.c.a.Agent    Version: 1.0.0
2024-06-13 16:01:47,476  main        INFO   c.a.c.a.Agent    ----- configuration --------------------------------------------------
2024-06-13 16:01:47,479  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-13 16:01:47,480  main        INFO   c.a.c.c.Config    ci360.jwt=f0cb22506600016b1805ee8b
2024-06-13 16:01:47,480  main        INFO   c.a.c.c.Config    proxy.server=proxy.np.ab
2024-06-13 16:01:47,480  main        INFO   c.a.c.c.Config    proxy.port=3128
2024-06-13 16:01:47,481  main        INFO   c.a.c.c.Config    agent.max_threads=5000
2024-06-13 16:01:47,481  main        INFO   c.a.c.c.Config    kafka.topic=sentCampaingEmail topic
2024-06-13 16:01:47,481  main        INFO   c.a.c.a.Agent    test mode = false
2024-06-13 16:01:47,481  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-13 16:01:47,482  main        INFO   c.a.c.c.Config    proxy.server=proxy.np.ab
2024-06-13 16:01:47,482  main        INFO   c.a.c.c.Config    proxy.port=3128
2024-06-13 16:01:47,483  main        INFO   c.a.c.c.Config    db.user=APP_CAMPAIGN_CDM2
2024-06-13 16:01:47,483  main        INFO   c.a.c.c.Config    db.database=APP_CAMPAIGN_CDM2
2024-06-13 16:01:47,483  main        INFO   c.a.c.c.Config    db.server=dbs02.te00.np.ab:1621
2024-06-13 16:01:47,483  main        INFO   c.a.c.c.Config    db.service_name=MNTE00DW.np.ab
2024-06-13 16:01:47,483  main        INFO   c.a.c.c.Config    db.init_connection_pool_size=10
2024-06-13 16:01:47,483  main        INFO   c.a.c.c.Config    db.max_connection_pool_size=200
2024-06-13 16:01:48,177  main        INFO   c.a.c.c.Config    event.process_event_with_prefix=c_
2024-06-13 16:01:48,178  main        INFO   c.a.c.c.Config    event.direct_event=c_direct
2024-06-13 16:01:48,179  main        INFO   c.a.c.c.Config    event.ignore_events=c_activitystart,c_abtestpathassignment
2024-06-13 16:01:48,179  main        INFO   c.a.c.a.Agent    ----- eof configuration --------------------------------------------------
2024-06-13 16:01:48,185  main        INFO   c.s.m.a.s.CI360Agent    Maximum upstream queue size: 1000
2024-06-13 16:01:48,186  main        INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 16:01:48,240  main        INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 16:01:48,478  2582590-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:01:48,479  2582590-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:01:48,480  2582590-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 16:02:03,480  2582590-22  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 16:02:03,481  2582590-22  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 16:02:03,487  2582590-17  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:02:03,487  2582590-17  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:02:03,487  2582590-17  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 16:02:18,488  2582590-17  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 16:02:18,488  2582590-17  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 16:02:18,522  2582590-18  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:02:18,522  2582590-18  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:02:18,522  2582590-18  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 16:02:33,522  2582590-18  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 16:02:33,523  2582590-18  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 16:02:33,527  2582590-21  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:02:33,527  2582590-21  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:02:33,527  2582590-21  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 16:02:48,408  main        INFO   c.a.c.a.Agent    running
2024-06-13 16:02:48,527  2582590-21  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 16:02:48,527  2582590-21  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 16:02:48,535  2582590-20  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:02:48,535  2582590-20  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:02:48,535  2582590-20  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 16:03:03,536  2582590-20  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 16:03:03,536  2582590-20  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 16:03:03,541  2582590-20  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:03:03,541  2582590-20  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:03:03,541  2582590-20  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 16:03:18,541  2582590-20  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 16:03:18,541  2582590-20  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 16:03:18,564  2582590-20  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:03:18,564  2582590-20  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:03:18,564  2582590-20  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 16:03:33,564  2582590-20  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 16:03:33,565  2582590-20  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 16:03:33,569  2582590-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:03:33,569  2582590-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:03:33,569  2582590-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 16:03:48,419  main        INFO   c.a.c.a.Agent    running
2024-06-13 16:03:48,569  2582590-22  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 16:03:48,569  2582590-22  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 16:03:48,574  2582590-21  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:03:48,574  2582590-21  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:03:48,574  2582590-21  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 16:04:03,574  2582590-21  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 16:04:03,575  2582590-21  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 16:04:03,579  2582590-20  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:04:03,580  2582590-20  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:04:03,580  2582590-20  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 16:04:18,580  2582590-20  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 16:04:18,580  2582590-20  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 16:04:18,622  2582590-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:04:18,623  2582590-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:04:18,623  2582590-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 16:04:33,623  2582590-22  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 16:04:33,623  2582590-22  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 16:04:33,627  2582590-21  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:04:33,627  2582590-21  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:04:33,627  2582590-21  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 16:04:48,430  main        INFO   c.a.c.a.Agent    running
2024-06-13 16:04:48,628  2582590-21  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 16:04:48,628  2582590-21  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 16:04:48,633  2582590-20  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:04:48,633  2582590-20  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:04:48,633  2582590-20  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 16:05:03,633  2582590-20  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 16:05:03,633  2582590-20  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 16:05:03,637  2582590-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:05:03,637  2582590-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:05:03,637  2582590-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 16:05:18,637  2582590-22  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 16:05:18,637  2582590-22  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 16:05:18,649  2582590-21  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:05:18,649  2582590-21  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:05:18,649  2582590-21  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 16:05:33,649  2582590-21  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 16:05:33,649  2582590-21  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 16:05:33,653  2582590-20  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:05:33,653  2582590-20  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:05:33,653  2582590-20  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 16:05:48,441  main        INFO   c.a.c.a.Agent    running
2024-06-13 16:05:48,654  2582590-20  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 16:05:48,654  2582590-20  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 16:05:48,659  2582590-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:05:48,659  2582590-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:05:48,659  2582590-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 16:06:03,659  2582590-22  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 16:06:03,659  2582590-22  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 16:06:03,663  2582590-21  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:06:03,664  2582590-21  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:06:03,664  2582590-21  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 16:06:18,664  2582590-21  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 16:06:18,664  2582590-21  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 16:06:18,690  2582590-20  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:06:18,691  2582590-20  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:06:18,691  2582590-20  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 16:06:33,691  2582590-20  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 16:06:33,691  2582590-20  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 16:06:33,697  2582590-20  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:06:33,697  2582590-20  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:06:33,697  2582590-20  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 16:06:48,451  main        INFO   c.a.c.a.Agent    running
2024-06-13 16:06:48,697  2582590-20  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 16:06:48,698  2582590-20  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 16:06:48,702  2582590-21  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:06:48,703  2582590-21  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:06:48,703  2582590-21  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 16:07:03,703  2582590-21  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 16:07:03,703  2582590-21  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 16:07:03,707  2582590-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:07:03,707  2582590-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:07:03,707  2582590-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 16:07:18,707  2582590-22  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 16:07:18,708  2582590-22  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 16:07:18,730  2582590-20  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:07:18,730  2582590-20  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-13 16:07:18,730  2582590-20  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 16:07:20,456  main        INFO   c.a.c.a.Agent    Stop reason: 'signal'
2024-06-13 16:07:20,469  main        ERROR  c.a.c.a.Agent    Error when closing stream to cloud, null
java.lang.NullPointerException: null
	at cz.ab.ci360.common.Kafka.stop(Kafka.java:61)
	at cz.ab.ci360.agent_ch_stream.Agent.stop(Agent.java:212)
	at cz.ab.ci360.agent_ch_stream.Agent.main(Agent.java:190)
2024-06-13 16:07:20,469  main        INFO   c.a.c.a.Agent    Exit code: 1
2024-06-13 16:27:57,100  main        INFO   c.a.c.a.Agent    
2024-06-13 16:27:57,101  main        INFO   c.a.c.a.Agent    ----- starting -------------------------------------------------------
2024-06-13 16:27:57,106  main        INFO   c.a.c.a.Agent    App: /opt/agents/ch-stream/agent-ch-stream.jar
2024-06-13 16:27:57,109  main        INFO   c.a.c.a.Agent    Version: 1.0.0
2024-06-13 16:27:57,109  main        INFO   c.a.c.a.Agent    ----- configuration --------------------------------------------------
2024-06-13 16:27:57,112  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-13 16:27:57,113  main        INFO   c.a.c.c.Config    ci360.jwt=f0cb22506600016b1805ee8b
2024-06-13 16:27:57,113  main        INFO   c.a.c.c.Config    proxy.server=proxy.np.ab
2024-06-13 16:27:57,113  main        INFO   c.a.c.c.Config    proxy.port=3128
2024-06-13 16:27:57,113  main        INFO   c.a.c.c.Config    agent.max_threads=5000
2024-06-13 16:27:57,113  main        INFO   c.a.c.c.Config    kafka.topic=sentCampaingEmail topic
2024-06-13 16:27:57,113  main        INFO   c.a.c.a.Agent    test mode = false
2024-06-13 16:27:57,114  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-13 16:27:57,115  main        INFO   c.a.c.c.Config    proxy.server=proxy.np.ab
2024-06-13 16:27:57,115  main        INFO   c.a.c.c.Config    proxy.port=3128
2024-06-13 16:27:57,115  main        INFO   c.a.c.c.Config    db.user=APP_CAMPAIGN_CDM2
2024-06-13 16:27:57,115  main        INFO   c.a.c.c.Config    db.database=APP_CAMPAIGN_CDM2
2024-06-13 16:27:57,115  main        INFO   c.a.c.c.Config    db.server=dbs02.te00.np.ab:1621
2024-06-13 16:27:57,115  main        INFO   c.a.c.c.Config    db.service_name=MNTE00DW.np.ab
2024-06-13 16:27:57,115  main        INFO   c.a.c.c.Config    db.init_connection_pool_size=10
2024-06-13 16:27:57,115  main        INFO   c.a.c.c.Config    db.max_connection_pool_size=200
2024-06-13 16:27:57,879  main        INFO   c.a.c.c.Config    event.process_event_with_prefix=c_
2024-06-13 16:27:57,880  main        INFO   c.a.c.c.Config    event.direct_event=c_direct
2024-06-13 16:27:57,880  main        INFO   c.a.c.c.Config    event.ignore_events=c_activitystart,c_abtestpathassignment
2024-06-13 16:27:57,880  main        INFO   c.a.c.a.Agent    ----- eof configuration --------------------------------------------------
2024-06-13 16:27:57,883  main        INFO   c.s.m.a.s.CI360Agent    Maximum upstream queue size: 1000
2024-06-13 16:27:57,884  main        INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 16:27:57,937  main        INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 16:27:58,453  3634860-21  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-13 16:28:58,104  main        INFO   c.a.c.a.Agent    running
2024-06-13 16:29:08,468  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-13 16:29:08,469  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-13 16:29:08,469  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 16:29:23,469  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 16:29:23,470  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 16:29:23,474  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-13 16:29:23,475  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-13 16:29:23,476  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 16:29:23,608  3634860-23  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-13 16:29:38,476  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 16:29:38,480  cheduler-1  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$1.streamClosed(Agent.java:158)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$sendFrame$7(WebSocketCoreSession.java:519)
	at org.eclipse.jetty.util.Callback$3.succeeded(Callback.java:155)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.notifyCallbackSuccess(TransformingFlusher.java:197)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher$Flusher.process(TransformingFlusher.java:154)
	at org.eclipse.jetty.util.IteratingCallback.processing(IteratingCallback.java:243)
	at org.eclipse.jetty.util.IteratingCallback.iterate(IteratingCallback.java:224)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.sendFrame(TransformingFlusher.java:77)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.sendFrame(WebSocketCoreSession.java:522)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.close(WebSocketCoreSession.java:239)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.processHandlerError(WebSocketCoreSession.java:371)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onIdleExpired(WebSocketConnection.java:242)
	at org.eclipse.jetty.io.ssl.SslConnection.onIdleExpired(SslConnection.java:360)
	at org.eclipse.jetty.io.AbstractEndPoint.onIdleExpired(AbstractEndPoint.java:407)
	at org.eclipse.jetty.io.IdleTimeout.checkIdleTimeout(IdleTimeout.java:170)
	at org.eclipse.jetty.io.IdleTimeout.idleCheck(IdleTimeout.java:112)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-13 16:29:58,117  main        INFO   c.a.c.a.Agent    running
2024-06-13 16:30:33,610  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-13 16:30:33,611  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-13 16:30:33,611  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 16:30:37,124  main        INFO   c.a.c.a.Agent    Stop reason: 'signal'
2024-06-13 16:30:37,134  main        ERROR  c.a.c.a.Agent    Error when closing stream to cloud, null
java.lang.NullPointerException: null
	at cz.ab.ci360.common.Kafka.stop(Kafka.java:61)
	at cz.ab.ci360.agent_ch_stream.Agent.stop(Agent.java:212)
	at cz.ab.ci360.agent_ch_stream.Agent.main(Agent.java:190)
2024-06-13 16:30:37,134  main        INFO   c.a.c.a.Agent    Exit code: 1
2024-06-13 16:31:17,904  main        INFO   c.a.c.a.Agent    
2024-06-13 16:31:17,906  main        INFO   c.a.c.a.Agent    ----- starting -------------------------------------------------------
2024-06-13 16:31:17,911  main        INFO   c.a.c.a.Agent    App: /opt/agents/ch-stream/agent-ch-stream.jar
2024-06-13 16:31:17,915  main        INFO   c.a.c.a.Agent    Version: 1.0.0
2024-06-13 16:31:17,915  main        INFO   c.a.c.a.Agent    ----- configuration --------------------------------------------------
2024-06-13 16:31:17,918  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-13 16:31:17,919  main        INFO   c.a.c.c.Config    ci360.jwt=f0cb22506600016b1805ee8b
2024-06-13 16:31:17,919  main        INFO   c.a.c.c.Config    proxy.server=
2024-06-13 16:31:17,919  main        INFO   c.a.c.c.Config    proxy.port=0
2024-06-13 16:31:17,920  main        INFO   c.a.c.c.Config    agent.max_threads=5000
2024-06-13 16:31:17,920  main        INFO   c.a.c.c.Config    kafka.topic=sentCampaingEmail topic
2024-06-13 16:31:17,920  main        INFO   c.a.c.a.Agent    test mode = false
2024-06-13 16:31:17,920  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-13 16:31:17,921  main        INFO   c.a.c.c.Config    proxy.server=
2024-06-13 16:31:17,921  main        INFO   c.a.c.c.Config    proxy.port=0
2024-06-13 16:31:17,922  main        INFO   c.a.c.c.Config    db.user=APP_CAMPAIGN_CDM2
2024-06-13 16:31:17,922  main        INFO   c.a.c.c.Config    db.database=APP_CAMPAIGN_CDM2
2024-06-13 16:31:17,922  main        INFO   c.a.c.c.Config    db.server=dbs02.te00.np.ab:1621
2024-06-13 16:31:17,922  main        INFO   c.a.c.c.Config    db.service_name=MNTE00DW.np.ab
2024-06-13 16:31:17,922  main        INFO   c.a.c.c.Config    db.init_connection_pool_size=10
2024-06-13 16:31:17,922  main        INFO   c.a.c.c.Config    db.max_connection_pool_size=200
2024-06-13 16:31:18,631  main        INFO   c.a.c.c.Config    event.process_event_with_prefix=c_
2024-06-13 16:31:18,631  main        INFO   c.a.c.c.Config    event.direct_event=c_direct
2024-06-13 16:31:18,632  main        INFO   c.a.c.c.Config    event.ignore_events=c_activitystart,c_abtestpathassignment
2024-06-13 16:31:18,633  main        INFO   c.a.c.a.Agent    ----- eof configuration --------------------------------------------------
2024-06-13 16:31:18,636  main        INFO   c.s.m.a.s.CI360Agent    Maximum upstream queue size: 1000
2024-06-13 16:31:18,636  main        INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 16:31:18,686  main        INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 16:31:19,141  2582590-21  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-13 16:32:18,845  main        INFO   c.a.c.a.Agent    running
2024-06-13 16:32:29,154  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-13 16:32:29,155  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-13 16:32:29,156  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 16:32:44,156  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 16:32:44,156  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 16:32:44,162  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-13 16:32:44,164  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-13 16:32:44,164  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 16:32:44,289  2582590-22  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-13 16:32:59,165  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 16:32:59,168  cheduler-1  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$1.streamClosed(Agent.java:158)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$sendFrame$7(WebSocketCoreSession.java:519)
	at org.eclipse.jetty.util.Callback$3.succeeded(Callback.java:155)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.notifyCallbackSuccess(TransformingFlusher.java:197)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher$Flusher.process(TransformingFlusher.java:154)
	at org.eclipse.jetty.util.IteratingCallback.processing(IteratingCallback.java:243)
	at org.eclipse.jetty.util.IteratingCallback.iterate(IteratingCallback.java:224)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.sendFrame(TransformingFlusher.java:77)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.sendFrame(WebSocketCoreSession.java:522)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.close(WebSocketCoreSession.java:239)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.processHandlerError(WebSocketCoreSession.java:371)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onIdleExpired(WebSocketConnection.java:242)
	at org.eclipse.jetty.io.ssl.SslConnection.onIdleExpired(SslConnection.java:360)
	at org.eclipse.jetty.io.AbstractEndPoint.onIdleExpired(AbstractEndPoint.java:407)
	at org.eclipse.jetty.io.IdleTimeout.checkIdleTimeout(IdleTimeout.java:170)
	at org.eclipse.jetty.io.IdleTimeout.idleCheck(IdleTimeout.java:112)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-13 16:33:18,856  main        INFO   c.a.c.a.Agent    running
2024-06-13 16:33:54,293  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-13 16:33:54,293  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-13 16:33:54,293  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 16:34:09,293  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 16:34:09,293  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 16:34:09,294  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-13 16:34:09,295  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-13 16:34:09,296  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 16:34:09,431  2582590-23  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-13 16:34:18,868  main        INFO   c.a.c.a.Agent    running
2024-06-13 16:34:24,296  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 16:34:24,297  cheduler-1  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$1.streamClosed(Agent.java:158)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$sendFrame$7(WebSocketCoreSession.java:519)
	at org.eclipse.jetty.util.Callback$3.succeeded(Callback.java:155)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.notifyCallbackSuccess(TransformingFlusher.java:197)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher$Flusher.process(TransformingFlusher.java:154)
	at org.eclipse.jetty.util.IteratingCallback.processing(IteratingCallback.java:243)
	at org.eclipse.jetty.util.IteratingCallback.iterate(IteratingCallback.java:224)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.sendFrame(TransformingFlusher.java:77)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.sendFrame(WebSocketCoreSession.java:522)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.close(WebSocketCoreSession.java:239)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.processHandlerError(WebSocketCoreSession.java:371)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onIdleExpired(WebSocketConnection.java:242)
	at org.eclipse.jetty.io.ssl.SslConnection.onIdleExpired(SslConnection.java:360)
	at org.eclipse.jetty.io.AbstractEndPoint.onIdleExpired(AbstractEndPoint.java:407)
	at org.eclipse.jetty.io.IdleTimeout.checkIdleTimeout(IdleTimeout.java:170)
	at org.eclipse.jetty.io.IdleTimeout.idleCheck(IdleTimeout.java:112)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-13 16:35:18,879  main        INFO   c.a.c.a.Agent    running
2024-06-13 16:35:19,435  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-13 16:35:19,435  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-13 16:35:19,435  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 16:35:34,435  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 16:35:34,435  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 16:35:34,436  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-13 16:35:34,436  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-13 16:35:34,436  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 16:35:34,575  2582590-20  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-13 16:35:49,436  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 16:35:49,437  cheduler-1  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$1.streamClosed(Agent.java:158)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$sendFrame$7(WebSocketCoreSession.java:519)
	at org.eclipse.jetty.util.Callback$3.succeeded(Callback.java:155)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.notifyCallbackSuccess(TransformingFlusher.java:197)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher$Flusher.process(TransformingFlusher.java:154)
	at org.eclipse.jetty.util.IteratingCallback.processing(IteratingCallback.java:243)
	at org.eclipse.jetty.util.IteratingCallback.iterate(IteratingCallback.java:224)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.sendFrame(TransformingFlusher.java:77)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.sendFrame(WebSocketCoreSession.java:522)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.close(WebSocketCoreSession.java:239)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.processHandlerError(WebSocketCoreSession.java:371)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onIdleExpired(WebSocketConnection.java:242)
	at org.eclipse.jetty.io.ssl.SslConnection.onIdleExpired(SslConnection.java:360)
	at org.eclipse.jetty.io.AbstractEndPoint.onIdleExpired(AbstractEndPoint.java:407)
	at org.eclipse.jetty.io.IdleTimeout.checkIdleTimeout(IdleTimeout.java:170)
	at org.eclipse.jetty.io.IdleTimeout.idleCheck(IdleTimeout.java:112)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-13 16:36:18,890  main        INFO   c.a.c.a.Agent    running
2024-06-13 16:36:44,579  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-13 16:36:44,579  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-13 16:36:44,579  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 16:36:59,579  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 16:36:59,579  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 16:36:59,580  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-13 16:36:59,580  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-13 16:36:59,580  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 16:36:59,722  2582590-18  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-13 16:37:14,580  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 16:37:14,581  cheduler-1  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$1.streamClosed(Agent.java:158)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$sendFrame$7(WebSocketCoreSession.java:519)
	at org.eclipse.jetty.util.Callback$3.succeeded(Callback.java:155)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.notifyCallbackSuccess(TransformingFlusher.java:197)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher$Flusher.process(TransformingFlusher.java:154)
	at org.eclipse.jetty.util.IteratingCallback.processing(IteratingCallback.java:243)
	at org.eclipse.jetty.util.IteratingCallback.iterate(IteratingCallback.java:224)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.sendFrame(TransformingFlusher.java:77)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.sendFrame(WebSocketCoreSession.java:522)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.close(WebSocketCoreSession.java:239)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.processHandlerError(WebSocketCoreSession.java:371)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onIdleExpired(WebSocketConnection.java:242)
	at org.eclipse.jetty.io.ssl.SslConnection.onIdleExpired(SslConnection.java:360)
	at org.eclipse.jetty.io.AbstractEndPoint.onIdleExpired(AbstractEndPoint.java:407)
	at org.eclipse.jetty.io.IdleTimeout.checkIdleTimeout(IdleTimeout.java:170)
	at org.eclipse.jetty.io.IdleTimeout.idleCheck(IdleTimeout.java:112)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-13 16:37:18,900  main        INFO   c.a.c.a.Agent    running
2024-06-13 16:38:09,725  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-13 16:38:09,725  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-13 16:38:09,725  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 16:38:18,910  main        INFO   c.a.c.a.Agent    running
2024-06-13 16:38:24,726  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 16:38:24,726  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 16:38:24,726  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-13 16:38:24,727  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-13 16:38:24,727  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 16:38:24,854  2582590-19  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-13 16:38:27,911  main        INFO   c.a.c.a.Agent    Stop reason: 'signal'
2024-06-13 16:38:27,921  main        ERROR  c.a.c.a.Agent    Error when closing stream to cloud, null
java.lang.NullPointerException: null
	at cz.ab.ci360.common.Kafka.stop(Kafka.java:61)
	at cz.ab.ci360.agent_ch_stream.Agent.stop(Agent.java:212)
	at cz.ab.ci360.agent_ch_stream.Agent.main(Agent.java:190)
2024-06-13 16:38:27,921  main        INFO   c.a.c.a.Agent    Exit code: 1
2024-06-13 17:32:24,896  main        INFO   c.a.c.a.Agent    
2024-06-13 17:32:24,898  main        INFO   c.a.c.a.Agent    ----- starting -------------------------------------------------------
2024-06-13 17:32:24,903  main        INFO   c.a.c.a.Agent    App: /opt/agents/ch-stream/agent-ch-stream.jar
2024-06-13 17:32:24,905  main        INFO   c.a.c.a.Agent    Version: 1.0.0
2024-06-13 17:32:24,905  main        INFO   c.a.c.a.Agent    ----- configuration --------------------------------------------------
2024-06-13 17:32:24,908  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-13 17:32:24,909  main        INFO   c.a.c.c.Config    ci360.jwt=f0cb22506600016b1805ee8b
2024-06-13 17:32:24,910  main        INFO   c.a.c.c.Config    proxy.server=
2024-06-13 17:32:24,910  main        INFO   c.a.c.c.Config    proxy.port=0
2024-06-13 17:32:24,910  main        INFO   c.a.c.c.Config    agent.max_threads=5000
2024-06-13 17:32:24,910  main        INFO   c.a.c.c.Config    kafka.topic=sentCampaingEmail topic
2024-06-13 17:32:24,910  main        INFO   c.a.c.a.Agent    test mode = false
2024-06-13 17:32:24,910  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-13 17:32:24,911  main        INFO   c.a.c.c.Config    proxy.server=
2024-06-13 17:32:24,911  main        INFO   c.a.c.c.Config    proxy.port=0
2024-06-13 17:32:24,912  main        INFO   c.a.c.c.Config    db.user=APP_CAMPAIGN_CDM2
2024-06-13 17:32:24,912  main        INFO   c.a.c.c.Config    db.database=APP_CAMPAIGN_CDM2
2024-06-13 17:32:24,912  main        INFO   c.a.c.c.Config    db.server=dbs02.te00.np.ab:1621
2024-06-13 17:32:24,912  main        INFO   c.a.c.c.Config    db.service_name=MNTE00DW.np.ab
2024-06-13 17:32:24,912  main        INFO   c.a.c.c.Config    db.init_connection_pool_size=10
2024-06-13 17:32:24,912  main        INFO   c.a.c.c.Config    db.max_connection_pool_size=200
2024-06-13 17:32:25,574  main        INFO   c.a.c.c.Config    event.process_event_with_prefix=c_
2024-06-13 17:32:25,574  main        INFO   c.a.c.c.Config    event.direct_event=c_direct
2024-06-13 17:32:25,575  main        INFO   c.a.c.c.Config    event.ignore_events=c_activitystart,c_abtestpathassignment
2024-06-13 17:32:25,575  main        INFO   c.a.c.a.Agent    ----- eof configuration --------------------------------------------------
2024-06-13 17:32:25,578  main        INFO   c.s.m.a.s.CI360Agent    Maximum upstream queue size: 1000
2024-06-13 17:32:25,578  main        INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 17:32:25,627  main        INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 17:32:40,777  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connect Timeout
2024-06-13 17:32:40,778  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connect Timeout
2024-06-13 17:32:40,779  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 17:32:55,779  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 17:32:55,779  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 17:33:10,783  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connect Timeout
2024-06-13 17:33:10,784  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connect Timeout
2024-06-13 17:33:10,784  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 17:33:25,768  main        INFO   c.a.c.a.Agent    running
2024-06-13 17:33:25,784  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 17:33:25,784  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 17:33:40,837  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connect Timeout
2024-06-13 17:33:40,837  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connect Timeout
2024-06-13 17:33:40,837  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 17:33:47,773  main        INFO   c.a.c.a.Agent    Stop reason: 'signal'
2024-06-13 17:33:47,783  main        ERROR  c.a.c.a.Agent    Error when closing stream to cloud, null
java.lang.NullPointerException: null
	at cz.ab.ci360.common.Kafka.stop(Kafka.java:61)
	at cz.ab.ci360.agent_ch_stream.Agent.stop(Agent.java:212)
	at cz.ab.ci360.agent_ch_stream.Agent.main(Agent.java:190)
2024-06-13 17:33:47,784  main        INFO   c.a.c.a.Agent    Exit code: 1
2024-06-13 17:34:38,602  main        INFO   c.a.c.a.Agent    
2024-06-13 17:34:38,604  main        INFO   c.a.c.a.Agent    ----- starting -------------------------------------------------------
2024-06-13 17:34:38,609  main        INFO   c.a.c.a.Agent    App: /opt/agents/ch-stream/agent-ch-stream.jar
2024-06-13 17:34:38,612  main        INFO   c.a.c.a.Agent    Version: 1.0.0
2024-06-13 17:34:38,612  main        INFO   c.a.c.a.Agent    ----- configuration --------------------------------------------------
2024-06-13 17:34:38,615  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-13 17:34:38,616  main        INFO   c.a.c.c.Config    ci360.jwt=f0cb22506600016b1805ee8b
2024-06-13 17:34:38,617  main        INFO   c.a.c.c.Config    proxy.server=
2024-06-13 17:34:38,617  main        INFO   c.a.c.c.Config    proxy.port=0
2024-06-13 17:34:38,617  main        INFO   c.a.c.c.Config    agent.max_threads=5000
2024-06-13 17:34:38,617  main        INFO   c.a.c.c.Config    kafka.topic=sentCampaingEmail topic
2024-06-13 17:34:38,617  main        INFO   c.a.c.a.Agent    test mode = false
2024-06-13 17:34:38,618  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-13 17:34:38,619  main        INFO   c.a.c.c.Config    proxy.server=
2024-06-13 17:34:38,619  main        INFO   c.a.c.c.Config    proxy.port=0
2024-06-13 17:34:38,619  main        INFO   c.a.c.c.Config    db.user=APP_CAMPAIGN_CDM2
2024-06-13 17:34:38,619  main        INFO   c.a.c.c.Config    db.database=APP_CAMPAIGN_CDM2
2024-06-13 17:34:38,619  main        INFO   c.a.c.c.Config    db.server=dbs02.te00.np.ab:1621
2024-06-13 17:34:38,619  main        INFO   c.a.c.c.Config    db.service_name=MNTE00DW.np.ab
2024-06-13 17:34:38,619  main        INFO   c.a.c.c.Config    db.init_connection_pool_size=10
2024-06-13 17:34:38,619  main        INFO   c.a.c.c.Config    db.max_connection_pool_size=200
2024-06-13 17:34:39,290  main        INFO   c.a.c.c.Config    event.process_event_with_prefix=c_
2024-06-13 17:34:39,291  main        INFO   c.a.c.c.Config    event.direct_event=c_direct
2024-06-13 17:34:39,291  main        INFO   c.a.c.c.Config    event.ignore_events=c_activitystart,c_abtestpathassignment
2024-06-13 17:34:39,291  main        INFO   c.a.c.a.Agent    ----- eof configuration --------------------------------------------------
2024-06-13 17:34:39,295  main        INFO   c.s.m.a.s.CI360Agent    Maximum upstream queue size: 1000
2024-06-13 17:34:39,295  main        INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 17:34:39,343  main        INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 17:34:54,508  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connect Timeout
2024-06-13 17:34:54,509  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connect Timeout
2024-06-13 17:34:54,510  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 17:35:09,510  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 17:35:09,510  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 17:35:24,515  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connect Timeout
2024-06-13 17:35:24,515  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connect Timeout
2024-06-13 17:35:24,515  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 17:35:39,480  main        INFO   c.a.c.a.Agent    running
2024-06-13 17:35:39,515  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 17:35:39,515  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 17:35:54,549  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connect Timeout
2024-06-13 17:35:54,549  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connect Timeout
2024-06-13 17:35:54,549  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 17:36:09,549  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 17:36:09,549  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 17:36:24,553  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connect Timeout
2024-06-13 17:36:24,553  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connect Timeout
2024-06-13 17:36:24,553  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 17:36:39,493  main        INFO   c.a.c.a.Agent    running
2024-06-13 17:36:39,553  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 17:36:39,554  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 17:36:54,574  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connect Timeout
2024-06-13 17:36:54,574  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connect Timeout
2024-06-13 17:36:54,575  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 17:37:09,575  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 17:37:09,575  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 17:37:24,594  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connect Timeout
2024-06-13 17:37:24,594  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connect Timeout
2024-06-13 17:37:24,594  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 17:37:39,504  main        INFO   c.a.c.a.Agent    running
2024-06-13 17:37:39,594  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 17:37:39,594  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 17:37:54,598  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connect Timeout
2024-06-13 17:37:54,598  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connect Timeout
2024-06-13 17:37:54,598  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 17:38:09,598  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 17:38:09,599  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 17:38:24,613  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connect Timeout
2024-06-13 17:38:24,613  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connect Timeout
2024-06-13 17:38:24,613  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 17:38:39,515  main        INFO   c.a.c.a.Agent    running
2024-06-13 17:38:39,613  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 17:38:39,614  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 17:38:54,635  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connect Timeout
2024-06-13 17:38:54,635  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connect Timeout
2024-06-13 17:38:54,635  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 17:39:09,636  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 17:39:09,636  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 17:39:24,677  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connect Timeout
2024-06-13 17:39:24,677  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connect Timeout
2024-06-13 17:39:24,677  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 17:39:39,526  main        INFO   c.a.c.a.Agent    running
2024-06-13 17:39:39,677  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 17:39:39,677  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 17:39:54,681  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connect Timeout
2024-06-13 17:39:54,681  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connect Timeout
2024-06-13 17:39:54,681  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 17:40:09,681  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 17:40:09,682  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 17:40:24,705  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connect Timeout
2024-06-13 17:40:24,705  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connect Timeout
2024-06-13 17:40:24,705  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 17:40:39,536  main        INFO   c.a.c.a.Agent    running
2024-06-13 17:40:39,705  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 17:40:39,706  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 17:40:54,709  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connect Timeout
2024-06-13 17:40:54,709  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connect Timeout
2024-06-13 17:40:54,709  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 17:41:09,710  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 17:41:09,710  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 17:41:24,731  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connect Timeout
2024-06-13 17:41:24,731  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connect Timeout
2024-06-13 17:41:24,731  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 17:41:39,546  main        INFO   c.a.c.a.Agent    running
2024-06-13 17:41:39,731  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 17:41:39,732  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 17:41:54,735  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connect Timeout
2024-06-13 17:41:54,735  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connect Timeout
2024-06-13 17:41:54,735  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 17:42:09,735  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 17:42:09,736  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 17:42:24,757  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connect Timeout
2024-06-13 17:42:24,757  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connect Timeout
2024-06-13 17:42:24,757  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 17:42:39,554  main        INFO   c.a.c.a.Agent    running
2024-06-13 17:42:39,757  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 17:42:39,758  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 17:42:54,762  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connect Timeout
2024-06-13 17:42:54,762  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connect Timeout
2024-06-13 17:42:54,762  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 17:43:09,762  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 17:43:09,762  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 17:43:24,766  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connect Timeout
2024-06-13 17:43:24,766  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connect Timeout
2024-06-13 17:43:24,766  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 17:43:39,563  main        INFO   c.a.c.a.Agent    running
2024-06-13 17:43:39,766  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 17:43:39,766  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 17:43:54,769  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connect Timeout
2024-06-13 17:43:54,770  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connect Timeout
2024-06-13 17:43:54,770  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 17:44:09,770  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 17:44:09,770  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 17:44:24,795  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connect Timeout
2024-06-13 17:44:24,795  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connect Timeout
2024-06-13 17:44:24,795  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 17:44:39,571  main        INFO   c.a.c.a.Agent    running
2024-06-13 17:44:39,795  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 17:44:39,796  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 17:44:54,803  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connect Timeout
2024-06-13 17:44:54,803  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connect Timeout
2024-06-13 17:44:54,803  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 17:45:09,803  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 17:45:09,803  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 17:45:24,828  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connect Timeout
2024-06-13 17:45:24,829  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connect Timeout
2024-06-13 17:45:24,829  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 17:45:39,580  main        INFO   c.a.c.a.Agent    running
2024-06-13 17:45:39,829  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 17:45:39,829  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 17:45:54,832  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connect Timeout
2024-06-13 17:45:54,833  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connect Timeout
2024-06-13 17:45:54,833  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 17:46:09,833  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 17:46:09,833  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 17:46:24,862  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connect Timeout
2024-06-13 17:46:24,862  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connect Timeout
2024-06-13 17:46:24,862  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 17:46:39,589  main        INFO   c.a.c.a.Agent    running
2024-06-13 17:46:39,862  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 17:46:39,862  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 17:46:54,865  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connect Timeout
2024-06-13 17:46:54,866  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connect Timeout
2024-06-13 17:46:54,866  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 17:47:09,866  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 17:47:09,866  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 17:47:24,874  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connect Timeout
2024-06-13 17:47:24,874  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connect Timeout
2024-06-13 17:47:24,874  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 17:47:39,598  main        INFO   c.a.c.a.Agent    running
2024-06-13 17:47:39,875  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 17:47:39,875  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 17:47:54,878  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connect Timeout
2024-06-13 17:47:54,878  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connect Timeout
2024-06-13 17:47:54,878  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 17:48:09,878  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 17:48:09,878  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 17:48:24,906  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connect Timeout
2024-06-13 17:48:24,906  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connect Timeout
2024-06-13 17:48:24,906  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 17:48:39,605  main        INFO   c.a.c.a.Agent    running
2024-06-13 17:48:39,906  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 17:48:39,907  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 17:48:54,910  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connect Timeout
2024-06-13 17:48:54,910  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connect Timeout
2024-06-13 17:48:54,910  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 17:49:09,910  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 17:49:09,910  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 17:49:24,945  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connect Timeout
2024-06-13 17:49:24,946  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connect Timeout
2024-06-13 17:49:24,946  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 17:49:39,613  main        INFO   c.a.c.a.Agent    running
2024-06-13 17:49:39,946  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 17:49:39,946  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 17:49:54,949  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connect Timeout
2024-06-13 17:49:54,949  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connect Timeout
2024-06-13 17:49:54,950  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 17:50:09,950  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 17:50:09,950  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 17:50:24,975  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connect Timeout
2024-06-13 17:50:24,975  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connect Timeout
2024-06-13 17:50:24,975  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 17:50:39,621  main        INFO   c.a.c.a.Agent    running
2024-06-13 17:50:39,975  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 17:50:39,976  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 17:50:54,979  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connect Timeout
2024-06-13 17:50:54,979  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connect Timeout
2024-06-13 17:50:54,979  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 17:51:09,979  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 17:51:09,979  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 17:51:25,000  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connect Timeout
2024-06-13 17:51:25,000  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connect Timeout
2024-06-13 17:51:25,000  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 17:51:39,629  main        INFO   c.a.c.a.Agent    running
2024-06-13 17:51:40,000  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 17:51:40,000  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 17:51:55,004  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connect Timeout
2024-06-13 17:51:55,004  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connect Timeout
2024-06-13 17:51:55,004  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 17:52:10,004  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 17:52:10,005  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 17:52:25,026  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connect Timeout
2024-06-13 17:52:25,027  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connect Timeout
2024-06-13 17:52:25,027  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 17:52:39,637  main        INFO   c.a.c.a.Agent    running
2024-06-13 17:52:40,027  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 17:52:40,027  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 17:52:55,030  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connect Timeout
2024-06-13 17:52:55,031  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connect Timeout
2024-06-13 17:52:55,031  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 17:53:10,031  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 17:53:10,031  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 17:53:25,055  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connect Timeout
2024-06-13 17:53:25,055  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connect Timeout
2024-06-13 17:53:25,055  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 17:53:39,646  main        INFO   c.a.c.a.Agent    running
2024-06-13 17:53:40,055  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 17:53:40,055  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-13 17:53:55,059  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connect Timeout
2024-06-13 17:53:55,059  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connect Timeout
2024-06-13 17:53:55,059  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-13 17:54:08,650  main        INFO   c.a.c.a.Agent    Stop reason: 'signal'
2024-06-13 17:54:08,661  main        ERROR  c.a.c.a.Agent    Error when closing stream to cloud, null
java.lang.NullPointerException: null
	at cz.ab.ci360.common.Kafka.stop(Kafka.java:61)
	at cz.ab.ci360.agent_ch_stream.Agent.stop(Agent.java:212)
	at cz.ab.ci360.agent_ch_stream.Agent.main(Agent.java:190)
2024-06-13 17:54:08,661  main        INFO   c.a.c.a.Agent    Exit code: 1
2024-06-13 17:54:10,059  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-13 17:54:10,059  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
