2024-06-21 00:00:00,077  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:01:00,087  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:02:00,095  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:03:00,102  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:04:00,109  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:05:00,117  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:06:00,126  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:07:00,133  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:08:00,140  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:09:00,148  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:10:00,167  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:11:00,174  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:12:00,181  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:13:00,188  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:14:00,196  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:15:00,203  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:16:00,210  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:17:00,217  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:18:00,224  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:19:00,232  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:20:00,239  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:21:00,246  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:22:00,253  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:23:00,261  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:24:00,268  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:25:00,275  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:26:00,283  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:27:00,290  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:28:00,298  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:29:00,305  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:30:00,312  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:31:00,320  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:32:00,327  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:33:00,334  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:34:00,341  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:35:00,348  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:36:00,356  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:37:00,363  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:38:00,370  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:39:00,377  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:40:00,385  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:41:00,392  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:42:00,399  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:43:00,406  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:44:00,412  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:45:00,418  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:46:00,424  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:47:00,431  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:48:00,437  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:49:00,443  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:50:00,449  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:51:00,456  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:52:00,462  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:53:00,468  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:54:00,475  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:55:00,481  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:56:00,487  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:57:00,494  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:58:00,501  main        INFO   c.a.c.a.Agent    running
2024-06-21 00:59:00,508  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:00:00,514  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:01:00,520  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:02:00,527  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:03:00,533  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:04:00,540  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:05:00,547  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:06:00,554  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:07:00,561  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:08:00,567  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:09:00,574  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:10:00,580  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:11:00,587  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:12:00,593  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:13:00,600  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:14:00,607  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:15:00,613  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:16:00,620  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:17:00,627  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:18:00,633  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:19:00,639  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:20:00,645  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:21:00,652  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:22:00,659  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:23:00,665  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:24:00,672  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:25:00,679  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:26:00,686  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:27:00,693  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:28:00,699  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:29:00,705  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:30:00,712  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:31:00,719  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:32:00,725  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:33:00,731  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:34:00,737  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:35:00,744  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:36:00,750  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:37:00,757  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:38:00,764  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:39:00,770  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:40:00,777  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:41:00,783  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:42:00,790  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:43:00,797  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:44:00,803  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:45:00,809  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:46:00,816  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:47:00,823  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:48:00,830  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:49:00,836  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:50:00,842  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:51:00,848  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:52:00,854  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:53:00,861  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:54:00,867  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:55:00,874  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:56:00,881  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:57:00,887  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:58:00,894  main        INFO   c.a.c.a.Agent    running
2024-06-21 01:59:00,900  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:00:00,907  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:01:00,913  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:02:00,920  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:03:00,926  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:04:00,932  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:05:00,938  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:06:00,944  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:07:00,950  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:08:00,955  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:09:00,961  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:10:00,967  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:11:00,973  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:12:00,978  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:13:00,984  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:14:00,990  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:15:00,996  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:16:01,002  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:17:01,008  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:18:01,014  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:19:01,022  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:20:01,028  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:21:01,035  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:22:01,041  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:23:01,048  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:24:01,055  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:25:01,061  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:26:01,069  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:27:01,075  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:28:01,082  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:29:01,089  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:30:01,096  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:31:01,103  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:32:01,109  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:33:01,115  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:34:01,122  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:35:01,128  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:36:01,134  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:37:01,141  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:38:01,148  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:39:01,154  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:40:01,160  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:41:01,167  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:42:01,173  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:43:01,180  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:44:01,187  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:45:01,193  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:46:01,200  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:47:01,207  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:48:01,214  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:49:01,221  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:50:01,228  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:51:01,234  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:52:01,241  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:53:01,247  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:54:01,253  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:55:01,260  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:56:01,267  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:57:01,274  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:58:01,281  main        INFO   c.a.c.a.Agent    running
2024-06-21 02:59:01,288  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:00:01,295  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:01:01,302  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:02:01,310  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:03:01,318  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:04:01,324  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:05:01,330  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:06:01,335  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:07:01,342  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:08:01,348  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:09:01,354  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:10:01,360  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:11:01,367  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:12:01,374  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:13:01,381  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:14:01,388  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:15:01,396  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:16:01,403  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:17:01,410  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:18:01,417  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:19:01,424  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:20:01,431  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:21:01,437  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:22:01,444  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:23:01,451  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:24:01,458  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:25:01,465  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:26:01,471  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:27:01,477  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:28:01,484  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:29:01,492  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:30:01,499  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:31:01,506  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:32:01,512  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:33:01,519  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:34:01,525  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:35:01,531  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:36:01,537  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:37:01,544  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:38:01,551  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:39:01,557  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:40:01,564  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:41:01,570  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:42:01,576  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:43:01,583  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:44:01,590  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:45:01,596  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:46:01,603  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:47:01,610  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:48:01,617  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:49:01,625  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:50:01,632  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:51:01,639  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:52:01,645  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:53:01,651  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:54:01,658  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:55:01,664  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:56:01,671  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:57:01,678  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:58:01,684  main        INFO   c.a.c.a.Agent    running
2024-06-21 03:59:01,691  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:00:01,698  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:01:01,704  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:02:01,710  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:03:01,716  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:04:01,722  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:05:01,729  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:06:01,735  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:07:01,742  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:08:01,749  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:09:01,755  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:10:01,761  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:11:01,767  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:12:01,774  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:13:01,780  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:14:01,786  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:15:01,793  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:16:01,800  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:17:01,807  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:18:01,814  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:19:01,820  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:20:01,827  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:21:01,833  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:22:01,839  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:23:01,846  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:24:01,853  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:25:01,859  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:26:01,866  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:27:01,873  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:28:01,880  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:29:01,888  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:30:01,894  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:31:01,901  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:32:01,907  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:33:01,914  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:34:01,921  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:35:01,927  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:36:01,933  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:37:01,939  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:38:01,946  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:39:01,952  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:40:01,959  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:41:01,966  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:42:01,972  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:43:01,979  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:44:01,985  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:45:01,992  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:46:02,000  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:47:02,006  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:48:02,013  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:49:02,020  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:50:02,026  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:51:02,033  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:52:02,040  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:53:02,047  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:54:02,054  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:55:02,061  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:56:02,068  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:57:02,075  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:58:02,082  main        INFO   c.a.c.a.Agent    running
2024-06-21 04:59:02,089  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:00:02,096  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:01:02,103  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:02:02,109  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:03:02,116  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:04:02,123  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:05:02,130  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:06:02,136  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:07:02,142  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:08:02,149  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:09:02,155  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:10:02,162  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:11:02,170  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:12:02,177  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:13:02,184  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:14:02,191  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:15:02,198  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:16:02,205  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:17:02,211  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:18:02,217  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:19:02,223  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:20:02,229  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:21:02,234  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:22:02,240  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:23:02,247  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:24:02,254  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:25:02,261  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:26:02,268  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:27:02,275  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:28:02,282  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:29:02,289  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:30:02,296  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:31:02,304  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:32:02,312  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:33:02,319  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:34:02,326  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:35:02,333  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:36:02,340  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:37:02,347  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:38:02,353  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:39:02,360  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:40:02,367  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:41:02,373  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:42:02,379  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:43:02,386  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:44:02,393  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:45:02,400  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:46:02,407  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:47:02,414  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:48:02,421  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:49:02,428  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:50:02,435  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:51:02,441  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:52:02,448  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:53:02,455  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:54:02,461  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:55:02,468  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:56:02,474  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:57:02,480  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:58:02,490  main        INFO   c.a.c.a.Agent    running
2024-06-21 05:59:02,497  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:00:02,504  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:01:02,511  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:02:02,517  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:03:02,523  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:04:02,529  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:05:02,536  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:06:02,542  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:07:02,547  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:08:02,553  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:09:02,559  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:10:02,567  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:11:02,573  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:12:02,579  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:13:02,586  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:14:02,591  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:15:02,598  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:16:02,604  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:17:02,610  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:18:02,617  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:19:02,624  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:20:02,630  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:21:02,637  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:22:02,644  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:23:02,650  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:24:02,658  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:25:02,665  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:26:02,672  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:27:02,678  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:28:02,686  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:29:02,692  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:30:02,700  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:31:02,707  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:32:02,715  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:33:02,722  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:34:02,728  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:35:02,735  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:36:02,742  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:37:02,748  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:38:02,756  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:39:02,762  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:40:02,769  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:41:02,776  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:42:02,783  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:43:02,790  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:44:02,796  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:45:02,803  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:46:02,810  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:47:02,817  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:48:02,824  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:49:02,831  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:50:02,838  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:51:02,844  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:52:02,851  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:53:02,858  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:54:02,864  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:55:02,871  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:56:02,877  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:57:02,884  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:58:02,891  main        INFO   c.a.c.a.Agent    running
2024-06-21 06:59:02,897  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:00:02,903  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:01:02,910  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:02:02,916  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:03:02,922  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:04:02,929  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:05:02,934  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:06:02,941  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:07:02,948  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:08:02,955  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:09:02,962  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:10:02,968  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:11:02,974  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:12:02,981  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:13:02,987  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:14:02,994  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:15:03,000  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:16:03,007  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:17:03,013  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:18:03,020  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:19:03,026  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:20:03,032  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:21:03,039  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:22:03,046  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:23:03,052  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:24:03,059  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:25:03,066  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:26:03,073  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:27:03,079  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:28:03,086  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:29:03,092  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:30:03,098  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:31:03,105  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:32:03,112  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:33:03,119  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:34:03,126  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:35:03,132  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:36:03,138  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:37:03,144  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:38:03,152  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:39:03,159  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:40:03,165  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:41:03,172  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:42:03,178  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:43:03,185  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:44:03,192  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:45:03,198  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:46:03,204  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:47:03,210  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:48:03,217  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:49:03,224  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:50:03,232  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:51:03,238  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:52:03,245  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:53:03,251  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:54:03,258  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:55:03,264  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:56:03,270  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:57:03,277  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:58:03,283  main        INFO   c.a.c.a.Agent    running
2024-06-21 07:59:03,290  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:00:03,296  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:01:03,303  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:02:03,309  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:03:03,315  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:04:03,320  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:05:03,326  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:06:03,332  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:07:03,338  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:08:03,344  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:09:03,350  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:10:03,355  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:11:03,361  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:12:03,367  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:13:03,373  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:14:03,379  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:15:03,384  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:16:03,390  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:17:03,397  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:18:03,403  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:19:03,410  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:20:03,416  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:21:03,423  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:22:03,429  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:23:03,437  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:24:03,443  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:25:03,450  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:26:03,457  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:27:03,465  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:28:03,471  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:29:03,478  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:30:03,484  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:31:03,495  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:32:03,502  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:33:03,508  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:34:03,514  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:35:03,521  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:36:03,527  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:37:03,533  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:38:03,540  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:39:03,546  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:40:03,552  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:41:03,559  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:42:03,565  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:43:03,573  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:44:03,579  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:45:03,586  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:46:03,593  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:47:03,600  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:48:03,607  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:49:03,613  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:50:03,620  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:51:03,627  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:52:03,633  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:53:03,639  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:54:03,646  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:55:03,652  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:56:03,659  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:57:03,665  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:58:03,675  main        INFO   c.a.c.a.Agent    running
2024-06-21 08:59:03,682  main        INFO   c.a.c.a.Agent    running
2024-06-21 09:00:03,688  main        INFO   c.a.c.a.Agent    running
2024-06-21 09:01:03,695  main        INFO   c.a.c.a.Agent    running
2024-06-21 09:02:03,702  main        INFO   c.a.c.a.Agent    running
2024-06-21 09:03:03,709  main        INFO   c.a.c.a.Agent    running
2024-06-21 09:03:34,713  main        INFO   c.a.c.a.Agent    Stop reason: 'signal'
2024-06-21 09:03:34,714  main        ERROR  c.a.c.a.Agent    Error when closing stream to cloud, null
java.lang.NullPointerException: null
	at cz.ab.ci360.common.SqlConnectionPool.stop(SqlConnectionPool.java:102)
	at cz.ab.ci360.agent_ch_stream.Agent.stop(Agent.java:232)
	at cz.ab.ci360.agent_ch_stream.Agent.main(Agent.java:212)
2024-06-21 09:03:34,714  main        INFO   c.a.c.a.Agent    Exit code: 1
2024-06-21 09:05:06,892  main        INFO   c.a.c.a.Agent    
2024-06-21 09:05:06,894  main        INFO   c.a.c.a.Agent    ----- starting -------------------------------------------------------
2024-06-21 09:05:06,898  main        INFO   c.a.c.a.Agent    App: /opt/agents/ch-stream/agent-ch-stream.jar
2024-06-21 09:05:06,901  main        INFO   c.a.c.a.Agent    Version: 1.0.0
2024-06-21 09:05:06,901  main        INFO   c.a.c.a.Agent    ----- configuration --------------------------------------------------
2024-06-21 09:05:06,904  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-21 09:05:06,905  main        INFO   c.a.c.c.Config    ci360.tenantID=f0cb22506600016b1805ee8b
2024-06-21 09:05:06,906  main        INFO   c.a.c.c.Config    agent.max_threads=5000
2024-06-21 09:05:06,906  main        INFO   c.a.c.c.Config    kafka.topic=sentCampaingEmail
2024-06-21 09:05:06,906  main        INFO   c.a.c.c.Config    agent.kafka=false
2024-06-21 09:05:06,906  main        INFO   c.a.c.c.Config    proxy.server=proxy.np.ab
2024-06-21 09:05:06,906  main        INFO   c.a.c.c.Config    proxy.port=3128
2024-06-21 09:05:06,907  main        INFO   c.a.c.c.Config    proxy.realm=proxy
2024-06-21 09:05:06,907  main        INFO   c.a.c.c.Config    proxy.user=sascloud_user
2024-06-21 09:05:06,907  main        INFO   c.a.c.c.Config    proxy.non_proxy_hosts=app-sas-test.np.ab
2024-06-21 09:05:06,907  main        INFO   c.a.c.a.Agent    test mode = false
2024-06-21 09:05:06,907  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-21 09:05:06,908  main        INFO   c.a.c.c.Config    db.user=APP_CAMPAIGN_CDM2
2024-06-21 09:05:06,908  main        INFO   c.a.c.c.Config    db.database=APP_CAMPAIGN_CDM2
2024-06-21 09:05:06,908  main        INFO   c.a.c.c.Config    db.server=DBS01.DE11.NP.AB:1621
2024-06-21 09:05:06,908  main        INFO   c.a.c.c.Config    db.service_name=MNDE11DW.NP.AB
2024-06-21 09:05:06,908  main        INFO   c.a.c.c.Config    db.init_connection_pool_size=10
2024-06-21 09:05:06,908  main        INFO   c.a.c.c.Config    db.max_connection_pool_size=200
2024-06-21 09:05:07,678  main        INFO   c.a.c.c.Config    event.process_event_with_prefix=c_
2024-06-21 09:05:07,679  main        INFO   c.a.c.c.Config    event.direct_event=c_direct
2024-06-21 09:05:07,679  main        INFO   c.a.c.c.Config    event.ignore_events=c_activitystart,c_abtestpathassignment
2024-06-21 09:05:07,679  main        INFO   c.a.c.a.Agent    ----- eof configuration --------------------------------------------------
2024-06-21 09:05:07,683  main        INFO   c.s.m.a.s.CI360Agent    Maximum upstream queue size: 1000
2024-06-21 09:05:07,684  main        INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-21 09:05:07,740  main        INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-21 09:05:08,417  8557915-21  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-21 09:05:08,482  8557915-22  INFO   c.s.m.a.s.StreamWebSocket    Agent version: v2402
2024-06-21 09:05:08,498  8557915-22  INFO   c.s.m.a.s.StreamWebSocket    Gateway version: 1.0.125
2024-06-21 09:05:08,498  8557915-22  INFO   c.s.m.a.s.StreamWebSocket    Gateway supported versions: v2406,v2405,v2404,v2403,v2402,v2401,v2312
2024-06-21 09:05:08,498  8557915-22  INFO   c.s.m.a.s.StreamWebSocket    Gateway warning versions: v2311,v2310,v2309
2024-06-21 09:05:21,343  2-thread-1  INFO   c.a.c.a.Event    Event received, event: c_DCH, will be processed...
2024-06-21 09:05:21,344  2-thread-1  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"463860","externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"463860","session":"463860","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"subject_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"448e1dd2-6e51-3023-a56e-ba36e24be8af","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"463860","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","Text":"Test CH","message_id":"0","sessionId":"463860","event_category":"unifiedAndEngage","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"448e1dd2-6e51-3023-a56e-ba36e24be8af","guid":"dd1e824f-05b7-4c4c-a610-53d716eac66b","applicationId":"SAS360Proxy","customer_id":"33a1c5ca4227efd37dfd2b0bf6f645ffd189c07d1912be75ebad71e9988add2f453ae735584c8c9fbb3ad32b0850a22c","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"dd1e824f-05b7-4c4c-a610-53d716eac66b"}
2024-06-21 09:05:21,467  2-thread-1  ERROR  c.a.c.c.Http    Response, Code: 407 , Message: 
2024-06-21 09:05:21,467  2-thread-1  DEBUG  c.a.c.a.CI360Api    Task API json: 
2024-06-21 09:05:21,469  2-thread-1  ERROR  c.a.c.a.CI360Api    Exception()
org.json.JSONException: A JSONObject text must begin with '{' at 0 [character 1 line 1]
	at org.json.JSONTokener.syntaxError(JSONTokener.java:503)
	at org.json.JSONObject.<init>(JSONObject.java:213)
	at org.json.JSONObject.<init>(JSONObject.java:430)
	at cz.ab.ci360.agent_ch_stream.CI360Api.getTaskCustomProperties(CI360Api.java:43)
	at cz.ab.ci360.cache.TaskCache.getCI360ApiTaskCp(TaskCache.java:92)
	at cz.ab.ci360.cache.TaskCache.getTask(TaskCache.java:68)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:99)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-21 09:05:21,488  2-thread-1  ERROR  c.a.c.c.TaskCache    Exception()
java.sql.BatchUpdateException: ORA-01950: no privileges on tablespace 'USERS'

	at oracle.jdbc.driver.OraclePreparedStatement.executeLargeBatch(OraclePreparedStatement.java:9711)
	at oracle.jdbc.driver.T4CPreparedStatement.executeLargeBatch(T4CPreparedStatement.java:1447)
	at oracle.jdbc.driver.OraclePreparedStatement.executeBatch(OraclePreparedStatement.java:9487)
	at oracle.jdbc.driver.OracleStatementWrapper.executeBatch(OracleStatementWrapper.java:237)
	at cz.ab.ci360.cache.TaskCache.saveTaskCpToCache(TaskCache.java:210)
	at cz.ab.ci360.cache.TaskCache.getTask(TaskCache.java:72)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:99)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-21 09:05:21,497  2-thread-1  ERROR  c.a.c.c.Http    Response, Code: 407 , Message: 
2024-06-21 09:05:21,497  2-thread-1  DEBUG  c.a.c.a.CI360Api    Creative API json: 
2024-06-21 09:05:21,497  2-thread-1  ERROR  c.a.c.a.CI360Api    Exception()
org.json.JSONException: A JSONObject text must begin with '{' at 0 [character 1 line 1]
	at org.json.JSONTokener.syntaxError(JSONTokener.java:503)
	at org.json.JSONObject.<init>(JSONObject.java:213)
	at org.json.JSONObject.<init>(JSONObject.java:430)
	at cz.ab.ci360.agent_ch_stream.CI360Api.getCreativeAttributes(CI360Api.java:84)
	at cz.ab.ci360.cache.CreativeCache.getCI360ApiCreativeCp(CreativeCache.java:91)
	at cz.ab.ci360.cache.CreativeCache.getCreative(CreativeCache.java:67)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:100)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-21 09:05:21,510  2-thread-1  ERROR  c.a.c.c.CreativeCache    Exception()
java.sql.BatchUpdateException: ORA-01950: no privileges on tablespace 'USERS'

	at oracle.jdbc.driver.OraclePreparedStatement.executeLargeBatch(OraclePreparedStatement.java:9711)
	at oracle.jdbc.driver.T4CPreparedStatement.executeLargeBatch(T4CPreparedStatement.java:1447)
	at oracle.jdbc.driver.OraclePreparedStatement.executeBatch(OraclePreparedStatement.java:9487)
	at oracle.jdbc.driver.OracleStatementWrapper.executeBatch(OracleStatementWrapper.java:237)
	at cz.ab.ci360.cache.CreativeCache.saveCreativeCpToCache(CreativeCache.java:167)
	at cz.ab.ci360.cache.CreativeCache.getCreative(CreativeCache.java:71)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:100)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-21 09:05:21,512  2-thread-1  ERROR  c.a.c.c.CampaignCache    Exception()
java.sql.SQLSyntaxErrorException: ORA-00942: table or view does not exist

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForDescribe(T4CPreparedStatement.java:807)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:983)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1168)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.executeQuery(OraclePreparedStatement.java:3713)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1167)
	at cz.ab.ci360.cache.CampaignCache.loadDatabaseCampaignCp(CampaignCache.java:85)
	at cz.ab.ci360.cache.CampaignCache.getCamapign(CampaignCache.java:53)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:101)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-00942: table or view does not exist

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 23 common frames omitted
2024-06-21 09:05:21,515  2-thread-1  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = 448e1dd2-6e51-3023-a56e-ba36e24be8af, UPDATED_DTTM = 2024-06-21 09:05:21.514, CREATIVE_VERSION_ID = , CONTROL_GROUP_FLG = , IDENTITY_ID = 448e1dd2-6e51-3023-a56e-ba36e24be8af, SPOT_ID = , LEAD_ID = 448e1dd2-6e51-3023-a56e-ba36e24be8af_06b9ab19-a082-4bc9-9858-47595f992ca8_*************, CREATIVE_ID = , CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = XNA, CONTACT_POLICY_PRODUCT_CODE = XNA, SUBJECT_ID = 463860, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = 0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG, CAMP_SUBTYPE_CODE = XNA, CONTACT_POLICY_TYPE_CODE = XNA, COMM_TYPE_CODE = XNA, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = 33a1c5ca4227efd37dfd2b0bf6f645ffd189c07d1912be75ebad71e9988add2f453ae735584c8c9fbb3ad32b0850a22c, CONTACT_DTTM = 2024-06-21 09:05:21.098, CONTACT_DT = 2024-06-21 00:00:00.0, RTC_ID = 06b9ab19-a082-4bc9-9858-47595f992ca8, CAMPAIGN_MESSAGE_CD = -1, AUD_OCCURENCE_ID = , CONTACT_ID = dd1e824f-05b7-4c4c-a610-53d716eac66b, EMAIL_IMPRINT_URL = , SESSION_ID = 463860, TASK_ID = ********-4dff-43c7-92a6-5a74243beb01, CONTACT_CHANNEL_CD = XNA, INSERTED_DTTM = 2024-06-21 09:05:21.514, 
2024-06-21 09:05:21,516  2-thread-1  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-21 09:05:21,526  2-thread-1  ERROR  c.a.c.a.Event    ErrorCode:CH_02 - DB - not possible to write payload to DB
java.sql.SQLSyntaxErrorException: ORA-01950: no privileges on tablespace 'USERS'

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForRows(T4CPreparedStatement.java:970)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1205)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.execute(OraclePreparedStatement.java:3778)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.execute(OraclePreparedStatementWrapper.java:1081)
	at cz.ab.ci360.agent_ch_stream.Event.saveToDb(Event.java:397)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:116)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-01950: no privileges on tablespace 'USERS'

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 21 common frames omitted
2024-06-21 09:05:21,526  2-thread-1  ERROR  c.a.c.a.Event    Error when processing event.
java.sql.SQLSyntaxErrorException: ORA-01950: no privileges on tablespace 'USERS'

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForRows(T4CPreparedStatement.java:970)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1205)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.execute(OraclePreparedStatement.java:3778)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.execute(OraclePreparedStatementWrapper.java:1081)
	at cz.ab.ci360.agent_ch_stream.Event.saveToDb(Event.java:397)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:116)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-01950: no privileges on tablespace 'USERS'

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 21 common frames omitted
2024-06-21 09:05:21,526  2-thread-1  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"463860","externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"463860","session":"463860","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"subject_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"448e1dd2-6e51-3023-a56e-ba36e24be8af","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"463860","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","Text":"Test CH","message_id":"0","sessionId":"463860","event_category":"unifiedAndEngage","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"448e1dd2-6e51-3023-a56e-ba36e24be8af","guid":"dd1e824f-05b7-4c4c-a610-53d716eac66b","applicationId":"SAS360Proxy","customer_id":"33a1c5ca4227efd37dfd2b0bf6f645ffd189c07d1912be75ebad71e9988add2f453ae735584c8c9fbb3ad32b0850a22c","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"dd1e824f-05b7-4c4c-a610-53d716eac66b"}
2024-06-21 09:06:07,888  main        INFO   c.a.c.a.Agent    running
2024-06-21 09:07:07,900  main        INFO   c.a.c.a.Agent    running
2024-06-21 09:08:07,912  main        INFO   c.a.c.a.Agent    running
2024-06-21 09:09:07,923  main        INFO   c.a.c.a.Agent    running
2024-06-21 09:10:07,934  main        INFO   c.a.c.a.Agent    running
2024-06-21 09:11:07,943  main        INFO   c.a.c.a.Agent    running
2024-06-21 09:12:07,952  main        INFO   c.a.c.a.Agent    running
2024-06-21 09:13:07,960  main        INFO   c.a.c.a.Agent    running
2024-06-21 09:14:07,968  main        INFO   c.a.c.a.Agent    running
2024-06-21 09:15:07,976  main        INFO   c.a.c.a.Agent    running
2024-06-21 09:16:07,984  main        INFO   c.a.c.a.Agent    running
2024-06-21 09:17:07,993  main        INFO   c.a.c.a.Agent    running
2024-06-21 09:18:08,000  main        INFO   c.a.c.a.Agent    running
2024-06-21 09:19:08,008  main        INFO   c.a.c.a.Agent    running
2024-06-21 09:20:08,016  main        INFO   c.a.c.a.Agent    running
2024-06-21 09:20:12,017  main        INFO   c.a.c.a.Agent    Stop reason: 'signal'
2024-06-21 09:20:12,027  main        INFO   c.s.m.a.s.CI360Agent    Stopping event stream connection now
2024-06-21 09:20:12,028  main        INFO   o.e.j.w.c.WebSocketClient    Shutdown WebSocketClient@c1a2a8ff[coreClient=WebSocketCoreClient@2a5b3fee{STARTED},openSessions.size=1]
2024-06-21 09:20:12,032  8557915-21  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: null
2024-06-21 09:20:12,032  8557915-21  DEBUG  c.a.c.a.Agent    Stream closed
2024-06-21 09:20:12,037  8557915-21  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1006] Session Closed
2024-06-21 09:20:12,038  8557915-21  DEBUG  c.a.c.a.Agent    Stream closed
2024-06-21 09:20:13,040  main        INFO   c.a.c.a.Agent    Exit code: 0
2024-06-21 09:39:51,693  main        INFO   c.a.c.a.Agent    
2024-06-21 09:39:51,694  main        INFO   c.a.c.a.Agent    ----- starting -------------------------------------------------------
2024-06-21 09:39:51,699  main        INFO   c.a.c.a.Agent    App: /opt/agents/ch-stream/agent-ch-stream.jar
2024-06-21 09:39:51,702  main        INFO   c.a.c.a.Agent    Version: 1.0.0
2024-06-21 09:39:51,702  main        INFO   c.a.c.a.Agent    ----- configuration --------------------------------------------------
2024-06-21 09:39:51,705  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-21 09:39:51,706  main        INFO   c.a.c.c.Config    ci360.tenantID=f0cb22506600016b1805ee8b
2024-06-21 09:39:51,707  main        INFO   c.a.c.c.Config    agent.max_threads=5000
2024-06-21 09:39:51,707  main        INFO   c.a.c.c.Config    kafka.topic=sentCampaingEmail
2024-06-21 09:39:51,707  main        INFO   c.a.c.c.Config    agent.kafka=false
2024-06-21 09:39:51,707  main        INFO   c.a.c.c.Config    proxy.server=proxy.np.ab
2024-06-21 09:39:51,707  main        INFO   c.a.c.c.Config    proxy.port=3128
2024-06-21 09:39:51,707  main        INFO   c.a.c.c.Config    proxy.realm=proxy
2024-06-21 09:39:51,707  main        INFO   c.a.c.c.Config    proxy.user=sascloud_user
2024-06-21 09:39:51,707  main        INFO   c.a.c.c.Config    proxy.non_proxy_hosts=app-sas-test.np.ab
2024-06-21 09:39:51,708  main        INFO   c.a.c.a.Agent    test mode = false
2024-06-21 09:39:51,708  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-21 09:39:51,708  main        INFO   c.a.c.c.Config    db.user=APP_CAMPAIGN_CDM2
2024-06-21 09:39:51,708  main        INFO   c.a.c.c.Config    db.database=APP_CAMPAIGN_CDM2
2024-06-21 09:39:51,708  main        INFO   c.a.c.c.Config    db.server=DBS01.DE11.NP.AB:1621
2024-06-21 09:39:51,708  main        INFO   c.a.c.c.Config    db.service_name=MNDE11DW.NP.AB
2024-06-21 09:39:51,708  main        INFO   c.a.c.c.Config    db.init_connection_pool_size=10
2024-06-21 09:39:51,709  main        INFO   c.a.c.c.Config    db.max_connection_pool_size=200
2024-06-21 09:39:52,394  main        INFO   c.a.c.c.Config    event.process_event_with_prefix=c_
2024-06-21 09:39:52,395  main        INFO   c.a.c.c.Config    event.direct_event=c_direct
2024-06-21 09:39:52,395  main        INFO   c.a.c.c.Config    event.ignore_events=c_activitystart,c_abtestpathassignment
2024-06-21 09:39:52,395  main        INFO   c.a.c.a.Agent    ----- eof configuration --------------------------------------------------
2024-06-21 09:39:52,398  main        INFO   c.s.m.a.s.CI360Agent    Maximum upstream queue size: 1000
2024-06-21 09:39:52,399  main        INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-21 09:39:52,449  main        INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-21 09:39:53,060  8094269-23  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-21 09:39:53,153  8094269-22  INFO   c.s.m.a.s.StreamWebSocket    Agent version: v2402
2024-06-21 09:39:53,167  8094269-22  INFO   c.s.m.a.s.StreamWebSocket    Gateway version: 1.0.125
2024-06-21 09:39:53,167  8094269-22  INFO   c.s.m.a.s.StreamWebSocket    Gateway supported versions: v2406,v2405,v2404,v2403,v2402,v2401,v2312
2024-06-21 09:39:53,167  8094269-22  INFO   c.s.m.a.s.StreamWebSocket    Gateway warning versions: v2311,v2310,v2309
2024-06-21 09:40:05,619  2-thread-1  INFO   c.a.c.a.Event    Event received, event: c_DCH, will be processed...
2024-06-21 09:40:05,620  2-thread-1  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"463860","externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"463860","session":"463860","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"subject_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"448e1dd2-6e51-3023-a56e-ba36e24be8af","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"463860","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","Text":"Test CH","message_id":"0","sessionId":"463860","event_category":"unifiedAndEngage","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"448e1dd2-6e51-3023-a56e-ba36e24be8af","guid":"4d1a781c-a838-4673-88bd-4f782397d78a","applicationId":"SAS360Proxy","customer_id":"33a1c5ca4227efd37dfd2b0bf6f645ffd189c07d1912be75ebad71e9988add2f453ae735584c8c9fbb3ad32b0850a22c","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"4d1a781c-a838-4673-88bd-4f782397d78a"}
2024-06-21 09:40:05,735  2-thread-1  ERROR  c.a.c.c.Http    Response, Code: 407 , Message: 
2024-06-21 09:40:05,735  2-thread-1  DEBUG  c.a.c.a.CI360Api    Task API json: 
2024-06-21 09:40:05,745  2-thread-1  ERROR  c.a.c.a.CI360Api    Exception()
org.json.JSONException: A JSONObject text must begin with '{' at 0 [character 1 line 1]
	at org.json.JSONTokener.syntaxError(JSONTokener.java:503)
	at org.json.JSONObject.<init>(JSONObject.java:213)
	at org.json.JSONObject.<init>(JSONObject.java:430)
	at cz.ab.ci360.agent_ch_stream.CI360Api.getTaskCustomProperties(CI360Api.java:43)
	at cz.ab.ci360.cache.TaskCache.getCI360ApiTaskCp(TaskCache.java:92)
	at cz.ab.ci360.cache.TaskCache.getTask(TaskCache.java:68)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:99)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-21 09:40:05,763  2-thread-1  ERROR  c.a.c.c.TaskCache    Exception()
java.sql.BatchUpdateException: ORA-00001: unique constraint (APP_CAMPAIGN_CDM2.SYS_C009559860) violated

	at oracle.jdbc.driver.OraclePreparedStatement.executeLargeBatch(OraclePreparedStatement.java:9711)
	at oracle.jdbc.driver.T4CPreparedStatement.executeLargeBatch(T4CPreparedStatement.java:1447)
	at oracle.jdbc.driver.OraclePreparedStatement.executeBatch(OraclePreparedStatement.java:9487)
	at oracle.jdbc.driver.OracleStatementWrapper.executeBatch(OracleStatementWrapper.java:237)
	at cz.ab.ci360.cache.TaskCache.saveTaskCpToCache(TaskCache.java:210)
	at cz.ab.ci360.cache.TaskCache.getTask(TaskCache.java:72)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:99)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-21 09:40:05,767  2-thread-1  ERROR  c.a.c.c.Http    Response, Code: 407 , Message: 
2024-06-21 09:40:05,768  2-thread-1  DEBUG  c.a.c.a.CI360Api    Creative API json: 
2024-06-21 09:40:05,768  2-thread-1  ERROR  c.a.c.a.CI360Api    Exception()
org.json.JSONException: A JSONObject text must begin with '{' at 0 [character 1 line 1]
	at org.json.JSONTokener.syntaxError(JSONTokener.java:503)
	at org.json.JSONObject.<init>(JSONObject.java:213)
	at org.json.JSONObject.<init>(JSONObject.java:430)
	at cz.ab.ci360.agent_ch_stream.CI360Api.getCreativeAttributes(CI360Api.java:84)
	at cz.ab.ci360.cache.CreativeCache.getCI360ApiCreativeCp(CreativeCache.java:91)
	at cz.ab.ci360.cache.CreativeCache.getCreative(CreativeCache.java:67)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:100)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-21 09:40:05,776  2-thread-1  ERROR  c.a.c.c.CreativeCache    Exception()
java.sql.BatchUpdateException: ORA-01950: no privileges on tablespace 'USERS'

	at oracle.jdbc.driver.OraclePreparedStatement.executeLargeBatch(OraclePreparedStatement.java:9711)
	at oracle.jdbc.driver.T4CPreparedStatement.executeLargeBatch(T4CPreparedStatement.java:1447)
	at oracle.jdbc.driver.OraclePreparedStatement.executeBatch(OraclePreparedStatement.java:9487)
	at oracle.jdbc.driver.OracleStatementWrapper.executeBatch(OracleStatementWrapper.java:237)
	at cz.ab.ci360.cache.CreativeCache.saveCreativeCpToCache(CreativeCache.java:167)
	at cz.ab.ci360.cache.CreativeCache.getCreative(CreativeCache.java:71)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:100)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-21 09:40:05,779  2-thread-1  ERROR  c.a.c.c.CampaignCache    Exception()
java.sql.SQLSyntaxErrorException: ORA-00942: table or view does not exist

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForDescribe(T4CPreparedStatement.java:807)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:983)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1168)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.executeQuery(OraclePreparedStatement.java:3713)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1167)
	at cz.ab.ci360.cache.CampaignCache.loadDatabaseCampaignCp(CampaignCache.java:85)
	at cz.ab.ci360.cache.CampaignCache.getCamapign(CampaignCache.java:53)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:101)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-00942: table or view does not exist

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 23 common frames omitted
2024-06-21 09:40:05,782  2-thread-1  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = 448e1dd2-6e51-3023-a56e-ba36e24be8af, UPDATED_DTTM = 2024-06-21 09:40:05.781, CREATIVE_VERSION_ID = , CONTROL_GROUP_FLG = , IDENTITY_ID = 448e1dd2-6e51-3023-a56e-ba36e24be8af, SPOT_ID = , LEAD_ID = 448e1dd2-6e51-3023-a56e-ba36e24be8af_06b9ab19-a082-4bc9-9858-47595f992ca8_*************, CREATIVE_ID = , CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = XNA, CONTACT_POLICY_PRODUCT_CODE = XNA, SUBJECT_ID = 463860, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = 0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG, CAMP_SUBTYPE_CODE = XNA, CONTACT_POLICY_TYPE_CODE = XNA, COMM_TYPE_CODE = XNA, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = 33a1c5ca4227efd37dfd2b0bf6f645ffd189c07d1912be75ebad71e9988add2f453ae735584c8c9fbb3ad32b0850a22c, CONTACT_DTTM = 2024-06-21 09:40:05.43, CONTACT_DT = 2024-06-21 00:00:00.0, RTC_ID = 06b9ab19-a082-4bc9-9858-47595f992ca8, CAMPAIGN_MESSAGE_CD = -1, AUD_OCCURENCE_ID = , CONTACT_ID = 4d1a781c-a838-4673-88bd-4f782397d78a, EMAIL_IMPRINT_URL = , SESSION_ID = 463860, TASK_ID = ********-4dff-43c7-92a6-5a74243beb01, CONTACT_CHANNEL_CD = XNA, INSERTED_DTTM = 2024-06-21 09:40:05.781, 
2024-06-21 09:40:05,782  2-thread-1  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-21 09:40:05,792  2-thread-1  ERROR  c.a.c.a.Event    ErrorCode:CH_02 - DB - not possible to write payload to DB
java.sql.SQLSyntaxErrorException: ORA-01950: no privileges on tablespace 'USERS'

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForRows(T4CPreparedStatement.java:970)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1205)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.execute(OraclePreparedStatement.java:3778)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.execute(OraclePreparedStatementWrapper.java:1081)
	at cz.ab.ci360.agent_ch_stream.Event.saveToDb(Event.java:397)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:116)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-01950: no privileges on tablespace 'USERS'

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 21 common frames omitted
2024-06-21 09:40:05,792  2-thread-1  ERROR  c.a.c.a.Event    Error when processing event.
java.sql.SQLSyntaxErrorException: ORA-01950: no privileges on tablespace 'USERS'

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForRows(T4CPreparedStatement.java:970)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1205)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.execute(OraclePreparedStatement.java:3778)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.execute(OraclePreparedStatementWrapper.java:1081)
	at cz.ab.ci360.agent_ch_stream.Event.saveToDb(Event.java:397)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:116)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-01950: no privileges on tablespace 'USERS'

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 21 common frames omitted
2024-06-21 09:40:05,792  2-thread-1  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"463860","externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"463860","session":"463860","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"subject_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"448e1dd2-6e51-3023-a56e-ba36e24be8af","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"463860","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","Text":"Test CH","message_id":"0","sessionId":"463860","event_category":"unifiedAndEngage","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"448e1dd2-6e51-3023-a56e-ba36e24be8af","guid":"4d1a781c-a838-4673-88bd-4f782397d78a","applicationId":"SAS360Proxy","customer_id":"33a1c5ca4227efd37dfd2b0bf6f645ffd189c07d1912be75ebad71e9988add2f453ae735584c8c9fbb3ad32b0850a22c","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"4d1a781c-a838-4673-88bd-4f782397d78a"}
2024-06-21 09:40:52,608  main        INFO   c.a.c.a.Agent    running
2024-06-21 09:41:52,618  main        INFO   c.a.c.a.Agent    running
2024-06-21 09:42:52,629  main        INFO   c.a.c.a.Agent    running
2024-06-21 09:43:52,640  main        INFO   c.a.c.a.Agent    running
2024-06-21 09:44:52,650  main        INFO   c.a.c.a.Agent    running
2024-06-21 09:45:52,660  main        INFO   c.a.c.a.Agent    running
2024-06-21 09:46:52,669  main        INFO   c.a.c.a.Agent    running
2024-06-21 09:47:52,679  main        INFO   c.a.c.a.Agent    running
2024-06-21 09:48:52,688  main        INFO   c.a.c.a.Agent    running
2024-06-21 09:49:52,697  main        INFO   c.a.c.a.Agent    running
2024-06-21 09:50:52,706  main        INFO   c.a.c.a.Agent    running
2024-06-21 09:51:52,715  main        INFO   c.a.c.a.Agent    running
2024-06-21 09:52:21,719  main        INFO   c.a.c.a.Agent    Stop reason: 'signal'
2024-06-21 09:52:21,729  main        INFO   c.s.m.a.s.CI360Agent    Stopping event stream connection now
2024-06-21 09:52:21,731  main        INFO   o.e.j.w.c.WebSocketClient    Shutdown WebSocketClient@ffb97772[coreClient=WebSocketCoreClient@c2db68f{STARTED},openSessions.size=1]
2024-06-21 09:52:21,733  8094269-20  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: null
2024-06-21 09:52:21,734  8094269-20  DEBUG  c.a.c.a.Agent    Stream closed
2024-06-21 09:52:21,739  8094269-20  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1006] Session Closed
2024-06-21 09:52:21,741  8094269-20  DEBUG  c.a.c.a.Agent    Stream closed
2024-06-21 09:52:22,742  main        INFO   c.a.c.a.Agent    Exit code: 0
2024-06-21 09:52:29,170  main        INFO   c.a.c.a.Agent    
2024-06-21 09:52:29,171  main        INFO   c.a.c.a.Agent    ----- starting -------------------------------------------------------
2024-06-21 09:52:29,176  main        INFO   c.a.c.a.Agent    App: /opt/agents/ch-stream/agent-ch-stream.jar
2024-06-21 09:52:29,178  main        INFO   c.a.c.a.Agent    Version: 1.0.0
2024-06-21 09:52:29,178  main        INFO   c.a.c.a.Agent    ----- configuration --------------------------------------------------
2024-06-21 09:52:29,181  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-21 09:52:29,182  main        INFO   c.a.c.c.Config    ci360.tenantID=f0cb22506600016b1805ee8b
2024-06-21 09:52:29,183  main        INFO   c.a.c.c.Config    agent.max_threads=5000
2024-06-21 09:52:29,183  main        INFO   c.a.c.c.Config    kafka.topic=sentCampaingEmail
2024-06-21 09:52:29,183  main        INFO   c.a.c.c.Config    agent.kafka=false
2024-06-21 09:52:29,184  main        INFO   c.a.c.c.Config    proxy.server=proxy.np.ab
2024-06-21 09:52:29,184  main        INFO   c.a.c.c.Config    proxy.port=3128
2024-06-21 09:52:29,184  main        INFO   c.a.c.c.Config    proxy.realm=proxy
2024-06-21 09:52:29,184  main        INFO   c.a.c.c.Config    proxy.user=sascloud_user
2024-06-21 09:52:29,184  main        INFO   c.a.c.c.Config    proxy.non_proxy_hosts=app-sas-test.np.ab
2024-06-21 09:52:29,184  main        INFO   c.a.c.a.Agent    test mode = false
2024-06-21 09:52:29,184  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-21 09:52:29,185  main        INFO   c.a.c.c.Config    db.user=APP_CAMPAIGN_CDM2
2024-06-21 09:52:29,185  main        INFO   c.a.c.c.Config    db.database=APP_CAMPAIGN_CDM2
2024-06-21 09:52:29,185  main        INFO   c.a.c.c.Config    db.server=DBS01.DE11.NP.AB:1621
2024-06-21 09:52:29,185  main        INFO   c.a.c.c.Config    db.service_name=MNDE11DW.NP.AB
2024-06-21 09:52:29,185  main        INFO   c.a.c.c.Config    db.init_connection_pool_size=10
2024-06-21 09:52:29,185  main        INFO   c.a.c.c.Config    db.max_connection_pool_size=200
2024-06-21 09:52:29,844  main        INFO   c.a.c.c.Config    event.process_event_with_prefix=c_
2024-06-21 09:52:29,845  main        INFO   c.a.c.c.Config    event.direct_event=c_direct
2024-06-21 09:52:29,845  main        INFO   c.a.c.c.Config    event.ignore_events=c_activitystart,c_abtestpathassignment
2024-06-21 09:52:29,845  main        INFO   c.a.c.a.Agent    ----- eof configuration --------------------------------------------------
2024-06-21 09:52:29,848  main        INFO   c.s.m.a.s.CI360Agent    Maximum upstream queue size: 1000
2024-06-21 09:52:29,848  main        INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-21 09:52:29,905  main        INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-21 09:52:30,541  4155890-19  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-21 09:52:30,606  4155890-22  INFO   c.s.m.a.s.StreamWebSocket    Agent version: v2402
2024-06-21 09:52:30,620  4155890-22  INFO   c.s.m.a.s.StreamWebSocket    Gateway version: 1.0.125
2024-06-21 09:52:30,620  4155890-22  INFO   c.s.m.a.s.StreamWebSocket    Gateway supported versions: v2406,v2405,v2404,v2403,v2402,v2401,v2312
2024-06-21 09:52:30,620  4155890-22  INFO   c.s.m.a.s.StreamWebSocket    Gateway warning versions: v2311,v2310,v2309
2024-06-21 09:53:30,053  main        INFO   c.a.c.a.Agent    running
2024-06-21 09:54:30,065  main        INFO   c.a.c.a.Agent    running
2024-06-21 09:55:30,077  main        INFO   c.a.c.a.Agent    running
2024-06-21 09:56:30,088  main        INFO   c.a.c.a.Agent    running
2024-06-21 09:57:02,703  2-thread-1  INFO   c.a.c.a.Event    Event received, event: c_DCH, will be processed...
2024-06-21 09:57:02,703  2-thread-1  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"463860","externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"463860","session":"463860","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"subject_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"448e1dd2-6e51-3023-a56e-ba36e24be8af","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"463860","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","Text":"Test CH","message_id":"0","sessionId":"463860","event_category":"unifiedAndEngage","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"448e1dd2-6e51-3023-a56e-ba36e24be8af","guid":"63d453f6-f35c-4e36-b8b0-3e27db949de4","applicationId":"SAS360Proxy","customer_id":"33a1c5ca4227efd37dfd2b0bf6f645ffd189c07d1912be75ebad71e9988add2f453ae735584c8c9fbb3ad32b0850a22c","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"63d453f6-f35c-4e36-b8b0-3e27db949de4"}
2024-06-21 09:57:02,836  2-thread-1  ERROR  c.a.c.c.Http    Response, Code: 407 , Message: 
2024-06-21 09:57:02,837  2-thread-1  DEBUG  c.a.c.a.CI360Api    Task API json: 
2024-06-21 09:57:02,839  2-thread-1  ERROR  c.a.c.a.CI360Api    Exception()
org.json.JSONException: A JSONObject text must begin with '{' at 0 [character 1 line 1]
	at org.json.JSONTokener.syntaxError(JSONTokener.java:503)
	at org.json.JSONObject.<init>(JSONObject.java:213)
	at org.json.JSONObject.<init>(JSONObject.java:430)
	at cz.ab.ci360.agent_ch_stream.CI360Api.getTaskCustomProperties(CI360Api.java:43)
	at cz.ab.ci360.cache.TaskCache.getCI360ApiTaskCp(TaskCache.java:92)
	at cz.ab.ci360.cache.TaskCache.getTask(TaskCache.java:68)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:99)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-21 09:57:02,845  2-thread-1  ERROR  c.a.c.c.TaskCache    Exception()
java.sql.BatchUpdateException: ORA-00001: unique constraint (APP_CAMPAIGN_CDM2.SYS_C009559860) violated

	at oracle.jdbc.driver.OraclePreparedStatement.executeLargeBatch(OraclePreparedStatement.java:9711)
	at oracle.jdbc.driver.T4CPreparedStatement.executeLargeBatch(T4CPreparedStatement.java:1447)
	at oracle.jdbc.driver.OraclePreparedStatement.executeBatch(OraclePreparedStatement.java:9487)
	at oracle.jdbc.driver.OracleStatementWrapper.executeBatch(OracleStatementWrapper.java:237)
	at cz.ab.ci360.cache.TaskCache.saveTaskCpToCache(TaskCache.java:210)
	at cz.ab.ci360.cache.TaskCache.getTask(TaskCache.java:72)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:99)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-21 09:57:02,851  2-thread-1  ERROR  c.a.c.c.Http    Response, Code: 407 , Message: 
2024-06-21 09:57:02,851  2-thread-1  DEBUG  c.a.c.a.CI360Api    Creative API json: 
2024-06-21 09:57:02,851  2-thread-1  ERROR  c.a.c.a.CI360Api    Exception()
org.json.JSONException: A JSONObject text must begin with '{' at 0 [character 1 line 1]
	at org.json.JSONTokener.syntaxError(JSONTokener.java:503)
	at org.json.JSONObject.<init>(JSONObject.java:213)
	at org.json.JSONObject.<init>(JSONObject.java:430)
	at cz.ab.ci360.agent_ch_stream.CI360Api.getCreativeAttributes(CI360Api.java:84)
	at cz.ab.ci360.cache.CreativeCache.getCI360ApiCreativeCp(CreativeCache.java:91)
	at cz.ab.ci360.cache.CreativeCache.getCreative(CreativeCache.java:67)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:100)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-21 09:57:02,861  2-thread-1  ERROR  c.a.c.c.CreativeCache    Exception()
java.sql.BatchUpdateException: ORA-01400: cannot insert NULL into ("APP_CAMPAIGN_CDM2"."CIE_CONTACT_HISTORY_STREAM_CRT_CP_CACHE"."CREATIVE_VERSION_ID")

	at oracle.jdbc.driver.OraclePreparedStatement.executeLargeBatch(OraclePreparedStatement.java:9711)
	at oracle.jdbc.driver.T4CPreparedStatement.executeLargeBatch(T4CPreparedStatement.java:1447)
	at oracle.jdbc.driver.OraclePreparedStatement.executeBatch(OraclePreparedStatement.java:9487)
	at oracle.jdbc.driver.OracleStatementWrapper.executeBatch(OracleStatementWrapper.java:237)
	at cz.ab.ci360.cache.CreativeCache.saveCreativeCpToCache(CreativeCache.java:167)
	at cz.ab.ci360.cache.CreativeCache.getCreative(CreativeCache.java:71)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:100)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-21 09:57:02,863  2-thread-1  ERROR  c.a.c.c.CampaignCache    Exception()
java.sql.SQLSyntaxErrorException: ORA-00942: table or view does not exist

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForDescribe(T4CPreparedStatement.java:807)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:983)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1168)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.executeQuery(OraclePreparedStatement.java:3713)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1167)
	at cz.ab.ci360.cache.CampaignCache.loadDatabaseCampaignCp(CampaignCache.java:85)
	at cz.ab.ci360.cache.CampaignCache.getCamapign(CampaignCache.java:53)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:101)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-00942: table or view does not exist

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 23 common frames omitted
2024-06-21 09:57:02,867  2-thread-1  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = 448e1dd2-6e51-3023-a56e-ba36e24be8af, UPDATED_DTTM = 2024-06-21 09:57:02.866, CREATIVE_VERSION_ID = , CONTROL_GROUP_FLG = , IDENTITY_ID = 448e1dd2-6e51-3023-a56e-ba36e24be8af, SPOT_ID = , LEAD_ID = 448e1dd2-6e51-3023-a56e-ba36e24be8af_06b9ab19-a082-4bc9-9858-47595f992ca8_*************, CREATIVE_ID = , CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = XNA, CONTACT_POLICY_PRODUCT_CODE = XNA, SUBJECT_ID = 463860, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = 0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG, CAMP_SUBTYPE_CODE = XNA, CONTACT_POLICY_TYPE_CODE = XNA, COMM_TYPE_CODE = XNA, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = 33a1c5ca4227efd37dfd2b0bf6f645ffd189c07d1912be75ebad71e9988add2f453ae735584c8c9fbb3ad32b0850a22c, CONTACT_DTTM = 2024-06-21 09:54:14.926, CONTACT_DT = 2024-06-21 00:00:00.0, RTC_ID = 06b9ab19-a082-4bc9-9858-47595f992ca8, CAMPAIGN_MESSAGE_CD = -1, AUD_OCCURENCE_ID = , CONTACT_ID = 63d453f6-f35c-4e36-b8b0-3e27db949de4, EMAIL_IMPRINT_URL = , SESSION_ID = 463860, TASK_ID = ********-4dff-43c7-92a6-5a74243beb01, CONTACT_CHANNEL_CD = XNA, INSERTED_DTTM = 2024-06-21 09:57:02.866, 
2024-06-21 09:57:02,867  2-thread-1  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-21 09:57:02,874  2-thread-1  ERROR  c.a.c.a.Event    ErrorCode:CH_02 - DB - not possible to write payload to DB
java.sql.SQLException: ORA-12899: value too large for column "APP_CAMPAIGN_CDM2"."CIE_CONTACT_HISTORY_STREAM"."CUSTOMER_ID" (actual: 96, maximum: 36)

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForRows(T4CPreparedStatement.java:970)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1205)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.execute(OraclePreparedStatement.java:3778)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.execute(OraclePreparedStatementWrapper.java:1081)
	at cz.ab.ci360.agent_ch_stream.Event.saveToDb(Event.java:397)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:116)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-12899: value too large for column "APP_CAMPAIGN_CDM2"."CIE_CONTACT_HISTORY_STREAM"."CUSTOMER_ID" (actual: 96, maximum: 36)

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 21 common frames omitted
2024-06-21 09:57:02,874  2-thread-1  ERROR  c.a.c.a.Event    Error when processing event.
java.sql.SQLException: ORA-12899: value too large for column "APP_CAMPAIGN_CDM2"."CIE_CONTACT_HISTORY_STREAM"."CUSTOMER_ID" (actual: 96, maximum: 36)

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForRows(T4CPreparedStatement.java:970)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1205)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.execute(OraclePreparedStatement.java:3778)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.execute(OraclePreparedStatementWrapper.java:1081)
	at cz.ab.ci360.agent_ch_stream.Event.saveToDb(Event.java:397)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:116)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-12899: value too large for column "APP_CAMPAIGN_CDM2"."CIE_CONTACT_HISTORY_STREAM"."CUSTOMER_ID" (actual: 96, maximum: 36)

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 21 common frames omitted
2024-06-21 09:57:02,874  2-thread-1  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"463860","externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"463860","session":"463860","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"subject_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"448e1dd2-6e51-3023-a56e-ba36e24be8af","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"463860","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","Text":"Test CH","message_id":"0","sessionId":"463860","event_category":"unifiedAndEngage","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"448e1dd2-6e51-3023-a56e-ba36e24be8af","guid":"63d453f6-f35c-4e36-b8b0-3e27db949de4","applicationId":"SAS360Proxy","customer_id":"33a1c5ca4227efd37dfd2b0bf6f645ffd189c07d1912be75ebad71e9988add2f453ae735584c8c9fbb3ad32b0850a22c","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"63d453f6-f35c-4e36-b8b0-3e27db949de4"}
2024-06-21 09:57:30,098  main        INFO   c.a.c.a.Agent    running
2024-06-21 09:58:30,109  main        INFO   c.a.c.a.Agent    running
2024-06-21 09:58:34,909  2-thread-2  INFO   c.a.c.a.Event    Event received, event: c_DCH, will be processed...
2024-06-21 09:58:34,909  2-thread-2  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"463860","externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"463860","session":"463860","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"subject_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"448e1dd2-6e51-3023-a56e-ba36e24be8af","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"463860","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","Text":"Test CH","message_id":"0","sessionId":"463860","event_category":"unifiedAndEngage","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"448e1dd2-6e51-3023-a56e-ba36e24be8af","guid":"924dc08e-3d26-442d-a543-6edb5718d68d","applicationId":"SAS360Proxy","customer_id":"33a1c5ca4227efd37dfd2b0bf6f645ffd189c07d1912be75ebad71e9988add2f453ae735584c8c9fbb3ad32b0850a22c","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"924dc08e-3d26-442d-a543-6edb5718d68d"}
2024-06-21 09:58:34,912  2-thread-2  ERROR  c.a.c.c.CampaignCache    Exception()
java.sql.SQLSyntaxErrorException: ORA-00942: table or view does not exist

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForDescribe(T4CPreparedStatement.java:807)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:983)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1168)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.executeQuery(OraclePreparedStatement.java:3713)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1167)
	at cz.ab.ci360.cache.CampaignCache.loadDatabaseCampaignCp(CampaignCache.java:85)
	at cz.ab.ci360.cache.CampaignCache.getCamapign(CampaignCache.java:53)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:101)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-00942: table or view does not exist

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 23 common frames omitted
2024-06-21 09:58:34,914  2-thread-2  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = 448e1dd2-6e51-3023-a56e-ba36e24be8af, UPDATED_DTTM = 2024-06-21 09:58:34.913, CREATIVE_VERSION_ID = , CONTROL_GROUP_FLG = , IDENTITY_ID = 448e1dd2-6e51-3023-a56e-ba36e24be8af, SPOT_ID = , LEAD_ID = 448e1dd2-6e51-3023-a56e-ba36e24be8af_06b9ab19-a082-4bc9-9858-47595f992ca8_*************, CREATIVE_ID = , CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = XNA, CONTACT_POLICY_PRODUCT_CODE = XNA, SUBJECT_ID = 463860, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = 0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG, CAMP_SUBTYPE_CODE = XNA, CONTACT_POLICY_TYPE_CODE = XNA, COMM_TYPE_CODE = XNA, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = 33a1c5ca4227efd37dfd2b0bf6f645ffd189c07d1912be75ebad71e9988add2f453ae735584c8c9fbb3ad32b0850a22c, CONTACT_DTTM = 2024-06-21 09:55:03.554, CONTACT_DT = 2024-06-21 00:00:00.0, RTC_ID = 06b9ab19-a082-4bc9-9858-47595f992ca8, CAMPAIGN_MESSAGE_CD = -1, AUD_OCCURENCE_ID = , CONTACT_ID = 924dc08e-3d26-442d-a543-6edb5718d68d, EMAIL_IMPRINT_URL = , SESSION_ID = 463860, TASK_ID = ********-4dff-43c7-92a6-5a74243beb01, CONTACT_CHANNEL_CD = XNA, INSERTED_DTTM = 2024-06-21 09:58:34.913, 
2024-06-21 09:58:34,914  2-thread-2  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-21 09:58:34,916  2-thread-2  ERROR  c.a.c.a.Event    ErrorCode:CH_02 - DB - not possible to write payload to DB
java.sql.SQLException: ORA-12899: value too large for column "APP_CAMPAIGN_CDM2"."CIE_CONTACT_HISTORY_STREAM"."CUSTOMER_ID" (actual: 96, maximum: 36)

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForRows(T4CPreparedStatement.java:970)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1205)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.execute(OraclePreparedStatement.java:3778)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.execute(OraclePreparedStatementWrapper.java:1081)
	at cz.ab.ci360.agent_ch_stream.Event.saveToDb(Event.java:397)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:116)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-12899: value too large for column "APP_CAMPAIGN_CDM2"."CIE_CONTACT_HISTORY_STREAM"."CUSTOMER_ID" (actual: 96, maximum: 36)

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 21 common frames omitted
2024-06-21 09:58:34,916  2-thread-2  ERROR  c.a.c.a.Event    Error when processing event.
java.sql.SQLException: ORA-12899: value too large for column "APP_CAMPAIGN_CDM2"."CIE_CONTACT_HISTORY_STREAM"."CUSTOMER_ID" (actual: 96, maximum: 36)

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForRows(T4CPreparedStatement.java:970)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1205)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.execute(OraclePreparedStatement.java:3778)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.execute(OraclePreparedStatementWrapper.java:1081)
	at cz.ab.ci360.agent_ch_stream.Event.saveToDb(Event.java:397)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:116)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-12899: value too large for column "APP_CAMPAIGN_CDM2"."CIE_CONTACT_HISTORY_STREAM"."CUSTOMER_ID" (actual: 96, maximum: 36)

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 21 common frames omitted
2024-06-21 09:58:34,917  2-thread-2  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"463860","externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"463860","session":"463860","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"subject_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"448e1dd2-6e51-3023-a56e-ba36e24be8af","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"463860","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","Text":"Test CH","message_id":"0","sessionId":"463860","event_category":"unifiedAndEngage","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"448e1dd2-6e51-3023-a56e-ba36e24be8af","guid":"924dc08e-3d26-442d-a543-6edb5718d68d","applicationId":"SAS360Proxy","customer_id":"33a1c5ca4227efd37dfd2b0bf6f645ffd189c07d1912be75ebad71e9988add2f453ae735584c8c9fbb3ad32b0850a22c","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"924dc08e-3d26-442d-a543-6edb5718d68d"}
2024-06-21 09:59:30,117  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:00:26,112  2-thread-3  INFO   c.a.c.a.Event    Event received, event: c_DCH, will be processed...
2024-06-21 10:00:26,112  2-thread-3  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"463860","externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"463860","session":"463860","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"subject_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"448e1dd2-6e51-3023-a56e-ba36e24be8af","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"463860","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","Text":"Test CH","message_id":"0","sessionId":"463860","event_category":"unifiedAndEngage","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"448e1dd2-6e51-3023-a56e-ba36e24be8af","guid":"35b5663a-6242-4134-a02e-9de3bb75c83e","applicationId":"SAS360Proxy","customer_id":"33a1c5ca4227efd37dfd2b0bf6f645ffd189c07d1912be75ebad71e9988add2f453ae735584c8c9fbb3ad32b0850a22c","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"35b5663a-6242-4134-a02e-9de3bb75c83e"}
2024-06-21 10:00:26,120  2-thread-3  ERROR  c.a.c.c.CampaignCache    Exception()
java.sql.SQLSyntaxErrorException: ORA-00942: table or view does not exist

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForDescribe(T4CPreparedStatement.java:807)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:983)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1168)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.executeQuery(OraclePreparedStatement.java:3713)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1167)
	at cz.ab.ci360.cache.CampaignCache.loadDatabaseCampaignCp(CampaignCache.java:85)
	at cz.ab.ci360.cache.CampaignCache.getCamapign(CampaignCache.java:53)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:101)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-00942: table or view does not exist

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 23 common frames omitted
2024-06-21 10:00:26,121  2-thread-3  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = 448e1dd2-6e51-3023-a56e-ba36e24be8af, UPDATED_DTTM = 2024-06-21 10:00:26.12, CREATIVE_VERSION_ID = , CONTROL_GROUP_FLG = , IDENTITY_ID = 448e1dd2-6e51-3023-a56e-ba36e24be8af, SPOT_ID = , LEAD_ID = 448e1dd2-6e51-3023-a56e-ba36e24be8af_06b9ab19-a082-4bc9-9858-47595f992ca8_*************, CREATIVE_ID = , CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = XNA, CONTACT_POLICY_PRODUCT_CODE = XNA, SUBJECT_ID = 463860, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = 0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG, CAMP_SUBTYPE_CODE = XNA, CONTACT_POLICY_TYPE_CODE = XNA, COMM_TYPE_CODE = XNA, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = 33a1c5ca4227efd37dfd2b0bf6f645ffd189c07d1912be75ebad71e9988add2f453ae735584c8c9fbb3ad32b0850a22c, CONTACT_DTTM = 2024-06-21 09:56:11.085, CONTACT_DT = 2024-06-21 00:00:00.0, RTC_ID = 06b9ab19-a082-4bc9-9858-47595f992ca8, CAMPAIGN_MESSAGE_CD = -1, AUD_OCCURENCE_ID = , CONTACT_ID = 35b5663a-6242-4134-a02e-9de3bb75c83e, EMAIL_IMPRINT_URL = , SESSION_ID = 463860, TASK_ID = ********-4dff-43c7-92a6-5a74243beb01, CONTACT_CHANNEL_CD = XNA, INSERTED_DTTM = 2024-06-21 10:00:26.12, 
2024-06-21 10:00:26,121  2-thread-3  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-21 10:00:26,123  2-thread-3  ERROR  c.a.c.a.Event    ErrorCode:CH_02 - DB - not possible to write payload to DB
java.sql.SQLException: ORA-12899: value too large for column "APP_CAMPAIGN_CDM2"."CIE_CONTACT_HISTORY_STREAM"."CUSTOMER_ID" (actual: 96, maximum: 36)

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForRows(T4CPreparedStatement.java:970)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1205)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.execute(OraclePreparedStatement.java:3778)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.execute(OraclePreparedStatementWrapper.java:1081)
	at cz.ab.ci360.agent_ch_stream.Event.saveToDb(Event.java:397)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:116)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-12899: value too large for column "APP_CAMPAIGN_CDM2"."CIE_CONTACT_HISTORY_STREAM"."CUSTOMER_ID" (actual: 96, maximum: 36)

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 21 common frames omitted
2024-06-21 10:00:26,123  2-thread-3  ERROR  c.a.c.a.Event    Error when processing event.
java.sql.SQLException: ORA-12899: value too large for column "APP_CAMPAIGN_CDM2"."CIE_CONTACT_HISTORY_STREAM"."CUSTOMER_ID" (actual: 96, maximum: 36)

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForRows(T4CPreparedStatement.java:970)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1205)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.execute(OraclePreparedStatement.java:3778)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.execute(OraclePreparedStatementWrapper.java:1081)
	at cz.ab.ci360.agent_ch_stream.Event.saveToDb(Event.java:397)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:116)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-12899: value too large for column "APP_CAMPAIGN_CDM2"."CIE_CONTACT_HISTORY_STREAM"."CUSTOMER_ID" (actual: 96, maximum: 36)

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 21 common frames omitted
2024-06-21 10:00:26,123  2-thread-3  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"463860","externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"463860","session":"463860","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"subject_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"448e1dd2-6e51-3023-a56e-ba36e24be8af","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"463860","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","Text":"Test CH","message_id":"0","sessionId":"463860","event_category":"unifiedAndEngage","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"448e1dd2-6e51-3023-a56e-ba36e24be8af","guid":"35b5663a-6242-4134-a02e-9de3bb75c83e","applicationId":"SAS360Proxy","customer_id":"33a1c5ca4227efd37dfd2b0bf6f645ffd189c07d1912be75ebad71e9988add2f453ae735584c8c9fbb3ad32b0850a22c","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"35b5663a-6242-4134-a02e-9de3bb75c83e"}
2024-06-21 10:00:30,126  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:01:20,339  2-thread-4  INFO   c.a.c.a.Event    Event received, event: c_DCH, will be processed...
2024-06-21 10:01:20,340  2-thread-4  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"463860","externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"463860","session":"463860","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"subject_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"448e1dd2-6e51-3023-a56e-ba36e24be8af","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"463860","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","Text":"Test CH","message_id":"0","sessionId":"463860","event_category":"unifiedAndEngage","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"448e1dd2-6e51-3023-a56e-ba36e24be8af","guid":"61f70fb1-a523-4550-afc6-10c22bb3cdb5","applicationId":"SAS360Proxy","customer_id":"33a1c5ca4227efd37dfd2b0bf6f645ffd189c07d1912be75ebad71e9988add2f453ae735584c8c9fbb3ad32b0850a22c","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"61f70fb1-a523-4550-afc6-10c22bb3cdb5"}
2024-06-21 10:01:20,346  2-thread-4  ERROR  c.a.c.c.CampaignCache    Exception()
java.sql.SQLSyntaxErrorException: ORA-00942: table or view does not exist

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForDescribe(T4CPreparedStatement.java:807)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:983)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1168)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.executeQuery(OraclePreparedStatement.java:3713)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1167)
	at cz.ab.ci360.cache.CampaignCache.loadDatabaseCampaignCp(CampaignCache.java:85)
	at cz.ab.ci360.cache.CampaignCache.getCamapign(CampaignCache.java:53)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:101)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-00942: table or view does not exist

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 23 common frames omitted
2024-06-21 10:01:20,347  2-thread-4  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = 448e1dd2-6e51-3023-a56e-ba36e24be8af, UPDATED_DTTM = 2024-06-21 10:01:20.346, CREATIVE_VERSION_ID = , CONTROL_GROUP_FLG = , IDENTITY_ID = 448e1dd2-6e51-3023-a56e-ba36e24be8af, SPOT_ID = , LEAD_ID = 448e1dd2-6e51-3023-a56e-ba36e24be8af_06b9ab19-a082-4bc9-9858-47595f992ca8_*************, CREATIVE_ID = , CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = XNA, CONTACT_POLICY_PRODUCT_CODE = XNA, SUBJECT_ID = 463860, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = 0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG, CAMP_SUBTYPE_CODE = XNA, CONTACT_POLICY_TYPE_CODE = XNA, COMM_TYPE_CODE = XNA, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = 33a1c5ca4227efd37dfd2b0bf6f645ffd189c07d1912be75ebad71e9988add2f453ae735584c8c9fbb3ad32b0850a22c, CONTACT_DTTM = 2024-06-21 09:56:38.765, CONTACT_DT = 2024-06-21 00:00:00.0, RTC_ID = 06b9ab19-a082-4bc9-9858-47595f992ca8, CAMPAIGN_MESSAGE_CD = -1, AUD_OCCURENCE_ID = , CONTACT_ID = 61f70fb1-a523-4550-afc6-10c22bb3cdb5, EMAIL_IMPRINT_URL = , SESSION_ID = 463860, TASK_ID = ********-4dff-43c7-92a6-5a74243beb01, CONTACT_CHANNEL_CD = XNA, INSERTED_DTTM = 2024-06-21 10:01:20.346, 
2024-06-21 10:01:20,347  2-thread-4  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-21 10:01:20,349  2-thread-4  ERROR  c.a.c.a.Event    ErrorCode:CH_02 - DB - not possible to write payload to DB
java.sql.SQLException: ORA-12899: value too large for column "APP_CAMPAIGN_CDM2"."CIE_CONTACT_HISTORY_STREAM"."CUSTOMER_ID" (actual: 96, maximum: 36)

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForRows(T4CPreparedStatement.java:970)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1205)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.execute(OraclePreparedStatement.java:3778)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.execute(OraclePreparedStatementWrapper.java:1081)
	at cz.ab.ci360.agent_ch_stream.Event.saveToDb(Event.java:397)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:116)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-12899: value too large for column "APP_CAMPAIGN_CDM2"."CIE_CONTACT_HISTORY_STREAM"."CUSTOMER_ID" (actual: 96, maximum: 36)

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 21 common frames omitted
2024-06-21 10:01:20,349  2-thread-4  ERROR  c.a.c.a.Event    Error when processing event.
java.sql.SQLException: ORA-12899: value too large for column "APP_CAMPAIGN_CDM2"."CIE_CONTACT_HISTORY_STREAM"."CUSTOMER_ID" (actual: 96, maximum: 36)

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForRows(T4CPreparedStatement.java:970)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1205)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.execute(OraclePreparedStatement.java:3778)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.execute(OraclePreparedStatementWrapper.java:1081)
	at cz.ab.ci360.agent_ch_stream.Event.saveToDb(Event.java:397)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:116)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-12899: value too large for column "APP_CAMPAIGN_CDM2"."CIE_CONTACT_HISTORY_STREAM"."CUSTOMER_ID" (actual: 96, maximum: 36)

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 21 common frames omitted
2024-06-21 10:01:20,349  2-thread-4  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"463860","externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"463860","session":"463860","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"subject_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"448e1dd2-6e51-3023-a56e-ba36e24be8af","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"463860","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","Text":"Test CH","message_id":"0","sessionId":"463860","event_category":"unifiedAndEngage","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"448e1dd2-6e51-3023-a56e-ba36e24be8af","guid":"61f70fb1-a523-4550-afc6-10c22bb3cdb5","applicationId":"SAS360Proxy","customer_id":"33a1c5ca4227efd37dfd2b0bf6f645ffd189c07d1912be75ebad71e9988add2f453ae735584c8c9fbb3ad32b0850a22c","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"61f70fb1-a523-4550-afc6-10c22bb3cdb5"}
2024-06-21 10:01:26,716  2-thread-5  INFO   c.a.c.a.Event    Event received, event: c_DCH, will be processed...
2024-06-21 10:01:26,716  2-thread-5  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"463860","externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"463860","session":"463860","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"subject_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"448e1dd2-6e51-3023-a56e-ba36e24be8af","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"463860","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","Text":"Test CH","message_id":"0","sessionId":"463860","event_category":"unifiedAndEngage","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"448e1dd2-6e51-3023-a56e-ba36e24be8af","guid":"b6128e5c-14a9-43c1-8c26-fb9c94350601","applicationId":"SAS360Proxy","customer_id":"33a1c5ca4227efd37dfd2b0bf6f645ffd189c07d1912be75ebad71e9988add2f453ae735584c8c9fbb3ad32b0850a22c","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"b6128e5c-14a9-43c1-8c26-fb9c94350601"}
2024-06-21 10:01:26,724  2-thread-5  ERROR  c.a.c.c.CampaignCache    Exception()
java.sql.SQLSyntaxErrorException: ORA-00942: table or view does not exist

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForDescribe(T4CPreparedStatement.java:807)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:983)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1168)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.executeQuery(OraclePreparedStatement.java:3713)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1167)
	at cz.ab.ci360.cache.CampaignCache.loadDatabaseCampaignCp(CampaignCache.java:85)
	at cz.ab.ci360.cache.CampaignCache.getCamapign(CampaignCache.java:53)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:101)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-00942: table or view does not exist

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 23 common frames omitted
2024-06-21 10:01:26,725  2-thread-5  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = 448e1dd2-6e51-3023-a56e-ba36e24be8af, UPDATED_DTTM = 2024-06-21 10:01:26.725, CREATIVE_VERSION_ID = , CONTROL_GROUP_FLG = , IDENTITY_ID = 448e1dd2-6e51-3023-a56e-ba36e24be8af, SPOT_ID = , LEAD_ID = 448e1dd2-6e51-3023-a56e-ba36e24be8af_06b9ab19-a082-4bc9-9858-47595f992ca8_*************, CREATIVE_ID = , CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = XNA, CONTACT_POLICY_PRODUCT_CODE = XNA, SUBJECT_ID = 463860, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = 0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG, CAMP_SUBTYPE_CODE = XNA, CONTACT_POLICY_TYPE_CODE = XNA, COMM_TYPE_CODE = XNA, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = 33a1c5ca4227efd37dfd2b0bf6f645ffd189c07d1912be75ebad71e9988add2f453ae735584c8c9fbb3ad32b0850a22c, CONTACT_DTTM = 2024-06-21 09:56:43.425, CONTACT_DT = 2024-06-21 00:00:00.0, RTC_ID = 06b9ab19-a082-4bc9-9858-47595f992ca8, CAMPAIGN_MESSAGE_CD = -1, AUD_OCCURENCE_ID = , CONTACT_ID = b6128e5c-14a9-43c1-8c26-fb9c94350601, EMAIL_IMPRINT_URL = , SESSION_ID = 463860, TASK_ID = ********-4dff-43c7-92a6-5a74243beb01, CONTACT_CHANNEL_CD = XNA, INSERTED_DTTM = 2024-06-21 10:01:26.725, 
2024-06-21 10:01:26,725  2-thread-5  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-21 10:01:26,729  2-thread-5  ERROR  c.a.c.a.Event    ErrorCode:CH_02 - DB - not possible to write payload to DB
java.sql.SQLException: ORA-12899: value too large for column "APP_CAMPAIGN_CDM2"."CIE_CONTACT_HISTORY_STREAM"."CUSTOMER_ID" (actual: 96, maximum: 36)

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForRows(T4CPreparedStatement.java:970)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1205)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.execute(OraclePreparedStatement.java:3778)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.execute(OraclePreparedStatementWrapper.java:1081)
	at cz.ab.ci360.agent_ch_stream.Event.saveToDb(Event.java:397)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:116)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-12899: value too large for column "APP_CAMPAIGN_CDM2"."CIE_CONTACT_HISTORY_STREAM"."CUSTOMER_ID" (actual: 96, maximum: 36)

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 21 common frames omitted
2024-06-21 10:01:26,729  2-thread-5  ERROR  c.a.c.a.Event    Error when processing event.
java.sql.SQLException: ORA-12899: value too large for column "APP_CAMPAIGN_CDM2"."CIE_CONTACT_HISTORY_STREAM"."CUSTOMER_ID" (actual: 96, maximum: 36)

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForRows(T4CPreparedStatement.java:970)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1205)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.execute(OraclePreparedStatement.java:3778)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.execute(OraclePreparedStatementWrapper.java:1081)
	at cz.ab.ci360.agent_ch_stream.Event.saveToDb(Event.java:397)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:116)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-12899: value too large for column "APP_CAMPAIGN_CDM2"."CIE_CONTACT_HISTORY_STREAM"."CUSTOMER_ID" (actual: 96, maximum: 36)

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 21 common frames omitted
2024-06-21 10:01:26,729  2-thread-5  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"463860","externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"463860","session":"463860","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"subject_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"448e1dd2-6e51-3023-a56e-ba36e24be8af","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"463860","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","Text":"Test CH","message_id":"0","sessionId":"463860","event_category":"unifiedAndEngage","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"448e1dd2-6e51-3023-a56e-ba36e24be8af","guid":"b6128e5c-14a9-43c1-8c26-fb9c94350601","applicationId":"SAS360Proxy","customer_id":"33a1c5ca4227efd37dfd2b0bf6f645ffd189c07d1912be75ebad71e9988add2f453ae735584c8c9fbb3ad32b0850a22c","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"b6128e5c-14a9-43c1-8c26-fb9c94350601"}
2024-06-21 10:01:30,133  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:02:30,140  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:03:30,148  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:04:30,157  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:05:30,165  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:06:30,173  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:07:30,181  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:08:30,189  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:09:30,197  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:10:30,205  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:11:30,212  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:12:30,219  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:13:30,226  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:14:30,233  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:15:30,240  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:16:30,248  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:17:30,256  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:18:30,264  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:19:30,272  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:20:30,280  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:21:30,288  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:22:30,297  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:23:30,306  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:24:30,314  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:25:30,323  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:26:30,332  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:27:30,339  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:28:30,347  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:29:30,355  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:30:30,364  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:31:30,372  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:32:30,380  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:33:30,388  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:34:30,396  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:35:30,404  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:36:30,412  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:37:30,420  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:38:30,428  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:39:30,436  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:40:30,443  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:41:30,451  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:42:30,459  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:43:30,466  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:44:30,474  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:45:30,481  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:46:30,489  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:47:30,497  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:48:30,506  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:49:30,514  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:50:30,522  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:51:30,530  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:52:30,538  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:53:30,546  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:54:30,554  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:55:30,562  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:56:30,569  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:57:30,577  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:58:30,585  main        INFO   c.a.c.a.Agent    running
2024-06-21 10:59:30,593  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:00:30,602  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:01:30,609  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:02:30,617  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:03:30,625  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:04:30,633  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:05:30,641  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:06:30,650  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:07:30,658  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:08:30,666  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:09:30,674  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:10:30,681  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:11:30,691  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:12:30,699  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:13:30,707  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:14:30,715  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:15:30,723  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:16:30,731  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:17:30,739  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:18:30,747  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:19:30,755  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:20:30,765  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:21:30,772  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:22:30,778  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:23:30,785  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:24:30,793  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:25:30,801  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:26:30,808  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:27:30,816  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:28:30,823  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:29:30,831  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:30:30,838  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:31:30,846  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:32:30,854  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:33:30,862  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:34:30,869  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:35:30,876  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:36:30,884  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:37:30,890  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:38:30,898  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:39:30,905  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:40:30,912  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:41:30,919  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:42:30,926  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:43:30,933  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:44:30,940  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:45:30,946  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:46:30,953  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:47:30,959  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:48:30,967  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:49:30,974  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:50:30,981  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:51:30,988  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:52:30,996  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:53:31,003  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:54:31,009  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:55:31,017  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:56:31,024  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:57:31,031  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:58:31,037  main        INFO   c.a.c.a.Agent    running
2024-06-21 11:59:31,044  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:00:31,050  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:01:31,057  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:02:31,063  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:03:31,068  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:04:31,075  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:05:31,081  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:06:31,088  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:07:31,095  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:08:31,101  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:09:31,107  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:10:31,113  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:11:31,119  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:12:31,125  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:13:31,131  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:14:31,137  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:15:31,143  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:16:31,150  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:17:31,156  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:18:31,162  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:19:31,169  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:20:31,175  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:21:31,182  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:22:31,188  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:23:31,195  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:24:31,202  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:25:31,209  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:26:31,215  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:27:31,222  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:28:31,229  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:29:31,235  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:30:31,242  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:31:31,249  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:32:31,255  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:33:31,262  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:34:31,269  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:35:31,275  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:36:31,281  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:37:31,288  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:38:31,295  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:39:31,302  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:40:31,308  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:41:31,315  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:42:31,322  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:43:31,329  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:44:31,335  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:45:31,342  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:46:31,348  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:47:31,356  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:48:31,363  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:49:31,370  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:50:31,378  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:51:31,385  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:52:31,392  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:53:31,399  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:54:31,406  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:55:31,413  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:56:31,420  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:57:31,427  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:58:31,433  main        INFO   c.a.c.a.Agent    running
2024-06-21 12:59:31,439  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:00:31,445  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:01:31,451  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:02:31,456  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:03:31,463  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:04:31,469  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:05:31,474  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:06:31,481  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:07:31,488  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:08:31,495  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:09:31,502  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:10:31,509  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:11:31,515  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:12:31,522  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:13:31,528  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:14:31,534  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:15:31,541  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:16:31,547  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:17:31,554  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:18:31,561  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:19:31,567  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:20:31,574  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:21:31,581  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:22:31,588  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:23:31,595  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:24:31,603  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:25:31,610  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:26:31,617  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:27:31,624  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:28:31,630  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:29:28,270  2-thread-6  INFO   c.a.c.a.Event    Event received, event: c_DCH, will be processed...
2024-06-21 13:29:28,270  2-thread-6  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"463860","externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"463860","session":"463860","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"subject_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"448e1dd2-6e51-3023-a56e-ba36e24be8af","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"463860","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","Text":"Test CH","message_id":"0","sessionId":"463860","event_category":"unifiedAndEngage","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"448e1dd2-6e51-3023-a56e-ba36e24be8af","guid":"a4920979-af82-46d4-8974-30f946a11ed2","applicationId":"SAS360Proxy","customer_id":"33a1c5ca4227efd37dfd2b0bf6f645ffd189c07d1912be75ebad71e9988add2f453ae735584c8c9fbb3ad32b0850a22c","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"a4920979-af82-46d4-8974-30f946a11ed2"}
2024-06-21 13:29:28,273  2-thread-6  ERROR  c.a.c.c.CampaignCache    Exception()
java.sql.SQLSyntaxErrorException: ORA-00942: table or view does not exist

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForDescribe(T4CPreparedStatement.java:807)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:983)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1168)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.executeQuery(OraclePreparedStatement.java:3713)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1167)
	at cz.ab.ci360.cache.CampaignCache.loadDatabaseCampaignCp(CampaignCache.java:85)
	at cz.ab.ci360.cache.CampaignCache.getCamapign(CampaignCache.java:53)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:101)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-00942: table or view does not exist

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 23 common frames omitted
2024-06-21 13:29:28,274  2-thread-6  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = 448e1dd2-6e51-3023-a56e-ba36e24be8af, UPDATED_DTTM = 2024-06-21 13:29:28.274, CREATIVE_VERSION_ID = , CONTROL_GROUP_FLG = , IDENTITY_ID = 448e1dd2-6e51-3023-a56e-ba36e24be8af, SPOT_ID = , LEAD_ID = 448e1dd2-6e51-3023-a56e-ba36e24be8af_06b9ab19-a082-4bc9-9858-47595f992ca8_*************, CREATIVE_ID = , CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = XNA, CONTACT_POLICY_PRODUCT_CODE = XNA, SUBJECT_ID = 463860, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = 0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG, CAMP_SUBTYPE_CODE = XNA, CONTACT_POLICY_TYPE_CODE = XNA, COMM_TYPE_CODE = XNA, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = 33a1c5ca4227efd37dfd2b0bf6f645ffd189c07d1912be75ebad71e9988add2f453ae735584c8c9fbb3ad32b0850a22c, CONTACT_DTTM = 2024-06-21 13:29:28.082, CONTACT_DT = 2024-06-21 00:00:00.0, RTC_ID = 06b9ab19-a082-4bc9-9858-47595f992ca8, CAMPAIGN_MESSAGE_CD = -1, AUD_OCCURENCE_ID = , CONTACT_ID = a4920979-af82-46d4-8974-30f946a11ed2, EMAIL_IMPRINT_URL = , SESSION_ID = 463860, TASK_ID = ********-4dff-43c7-92a6-5a74243beb01, CONTACT_CHANNEL_CD = XNA, INSERTED_DTTM = 2024-06-21 13:29:28.274, 
2024-06-21 13:29:28,274  2-thread-6  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-21 13:29:28,283  2-thread-6  ERROR  c.a.c.a.Event    ErrorCode:CH_02 - DB - not possible to write payload to DB
java.sql.SQLIntegrityConstraintViolationException: ORA-01400: cannot insert NULL into ("APP_CAMPAIGN_CDM2"."CIE_CONTACT_HISTORY_STREAM"."ARCHIVED_FLAG")

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForRows(T4CPreparedStatement.java:970)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1205)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.execute(OraclePreparedStatement.java:3778)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.execute(OraclePreparedStatementWrapper.java:1081)
	at cz.ab.ci360.agent_ch_stream.Event.saveToDb(Event.java:397)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:116)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-01400: cannot insert NULL into ("APP_CAMPAIGN_CDM2"."CIE_CONTACT_HISTORY_STREAM"."ARCHIVED_FLAG")

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 21 common frames omitted
2024-06-21 13:29:28,284  2-thread-6  ERROR  c.a.c.a.Event    Error when processing event.
java.sql.SQLIntegrityConstraintViolationException: ORA-01400: cannot insert NULL into ("APP_CAMPAIGN_CDM2"."CIE_CONTACT_HISTORY_STREAM"."ARCHIVED_FLAG")

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForRows(T4CPreparedStatement.java:970)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1205)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.execute(OraclePreparedStatement.java:3778)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.execute(OraclePreparedStatementWrapper.java:1081)
	at cz.ab.ci360.agent_ch_stream.Event.saveToDb(Event.java:397)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:116)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-01400: cannot insert NULL into ("APP_CAMPAIGN_CDM2"."CIE_CONTACT_HISTORY_STREAM"."ARCHIVED_FLAG")

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 21 common frames omitted
2024-06-21 13:29:28,284  2-thread-6  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"463860","externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"463860","session":"463860","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"subject_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"448e1dd2-6e51-3023-a56e-ba36e24be8af","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"463860","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","Text":"Test CH","message_id":"0","sessionId":"463860","event_category":"unifiedAndEngage","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"448e1dd2-6e51-3023-a56e-ba36e24be8af","guid":"a4920979-af82-46d4-8974-30f946a11ed2","applicationId":"SAS360Proxy","customer_id":"33a1c5ca4227efd37dfd2b0bf6f645ffd189c07d1912be75ebad71e9988add2f453ae735584c8c9fbb3ad32b0850a22c","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"a4920979-af82-46d4-8974-30f946a11ed2"}
2024-06-21 13:29:31,636  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:30:31,642  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:31:31,649  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:32:31,656  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:33:31,662  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:34:31,669  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:35:31,676  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:36:31,683  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:37:31,689  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:37:40,847  2-thread-7  INFO   c.a.c.a.Event    Event received, event: c_DCH, will be processed...
2024-06-21 13:37:40,847  2-thread-7  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"463860","externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"463860","session":"463860","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"subject_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"448e1dd2-6e51-3023-a56e-ba36e24be8af","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"463860","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","Text":"Test CH","message_id":"0","sessionId":"463860","event_category":"unifiedAndEngage","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"448e1dd2-6e51-3023-a56e-ba36e24be8af","guid":"1e893648-927e-439b-ada5-c859557c4b04","applicationId":"SAS360Proxy","customer_id":"33a1c5ca4227efd37dfd2b0bf6f645ffd189c07d1912be75ebad71e9988add2f453ae735584c8c9fbb3ad32b0850a22c","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"1e893648-927e-439b-ada5-c859557c4b04"}
2024-06-21 13:37:40,849  2-thread-7  ERROR  c.a.c.c.CampaignCache    Exception()
java.sql.SQLSyntaxErrorException: ORA-00942: table or view does not exist

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForDescribe(T4CPreparedStatement.java:807)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:983)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1168)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.executeQuery(OraclePreparedStatement.java:3713)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1167)
	at cz.ab.ci360.cache.CampaignCache.loadDatabaseCampaignCp(CampaignCache.java:85)
	at cz.ab.ci360.cache.CampaignCache.getCamapign(CampaignCache.java:53)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:101)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-00942: table or view does not exist

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 23 common frames omitted
2024-06-21 13:37:40,850  2-thread-7  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = 448e1dd2-6e51-3023-a56e-ba36e24be8af, UPDATED_DTTM = 2024-06-21 13:37:40.849, CREATIVE_VERSION_ID = , CONTROL_GROUP_FLG = , IDENTITY_ID = 448e1dd2-6e51-3023-a56e-ba36e24be8af, SPOT_ID = , LEAD_ID = 448e1dd2-6e51-3023-a56e-ba36e24be8af_06b9ab19-a082-4bc9-9858-47595f992ca8_*************, CREATIVE_ID = , CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = XNA, CONTACT_POLICY_PRODUCT_CODE = XNA, SUBJECT_ID = 463860, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = 0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG, CAMP_SUBTYPE_CODE = XNA, CONTACT_POLICY_TYPE_CODE = XNA, COMM_TYPE_CODE = XNA, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = 33a1c5ca4227efd37dfd2b0bf6f645ffd189c07d1912be75ebad71e9988add2f453ae735584c8c9fbb3ad32b0850a22c, CONTACT_DTTM = 2024-06-21 13:37:40.687, CONTACT_DT = 2024-06-21 00:00:00.0, RTC_ID = 06b9ab19-a082-4bc9-9858-47595f992ca8, CAMPAIGN_MESSAGE_CD = -1, AUD_OCCURENCE_ID = , CONTACT_ID = 1e893648-927e-439b-ada5-c859557c4b04, EMAIL_IMPRINT_URL = , SESSION_ID = 463860, TASK_ID = ********-4dff-43c7-92a6-5a74243beb01, CONTACT_CHANNEL_CD = XNA, INSERTED_DTTM = 2024-06-21 13:37:40.849, 
2024-06-21 13:37:40,850  2-thread-7  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-21 13:37:40,866  2-thread-7  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-21 13:38:31,696  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:39:31,702  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:40:31,708  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:41:31,715  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:41:39,107  2-thread-8  INFO   c.a.c.a.Event    Event received, event: c_DCH, will be processed...
2024-06-21 13:41:39,107  2-thread-8  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","session":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"subject_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"5092beb6-3796-347c-bc98-9688b1b30792","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","Text":"Test CH","message_id":"0","sessionId":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","event_category":"unifiedAndEngage","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"5092beb6-3796-347c-bc98-9688b1b30792","guid":"244a5d5f-d407-4f7c-8db1-9aebf8396d4b","applicationId":"SAS360Proxy","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"244a5d5f-d407-4f7c-8db1-9aebf8396d4b"}
2024-06-21 13:41:39,109  2-thread-8  ERROR  c.a.c.c.CampaignCache    Exception()
java.sql.SQLSyntaxErrorException: ORA-00942: table or view does not exist

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForDescribe(T4CPreparedStatement.java:807)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:983)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1168)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.executeQuery(OraclePreparedStatement.java:3713)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1167)
	at cz.ab.ci360.cache.CampaignCache.loadDatabaseCampaignCp(CampaignCache.java:85)
	at cz.ab.ci360.cache.CampaignCache.getCamapign(CampaignCache.java:53)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:101)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-00942: table or view does not exist

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 23 common frames omitted
2024-06-21 13:41:39,110  2-thread-8  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = 5092beb6-3796-347c-bc98-9688b1b30792, UPDATED_DTTM = 2024-06-21 13:41:39.11, CREATIVE_VERSION_ID = , CONTROL_GROUP_FLG = , IDENTITY_ID = 5092beb6-3796-347c-bc98-9688b1b30792, SPOT_ID = , LEAD_ID = 5092beb6-3796-347c-bc98-9688b1b30792_06b9ab19-a082-4bc9-9858-47595f992ca8_*************, CREATIVE_ID = , CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = XNA, CONTACT_POLICY_PRODUCT_CODE = XNA, SUBJECT_ID = b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = 0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG, CAMP_SUBTYPE_CODE = XNA, CONTACT_POLICY_TYPE_CODE = XNA, COMM_TYPE_CODE = XNA, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = , CONTACT_DTTM = 2024-06-21 13:41:38.853, CONTACT_DT = 2024-06-21 00:00:00.0, RTC_ID = 06b9ab19-a082-4bc9-9858-47595f992ca8, CAMPAIGN_MESSAGE_CD = -1, AUD_OCCURENCE_ID = , CONTACT_ID = 244a5d5f-d407-4f7c-8db1-9aebf8396d4b, EMAIL_IMPRINT_URL = , SESSION_ID = b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c, TASK_ID = ********-4dff-43c7-92a6-5a74243beb01, CONTACT_CHANNEL_CD = XNA, INSERTED_DTTM = 2024-06-21 13:41:39.11, 
2024-06-21 13:41:39,110  2-thread-8  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-21 13:41:39,118  2-thread-8  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-21 13:42:02,516  2-thread-9  INFO   c.a.c.a.Event    Event received, event: c_DCH, will be processed...
2024-06-21 13:42:02,516  2-thread-9  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","session":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"subject_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"5092beb6-3796-347c-bc98-9688b1b30792","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","Text":"Test CH","message_id":"0","sessionId":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","event_category":"unifiedAndEngage","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"5092beb6-3796-347c-bc98-9688b1b30792","guid":"5d56c3ae-df47-4d13-bdca-2dc07f1b65ae","applicationId":"SAS360Proxy","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"5d56c3ae-df47-4d13-bdca-2dc07f1b65ae"}
2024-06-21 13:42:02,521  2-thread-9  ERROR  c.a.c.c.CampaignCache    Exception()
java.sql.SQLSyntaxErrorException: ORA-00942: table or view does not exist

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForDescribe(T4CPreparedStatement.java:807)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:983)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1168)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.executeQuery(OraclePreparedStatement.java:3713)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1167)
	at cz.ab.ci360.cache.CampaignCache.loadDatabaseCampaignCp(CampaignCache.java:85)
	at cz.ab.ci360.cache.CampaignCache.getCamapign(CampaignCache.java:53)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:101)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-00942: table or view does not exist

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 23 common frames omitted
2024-06-21 13:42:02,521  2-thread-9  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = 5092beb6-3796-347c-bc98-9688b1b30792, UPDATED_DTTM = 2024-06-21 13:42:02.521, CREATIVE_VERSION_ID = , CONTROL_GROUP_FLG = , IDENTITY_ID = 5092beb6-3796-347c-bc98-9688b1b30792, SPOT_ID = , LEAD_ID = 5092beb6-3796-347c-bc98-9688b1b30792_06b9ab19-a082-4bc9-9858-47595f992ca8_*************, CREATIVE_ID = , CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = XNA, CONTACT_POLICY_PRODUCT_CODE = XNA, SUBJECT_ID = b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = 0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG, CAMP_SUBTYPE_CODE = XNA, CONTACT_POLICY_TYPE_CODE = XNA, COMM_TYPE_CODE = XNA, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = , CONTACT_DTTM = 2024-06-21 13:42:02.357, CONTACT_DT = 2024-06-21 00:00:00.0, RTC_ID = 06b9ab19-a082-4bc9-9858-47595f992ca8, CAMPAIGN_MESSAGE_CD = -1, AUD_OCCURENCE_ID = , CONTACT_ID = 5d56c3ae-df47-4d13-bdca-2dc07f1b65ae, EMAIL_IMPRINT_URL = , SESSION_ID = b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c, TASK_ID = ********-4dff-43c7-92a6-5a74243beb01, CONTACT_CHANNEL_CD = XNA, INSERTED_DTTM = 2024-06-21 13:42:02.521, 
2024-06-21 13:42:02,521  2-thread-9  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-21 13:42:02,526  2-thread-9  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-21 13:42:31,722  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:43:31,728  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:44:31,734  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:45:31,740  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:46:31,747  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:47:31,753  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:48:31,760  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:49:31,768  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:50:31,775  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:51:31,782  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:52:31,789  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:53:31,796  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:54:31,803  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:55:31,811  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:56:31,818  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:57:31,825  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:58:31,832  main        INFO   c.a.c.a.Agent    running
2024-06-21 13:59:31,839  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:00:31,845  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:01:31,851  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:01:42,861  -thread-10  INFO   c.a.c.a.Event    Event received, event: c_DCH, will be processed...
2024-06-21 14:01:42,861  -thread-10  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","session":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"subject_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"5092beb6-3796-347c-bc98-9688b1b30792","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","sentEmail":"Y","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","textEmailContact":"<EMAIL>","Text":"Test CH","message_id":"0","sessionId":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","event_category":"unifiedAndEngage","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"5092beb6-3796-347c-bc98-9688b1b30792","guid":"aa258146-3589-4df1-9312-e1c7065a4e65","applicationId":"SAS360Proxy","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"aa258146-3589-4df1-9312-e1c7065a4e65"}
2024-06-21 14:01:42,864  -thread-10  ERROR  c.a.c.c.CampaignCache    Exception()
java.sql.SQLSyntaxErrorException: ORA-00942: table or view does not exist

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForDescribe(T4CPreparedStatement.java:807)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:983)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1168)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.executeQuery(OraclePreparedStatement.java:3713)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1167)
	at cz.ab.ci360.cache.CampaignCache.loadDatabaseCampaignCp(CampaignCache.java:85)
	at cz.ab.ci360.cache.CampaignCache.getCamapign(CampaignCache.java:53)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:101)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-00942: table or view does not exist

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 23 common frames omitted
2024-06-21 14:01:42,864  -thread-10  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = 5092beb6-3796-347c-bc98-9688b1b30792, UPDATED_DTTM = 2024-06-21 14:01:42.864, CREATIVE_VERSION_ID = , CONTROL_GROUP_FLG = , IDENTITY_ID = 5092beb6-3796-347c-bc98-9688b1b30792, SPOT_ID = , LEAD_ID = 5092beb6-3796-347c-bc98-9688b1b30792_06b9ab19-a082-4bc9-9858-47595f992ca8_*************, CREATIVE_ID = , CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = XNA, CONTACT_POLICY_PRODUCT_CODE = XNA, SUBJECT_ID = b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = 0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG, CAMP_SUBTYPE_CODE = XNA, CONTACT_POLICY_TYPE_CODE = XNA, COMM_TYPE_CODE = XNA, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = , CONTACT_DTTM = 2024-06-21 14:01:42.697, CONTACT_DT = 2024-06-21 00:00:00.0, RTC_ID = 06b9ab19-a082-4bc9-9858-47595f992ca8, CAMPAIGN_MESSAGE_CD = -1, AUD_OCCURENCE_ID = , CONTACT_ID = aa258146-3589-4df1-9312-e1c7065a4e65, EMAIL_IMPRINT_URL = , SESSION_ID = b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c, TASK_ID = ********-4dff-43c7-92a6-5a74243beb01, CONTACT_CHANNEL_CD = XNA, INSERTED_DTTM = 2024-06-21 14:01:42.864, 
2024-06-21 14:01:42,864  -thread-10  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-21 14:01:42,874  -thread-10  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-21 14:01:43,065  -thread-11  INFO   c.a.c.a.Event    Event received, event: c_X_phsjkjjd, will be processed...
2024-06-21 14:01:43,065  -thread-11  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"externalCode":"TSK_97","channel_user_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","channelType":"external","outboundProperties.primaryVisualization":"BUTTON","Image":"fsgfsdg","creative_id":"4f2a7d44-1fa0-41aa-b693-e3ac21b3670e","vid":"5092beb6-3796-347c-bc98-9688b1b30792","internalTenantId":"********","sentEmail":"Y","response_tracking_code":"37bd20de-68e6-43cd-ba5b-8c330d246c10","primaryClickMeaning":"DISMISS_CLICK","activity_id":"23171abe-f2d0-4a63-a054-96bc8f250fe5","creative_version_id":"jgu9tP2Z2Dt.Cx_zFynwJ4fPlpfhUo4Y","eventName":"c_X_phsjkjjd","activity_trigger_event_id":"a7121ab1-bdda-4092-83aa-f61c05d438d8","event_channel":"external","task_version_id":"xyXsR3hjHp6NrAkCFPc9A35a4lKN2s6C","textEmailContact":"<EMAIL>","primaryVisualization":"BUTTON","activity_task_type":"EVENT","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"5092beb6-3796-347c-bc98-9688b1b30792","guid":"d72f4b87-bfe2-4bcc-ade1-c9eaff5d1bba","applicationId":"SAS360Proxy","communicationKind":"MARKETING","eventDesignedName":"c_X_phsjkjjd","subject_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","outboundProperties.timeSlot":"9-18","generatedTimestamp":"*************","timeSlot":"9-18","session":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","screen_info":"x@","task_id":"2b772d1a-68ba-4444-889d-2800218ec724","outboundProperties.Image":"fsgfsdg","activity_ia_tag_value":"a7121ab1-bdda-4092-83aa-f61c05d438d8","channel_user_type":"subject_id","parent_event":"external","eventname":"c_X_phsjkjjd","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"bc324933-5f23-45a0-80c1-34ce9e9aadd8","variant_id":"0","outboundProperties.communicationKind":"MARKETING","outboundProperties.primaryClickMeaning":"DISMISS_CLICK","creative_content":"fsdafsdfgsfdgsfd","parent_eventname":"Ext_Sit_Event","event":"c_X_phsjkjjd","channelId":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","timestamp":"*************","internal_tenant_id":"********","Text":"Test CH","message_id":"0","sessionId":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","event_category":"unifiedAndEngage","event_designed_name":"c_X_phsjkjjd","account":"f0cb22506600016b1805ee8b"},"rowKey":"d72f4b87-bfe2-4bcc-ade1-c9eaff5d1bba"}
2024-06-21 14:01:43,092  -thread-11  ERROR  c.a.c.c.Http    Response, Code: 407 , Message: 
2024-06-21 14:01:43,093  -thread-11  DEBUG  c.a.c.a.CI360Api    Task API json: 
2024-06-21 14:01:43,093  -thread-11  ERROR  c.a.c.a.CI360Api    Exception()
org.json.JSONException: A JSONObject text must begin with '{' at 0 [character 1 line 1]
	at org.json.JSONTokener.syntaxError(JSONTokener.java:503)
	at org.json.JSONObject.<init>(JSONObject.java:213)
	at org.json.JSONObject.<init>(JSONObject.java:430)
	at cz.ab.ci360.agent_ch_stream.CI360Api.getTaskCustomProperties(CI360Api.java:43)
	at cz.ab.ci360.cache.TaskCache.getCI360ApiTaskCp(TaskCache.java:92)
	at cz.ab.ci360.cache.TaskCache.getTask(TaskCache.java:68)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:99)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-21 14:01:43,100  -thread-11  ERROR  c.a.c.c.TaskCache    Exception()
java.sql.BatchUpdateException: ORA-00001: unique constraint (APP_CAMPAIGN_CDM2.SYS_C009559860) violated

	at oracle.jdbc.driver.OraclePreparedStatement.executeLargeBatch(OraclePreparedStatement.java:9711)
	at oracle.jdbc.driver.T4CPreparedStatement.executeLargeBatch(T4CPreparedStatement.java:1447)
	at oracle.jdbc.driver.OraclePreparedStatement.executeBatch(OraclePreparedStatement.java:9487)
	at oracle.jdbc.driver.OracleStatementWrapper.executeBatch(OracleStatementWrapper.java:237)
	at cz.ab.ci360.cache.TaskCache.saveTaskCpToCache(TaskCache.java:210)
	at cz.ab.ci360.cache.TaskCache.getTask(TaskCache.java:72)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:99)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-21 14:01:43,107  -thread-11  ERROR  c.a.c.c.Http    Response, Code: 407 , Message: 
2024-06-21 14:01:43,107  -thread-11  DEBUG  c.a.c.a.CI360Api    Creative API json: 
2024-06-21 14:01:43,107  -thread-11  ERROR  c.a.c.a.CI360Api    Exception()
org.json.JSONException: A JSONObject text must begin with '{' at 0 [character 1 line 1]
	at org.json.JSONTokener.syntaxError(JSONTokener.java:503)
	at org.json.JSONObject.<init>(JSONObject.java:213)
	at org.json.JSONObject.<init>(JSONObject.java:430)
	at cz.ab.ci360.agent_ch_stream.CI360Api.getCreativeAttributes(CI360Api.java:84)
	at cz.ab.ci360.cache.CreativeCache.getCI360ApiCreativeCp(CreativeCache.java:91)
	at cz.ab.ci360.cache.CreativeCache.getCreative(CreativeCache.java:67)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:100)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-21 14:01:43,116  -thread-11  ERROR  c.a.c.c.CampaignCache    Exception()
java.sql.SQLSyntaxErrorException: ORA-00942: table or view does not exist

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForDescribe(T4CPreparedStatement.java:807)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:983)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1168)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.executeQuery(OraclePreparedStatement.java:3713)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1167)
	at cz.ab.ci360.cache.CampaignCache.loadDatabaseCampaignCp(CampaignCache.java:85)
	at cz.ab.ci360.cache.CampaignCache.getCamapign(CampaignCache.java:53)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:101)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-00942: table or view does not exist

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 23 common frames omitted
2024-06-21 14:01:43,117  -thread-11  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = 5092beb6-3796-347c-bc98-9688b1b30792, UPDATED_DTTM = 2024-06-21 14:01:43.116, CREATIVE_VERSION_ID = jgu9tP2Z2Dt.Cx_zFynwJ4fPlpfhUo4Y, CONTROL_GROUP_FLG = , IDENTITY_ID = 5092beb6-3796-347c-bc98-9688b1b30792, SPOT_ID = , LEAD_ID = 5092beb6-3796-347c-bc98-9688b1b30792_37bd20de-68e6-43cd-ba5b-8c330d246c10_*************, CREATIVE_ID = 4f2a7d44-1fa0-41aa-b693-e3ac21b3670e, CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = XNA, CONTACT_POLICY_PRODUCT_CODE = XNA, SUBJECT_ID = b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = xyXsR3hjHp6NrAkCFPc9A35a4lKN2s6C, CAMP_SUBTYPE_CODE = XNA, CONTACT_POLICY_TYPE_CODE = XNA, COMM_TYPE_CODE = XNA, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = , CONTACT_DTTM = 2024-06-21 14:01:42.85, CONTACT_DT = 2024-06-21 00:00:00.0, RTC_ID = 37bd20de-68e6-43cd-ba5b-8c330d246c10, CAMPAIGN_MESSAGE_CD = -1, AUD_OCCURENCE_ID = , CONTACT_ID = d72f4b87-bfe2-4bcc-ade1-c9eaff5d1bba, EMAIL_IMPRINT_URL = , SESSION_ID = b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c, TASK_ID = 2b772d1a-68ba-4444-889d-2800218ec724, CONTACT_CHANNEL_CD = XNA, INSERTED_DTTM = 2024-06-21 14:01:43.116, 
2024-06-21 14:01:43,117  -thread-11  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-21 14:01:43,141  -thread-11  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-21 14:01:49,093  -thread-12  INFO   c.a.c.a.Event    Event received, event: c_DCH, will be processed...
2024-06-21 14:01:49,093  -thread-12  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"463860","externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"463860","session":"463860","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"subject_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"448e1dd2-6e51-3023-a56e-ba36e24be8af","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"463860","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","Text":"Test CH","message_id":"0","sessionId":"463860","event_category":"unifiedAndEngage","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"448e1dd2-6e51-3023-a56e-ba36e24be8af","guid":"6dbcebb2-c236-4dc0-829f-f53891022c58","applicationId":"SAS360Proxy","customer_id":"33a1c5ca4227efd37dfd2b0bf6f645ffd189c07d1912be75ebad71e9988add2f453ae735584c8c9fbb3ad32b0850a22c","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"6dbcebb2-c236-4dc0-829f-f53891022c58"}
2024-06-21 14:01:49,096  -thread-12  ERROR  c.a.c.c.CampaignCache    Exception()
java.sql.SQLSyntaxErrorException: ORA-00942: table or view does not exist

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForDescribe(T4CPreparedStatement.java:807)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:983)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1168)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.executeQuery(OraclePreparedStatement.java:3713)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1167)
	at cz.ab.ci360.cache.CampaignCache.loadDatabaseCampaignCp(CampaignCache.java:85)
	at cz.ab.ci360.cache.CampaignCache.getCamapign(CampaignCache.java:53)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:101)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-00942: table or view does not exist

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 23 common frames omitted
2024-06-21 14:01:49,096  -thread-12  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = 448e1dd2-6e51-3023-a56e-ba36e24be8af, UPDATED_DTTM = 2024-06-21 14:01:49.096, CREATIVE_VERSION_ID = , CONTROL_GROUP_FLG = , IDENTITY_ID = 448e1dd2-6e51-3023-a56e-ba36e24be8af, SPOT_ID = , LEAD_ID = 448e1dd2-6e51-3023-a56e-ba36e24be8af_06b9ab19-a082-4bc9-9858-47595f992ca8_*************, CREATIVE_ID = , CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = XNA, CONTACT_POLICY_PRODUCT_CODE = XNA, SUBJECT_ID = 463860, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = 0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG, CAMP_SUBTYPE_CODE = XNA, CONTACT_POLICY_TYPE_CODE = XNA, COMM_TYPE_CODE = XNA, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = 33a1c5ca4227efd37dfd2b0bf6f645ffd189c07d1912be75ebad71e9988add2f453ae735584c8c9fbb3ad32b0850a22c, CONTACT_DTTM = 2024-06-21 14:01:48.848, CONTACT_DT = 2024-06-21 00:00:00.0, RTC_ID = 06b9ab19-a082-4bc9-9858-47595f992ca8, CAMPAIGN_MESSAGE_CD = -1, AUD_OCCURENCE_ID = , CONTACT_ID = 6dbcebb2-c236-4dc0-829f-f53891022c58, EMAIL_IMPRINT_URL = , SESSION_ID = 463860, TASK_ID = ********-4dff-43c7-92a6-5a74243beb01, CONTACT_CHANNEL_CD = XNA, INSERTED_DTTM = 2024-06-21 14:01:49.096, 
2024-06-21 14:01:49,096  -thread-12  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-21 14:01:49,105  -thread-12  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-21 14:02:31,857  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:03:31,863  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:04:31,869  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:05:31,875  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:06:31,881  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:07:31,887  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:08:31,893  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:09:31,899  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:10:31,905  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:11:31,911  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:12:31,917  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:13:31,923  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:14:31,928  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:15:31,935  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:16:31,941  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:17:31,947  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:18:31,954  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:19:31,961  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:20:31,967  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:21:31,974  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:22:31,981  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:23:31,987  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:24:31,994  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:25:32,001  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:26:32,007  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:27:32,013  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:28:32,021  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:29:32,028  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:30:32,035  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:31:32,043  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:32:32,051  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:33:32,058  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:34:32,065  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:35:32,071  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:36:32,078  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:37:32,084  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:38:32,091  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:39:32,098  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:40:32,104  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:41:32,111  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:42:32,117  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:43:32,124  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:44:32,131  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:45:32,138  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:46:32,144  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:47:32,151  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:48:32,158  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:49:32,165  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:50:32,172  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:51:32,179  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:52:32,186  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:53:32,193  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:54:32,200  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:55:32,206  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:56:32,212  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:57:32,219  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:58:32,225  main        INFO   c.a.c.a.Agent    running
2024-06-21 14:59:32,232  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:00:32,238  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:01:32,245  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:02:32,251  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:03:32,258  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:04:32,264  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:05:32,271  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:06:32,278  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:07:32,285  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:08:32,293  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:09:32,301  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:10:32,308  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:11:32,315  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:12:32,323  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:13:32,330  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:14:32,336  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:15:32,343  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:16:32,349  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:17:32,356  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:18:32,362  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:19:32,369  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:20:32,376  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:21:32,383  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:22:32,390  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:23:32,396  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:24:32,403  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:25:32,410  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:26:32,418  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:27:32,425  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:28:32,432  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:29:32,439  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:30:32,445  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:31:32,451  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:32:32,458  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:33:32,465  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:34:32,471  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:35:32,477  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:36:32,484  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:37:32,491  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:38:32,498  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:39:32,504  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:40:32,512  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:41:32,519  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:42:32,526  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:43:32,533  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:44:32,540  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:45:32,547  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:46:32,554  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:47:32,560  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:48:32,567  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:49:32,574  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:50:32,581  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:51:32,589  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:52:32,597  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:53:32,604  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:54:32,611  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:55:32,618  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:56:32,625  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:57:32,632  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:58:32,639  main        INFO   c.a.c.a.Agent    running
2024-06-21 15:59:32,646  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:00:32,652  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:01:32,659  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:02:32,665  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:03:32,671  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:04:32,677  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:05:32,683  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:06:32,690  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:07:32,697  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:08:32,704  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:09:32,710  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:10:32,717  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:11:32,723  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:12:32,729  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:13:32,735  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:14:32,741  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:15:32,747  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:16:32,754  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:17:32,761  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:18:32,769  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:19:32,776  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:20:32,783  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:21:31,443  -thread-13  INFO   c.a.c.a.Event    Event received, event: c_DCH, will be processed...
2024-06-21 16:21:31,443  -thread-13  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","session":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"subject_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"5092beb6-3796-347c-bc98-9688b1b30792","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","sentEmail":"Y","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","textEmailContact":"<EMAIL>","Text":"Test CH","message_id":"0","sessionId":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","event_category":"unifiedAndEngage","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"5092beb6-3796-347c-bc98-9688b1b30792","guid":"1b0e8a1a-412b-4de2-873b-ba17768e75bd","applicationId":"SAS360Proxy","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"1b0e8a1a-412b-4de2-873b-ba17768e75bd"}
2024-06-21 16:21:31,448  -thread-13  ERROR  c.a.c.c.CampaignCache    Exception()
java.sql.SQLSyntaxErrorException: ORA-00942: table or view does not exist

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForDescribe(T4CPreparedStatement.java:807)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:983)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1168)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.executeQuery(OraclePreparedStatement.java:3713)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1167)
	at cz.ab.ci360.cache.CampaignCache.loadDatabaseCampaignCp(CampaignCache.java:85)
	at cz.ab.ci360.cache.CampaignCache.getCamapign(CampaignCache.java:53)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:101)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-00942: table or view does not exist

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 23 common frames omitted
2024-06-21 16:21:31,448  -thread-13  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = 5092beb6-3796-347c-bc98-9688b1b30792, UPDATED_DTTM = 2024-06-21 16:21:31.448, CREATIVE_VERSION_ID = , CONTROL_GROUP_FLG = , IDENTITY_ID = 5092beb6-3796-347c-bc98-9688b1b30792, SPOT_ID = , LEAD_ID = 5092beb6-3796-347c-bc98-9688b1b30792_06b9ab19-a082-4bc9-9858-47595f992ca8_*************, CREATIVE_ID = , CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = XNA, CONTACT_POLICY_PRODUCT_CODE = XNA, SUBJECT_ID = b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = 0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG, CAMP_SUBTYPE_CODE = XNA, CONTACT_POLICY_TYPE_CODE = XNA, COMM_TYPE_CODE = XNA, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = , CONTACT_DTTM = 2024-06-21 16:21:31.304, CONTACT_DT = 2024-06-21 00:00:00.0, RTC_ID = 06b9ab19-a082-4bc9-9858-47595f992ca8, CAMPAIGN_MESSAGE_CD = -1, AUD_OCCURENCE_ID = , CONTACT_ID = 1b0e8a1a-412b-4de2-873b-ba17768e75bd, EMAIL_IMPRINT_URL = , SESSION_ID = b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c, TASK_ID = ********-4dff-43c7-92a6-5a74243beb01, CONTACT_CHANNEL_CD = XNA, INSERTED_DTTM = 2024-06-21 16:21:31.448, 
2024-06-21 16:21:31,448  -thread-13  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-21 16:21:31,461  -thread-13  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-21 16:21:31,533  -thread-14  INFO   c.a.c.a.Event    Event received, event: c_X_phsjkjjd, will be processed...
2024-06-21 16:21:31,533  -thread-14  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"externalCode":"TSK_97","channel_user_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","channelType":"external","outboundProperties.primaryVisualization":"BUTTON","Image":"fsgfsdg","creative_id":"4f2a7d44-1fa0-41aa-b693-e3ac21b3670e","vid":"5092beb6-3796-347c-bc98-9688b1b30792","internalTenantId":"********","sentEmail":"Y","response_tracking_code":"37bd20de-68e6-43cd-ba5b-8c330d246c10","primaryClickMeaning":"DISMISS_CLICK","activity_id":"23171abe-f2d0-4a63-a054-96bc8f250fe5","creative_version_id":"jgu9tP2Z2Dt.Cx_zFynwJ4fPlpfhUo4Y","eventName":"c_X_phsjkjjd","activity_trigger_event_id":"a7121ab1-bdda-4092-83aa-f61c05d438d8","event_channel":"external","task_version_id":"xyXsR3hjHp6NrAkCFPc9A35a4lKN2s6C","textEmailContact":"<EMAIL>","primaryVisualization":"BUTTON","activity_task_type":"EVENT","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"5092beb6-3796-347c-bc98-9688b1b30792","guid":"bffb5c1d-f659-4ac4-a0c4-8013e4fb67ef","applicationId":"SAS360Proxy","communicationKind":"MARKETING","eventDesignedName":"c_X_phsjkjjd","subject_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","outboundProperties.timeSlot":"9-18","generatedTimestamp":"*************","timeSlot":"9-18","session":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","screen_info":"x@","task_id":"2b772d1a-68ba-4444-889d-2800218ec724","outboundProperties.Image":"fsgfsdg","activity_ia_tag_value":"a7121ab1-bdda-4092-83aa-f61c05d438d8","channel_user_type":"subject_id","parent_event":"external","eventname":"c_X_phsjkjjd","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"bc324933-5f23-45a0-80c1-34ce9e9aadd8","variant_id":"0","outboundProperties.communicationKind":"MARKETING","outboundProperties.primaryClickMeaning":"DISMISS_CLICK","creative_content":"fsdafsdfgsfdgsfd","parent_eventname":"Ext_Sit_Event","event":"c_X_phsjkjjd","channelId":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","timestamp":"*************","internal_tenant_id":"********","Text":"Test CH","message_id":"0","sessionId":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","event_category":"unifiedAndEngage","event_designed_name":"c_X_phsjkjjd","account":"f0cb22506600016b1805ee8b"},"rowKey":"bffb5c1d-f659-4ac4-a0c4-8013e4fb67ef"}
2024-06-21 16:21:31,535  -thread-14  ERROR  c.a.c.c.CampaignCache    Exception()
java.sql.SQLSyntaxErrorException: ORA-00942: table or view does not exist

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForDescribe(T4CPreparedStatement.java:807)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:983)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1168)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.executeQuery(OraclePreparedStatement.java:3713)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1167)
	at cz.ab.ci360.cache.CampaignCache.loadDatabaseCampaignCp(CampaignCache.java:85)
	at cz.ab.ci360.cache.CampaignCache.getCamapign(CampaignCache.java:53)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:101)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-00942: table or view does not exist

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 23 common frames omitted
2024-06-21 16:21:31,535  -thread-14  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = 5092beb6-3796-347c-bc98-9688b1b30792, UPDATED_DTTM = 2024-06-21 16:21:31.535, CREATIVE_VERSION_ID = jgu9tP2Z2Dt.Cx_zFynwJ4fPlpfhUo4Y, CONTROL_GROUP_FLG = , IDENTITY_ID = 5092beb6-3796-347c-bc98-9688b1b30792, SPOT_ID = , LEAD_ID = 5092beb6-3796-347c-bc98-9688b1b30792_37bd20de-68e6-43cd-ba5b-8c330d246c10_*************, CREATIVE_ID = 4f2a7d44-1fa0-41aa-b693-e3ac21b3670e, CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = XNA, CONTACT_POLICY_PRODUCT_CODE = XNA, SUBJECT_ID = b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = xyXsR3hjHp6NrAkCFPc9A35a4lKN2s6C, CAMP_SUBTYPE_CODE = XNA, CONTACT_POLICY_TYPE_CODE = XNA, COMM_TYPE_CODE = XNA, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = , CONTACT_DTTM = 2024-06-21 16:21:31.381, CONTACT_DT = 2024-06-21 00:00:00.0, RTC_ID = 37bd20de-68e6-43cd-ba5b-8c330d246c10, CAMPAIGN_MESSAGE_CD = -1, AUD_OCCURENCE_ID = , CONTACT_ID = bffb5c1d-f659-4ac4-a0c4-8013e4fb67ef, EMAIL_IMPRINT_URL = , SESSION_ID = b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c, TASK_ID = 2b772d1a-68ba-4444-889d-2800218ec724, CONTACT_CHANNEL_CD = XNA, INSERTED_DTTM = 2024-06-21 16:21:31.535, 
2024-06-21 16:21:31,536  -thread-14  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-21 16:21:31,540  -thread-14  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-21 16:21:32,789  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:22:32,796  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:23:18,925  -thread-16  INFO   c.a.c.a.Event    Event received, event: c_DCH, will be processed...
2024-06-21 16:23:18,925  -thread-16  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","session":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"subject_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"5092beb6-3796-347c-bc98-9688b1b30792","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","sentEmail":"Y","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","textEmailContact":"<EMAIL>","Text":"Test CH","message_id":"0","sessionId":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","event_category":"unifiedAndEngage","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"5092beb6-3796-347c-bc98-9688b1b30792","guid":"ca23d0ef-8449-41be-9690-dbd27441ad8e","applicationId":"SAS360Proxy","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"ca23d0ef-8449-41be-9690-dbd27441ad8e"}
2024-06-21 16:23:18,926  -thread-15  INFO   c.a.c.a.Event    Event received, event: c_X_phsjkjjd, will be processed...
2024-06-21 16:23:18,926  -thread-15  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"externalCode":"TSK_97","channel_user_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","channelType":"external","outboundProperties.primaryVisualization":"BUTTON","Image":"fsgfsdg","creative_id":"4f2a7d44-1fa0-41aa-b693-e3ac21b3670e","vid":"5092beb6-3796-347c-bc98-9688b1b30792","internalTenantId":"********","sentEmail":"Y","response_tracking_code":"37bd20de-68e6-43cd-ba5b-8c330d246c10","primaryClickMeaning":"DISMISS_CLICK","activity_id":"23171abe-f2d0-4a63-a054-96bc8f250fe5","creative_version_id":"jgu9tP2Z2Dt.Cx_zFynwJ4fPlpfhUo4Y","eventName":"c_X_phsjkjjd","activity_trigger_event_id":"a7121ab1-bdda-4092-83aa-f61c05d438d8","event_channel":"external","task_version_id":"xyXsR3hjHp6NrAkCFPc9A35a4lKN2s6C","textEmailContact":"<EMAIL>","primaryVisualization":"BUTTON","activity_task_type":"EVENT","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"5092beb6-3796-347c-bc98-9688b1b30792","guid":"a448978b-761a-495f-bed9-f02cc7677429","applicationId":"SAS360Proxy","communicationKind":"MARKETING","eventDesignedName":"c_X_phsjkjjd","subject_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","outboundProperties.timeSlot":"9-18","generatedTimestamp":"*************","timeSlot":"9-18","session":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","screen_info":"x@","task_id":"2b772d1a-68ba-4444-889d-2800218ec724","outboundProperties.Image":"fsgfsdg","activity_ia_tag_value":"a7121ab1-bdda-4092-83aa-f61c05d438d8","channel_user_type":"subject_id","parent_event":"external","eventname":"c_X_phsjkjjd","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"bc324933-5f23-45a0-80c1-34ce9e9aadd8","variant_id":"0","outboundProperties.communicationKind":"MARKETING","outboundProperties.primaryClickMeaning":"DISMISS_CLICK","creative_content":"fsdafsdfgsfdgsfd","parent_eventname":"Ext_Sit_Event","event":"c_X_phsjkjjd","channelId":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","timestamp":"*************","internal_tenant_id":"********","Text":"Test CH","message_id":"0","sessionId":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","event_category":"unifiedAndEngage","event_designed_name":"c_X_phsjkjjd","account":"f0cb22506600016b1805ee8b"},"rowKey":"a448978b-761a-495f-bed9-f02cc7677429"}
2024-06-21 16:23:18,927  -thread-16  ERROR  c.a.c.c.CampaignCache    Exception()
java.sql.SQLSyntaxErrorException: ORA-00942: table or view does not exist

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForDescribe(T4CPreparedStatement.java:807)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:983)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1168)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.executeQuery(OraclePreparedStatement.java:3713)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1167)
	at cz.ab.ci360.cache.CampaignCache.loadDatabaseCampaignCp(CampaignCache.java:85)
	at cz.ab.ci360.cache.CampaignCache.getCamapign(CampaignCache.java:53)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:101)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-00942: table or view does not exist

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 23 common frames omitted
2024-06-21 16:23:18,928  -thread-16  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = 5092beb6-3796-347c-bc98-9688b1b30792, UPDATED_DTTM = 2024-06-21 16:23:18.928, CREATIVE_VERSION_ID = , CONTROL_GROUP_FLG = , IDENTITY_ID = 5092beb6-3796-347c-bc98-9688b1b30792, SPOT_ID = , LEAD_ID = 5092beb6-3796-347c-bc98-9688b1b30792_06b9ab19-a082-4bc9-9858-47595f992ca8_*************, CREATIVE_ID = , CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = XNA, CONTACT_POLICY_PRODUCT_CODE = XNA, SUBJECT_ID = b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = 0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG, CAMP_SUBTYPE_CODE = XNA, CONTACT_POLICY_TYPE_CODE = XNA, COMM_TYPE_CODE = XNA, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = , CONTACT_DTTM = 2024-06-21 16:23:18.674, CONTACT_DT = 2024-06-21 00:00:00.0, RTC_ID = 06b9ab19-a082-4bc9-9858-47595f992ca8, CAMPAIGN_MESSAGE_CD = -1, AUD_OCCURENCE_ID = , CONTACT_ID = ca23d0ef-8449-41be-9690-dbd27441ad8e, EMAIL_IMPRINT_URL = , SESSION_ID = b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c, TASK_ID = ********-4dff-43c7-92a6-5a74243beb01, CONTACT_CHANNEL_CD = XNA, INSERTED_DTTM = 2024-06-21 16:23:18.928, 
2024-06-21 16:23:18,928  -thread-16  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-21 16:23:18,929  -thread-15  ERROR  c.a.c.c.CampaignCache    Exception()
java.sql.SQLSyntaxErrorException: ORA-00942: table or view does not exist

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForDescribe(T4CPreparedStatement.java:807)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:983)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1168)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.executeQuery(OraclePreparedStatement.java:3713)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1167)
	at cz.ab.ci360.cache.CampaignCache.loadDatabaseCampaignCp(CampaignCache.java:85)
	at cz.ab.ci360.cache.CampaignCache.getCamapign(CampaignCache.java:53)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:101)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-00942: table or view does not exist

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 23 common frames omitted
2024-06-21 16:23:18,930  -thread-15  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = 5092beb6-3796-347c-bc98-9688b1b30792, UPDATED_DTTM = 2024-06-21 16:23:18.93, CREATIVE_VERSION_ID = jgu9tP2Z2Dt.Cx_zFynwJ4fPlpfhUo4Y, CONTROL_GROUP_FLG = , IDENTITY_ID = 5092beb6-3796-347c-bc98-9688b1b30792, SPOT_ID = , LEAD_ID = 5092beb6-3796-347c-bc98-9688b1b30792_37bd20de-68e6-43cd-ba5b-8c330d246c10_*************, CREATIVE_ID = 4f2a7d44-1fa0-41aa-b693-e3ac21b3670e, CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = XNA, CONTACT_POLICY_PRODUCT_CODE = XNA, SUBJECT_ID = b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = xyXsR3hjHp6NrAkCFPc9A35a4lKN2s6C, CAMP_SUBTYPE_CODE = XNA, CONTACT_POLICY_TYPE_CODE = XNA, COMM_TYPE_CODE = XNA, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = , CONTACT_DTTM = 2024-06-21 16:23:18.671, CONTACT_DT = 2024-06-21 00:00:00.0, RTC_ID = 37bd20de-68e6-43cd-ba5b-8c330d246c10, CAMPAIGN_MESSAGE_CD = -1, AUD_OCCURENCE_ID = , CONTACT_ID = a448978b-761a-495f-bed9-f02cc7677429, EMAIL_IMPRINT_URL = , SESSION_ID = b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c, TASK_ID = 2b772d1a-68ba-4444-889d-2800218ec724, CONTACT_CHANNEL_CD = XNA, INSERTED_DTTM = 2024-06-21 16:23:18.93, 
2024-06-21 16:23:18,930  -thread-15  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-21 16:23:18,933  -thread-15  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-21 16:23:18,934  -thread-16  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-21 16:23:32,803  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:24:32,810  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:25:32,818  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:26:32,825  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:27:32,832  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:28:32,839  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:29:32,845  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:29:57,059  -thread-17  INFO   c.a.c.a.Event    Event received, event: c_DCH, will be processed...
2024-06-21 16:29:57,059  -thread-17  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","session":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"subject_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"5092beb6-3796-347c-bc98-9688b1b30792","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","sentEmail":"Y","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","textEmailContact":"<EMAIL>","Text":"Test CH","message_id":"0","sessionId":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","event_category":"unifiedAndEngage","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"5092beb6-3796-347c-bc98-9688b1b30792","guid":"cbb05626-8d1f-4d2b-992b-09b0dcaef7ed","applicationId":"SAS360Proxy","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"cbb05626-8d1f-4d2b-992b-09b0dcaef7ed"}
2024-06-21 16:29:57,062  -thread-17  ERROR  c.a.c.c.CampaignCache    Exception()
java.sql.SQLSyntaxErrorException: ORA-00942: table or view does not exist

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForDescribe(T4CPreparedStatement.java:807)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:983)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1168)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.executeQuery(OraclePreparedStatement.java:3713)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1167)
	at cz.ab.ci360.cache.CampaignCache.loadDatabaseCampaignCp(CampaignCache.java:85)
	at cz.ab.ci360.cache.CampaignCache.getCamapign(CampaignCache.java:53)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:101)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-00942: table or view does not exist

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 23 common frames omitted
2024-06-21 16:29:57,063  -thread-17  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = 5092beb6-3796-347c-bc98-9688b1b30792, UPDATED_DTTM = 2024-06-21 16:29:57.062, CREATIVE_VERSION_ID = , CONTROL_GROUP_FLG = , IDENTITY_ID = 5092beb6-3796-347c-bc98-9688b1b30792, SPOT_ID = , LEAD_ID = 5092beb6-3796-347c-bc98-9688b1b30792_06b9ab19-a082-4bc9-9858-47595f992ca8_*************, CREATIVE_ID = , CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = XNA, CONTACT_POLICY_PRODUCT_CODE = XNA, SUBJECT_ID = b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = 0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG, CAMP_SUBTYPE_CODE = XNA, CONTACT_POLICY_TYPE_CODE = XNA, COMM_TYPE_CODE = XNA, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = , CONTACT_DTTM = 2024-06-21 16:29:56.888, CONTACT_DT = 2024-06-21 00:00:00.0, RTC_ID = 06b9ab19-a082-4bc9-9858-47595f992ca8, CAMPAIGN_MESSAGE_CD = -1, AUD_OCCURENCE_ID = , CONTACT_ID = cbb05626-8d1f-4d2b-992b-09b0dcaef7ed, EMAIL_IMPRINT_URL = , SESSION_ID = b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c, TASK_ID = ********-4dff-43c7-92a6-5a74243beb01, CONTACT_CHANNEL_CD = XNA, INSERTED_DTTM = 2024-06-21 16:29:57.062, 
2024-06-21 16:29:57,063  -thread-17  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-21 16:29:57,068  -thread-17  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-21 16:29:57,118  -thread-18  INFO   c.a.c.a.Event    Event received, event: c_X_phsjkjjd, will be processed...
2024-06-21 16:29:57,118  -thread-18  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"externalCode":"TSK_97","channel_user_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","channelType":"external","outboundProperties.primaryVisualization":"BUTTON","Image":"fsgfsdg","creative_id":"4f2a7d44-1fa0-41aa-b693-e3ac21b3670e","vid":"5092beb6-3796-347c-bc98-9688b1b30792","internalTenantId":"********","sentEmail":"Y","response_tracking_code":"37bd20de-68e6-43cd-ba5b-8c330d246c10","primaryClickMeaning":"DISMISS_CLICK","activity_id":"23171abe-f2d0-4a63-a054-96bc8f250fe5","creative_version_id":"jgu9tP2Z2Dt.Cx_zFynwJ4fPlpfhUo4Y","eventName":"c_X_phsjkjjd","activity_trigger_event_id":"a7121ab1-bdda-4092-83aa-f61c05d438d8","event_channel":"external","task_version_id":"xyXsR3hjHp6NrAkCFPc9A35a4lKN2s6C","textEmailContact":"<EMAIL>","primaryVisualization":"BUTTON","activity_task_type":"EVENT","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"5092beb6-3796-347c-bc98-9688b1b30792","guid":"cc249666-d8e4-49dd-9994-d1a72a162ee0","applicationId":"SAS360Proxy","communicationKind":"MARKETING","eventDesignedName":"c_X_phsjkjjd","subject_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","outboundProperties.timeSlot":"9-18","generatedTimestamp":"*************","timeSlot":"9-18","session":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","screen_info":"x@","task_id":"2b772d1a-68ba-4444-889d-2800218ec724","outboundProperties.Image":"fsgfsdg","activity_ia_tag_value":"a7121ab1-bdda-4092-83aa-f61c05d438d8","channel_user_type":"subject_id","parent_event":"external","eventname":"c_X_phsjkjjd","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"bc324933-5f23-45a0-80c1-34ce9e9aadd8","variant_id":"0","outboundProperties.communicationKind":"MARKETING","outboundProperties.primaryClickMeaning":"DISMISS_CLICK","creative_content":"fsdafsdfgsfdgsfd","parent_eventname":"Ext_Sit_Event","event":"c_X_phsjkjjd","channelId":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","timestamp":"*************","internal_tenant_id":"********","Text":"Test CH","message_id":"0","sessionId":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","event_category":"unifiedAndEngage","event_designed_name":"c_X_phsjkjjd","account":"f0cb22506600016b1805ee8b"},"rowKey":"cc249666-d8e4-49dd-9994-d1a72a162ee0"}
2024-06-21 16:29:57,120  -thread-18  ERROR  c.a.c.c.CampaignCache    Exception()
java.sql.SQLSyntaxErrorException: ORA-00942: table or view does not exist

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForDescribe(T4CPreparedStatement.java:807)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:983)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1168)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.executeQuery(OraclePreparedStatement.java:3713)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1167)
	at cz.ab.ci360.cache.CampaignCache.loadDatabaseCampaignCp(CampaignCache.java:85)
	at cz.ab.ci360.cache.CampaignCache.getCamapign(CampaignCache.java:53)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:101)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-00942: table or view does not exist

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 23 common frames omitted
2024-06-21 16:29:57,121  -thread-18  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = 5092beb6-3796-347c-bc98-9688b1b30792, UPDATED_DTTM = 2024-06-21 16:29:57.12, CREATIVE_VERSION_ID = jgu9tP2Z2Dt.Cx_zFynwJ4fPlpfhUo4Y, CONTROL_GROUP_FLG = , IDENTITY_ID = 5092beb6-3796-347c-bc98-9688b1b30792, SPOT_ID = , LEAD_ID = 5092beb6-3796-347c-bc98-9688b1b30792_37bd20de-68e6-43cd-ba5b-8c330d246c10_*************, CREATIVE_ID = 4f2a7d44-1fa0-41aa-b693-e3ac21b3670e, CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = XNA, CONTACT_POLICY_PRODUCT_CODE = XNA, SUBJECT_ID = b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = xyXsR3hjHp6NrAkCFPc9A35a4lKN2s6C, CAMP_SUBTYPE_CODE = XNA, CONTACT_POLICY_TYPE_CODE = XNA, COMM_TYPE_CODE = XNA, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = , CONTACT_DTTM = 2024-06-21 16:29:56.96, CONTACT_DT = 2024-06-21 00:00:00.0, RTC_ID = 37bd20de-68e6-43cd-ba5b-8c330d246c10, CAMPAIGN_MESSAGE_CD = -1, AUD_OCCURENCE_ID = , CONTACT_ID = cc249666-d8e4-49dd-9994-d1a72a162ee0, EMAIL_IMPRINT_URL = , SESSION_ID = b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c, TASK_ID = 2b772d1a-68ba-4444-889d-2800218ec724, CONTACT_CHANNEL_CD = XNA, INSERTED_DTTM = 2024-06-21 16:29:57.12, 
2024-06-21 16:29:57,121  -thread-18  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-21 16:29:57,126  -thread-18  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-21 16:30:32,852  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:31:32,859  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:32:32,865  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:33:11,936  -thread-19  INFO   c.a.c.a.Event    Event received, event: c_DCH, will be processed...
2024-06-21 16:33:11,937  -thread-19  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","session":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"subject_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"5092beb6-3796-347c-bc98-9688b1b30792","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","sentEmail":"Y","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","text":"Test CH","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","textEmailContact":"<EMAIL>","message_id":"0","sessionId":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","event_category":"unifiedAndEngage","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"5092beb6-3796-347c-bc98-9688b1b30792","guid":"14c05b93-e64a-4a59-ae19-ebc8d471fdf6","applicationId":"SAS360Proxy","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"14c05b93-e64a-4a59-ae19-ebc8d471fdf6"}
2024-06-21 16:33:11,937  -thread-20  INFO   c.a.c.a.Event    Event received, event: c_X_phsjkjjd, will be processed...
2024-06-21 16:33:11,937  -thread-20  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"externalCode":"TSK_97","channel_user_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","channelType":"external","outboundProperties.primaryVisualization":"BUTTON","Image":"fsgfsdg","creative_id":"4f2a7d44-1fa0-41aa-b693-e3ac21b3670e","vid":"5092beb6-3796-347c-bc98-9688b1b30792","internalTenantId":"********","sentEmail":"Y","response_tracking_code":"37bd20de-68e6-43cd-ba5b-8c330d246c10","primaryClickMeaning":"DISMISS_CLICK","activity_id":"23171abe-f2d0-4a63-a054-96bc8f250fe5","creative_version_id":"jgu9tP2Z2Dt.Cx_zFynwJ4fPlpfhUo4Y","eventName":"c_X_phsjkjjd","text":"Test CH","activity_trigger_event_id":"a7121ab1-bdda-4092-83aa-f61c05d438d8","event_channel":"external","task_version_id":"xyXsR3hjHp6NrAkCFPc9A35a4lKN2s6C","textEmailContact":"<EMAIL>","primaryVisualization":"BUTTON","activity_task_type":"EVENT","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"5092beb6-3796-347c-bc98-9688b1b30792","guid":"83ed82be-3a88-48e1-b8a7-3f6460ba069c","applicationId":"SAS360Proxy","communicationKind":"MARKETING","eventDesignedName":"c_X_phsjkjjd","subject_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","outboundProperties.timeSlot":"9-18","generatedTimestamp":"*************","timeSlot":"9-18","session":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","screen_info":"x@","task_id":"2b772d1a-68ba-4444-889d-2800218ec724","outboundProperties.Image":"fsgfsdg","activity_ia_tag_value":"a7121ab1-bdda-4092-83aa-f61c05d438d8","channel_user_type":"subject_id","parent_event":"external","eventname":"c_X_phsjkjjd","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"bc324933-5f23-45a0-80c1-34ce9e9aadd8","variant_id":"0","outboundProperties.communicationKind":"MARKETING","outboundProperties.primaryClickMeaning":"DISMISS_CLICK","creative_content":"fsdafsdfgsfdgsfd","parent_eventname":"Ext_Sit_Event","event":"c_X_phsjkjjd","channelId":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","timestamp":"*************","internal_tenant_id":"********","message_id":"0","sessionId":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","event_category":"unifiedAndEngage","event_designed_name":"c_X_phsjkjjd","account":"f0cb22506600016b1805ee8b"},"rowKey":"83ed82be-3a88-48e1-b8a7-3f6460ba069c"}
2024-06-21 16:33:11,939  -thread-19  ERROR  c.a.c.c.CampaignCache    Exception()
java.sql.SQLSyntaxErrorException: ORA-00942: table or view does not exist

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForDescribe(T4CPreparedStatement.java:807)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:983)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1168)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.executeQuery(OraclePreparedStatement.java:3713)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1167)
	at cz.ab.ci360.cache.CampaignCache.loadDatabaseCampaignCp(CampaignCache.java:85)
	at cz.ab.ci360.cache.CampaignCache.getCamapign(CampaignCache.java:53)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:101)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-00942: table or view does not exist

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 23 common frames omitted
2024-06-21 16:33:11,939  -thread-19  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = 5092beb6-3796-347c-bc98-9688b1b30792, UPDATED_DTTM = 2024-06-21 16:33:11.939, CREATIVE_VERSION_ID = , CONTROL_GROUP_FLG = , IDENTITY_ID = 5092beb6-3796-347c-bc98-9688b1b30792, SPOT_ID = , LEAD_ID = 5092beb6-3796-347c-bc98-9688b1b30792_06b9ab19-a082-4bc9-9858-47595f992ca8_*************, CREATIVE_ID = , CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = XNA, CONTACT_POLICY_PRODUCT_CODE = XNA, SUBJECT_ID = b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = 0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG, CAMP_SUBTYPE_CODE = XNA, CONTACT_POLICY_TYPE_CODE = XNA, COMM_TYPE_CODE = XNA, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = , CONTACT_DTTM = 2024-06-21 16:33:11.754, CONTACT_DT = 2024-06-21 00:00:00.0, RTC_ID = 06b9ab19-a082-4bc9-9858-47595f992ca8, CAMPAIGN_MESSAGE_CD = -1, AUD_OCCURENCE_ID = , CONTACT_ID = 14c05b93-e64a-4a59-ae19-ebc8d471fdf6, EMAIL_IMPRINT_URL = , SESSION_ID = b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c, TASK_ID = ********-4dff-43c7-92a6-5a74243beb01, CONTACT_CHANNEL_CD = XNA, INSERTED_DTTM = 2024-06-21 16:33:11.939, 
2024-06-21 16:33:11,939  -thread-19  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-21 16:33:11,941  -thread-20  ERROR  c.a.c.c.CampaignCache    Exception()
java.sql.SQLSyntaxErrorException: ORA-00942: table or view does not exist

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForDescribe(T4CPreparedStatement.java:807)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:983)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1168)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.executeQuery(OraclePreparedStatement.java:3713)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1167)
	at cz.ab.ci360.cache.CampaignCache.loadDatabaseCampaignCp(CampaignCache.java:85)
	at cz.ab.ci360.cache.CampaignCache.getCamapign(CampaignCache.java:53)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:101)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-00942: table or view does not exist

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 23 common frames omitted
2024-06-21 16:33:11,944  -thread-20  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = 5092beb6-3796-347c-bc98-9688b1b30792, UPDATED_DTTM = 2024-06-21 16:33:11.941, CREATIVE_VERSION_ID = jgu9tP2Z2Dt.Cx_zFynwJ4fPlpfhUo4Y, CONTROL_GROUP_FLG = , IDENTITY_ID = 5092beb6-3796-347c-bc98-9688b1b30792, SPOT_ID = , LEAD_ID = 5092beb6-3796-347c-bc98-9688b1b30792_37bd20de-68e6-43cd-ba5b-8c330d246c10_*************, CREATIVE_ID = 4f2a7d44-1fa0-41aa-b693-e3ac21b3670e, CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = XNA, CONTACT_POLICY_PRODUCT_CODE = XNA, SUBJECT_ID = b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = xyXsR3hjHp6NrAkCFPc9A35a4lKN2s6C, CAMP_SUBTYPE_CODE = XNA, CONTACT_POLICY_TYPE_CODE = XNA, COMM_TYPE_CODE = XNA, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = , CONTACT_DTTM = 2024-06-21 16:33:11.779, CONTACT_DT = 2024-06-21 00:00:00.0, RTC_ID = 37bd20de-68e6-43cd-ba5b-8c330d246c10, CAMPAIGN_MESSAGE_CD = -1, AUD_OCCURENCE_ID = , CONTACT_ID = 83ed82be-3a88-48e1-b8a7-3f6460ba069c, EMAIL_IMPRINT_URL = , SESSION_ID = b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c, TASK_ID = 2b772d1a-68ba-4444-889d-2800218ec724, CONTACT_CHANNEL_CD = XNA, INSERTED_DTTM = 2024-06-21 16:33:11.941, 
2024-06-21 16:33:11,944  -thread-20  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-21 16:33:11,945  -thread-19  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-21 16:33:11,947  -thread-20  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-21 16:33:30,310  -thread-21  INFO   c.a.c.a.Event    Event received, event: c_send, will be processed...
2024-06-21 16:33:30,310  -thread-21  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","testFlag":"0","generatedTimestamp":"1718980410099","screen_info":"x@","task_id":"eadf9947-5e69-4e7a-9228-3326a922bdcb","channelType":"email","emailSubject":"TEST SIT EMAIL","eventname":"c_send","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"5092beb6-3796-347c-bc98-9688b1b30792","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"a47c0bb9-9e3b-4dc7-8cdc-0e35efaf6766","variant_id":"0","internalTenantId":"********","response_tracking_code":"af3610c7-ead4-4b06-8e88-d8985c9df954","variant":"0","eventName":"c_send","event":"c_send","timestamp":"1718980410099","event_channel":"email","internal_tenant_id":"********","task_version_id":"ILCptqICvNX61e0N64fqrQ1.9qkEMj9u","recipientDomain":"sas.com","goal_guid":"a0672149-b594-4203-a50a-57d26706852b","event_category":"unifiedAndEngage","imprint_id":"8f80aaed-eab4-4cd8-a428-4421833f0e66","emailImprintURL":"https://d3on7v574i947w.cloudfront.net/8/cixniceu/8f80aaed-eab4-4cd8-a428-4421833f0e66.html","emailSendAgentId":"0faa5245-b66c-41b3-8eeb-defa88836703","datahub_id":"5092beb6-3796-347c-bc98-9688b1b30792","guid":"89f4edf9-6c73-4f61-b8f2-335217aa1562","event_designed_name":"c_send","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_send"},"rowKey":"89f4edf9-6c73-4f61-b8f2-335217aa1562"}
2024-06-21 16:33:30,351  -thread-21  ERROR  c.a.c.c.Http    Response, Code: 407 , Message: 
2024-06-21 16:33:30,351  -thread-21  DEBUG  c.a.c.a.CI360Api    Task API json: 
2024-06-21 16:33:30,351  -thread-21  ERROR  c.a.c.a.CI360Api    Exception()
org.json.JSONException: A JSONObject text must begin with '{' at 0 [character 1 line 1]
	at org.json.JSONTokener.syntaxError(JSONTokener.java:503)
	at org.json.JSONObject.<init>(JSONObject.java:213)
	at org.json.JSONObject.<init>(JSONObject.java:430)
	at cz.ab.ci360.agent_ch_stream.CI360Api.getTaskCustomProperties(CI360Api.java:43)
	at cz.ab.ci360.cache.TaskCache.getCI360ApiTaskCp(TaskCache.java:92)
	at cz.ab.ci360.cache.TaskCache.getTask(TaskCache.java:68)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:99)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-21 16:33:30,356  -thread-21  ERROR  c.a.c.c.TaskCache    Exception()
java.sql.BatchUpdateException: ORA-00001: unique constraint (APP_CAMPAIGN_CDM2.SYS_C009559860) violated

	at oracle.jdbc.driver.OraclePreparedStatement.executeLargeBatch(OraclePreparedStatement.java:9711)
	at oracle.jdbc.driver.T4CPreparedStatement.executeLargeBatch(T4CPreparedStatement.java:1447)
	at oracle.jdbc.driver.OraclePreparedStatement.executeBatch(OraclePreparedStatement.java:9487)
	at oracle.jdbc.driver.OracleStatementWrapper.executeBatch(OracleStatementWrapper.java:237)
	at cz.ab.ci360.cache.TaskCache.saveTaskCpToCache(TaskCache.java:210)
	at cz.ab.ci360.cache.TaskCache.getTask(TaskCache.java:72)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:99)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-21 16:33:30,358  -thread-21  ERROR  c.a.c.c.CampaignCache    Exception()
java.sql.SQLSyntaxErrorException: ORA-00942: table or view does not exist

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForDescribe(T4CPreparedStatement.java:807)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:983)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1168)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.executeQuery(OraclePreparedStatement.java:3713)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1167)
	at cz.ab.ci360.cache.CampaignCache.loadDatabaseCampaignCp(CampaignCache.java:85)
	at cz.ab.ci360.cache.CampaignCache.getCamapign(CampaignCache.java:53)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:101)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-00942: table or view does not exist

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 23 common frames omitted
2024-06-21 16:33:30,358  -thread-21  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = 5092beb6-3796-347c-bc98-9688b1b30792, UPDATED_DTTM = 2024-06-21 16:33:30.358, CREATIVE_VERSION_ID = , CONTROL_GROUP_FLG = , IDENTITY_ID = 5092beb6-3796-347c-bc98-9688b1b30792, SPOT_ID = , LEAD_ID = 5092beb6-3796-347c-bc98-9688b1b30792_af3610c7-ead4-4b06-8e88-d8985c9df954_1718980410099, CREATIVE_ID = , CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = XNA, CONTACT_POLICY_PRODUCT_CODE = XNA, SUBJECT_ID = b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c, MESSAGE_ID = , IMPRINT_ID = 8f80aaed-eab4-4cd8-a428-4421833f0e66, CI360_CONTACT_CHANNEL_NM = email, TASK_VERSION_ID = ILCptqICvNX61e0N64fqrQ1.9qkEMj9u, CAMP_SUBTYPE_CODE = XNA, CONTACT_POLICY_TYPE_CODE = XNA, COMM_TYPE_CODE = XNA, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = , CONTACT_DTTM = 2024-06-21 16:33:30.099, CONTACT_DT = 2024-06-21 00:00:00.0, RTC_ID = af3610c7-ead4-4b06-8e88-d8985c9df954, CAMPAIGN_MESSAGE_CD = -1, AUD_OCCURENCE_ID = , CONTACT_ID = 89f4edf9-6c73-4f61-b8f2-335217aa1562, EMAIL_IMPRINT_URL = https://d3on7v574i947w.cloudfront.net/8/cixniceu/8f80aaed-eab4-4cd8-a428-4421833f0e66.html, SESSION_ID = , TASK_ID = eadf9947-5e69-4e7a-9228-3326a922bdcb, CONTACT_CHANNEL_CD = Email, INSERTED_DTTM = 2024-06-21 16:33:30.358, 
2024-06-21 16:33:30,358  -thread-21  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-21 16:33:30,363  -thread-21  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-21 16:33:30,363  -thread-21  ERROR  c.a.c.a.Event    Error when processing event.
java.lang.NumberFormatException: For input string: "b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c"
	at java.base/java.lang.NumberFormatException.forInputString(NumberFormatException.java:65)
	at java.base/java.lang.Long.parseLong(Long.java:692)
	at java.base/java.lang.Long.parseLong(Long.java:817)
	at cz.ab.ci360.agent_ch_stream.Event.processEmail(Event.java:133)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:119)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-21 16:33:30,363  -thread-21  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","testFlag":"0","generatedTimestamp":"1718980410099","screen_info":"x@","task_id":"eadf9947-5e69-4e7a-9228-3326a922bdcb","channelType":"email","emailSubject":"TEST SIT EMAIL","eventname":"c_send","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"5092beb6-3796-347c-bc98-9688b1b30792","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"a47c0bb9-9e3b-4dc7-8cdc-0e35efaf6766","variant_id":"0","internalTenantId":"********","response_tracking_code":"af3610c7-ead4-4b06-8e88-d8985c9df954","variant":"0","eventName":"c_send","event":"c_send","timestamp":"1718980410099","event_channel":"email","internal_tenant_id":"********","task_version_id":"ILCptqICvNX61e0N64fqrQ1.9qkEMj9u","recipientDomain":"sas.com","goal_guid":"a0672149-b594-4203-a50a-57d26706852b","event_category":"unifiedAndEngage","imprint_id":"8f80aaed-eab4-4cd8-a428-4421833f0e66","emailImprintURL":"https://d3on7v574i947w.cloudfront.net/8/cixniceu/8f80aaed-eab4-4cd8-a428-4421833f0e66.html","emailSendAgentId":"0faa5245-b66c-41b3-8eeb-defa88836703","datahub_id":"5092beb6-3796-347c-bc98-9688b1b30792","guid":"89f4edf9-6c73-4f61-b8f2-335217aa1562","event_designed_name":"c_send","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_send"},"rowKey":"89f4edf9-6c73-4f61-b8f2-335217aa1562"}
2024-06-21 16:33:32,872  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:34:32,879  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:35:32,886  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:36:32,893  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:37:32,899  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:38:32,906  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:39:32,912  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:40:32,919  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:41:32,926  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:42:32,932  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:43:32,939  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:44:32,946  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:45:32,953  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:46:32,961  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:47:32,968  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:48:32,975  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:49:32,982  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:50:32,989  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:51:32,996  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:52:33,003  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:53:33,010  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:54:33,017  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:55:33,024  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:56:33,031  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:57:33,038  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:58:33,044  main        INFO   c.a.c.a.Agent    running
2024-06-21 16:59:33,051  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:00:33,058  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:01:33,064  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:02:33,071  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:03:33,078  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:04:33,085  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:05:33,092  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:06:33,099  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:07:33,106  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:08:33,113  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:09:33,120  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:10:33,127  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:11:33,133  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:12:33,140  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:13:33,147  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:14:33,154  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:15:33,161  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:16:33,169  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:17:33,175  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:18:33,182  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:19:33,188  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:20:33,195  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:21:33,203  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:22:33,210  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:23:33,218  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:24:33,225  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:25:33,233  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:26:33,240  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:27:33,247  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:28:33,254  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:29:33,261  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:30:33,268  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:31:33,276  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:32:33,283  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:33:33,291  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:34:33,298  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:35:33,306  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:36:33,313  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:37:33,321  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:38:33,328  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:39:33,335  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:40:33,341  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:41:33,348  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:42:33,355  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:43:33,362  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:44:33,369  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:45:33,375  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:46:33,383  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:47:33,390  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:48:33,397  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:49:33,405  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:50:33,413  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:51:33,420  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:52:33,427  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:53:33,434  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:54:33,441  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:55:33,447  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:56:33,454  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:57:33,460  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:58:33,466  main        INFO   c.a.c.a.Agent    running
2024-06-21 17:59:33,471  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:00:33,478  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:01:33,484  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:02:33,491  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:03:33,497  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:04:33,503  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:05:33,509  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:06:33,515  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:07:33,521  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:08:33,527  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:09:33,533  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:10:33,539  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:11:33,545  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:12:33,552  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:13:33,558  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:14:33,564  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:15:33,571  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:16:33,579  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:17:33,585  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:18:33,592  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:19:33,598  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:20:33,605  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:21:33,612  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:22:33,619  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:23:33,626  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:24:33,633  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:25:33,640  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:26:33,647  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:27:33,655  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:28:33,662  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:29:33,668  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:30:33,675  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:31:33,682  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:32:33,689  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:33:33,695  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:34:33,702  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:35:33,709  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:36:33,717  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:37:33,724  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:38:33,731  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:39:33,738  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:40:33,744  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:41:33,751  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:42:33,759  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:43:33,766  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:44:33,773  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:45:33,780  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:46:33,786  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:47:33,793  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:48:33,800  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:49:33,808  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:50:33,814  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:51:33,821  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:52:33,829  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:53:33,836  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:54:33,842  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:55:33,849  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:56:33,856  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:57:33,862  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:58:33,869  main        INFO   c.a.c.a.Agent    running
2024-06-21 18:59:33,876  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:00:33,883  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:01:33,890  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:02:33,896  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:03:33,903  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:04:33,910  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:05:33,917  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:06:33,925  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:07:33,932  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:08:33,939  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:09:33,946  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:10:33,953  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:11:33,960  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:12:33,967  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:13:33,975  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:14:33,982  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:15:33,989  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:16:33,996  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:17:34,002  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:18:34,008  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:19:34,016  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:20:34,023  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:21:34,030  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:22:34,037  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:23:34,044  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:24:34,052  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:25:34,059  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:26:34,066  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:27:34,073  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:28:34,080  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:29:34,087  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:30:34,094  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:31:34,101  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:32:34,109  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:33:34,116  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:34:34,123  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:35:34,129  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:36:34,136  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:37:34,142  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:38:34,149  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:39:34,156  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:40:34,163  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:41:34,170  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:42:34,177  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:43:34,183  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:44:34,191  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:45:34,198  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:46:34,205  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:47:34,212  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:48:34,219  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:49:34,226  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:50:34,233  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:51:34,239  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:52:34,246  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:53:34,252  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:54:34,259  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:55:34,266  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:56:34,273  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:57:34,280  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:58:34,287  main        INFO   c.a.c.a.Agent    running
2024-06-21 19:59:34,293  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:00:34,300  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:01:34,306  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:02:34,311  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:03:34,318  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:04:34,324  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:05:34,330  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:06:34,335  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:07:34,341  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:08:34,347  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:09:34,353  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:10:34,359  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:11:34,365  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:12:34,374  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:13:34,383  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:14:34,390  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:15:34,398  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:16:34,405  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:17:34,413  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:18:34,420  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:19:34,427  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:20:34,434  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:21:34,440  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:22:34,447  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:23:34,453  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:24:34,461  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:25:34,468  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:26:34,474  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:27:34,480  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:28:34,487  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:29:34,493  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:30:34,500  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:31:34,507  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:32:34,513  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:33:34,520  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:34:34,526  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:35:34,533  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:36:34,540  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:37:34,546  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:38:34,553  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:39:34,559  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:40:34,566  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:41:34,573  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:42:34,580  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:43:34,587  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:44:34,594  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:45:34,601  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:46:34,608  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:47:34,615  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:48:34,622  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:49:34,628  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:50:34,636  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:51:34,643  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:52:34,650  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:53:34,658  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:54:34,664  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:55:34,670  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:56:34,675  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:57:34,681  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:58:34,687  main        INFO   c.a.c.a.Agent    running
2024-06-21 20:59:34,693  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:00:34,699  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:01:34,706  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:02:34,713  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:03:34,720  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:04:34,727  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:05:34,734  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:06:34,742  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:07:34,749  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:08:34,756  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:09:34,762  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:10:34,768  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:11:34,773  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:12:34,779  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:13:34,785  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:14:34,791  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:15:34,799  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:16:34,805  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:17:34,812  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:18:34,818  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:19:34,825  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:20:34,831  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:21:34,837  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:22:34,844  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:23:34,851  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:24:34,858  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:25:34,865  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:26:34,872  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:27:34,879  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:28:34,886  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:29:34,892  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:30:34,899  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:31:34,906  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:32:34,913  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:33:34,919  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:34:34,926  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:35:34,933  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:36:34,939  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:37:34,946  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:38:34,952  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:39:34,959  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:40:34,965  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:41:34,972  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:42:34,978  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:43:34,985  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:44:34,993  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:45:34,999  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:46:35,006  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:47:35,012  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:48:35,019  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:49:35,026  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:50:35,033  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:51:35,040  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:52:35,046  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:53:35,053  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:54:35,060  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:55:35,067  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:56:35,074  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:57:35,081  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:58:35,088  main        INFO   c.a.c.a.Agent    running
2024-06-21 21:59:35,095  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:00:35,101  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:01:35,107  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:02:35,112  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:03:35,118  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:04:35,124  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:05:35,129  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:06:35,135  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:07:35,141  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:08:35,146  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:09:35,152  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:10:35,158  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:11:35,164  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:12:35,170  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:13:35,175  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:14:35,181  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:15:35,187  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:16:35,193  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:17:35,198  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:18:35,204  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:19:35,211  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:20:35,218  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:21:35,225  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:22:35,232  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:23:35,238  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:24:35,244  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:25:35,251  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:26:35,258  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:27:35,266  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:28:35,273  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:29:35,280  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:30:35,288  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:31:35,295  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:32:35,302  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:33:35,308  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:34:35,314  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:35:35,321  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:36:35,329  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:37:35,335  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:38:35,342  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:39:35,348  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:40:35,355  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:41:35,362  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:42:35,369  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:43:35,376  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:44:35,383  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:45:35,391  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:46:35,398  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:47:35,405  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:48:35,412  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:49:35,419  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:50:35,426  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:51:35,433  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:52:35,440  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:53:35,446  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:54:35,453  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:55:35,460  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:56:35,467  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:57:35,473  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:58:35,479  main        INFO   c.a.c.a.Agent    running
2024-06-21 22:59:35,486  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:00:35,492  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:01:35,499  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:02:35,506  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:03:35,512  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:04:35,519  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:05:35,525  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:06:35,532  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:07:35,538  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:08:35,544  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:09:35,550  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:10:35,556  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:11:35,565  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:12:35,571  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:13:35,580  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:14:35,587  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:15:35,594  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:16:35,601  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:17:35,608  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:18:35,614  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:19:35,622  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:20:35,628  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:21:35,635  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:22:35,641  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:23:35,647  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:24:35,654  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:25:35,660  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:26:35,666  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:27:35,673  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:28:35,679  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:29:35,685  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:30:35,692  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:31:35,698  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:32:35,704  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:33:35,710  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:34:35,716  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:35:35,721  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:36:35,728  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:37:35,735  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:38:35,741  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:39:35,748  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:40:35,754  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:41:35,761  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:42:35,767  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:43:35,773  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:44:35,780  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:45:35,786  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:46:35,793  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:47:35,799  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:48:35,806  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:49:35,813  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:50:35,820  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:51:35,826  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:52:35,833  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:53:35,839  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:54:35,845  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:55:35,852  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:56:35,858  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:57:35,865  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:58:35,871  main        INFO   c.a.c.a.Agent    running
2024-06-21 23:59:35,877  main        INFO   c.a.c.a.Agent    running
