2024-06-18 10:58:30,679  main        INFO   c.a.c.a.Agent    
2024-06-18 10:58:30,682  main        INFO   c.a.c.a.Agent    ----- starting -------------------------------------------------------
2024-06-18 10:58:30,687  main        INFO   c.a.c.a.Agent    App: /opt/agents/ch-stream/agent-ch-stream.jar
2024-06-18 10:58:30,694  main        INFO   c.a.c.a.Agent    Version: 1.0.0
2024-06-18 10:58:30,694  main        INFO   c.a.c.a.Agent    ----- configuration --------------------------------------------------
2024-06-18 10:58:30,697  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-18 10:58:30,706  main        INFO   c.a.c.c.Config    ci360.jwt=f0cb22506600016b1805ee8b
2024-06-18 10:58:30,706  main        INFO   c.a.c.c.Config    agent.max_threads=5000
2024-06-18 10:58:30,706  main        INFO   c.a.c.c.Config    kafka.topic=sentCampaingEmail
2024-06-18 10:58:30,707  main        INFO   c.a.c.c.Config    agent.kafka=false
2024-06-18 10:58:30,707  main        INFO   c.a.c.c.Config    proxy.server=proxy.np.ab
2024-06-18 10:58:30,707  main        INFO   c.a.c.c.Config    proxy.port=3128
2024-06-18 10:58:30,707  main        INFO   c.a.c.c.Config    proxy.realm=proxy
2024-06-18 10:58:30,707  main        INFO   c.a.c.c.Config    proxy.user=sascloud_user
2024-06-18 10:58:30,711  main        ERROR  c.a.c.a.Agent    Inicialization error. java.lang.IllegalArgumentException: Input byte array has incorrect ending byte at 28
java.lang.IllegalArgumentException: Input byte array has incorrect ending byte at 28
	at java.base/java.util.Base64$Decoder.decode0(Base64.java:774)
	at java.base/java.util.Base64$Decoder.decode(Base64.java:538)
	at java.base/java.util.Base64$Decoder.decode(Base64.java:561)
	at cz.ab.ci360.common.Util.base64Decode(Util.java:30)
	at cz.ab.ci360.common.Config.loadKeyValueBase64(Config.java:38)
	at cz.ab.ci360.common.ProxySetting.init(ProxySetting.java:18)
	at cz.ab.ci360.agent_ch_stream.Agent.main(Agent.java:82)
2024-06-18 10:58:30,711  main        INFO   c.a.c.a.Agent    Exit code: 1
2024-06-18 10:59:17,526  main        INFO   c.a.c.a.Agent    
2024-06-18 10:59:17,528  main        INFO   c.a.c.a.Agent    ----- starting -------------------------------------------------------
2024-06-18 10:59:17,534  main        INFO   c.a.c.a.Agent    App: /opt/agents/ch-stream/agent-ch-stream.jar
2024-06-18 10:59:17,538  main        INFO   c.a.c.a.Agent    Version: 1.0.0
2024-06-18 10:59:17,538  main        INFO   c.a.c.a.Agent    ----- configuration --------------------------------------------------
2024-06-18 10:59:17,542  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-18 10:59:17,543  main        INFO   c.a.c.c.Config    ci360.jwt=f0cb22506600016b1805ee8b
2024-06-18 10:59:17,543  main        INFO   c.a.c.c.Config    agent.max_threads=5000
2024-06-18 10:59:17,543  main        INFO   c.a.c.c.Config    kafka.topic=sentCampaingEmail
2024-06-18 10:59:17,543  main        INFO   c.a.c.c.Config    agent.kafka=false
2024-06-18 10:59:17,543  main        INFO   c.a.c.c.Config    proxy.server=proxy.np.ab
2024-06-18 10:59:17,543  main        INFO   c.a.c.c.Config    proxy.port=3128
2024-06-18 10:59:17,543  main        INFO   c.a.c.c.Config    proxy.realm=proxy
2024-06-18 10:59:17,544  main        INFO   c.a.c.c.Config    proxy.user=sascloud_user
2024-06-18 10:59:17,544  main        INFO   c.a.c.c.Config    proxy.non_proxy_hosts=app-sas-test.np.ab
2024-06-18 10:59:17,544  main        INFO   c.a.c.a.Agent    test mode = false
2024-06-18 10:59:17,545  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-18 10:59:17,546  main        INFO   c.a.c.c.Config    db.user=APP_CAMPAIGN_CDM2
2024-06-18 10:59:17,546  main        INFO   c.a.c.c.Config    db.database=APP_CAMPAIGN_CDM2
2024-06-18 10:59:17,546  main        INFO   c.a.c.c.Config    db.server=dbs02.te00.np.ab:1621
2024-06-18 10:59:17,546  main        INFO   c.a.c.c.Config    db.service_name=MNTE00DW.np.ab
2024-06-18 10:59:17,546  main        INFO   c.a.c.c.Config    db.init_connection_pool_size=10
2024-06-18 10:59:17,546  main        INFO   c.a.c.c.Config    db.max_connection_pool_size=200
2024-06-18 10:59:18,296  main        INFO   c.a.c.c.Config    event.process_event_with_prefix=c_
2024-06-18 10:59:18,296  main        INFO   c.a.c.c.Config    event.direct_event=c_direct
2024-06-18 10:59:18,297  main        INFO   c.a.c.c.Config    event.ignore_events=c_activitystart,c_abtestpathassignment
2024-06-18 10:59:18,297  main        INFO   c.a.c.a.Agent    ----- eof configuration --------------------------------------------------
2024-06-18 10:59:18,302  main        INFO   c.s.m.a.s.CI360Agent    Maximum upstream queue size: 1000
2024-06-18 10:59:18,302  main        INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 10:59:18,373  main        INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 10:59:18,670  8094269-19  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 10:59:18,671  8094269-19  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 10:59:18,672  8094269-19  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 10:59:33,672  8094269-19  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 10:59:33,672  8094269-19  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 10:59:33,681  8094269-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 10:59:33,682  8094269-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 10:59:33,682  8094269-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 10:59:48,682  8094269-22  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 10:59:48,682  8094269-22  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 10:59:48,691  8094269-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 10:59:48,691  8094269-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 10:59:48,691  8094269-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:00:03,691  8094269-22  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:00:03,691  8094269-22  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:00:03,698  8094269-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:00:03,698  8094269-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:00:03,698  8094269-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:00:18,532  main        INFO   c.a.c.a.Agent    running
2024-06-18 11:00:18,698  8094269-22  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:00:18,699  8094269-22  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:00:18,748  8094269-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:00:18,748  8094269-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:00:18,748  8094269-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:00:33,749  8094269-22  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:00:33,749  8094269-22  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:00:33,755  8094269-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:00:33,756  8094269-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:00:33,756  8094269-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:00:48,756  8094269-22  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:00:48,756  8094269-22  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:00:48,764  8094269-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:00:48,764  8094269-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:00:48,764  8094269-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:01:03,764  8094269-22  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:01:03,765  8094269-22  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:01:03,771  8094269-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:01:03,771  8094269-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:01:03,771  8094269-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:01:18,544  main        INFO   c.a.c.a.Agent    running
2024-06-18 11:01:18,771  8094269-22  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:01:18,771  8094269-22  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:01:18,807  8094269-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:01:18,807  8094269-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:01:18,807  8094269-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:01:33,807  8094269-22  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:01:33,808  8094269-22  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:01:33,814  8094269-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:01:33,814  8094269-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:01:33,814  8094269-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:01:48,814  8094269-22  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:01:48,814  8094269-22  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:01:48,821  8094269-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:01:48,821  8094269-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:01:48,821  8094269-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:02:03,822  8094269-22  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:02:03,822  8094269-22  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:02:03,828  8094269-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:02:03,828  8094269-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:02:03,828  8094269-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:02:18,555  main        INFO   c.a.c.a.Agent    running
2024-06-18 11:02:18,828  8094269-22  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:02:18,829  8094269-22  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:02:18,881  8094269-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:02:18,881  8094269-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:02:18,882  8094269-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:02:33,882  8094269-22  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:02:33,882  8094269-22  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:02:33,889  8094269-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:02:33,889  8094269-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:02:33,889  8094269-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:02:48,889  8094269-22  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:02:48,889  8094269-22  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:02:48,896  8094269-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:02:48,897  8094269-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:02:48,897  8094269-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:03:03,897  8094269-22  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:03:03,897  8094269-22  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:03:03,904  8094269-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:03:03,904  8094269-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:03:03,905  8094269-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:03:18,567  main        INFO   c.a.c.a.Agent    running
2024-06-18 11:03:18,905  8094269-22  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:03:18,905  8094269-22  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:03:18,955  8094269-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:03:18,955  8094269-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:03:18,955  8094269-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:03:33,955  8094269-22  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:03:33,955  8094269-22  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:03:33,962  8094269-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:03:33,963  8094269-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:03:33,963  8094269-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:03:48,963  8094269-22  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:03:48,963  8094269-22  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:03:48,972  8094269-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:03:48,972  8094269-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:03:48,972  8094269-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:04:03,972  8094269-22  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:04:03,973  8094269-22  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:04:03,978  8094269-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:04:03,978  8094269-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:04:03,979  8094269-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:04:18,578  main        INFO   c.a.c.a.Agent    running
2024-06-18 11:04:18,979  8094269-22  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:04:18,979  8094269-22  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:04:18,998  8094269-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:04:18,998  8094269-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:04:18,998  8094269-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:04:33,998  8094269-22  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:04:33,999  8094269-22  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:04:34,005  8094269-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:04:34,005  8094269-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:04:34,005  8094269-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:04:49,005  8094269-22  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:04:49,006  8094269-22  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:04:49,012  8094269-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:04:49,013  8094269-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:04:49,013  8094269-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:05:04,013  8094269-22  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:05:04,013  8094269-22  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:05:04,021  8094269-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:05:04,021  8094269-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:05:04,021  8094269-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:05:18,588  main        INFO   c.a.c.a.Agent    running
2024-06-18 11:05:19,021  8094269-22  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:05:19,021  8094269-22  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:05:19,038  8094269-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:05:19,039  8094269-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:05:19,039  8094269-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:05:34,039  8094269-22  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:05:34,039  8094269-22  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:05:34,047  8094269-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:05:34,047  8094269-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:05:34,047  8094269-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:05:49,048  8094269-22  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:05:49,048  8094269-22  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:05:49,056  8094269-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:05:49,056  8094269-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:05:49,056  8094269-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:06:04,056  8094269-22  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:06:04,057  8094269-22  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:06:04,063  8094269-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:06:04,063  8094269-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:06:04,063  8094269-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:06:18,599  main        INFO   c.a.c.a.Agent    running
2024-06-18 11:06:19,063  8094269-22  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:06:19,064  8094269-22  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:06:19,080  8094269-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:06:19,080  8094269-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:06:19,080  8094269-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:06:34,080  8094269-22  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:06:34,081  8094269-22  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:06:34,086  8094269-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:06:34,086  8094269-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:06:34,086  8094269-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:06:49,087  8094269-22  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:06:49,087  8094269-22  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:06:49,093  8094269-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:06:49,093  8094269-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:06:49,093  8094269-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:07:04,093  8094269-22  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:07:04,094  8094269-22  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:07:04,099  8094269-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:07:04,100  8094269-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:07:04,100  8094269-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:07:18,606  main        INFO   c.a.c.a.Agent    running
2024-06-18 11:07:19,100  8094269-22  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:07:19,100  8094269-22  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:07:19,136  8094269-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:07:19,136  8094269-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:07:19,136  8094269-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:07:34,136  8094269-22  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:07:34,137  8094269-22  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:07:34,142  8094269-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:07:34,142  8094269-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:07:34,142  8094269-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:07:49,142  8094269-22  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:07:49,143  8094269-22  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:07:49,149  8094269-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:07:49,149  8094269-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:07:49,149  8094269-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:08:04,149  8094269-22  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:08:04,150  8094269-22  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:08:04,155  8094269-24  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:08:04,156  8094269-24  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:08:04,156  8094269-24  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:08:18,614  main        INFO   c.a.c.a.Agent    running
2024-06-18 11:08:19,156  8094269-24  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:08:19,156  8094269-24  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:08:19,184  8094269-24  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:08:19,184  8094269-24  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:08:19,184  8094269-24  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:08:34,184  8094269-24  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:08:34,185  8094269-24  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:08:34,191  8094269-24  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:08:34,191  8094269-24  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:08:34,191  8094269-24  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:08:49,191  8094269-24  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:08:49,191  8094269-24  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:08:49,198  8094269-24  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:08:49,198  8094269-24  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:08:49,198  8094269-24  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:09:04,198  8094269-24  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:09:04,198  8094269-24  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:09:04,203  8094269-24  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:09:04,203  8094269-24  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:09:04,203  8094269-24  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:09:18,622  main        INFO   c.a.c.a.Agent    running
2024-06-18 11:09:19,203  8094269-24  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:09:19,204  8094269-24  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:09:19,255  8094269-24  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:09:19,255  8094269-24  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:09:19,255  8094269-24  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:09:34,256  8094269-24  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:09:34,256  8094269-24  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:09:34,261  8094269-24  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:09:34,261  8094269-24  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:09:34,261  8094269-24  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:09:49,261  8094269-24  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:09:49,262  8094269-24  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:09:49,268  8094269-24  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:09:49,268  8094269-24  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:09:49,268  8094269-24  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:10:04,268  8094269-24  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:10:04,269  8094269-24  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:10:04,274  8094269-24  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:10:04,274  8094269-24  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:10:04,274  8094269-24  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:10:18,631  main        INFO   c.a.c.a.Agent    running
2024-06-18 11:10:19,274  8094269-24  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:10:19,275  8094269-24  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:10:19,287  8094269-24  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:10:19,287  8094269-24  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:10:19,287  8094269-24  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:10:34,287  8094269-24  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:10:34,288  8094269-24  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:10:34,293  8094269-24  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:10:34,294  8094269-24  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:10:34,294  8094269-24  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:10:49,294  8094269-24  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:10:49,294  8094269-24  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:10:49,300  8094269-24  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:10:49,300  8094269-24  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:10:49,300  8094269-24  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:11:04,300  8094269-24  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:11:04,301  8094269-24  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:11:04,306  8094269-24  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:11:04,306  8094269-24  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:11:04,306  8094269-24  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:11:18,639  main        INFO   c.a.c.a.Agent    running
2024-06-18 11:11:19,306  8094269-24  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:11:19,307  8094269-24  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:11:19,336  8094269-24  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:11:19,336  8094269-24  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:11:19,336  8094269-24  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:11:34,336  8094269-24  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:11:34,337  8094269-24  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:11:34,342  8094269-24  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:11:34,342  8094269-24  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:11:34,342  8094269-24  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:11:49,343  8094269-24  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:11:49,343  8094269-24  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:11:49,349  8094269-24  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:11:49,349  8094269-24  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:11:49,349  8094269-24  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:12:04,349  8094269-24  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:12:04,350  8094269-24  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:12:04,355  8094269-24  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:12:04,356  8094269-24  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:12:04,356  8094269-24  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:12:18,647  main        INFO   c.a.c.a.Agent    running
2024-06-18 11:12:19,356  8094269-24  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:12:19,356  8094269-24  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:12:19,383  8094269-24  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:12:19,383  8094269-24  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:12:19,383  8094269-24  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:12:34,383  8094269-24  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:12:34,384  8094269-24  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:12:34,389  8094269-24  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:12:34,389  8094269-24  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:12:34,389  8094269-24  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:12:49,390  8094269-24  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:12:49,390  8094269-24  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:12:49,396  8094269-24  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:12:49,396  8094269-24  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:12:49,396  8094269-24  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:13:04,396  8094269-24  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:13:04,396  8094269-24  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:13:04,401  8094269-24  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:13:04,402  8094269-24  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:13:04,402  8094269-24  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:13:18,655  main        INFO   c.a.c.a.Agent    running
2024-06-18 11:13:19,402  8094269-24  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:13:19,402  8094269-24  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:13:19,426  8094269-24  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:13:19,426  8094269-24  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:13:19,426  8094269-24  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:13:34,426  8094269-24  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:13:34,427  8094269-24  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:13:34,432  8094269-24  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:13:34,433  8094269-24  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:13:34,433  8094269-24  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:13:49,433  8094269-24  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:13:49,433  8094269-24  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:13:49,439  8094269-24  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:13:49,439  8094269-24  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:13:49,439  8094269-24  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:14:04,439  8094269-24  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:14:04,440  8094269-24  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:14:04,445  8094269-24  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:14:04,445  8094269-24  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:14:04,445  8094269-24  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:14:18,663  main        INFO   c.a.c.a.Agent    running
2024-06-18 11:14:19,445  8094269-24  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:14:19,446  8094269-24  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:14:19,481  8094269-24  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:14:19,481  8094269-24  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:14:19,481  8094269-24  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:14:34,481  8094269-24  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:14:34,482  8094269-24  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:14:34,487  8094269-24  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:14:34,487  8094269-24  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:14:34,488  8094269-24  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:14:49,488  8094269-24  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:14:49,488  8094269-24  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:14:49,494  8094269-24  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:14:49,494  8094269-24  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:14:49,494  8094269-24  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:15:04,494  8094269-24  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:15:04,494  8094269-24  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:15:04,500  8094269-24  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:15:04,500  8094269-24  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:15:04,500  8094269-24  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:15:18,671  main        INFO   c.a.c.a.Agent    running
2024-06-18 11:15:19,500  8094269-24  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:15:19,501  8094269-24  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:15:19,511  8094269-24  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:15:19,511  8094269-24  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:15:19,512  8094269-24  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:15:34,512  8094269-24  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:15:34,512  8094269-24  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:15:34,518  8094269-24  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:15:34,518  8094269-24  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:15:34,518  8094269-24  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:15:49,518  8094269-24  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 11:15:49,518  8094269-24  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 11:15:49,525  8094269-24  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:15:49,525  8094269-24  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 11:15:49,526  8094269-24  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 11:15:57,677  main        INFO   c.a.c.a.Agent    Stop reason: 'signal'
2024-06-18 11:15:58,685  main        INFO   c.a.c.a.Agent    Exit code: 0
2024-06-18 13:47:05,408  main        INFO   c.a.c.a.Agent    
2024-06-18 13:47:05,410  main        INFO   c.a.c.a.Agent    ----- starting -------------------------------------------------------
2024-06-18 13:47:05,414  main        INFO   c.a.c.a.Agent    App: /opt/agents/ch-stream/agent-ch-stream.jar
2024-06-18 13:47:05,417  main        INFO   c.a.c.a.Agent    Version: 1.0.0
2024-06-18 13:47:05,417  main        INFO   c.a.c.a.Agent    ----- configuration --------------------------------------------------
2024-06-18 13:47:05,420  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-18 13:47:05,421  main        INFO   c.a.c.c.Config    ci360.jwt=f0cb22506600016b1805ee8b
2024-06-18 13:47:05,421  main        INFO   c.a.c.c.Config    agent.max_threads=5000
2024-06-18 13:47:05,421  main        INFO   c.a.c.c.Config    kafka.topic=sentCampaingEmail
2024-06-18 13:47:05,421  main        INFO   c.a.c.c.Config    agent.kafka=false
2024-06-18 13:47:05,421  main        INFO   c.a.c.c.Config    proxy.server=proxy.np.ab
2024-06-18 13:47:05,422  main        INFO   c.a.c.c.Config    proxy.port=3128
2024-06-18 13:47:05,422  main        INFO   c.a.c.c.Config    proxy.realm=proxy
2024-06-18 13:47:05,422  main        INFO   c.a.c.c.Config    proxy.user=sascloud_user
2024-06-18 13:47:05,422  main        INFO   c.a.c.c.Config    proxy.non_proxy_hosts=app-sas-test.np.ab
2024-06-18 13:47:05,422  main        INFO   c.a.c.a.Agent    test mode = false
2024-06-18 13:47:05,423  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-18 13:47:05,423  main        INFO   c.a.c.c.Config    db.user=APP_CAMPAIGN_CDM2
2024-06-18 13:47:05,423  main        INFO   c.a.c.c.Config    db.database=APP_CAMPAIGN_CDM2
2024-06-18 13:47:05,423  main        INFO   c.a.c.c.Config    db.server=dbs02.te00.np.ab:1621
2024-06-18 13:47:05,423  main        INFO   c.a.c.c.Config    db.service_name=MNTE00DW.np.ab
2024-06-18 13:47:05,423  main        INFO   c.a.c.c.Config    db.init_connection_pool_size=10
2024-06-18 13:47:05,423  main        INFO   c.a.c.c.Config    db.max_connection_pool_size=200
2024-06-18 13:47:06,109  main        INFO   c.a.c.c.Config    event.process_event_with_prefix=c_
2024-06-18 13:47:06,109  main        INFO   c.a.c.c.Config    event.direct_event=c_direct
2024-06-18 13:47:06,109  main        INFO   c.a.c.c.Config    event.ignore_events=c_activitystart,c_abtestpathassignment
2024-06-18 13:47:06,110  main        INFO   c.a.c.a.Agent    ----- eof configuration --------------------------------------------------
2024-06-18 13:47:06,113  main        INFO   c.s.m.a.s.CI360Agent    Maximum upstream queue size: 1000
2024-06-18 13:47:06,113  main        INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 13:47:06,164  main        INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 13:47:06,368  3696921-19  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 13:47:06,369  3696921-19  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 13:47:06,370  3696921-19  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 13:47:21,370  3696921-19  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 13:47:21,370  3696921-19  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 13:47:21,379  3696921-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 13:47:21,379  3696921-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 13:47:21,379  3696921-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 13:47:36,379  3696921-22  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 13:47:36,380  3696921-22  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 13:47:36,387  3696921-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 13:47:36,387  3696921-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 13:47:36,387  3696921-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 13:47:51,387  3696921-22  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 13:47:51,388  3696921-22  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 13:47:51,395  3696921-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 13:47:51,395  3696921-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 13:47:51,395  3696921-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 13:48:06,320  main        INFO   c.a.c.a.Agent    running
2024-06-18 13:48:06,395  3696921-22  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 13:48:06,395  3696921-22  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 13:48:06,404  3696921-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 13:48:06,404  3696921-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 13:48:06,404  3696921-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 13:48:21,405  3696921-22  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 13:48:21,405  3696921-22  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 13:48:21,449  3696921-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 13:48:21,449  3696921-22  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 13:48:21,450  3696921-22  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 13:48:35,327  main        INFO   c.a.c.a.Agent    Stop reason: 'signal'
2024-06-18 13:48:36,339  main        INFO   c.a.c.a.Agent    Exit code: 0
2024-06-18 13:48:36,450  3696921-22  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 13:48:36,450  3696921-22  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 13:48:36,456  3696921-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 13:48:36,457  3696921-22  DEBUG  c.a.c.a.Agent    Stream closed
2024-06-18 22:32:28,917  main        INFO   c.a.c.a.Agent    
2024-06-18 22:32:28,919  main        INFO   c.a.c.a.Agent    ----- starting -------------------------------------------------------
2024-06-18 22:32:28,924  main        INFO   c.a.c.a.Agent    App: /opt/agents/ch-stream/agent-ch-stream.jar
2024-06-18 22:32:28,927  main        INFO   c.a.c.a.Agent    Version: 1.0.0
2024-06-18 22:32:28,927  main        INFO   c.a.c.a.Agent    ----- configuration --------------------------------------------------
2024-06-18 22:32:28,930  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-18 22:32:28,931  main        INFO   c.a.c.c.Config    ci360.jwt=f0cb22506600016b1805ee8b
2024-06-18 22:32:28,931  main        INFO   c.a.c.c.Config    agent.max_threads=5000
2024-06-18 22:32:28,932  main        INFO   c.a.c.c.Config    kafka.topic=sentCampaingEmail
2024-06-18 22:32:28,932  main        INFO   c.a.c.c.Config    agent.kafka=false
2024-06-18 22:32:28,932  main        INFO   c.a.c.c.Config    proxy.server=proxy.np.ab
2024-06-18 22:32:28,932  main        INFO   c.a.c.c.Config    proxy.port=3128
2024-06-18 22:32:28,932  main        INFO   c.a.c.c.Config    proxy.realm=proxy
2024-06-18 22:32:28,932  main        INFO   c.a.c.c.Config    proxy.user=sascloud_user
2024-06-18 22:32:28,933  main        INFO   c.a.c.c.Config    proxy.non_proxy_hosts=app-sas-test.np.ab
2024-06-18 22:32:28,933  main        INFO   c.a.c.a.Agent    test mode = false
2024-06-18 22:32:28,934  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-18 22:32:28,934  main        INFO   c.a.c.c.Config    db.user=APP_CAMPAIGN_CDM2
2024-06-18 22:32:28,934  main        INFO   c.a.c.c.Config    db.database=APP_CAMPAIGN_CDM2
2024-06-18 22:32:28,934  main        INFO   c.a.c.c.Config    db.server=dbs02.te00.np.ab:1621
2024-06-18 22:32:28,934  main        INFO   c.a.c.c.Config    db.service_name=MNTE00DW.np.ab
2024-06-18 22:32:28,934  main        INFO   c.a.c.c.Config    db.init_connection_pool_size=10
2024-06-18 22:32:28,934  main        INFO   c.a.c.c.Config    db.max_connection_pool_size=200
2024-06-18 22:32:29,596  main        INFO   c.a.c.c.Config    event.process_event_with_prefix=c_
2024-06-18 22:32:29,596  main        INFO   c.a.c.c.Config    event.direct_event=c_direct
2024-06-18 22:32:29,597  main        INFO   c.a.c.c.Config    event.ignore_events=c_activitystart,c_abtestpathassignment
2024-06-18 22:32:29,597  main        INFO   c.a.c.a.Agent    ----- eof configuration --------------------------------------------------
2024-06-18 22:32:29,600  main        INFO   c.s.m.a.s.CI360Agent    Maximum upstream queue size: 1000
2024-06-18 22:32:29,600  main        INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 22:32:29,653  main        INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 22:32:29,878  1764601-18  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 22:32:29,880  1764601-18  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 22:32:29,880  1764601-18  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 22:32:44,880  1764601-18  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 22:32:44,880  1764601-18  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 22:32:44,888  1764601-17  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 22:32:44,888  1764601-17  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 22:32:44,889  1764601-17  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 22:32:59,889  1764601-17  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 22:32:59,889  1764601-17  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 22:32:59,896  1764601-23  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 22:32:59,896  1764601-23  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 22:32:59,896  1764601-23  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 22:33:14,896  1764601-23  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 22:33:14,897  1764601-23  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 22:33:14,946  1764601-23  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 22:33:14,946  1764601-23  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 22:33:14,946  1764601-23  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 22:33:29,815  main        INFO   c.a.c.a.Agent    running
2024-06-18 22:33:29,946  1764601-23  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 22:33:29,946  1764601-23  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 22:33:29,953  1764601-23  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 22:33:29,953  1764601-23  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 22:33:29,953  1764601-23  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 22:33:44,953  1764601-23  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 22:33:44,954  1764601-23  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 22:33:44,960  1764601-23  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 22:33:44,960  1764601-23  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 22:33:44,961  1764601-23  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 22:33:59,961  1764601-23  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 22:33:59,961  1764601-23  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 22:33:59,968  1764601-23  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 22:33:59,968  1764601-23  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 22:33:59,968  1764601-23  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 22:34:14,968  1764601-23  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 22:34:14,968  1764601-23  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 22:34:14,998  1764601-23  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 22:34:14,998  1764601-23  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 22:34:14,998  1764601-23  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 22:34:29,827  main        INFO   c.a.c.a.Agent    running
2024-06-18 22:34:29,998  1764601-23  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 22:34:29,999  1764601-23  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 22:34:30,006  1764601-23  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 22:34:30,006  1764601-23  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 22:34:30,006  1764601-23  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 22:34:45,007  1764601-23  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 22:34:45,007  1764601-23  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 22:34:45,013  1764601-23  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 22:34:45,013  1764601-23  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 22:34:45,013  1764601-23  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 22:35:00,014  1764601-23  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 22:35:00,014  1764601-23  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 22:35:00,021  1764601-23  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 22:35:00,022  1764601-23  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 22:35:00,022  1764601-23  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 22:35:15,022  1764601-23  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 22:35:15,022  1764601-23  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 22:35:15,046  1764601-23  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 22:35:15,046  1764601-23  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: org.eclipse.jetty.websocket.core.exception.UpgradeException: 0 null
2024-06-18 22:35:15,046  1764601-23  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 22:35:16,836  main        INFO   c.a.c.a.Agent    Stop reason: 'signal'
2024-06-18 22:35:17,844  main        INFO   c.a.c.a.Agent    Exit code: 0
2024-06-18 22:35:46,892  main        INFO   c.a.c.a.Agent    
2024-06-18 22:35:46,894  main        INFO   c.a.c.a.Agent    ----- starting -------------------------------------------------------
2024-06-18 22:35:46,899  main        INFO   c.a.c.a.Agent    App: /opt/agents/ch-stream/agent-ch-stream.jar
2024-06-18 22:35:46,901  main        INFO   c.a.c.a.Agent    Version: 1.0.0
2024-06-18 22:35:46,901  main        INFO   c.a.c.a.Agent    ----- configuration --------------------------------------------------
2024-06-18 22:35:46,904  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-18 22:35:46,905  main        INFO   c.a.c.c.Config    ci360.jwt=f0cb22506600016b1805ee8b
2024-06-18 22:35:46,905  main        INFO   c.a.c.c.Config    agent.max_threads=5000
2024-06-18 22:35:46,905  main        INFO   c.a.c.c.Config    kafka.topic=sentCampaingEmail
2024-06-18 22:35:46,906  main        INFO   c.a.c.c.Config    agent.kafka=false
2024-06-18 22:35:46,906  main        INFO   c.a.c.c.Config    proxy.server=proxy.np.ab
2024-06-18 22:35:46,906  main        INFO   c.a.c.c.Config    proxy.port=3128
2024-06-18 22:35:46,906  main        INFO   c.a.c.c.Config    proxy.realm=proxy
2024-06-18 22:35:46,906  main        INFO   c.a.c.c.Config    proxy.user=sascloud_user
2024-06-18 22:35:46,907  main        INFO   c.a.c.c.Config    proxy.non_proxy_hosts=app-sas-test.np.ab
2024-06-18 22:35:46,907  main        INFO   c.a.c.a.Agent    test mode = false
2024-06-18 22:35:46,908  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-18 22:35:46,908  main        INFO   c.a.c.c.Config    db.user=APP_CAMPAIGN_CDM2
2024-06-18 22:35:46,908  main        INFO   c.a.c.c.Config    db.database=APP_CAMPAIGN_CDM2
2024-06-18 22:35:46,908  main        INFO   c.a.c.c.Config    db.server=dbs02.te00.np.ab:1621
2024-06-18 22:35:46,908  main        INFO   c.a.c.c.Config    db.service_name=MNTE00DW.np.ab
2024-06-18 22:35:46,908  main        INFO   c.a.c.c.Config    db.init_connection_pool_size=10
2024-06-18 22:35:46,908  main        INFO   c.a.c.c.Config    db.max_connection_pool_size=200
2024-06-18 22:35:47,608  main        INFO   c.a.c.c.Config    event.process_event_with_prefix=c_
2024-06-18 22:35:47,608  main        INFO   c.a.c.c.Config    event.direct_event=c_direct
2024-06-18 22:35:47,608  main        INFO   c.a.c.c.Config    event.ignore_events=c_activitystart,c_abtestpathassignment
2024-06-18 22:35:47,608  main        INFO   c.a.c.a.Agent    ----- eof configuration --------------------------------------------------
2024-06-18 22:35:47,611  main        INFO   c.s.m.a.s.CI360Agent    Maximum upstream queue size: 1000
2024-06-18 22:35:47,612  main        INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 22:35:47,661  main        INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 22:35:48,098  0016558-21  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-18 22:36:47,817  main        INFO   c.a.c.a.Agent    running
2024-06-18 22:36:58,116  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-18 22:36:58,117  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-18 22:36:58,118  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 22:37:13,118  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 22:37:13,118  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 22:37:13,126  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-18 22:37:13,129  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-18 22:37:13,129  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 22:37:13,276  0016558-23  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-18 22:37:28,129  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 22:37:28,133  cheduler-1  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$2.streamClosed(Agent.java:191)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$sendFrame$7(WebSocketCoreSession.java:519)
	at org.eclipse.jetty.util.Callback$3.succeeded(Callback.java:155)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.notifyCallbackSuccess(TransformingFlusher.java:197)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher$Flusher.process(TransformingFlusher.java:154)
	at org.eclipse.jetty.util.IteratingCallback.processing(IteratingCallback.java:243)
	at org.eclipse.jetty.util.IteratingCallback.iterate(IteratingCallback.java:224)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.sendFrame(TransformingFlusher.java:77)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.sendFrame(WebSocketCoreSession.java:522)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.close(WebSocketCoreSession.java:239)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.processHandlerError(WebSocketCoreSession.java:371)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onIdleExpired(WebSocketConnection.java:242)
	at org.eclipse.jetty.io.ssl.SslConnection.onIdleExpired(SslConnection.java:360)
	at org.eclipse.jetty.io.AbstractEndPoint.onIdleExpired(AbstractEndPoint.java:407)
	at org.eclipse.jetty.io.IdleTimeout.checkIdleTimeout(IdleTimeout.java:170)
	at org.eclipse.jetty.io.IdleTimeout.idleCheck(IdleTimeout.java:112)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-18 22:37:47,830  main        INFO   c.a.c.a.Agent    running
2024-06-18 22:38:23,279  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-18 22:38:23,279  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-18 22:38:23,279  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 22:38:38,279  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 22:38:38,280  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 22:38:38,281  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-18 22:38:38,281  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-18 22:38:38,281  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 22:38:38,431  0016558-22  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-18 22:38:47,841  main        INFO   c.a.c.a.Agent    running
2024-06-18 22:38:53,281  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 22:38:53,282  cheduler-1  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$2.streamClosed(Agent.java:191)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$sendFrame$7(WebSocketCoreSession.java:519)
	at org.eclipse.jetty.util.Callback$3.succeeded(Callback.java:155)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.notifyCallbackSuccess(TransformingFlusher.java:197)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher$Flusher.process(TransformingFlusher.java:154)
	at org.eclipse.jetty.util.IteratingCallback.processing(IteratingCallback.java:243)
	at org.eclipse.jetty.util.IteratingCallback.iterate(IteratingCallback.java:224)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.sendFrame(TransformingFlusher.java:77)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.sendFrame(WebSocketCoreSession.java:522)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.close(WebSocketCoreSession.java:239)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.processHandlerError(WebSocketCoreSession.java:371)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onIdleExpired(WebSocketConnection.java:242)
	at org.eclipse.jetty.io.ssl.SslConnection.onIdleExpired(SslConnection.java:360)
	at org.eclipse.jetty.io.AbstractEndPoint.onIdleExpired(AbstractEndPoint.java:407)
	at org.eclipse.jetty.io.IdleTimeout.checkIdleTimeout(IdleTimeout.java:170)
	at org.eclipse.jetty.io.IdleTimeout.idleCheck(IdleTimeout.java:112)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-18 22:39:47,851  main        INFO   c.a.c.a.Agent    running
2024-06-18 22:39:48,433  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-18 22:39:48,434  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-18 22:39:48,434  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 22:40:03,435  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 22:40:03,435  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 22:40:03,435  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-18 22:40:03,435  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-18 22:40:03,436  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 22:40:03,555  0016558-20  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-18 22:40:18,436  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 22:40:18,436  cheduler-1  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$2.streamClosed(Agent.java:191)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$sendFrame$7(WebSocketCoreSession.java:519)
	at org.eclipse.jetty.util.Callback$3.succeeded(Callback.java:155)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.notifyCallbackSuccess(TransformingFlusher.java:197)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher$Flusher.process(TransformingFlusher.java:154)
	at org.eclipse.jetty.util.IteratingCallback.processing(IteratingCallback.java:243)
	at org.eclipse.jetty.util.IteratingCallback.iterate(IteratingCallback.java:224)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.sendFrame(TransformingFlusher.java:77)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.sendFrame(WebSocketCoreSession.java:522)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.close(WebSocketCoreSession.java:239)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.processHandlerError(WebSocketCoreSession.java:371)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onIdleExpired(WebSocketConnection.java:242)
	at org.eclipse.jetty.io.ssl.SslConnection.onIdleExpired(SslConnection.java:360)
	at org.eclipse.jetty.io.AbstractEndPoint.onIdleExpired(AbstractEndPoint.java:407)
	at org.eclipse.jetty.io.IdleTimeout.checkIdleTimeout(IdleTimeout.java:170)
	at org.eclipse.jetty.io.IdleTimeout.idleCheck(IdleTimeout.java:112)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-18 22:40:47,862  main        INFO   c.a.c.a.Agent    running
2024-06-18 22:41:13,557  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-18 22:41:13,558  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-18 22:41:13,558  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 22:41:28,558  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 22:41:28,558  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 22:41:28,559  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-18 22:41:28,559  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-18 22:41:28,560  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 22:41:28,678  0016558-19  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-18 22:41:43,560  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 22:41:43,560  cheduler-1  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$2.streamClosed(Agent.java:191)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$sendFrame$7(WebSocketCoreSession.java:519)
	at org.eclipse.jetty.util.Callback$3.succeeded(Callback.java:155)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.notifyCallbackSuccess(TransformingFlusher.java:197)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher$Flusher.process(TransformingFlusher.java:154)
	at org.eclipse.jetty.util.IteratingCallback.processing(IteratingCallback.java:243)
	at org.eclipse.jetty.util.IteratingCallback.iterate(IteratingCallback.java:224)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.sendFrame(TransformingFlusher.java:77)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.sendFrame(WebSocketCoreSession.java:522)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.close(WebSocketCoreSession.java:239)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.processHandlerError(WebSocketCoreSession.java:371)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onIdleExpired(WebSocketConnection.java:242)
	at org.eclipse.jetty.io.ssl.SslConnection.onIdleExpired(SslConnection.java:360)
	at org.eclipse.jetty.io.AbstractEndPoint.onIdleExpired(AbstractEndPoint.java:407)
	at org.eclipse.jetty.io.IdleTimeout.checkIdleTimeout(IdleTimeout.java:170)
	at org.eclipse.jetty.io.IdleTimeout.idleCheck(IdleTimeout.java:112)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-18 22:41:47,872  main        INFO   c.a.c.a.Agent    running
2024-06-18 22:42:38,681  cheduler-1  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: Connection Idle Timeout
2024-06-18 22:42:38,681  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: Connection Idle Timeout
2024-06-18 22:42:38,682  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 22:42:47,881  main        INFO   c.a.c.a.Agent    running
2024-06-18 22:42:53,682  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 22:42:53,683  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-18 22:42:53,683  cheduler-1  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1001] Connection Idle Timeout
2024-06-18 22:42:53,683  cheduler-1  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1001: Connection Idle Timeout
2024-06-18 22:42:53,683  cheduler-1  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-18 22:42:53,874  0016558-18  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-18 22:43:08,684  cheduler-1  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-18 22:43:08,684  cheduler-1  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$2.streamClosed(Agent.java:191)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$sendFrame$7(WebSocketCoreSession.java:519)
	at org.eclipse.jetty.util.Callback$3.succeeded(Callback.java:155)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.notifyCallbackSuccess(TransformingFlusher.java:197)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher$Flusher.process(TransformingFlusher.java:154)
	at org.eclipse.jetty.util.IteratingCallback.processing(IteratingCallback.java:243)
	at org.eclipse.jetty.util.IteratingCallback.iterate(IteratingCallback.java:224)
	at org.eclipse.jetty.websocket.core.internal.TransformingFlusher.sendFrame(TransformingFlusher.java:77)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.sendFrame(WebSocketCoreSession.java:522)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.close(WebSocketCoreSession.java:239)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.processHandlerError(WebSocketCoreSession.java:371)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onIdleExpired(WebSocketConnection.java:242)
	at org.eclipse.jetty.io.ssl.SslConnection.onIdleExpired(SslConnection.java:360)
	at org.eclipse.jetty.io.AbstractEndPoint.onIdleExpired(AbstractEndPoint.java:407)
	at org.eclipse.jetty.io.IdleTimeout.checkIdleTimeout(IdleTimeout.java:170)
	at org.eclipse.jetty.io.IdleTimeout.idleCheck(IdleTimeout.java:112)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-18 22:43:47,889  main        INFO   c.a.c.a.Agent    running
2024-06-18 22:43:47,889  main        INFO   c.a.c.a.Agent    Stop reason: 'signal'
2024-06-18 22:43:47,897  main        INFO   c.s.m.a.s.CI360Agent    Stopping event stream connection now
2024-06-18 22:43:47,898  main        INFO   o.e.j.w.c.WebSocketClient    Shutdown WebSocketClient@8167dc83[coreClient=WebSocketCoreClient@ec2cc4{STARTED},openSessions.size=1]
2024-06-18 22:43:47,899  0016558-22  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: null
2024-06-18 22:43:47,900  0016558-22  DEBUG  c.a.c.a.Agent    Stream closed
2024-06-18 22:43:47,900  0016558-22  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1006] Session Closed
2024-06-18 22:43:47,900  0016558-22  DEBUG  c.a.c.a.Agent    Stream closed
2024-06-18 22:43:48,907  main        INFO   c.a.c.a.Agent    Exit code: 0
