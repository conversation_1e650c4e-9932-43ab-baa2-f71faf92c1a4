#!/bin/bash
echo

app_name="Air Bank CI360 AGENT CH STREAM"
running=0

#test if start.pid exists and P<PERSON>  is running
if test -f "start.pid"; 
then
	if ps -p `cat start.pid` > /dev/null 
	then
		running=1
		echo "$app_name is already running !"
	fi
fi



if [ "$running" == "0" ]
then
	#start application
	echo Starting $app_name

	java -Dlogback.configurationFile=logback.xml -DconfigFile=app.config  -jar agent-ch-stream.jar 2>&1 &  echo $! > start.pid
	#java  -Dlogback.configurationFile=logback.xml -DconfigFile=app.config  -jar agent-ch-stream.jar -Dhttp.proxyHost=proxy.np.ab -Dhttp.proxyPort=3128 -Dhttp.proxyUser=sascloud_user -Dhttp.proxyPassword=pgRN1Udas5I55abertxx -Dhttp.proxyRealm="proxy" -Dhttp.nonProxyHosts=app-sas-test.np.ab "$@" 2>&1 &  echo $! > start.pid

fi

echo
