apiVersion: v1
kind: ConfigMap
metadata:
  name: sas-agent-kafka-configmap
data:
  airbank.ci360.url: http://external-services-mock.de03.ingress.np.ab/sas360/marketingGateway/events
  kafka.ab.topics.generalContract: de03.cz.airbank.ams.generalcontract.application.change.v1
  kafka.ab.topics.customerRelation: de03.cz.airbank.obs.generalcontract.customerrelations.change.v1
  kafka.ab.topics.pensionApplication: de03.cz.airbank.ams.pensionstatus.application.change.v1
  kafka.ab.topics.investmentApplication: de03.cz.airbank.ams.investmentsstatus.application.change.v1
  kafka.ab.topics.successfulDevicePairing: de03.cz.airbank.rmd.devicepairing.success.v1
  kafka.ab.topics.cardDigitalization: de03.cz.airbank.sas.campaign.result.v1
  kafka.ab.topics.consent: de03.cz.airbank.cml.marketing.consents.change.v1
  kafka.ab.topics.loanApplication: de03.cz.airbank.ams.cashloan.application.status.v1
  kafka.ab.topics.consolidationApplication: de03.cz.airbank.ams.consolidation.application.status.v1
  kafka.ab.topics.overdraftApplication: de03.cz.airbank.ams.overdraft.application.status.v1
  kafka.ab.topics.mortgageApplication: de03.cz.airbank.ams.mortgage.application.status.v1
  kafka.ab.topics.mortgageRefApplication: de03.cz.airbank.ams.mortgageref.application.status.v1
  kafka.ab.topics.splitPaymentApplication: de03.cz.airbank.ams.splitpayment.application.status.v1
  kafka.ab.topics.travelInsuranceApplication: de03.cz.airbank.ams.travelinsurance.application.status.v1
  kafka.ab.topics.accountApplicationStatus: de03.cz.airbank.ams.account.application.status.v1
  kafka.ab.topics.stockEtfApplicationStatus: de03.cz.airbank.ams.stocketf.application.status.v1
  kafka.ab.topics.transactions: de03.cz.airbank.obs.transaction.customertransaction.v2
  kafka.ab.topics.loanProductStatusChange: de03.cz.airbank.obs.loan.loanstatuschange.v1
  kafka.ab.topics.airbankClientUnityMemberPartyRemoved: de03.cz.airbank.o2.proxy.registration.airbankclientunitymemberpartyremoved.v1
  kafka.ab.topics.airbankClientUnityMemberPartyDeactivated: de03.cz.airbank.o2.proxy.registration.airbankclientunitymemberpartydeactivated.v1
  kafka.ab.topics.airbankClientUnityMemberDeactivated: de03.cz.airbank.o2.proxy.registration.airbankclientunitymemberdeactivated.v1
  kafka.ab.topics.plannedCallCreated: de03.cz.airbank.sas.campaign.result.v1
  kafka.ab.topics.sas360messageResult: de03.cz.airbank.sas.campaign.result.v1
  wsc.o2proxy.unity.url: http://o2-proxy.de03.ingress.np.ab/ws/sas-agent-kafka/unity
