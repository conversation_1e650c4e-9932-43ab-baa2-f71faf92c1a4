apiVersion: v1
kind: ConfigMap
metadata:
  name: sas-agent-kafka-configmap
data:
  kafka.ab.topics.generalContract: nonprod.cz.airbank.ams.generalcontract.application.change.v1
  kafka.ab.topics.customerRelation: nonprod.cz.airbank.obs.generalcontract.customerrelations.change.v1
  kafka.ab.topics.consent: nonprod.cz.airbank.cml.marketing.consents.change.v1
  kafka.ab.topics.loanApplication: nonprod.cz.airbank.ams.cashloan.application.status.v1
  kafka.ab.topics.consolidationApplication: nonprod.cz.airbank.ams.consolidation.application.status.v1
  kafka.ab.topics.overdraftApplication: nonprod.cz.airbank.ams.overdraft.application.status.v1
  kafka.ab.topics.mortgageApplication: nonprod.cz.airbank.ams.mortgage.application.status.v1
  kafka.ab.topics.mortgageRefApplication: nonprod.cz.airbank.ams.mortgageref.application.status.v1
  kafka.ab.topics.splitPaymentApplication: nonprod.cz.airbank.ams.splitpayment.application.status.v1
  kafka.ab.topics.travelInsuranceApplication: nonprod.cz.airbank.ams.travelinsurance.application.status.v1
  kafka.ab.topics.accountApplicationStatus: nonprod.cz.airbank.ams.account.application.status.v1
  kafka.ab.topics.stockEtfApplicationStatus: nonprod.cz.airbank.ams.stocketf.application.status.v1
  kafka.ab.topics.transactions: nonprod.cz.airbank.obs.transaction.customertransaction.v2
  kafka.ab.topics.loanProductStatusChange: nonprod.cz.airbank.obs.loan.loanstatuschange.v1
  spring.datasource.url: ************************************************************
  kafka.ab.topics.pensionApplication: nonprod.cz.airbank.ams.pensionstatus.application.change.v1
  kafka.ab.topics.investmentApplication: nonprod.cz.airbank.ams.investmentsstatus.application.change.v1
  kafka.ab.topics.sas360messageResult: nonprod.cz.airbank.sas.campaign.result.v1
  airbank.kafka.sas360messageResult.topics: nonprod.cz.airbank.sas.campaign.result.v1
  kafka.ab.topics.successfulDevicePairing: nonprod.cz.airbank.rmd.devicepairing.success.v1
  kafka.ab.topics.cardDigitalization: nonprod.cz.airbank.cms.card.digitization.v1
  kafka.ab.topics.airbankClientUnityMemberPartyRemoved: nonprod.cz.airbank.o2.proxy.registration.airbankclientunitymemberpartyremoved.v1
  kafka.ab.topics.airbankClientUnityMemberPartyDeactivated: nonprod.cz.airbank.o2.proxy.registration.airbankclientunitymemberpartydeactivated.v1
  kafka.ab.topics.airbankClientUnityMemberDeactivated: nonprod.cz.airbank.o2.proxy.registration.airbankclientunitymemberdeactivated.v1
  kafka.ab.topics.plannedCallCreated: nonprod.cz.airbank.cml.planned.call.created.v1
  wsc.o2proxy.unity.url: http://o2-proxy.ingress.ab.nonprod/ws/sas-agent-kafka/unity
