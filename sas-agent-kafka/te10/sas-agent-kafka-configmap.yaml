apiVersion: v1
kind: ConfigMap
metadata:
  name: sas-agent-kafka-configmap
data:
  kafka.ab.topics.generalContract: te10.cz.airbank.ams.generalcontract.application.change.v1
  kafka.ab.topics.customerRelation: te10.cz.airbank.obs.generalcontract.customerrelations.change.v1
  kafka.ab.topics.consent: te10.cz.airbank.cml.marketing.consents.change.v1
  kafka.ab.topics.loanApplication: te10.cz.airbank.ams.cashloan.application.status.v1
  kafka.ab.topics.consolidationApplication: te10.cz.airbank.ams.consolidation.application.status.v1
  kafka.ab.topics.overdraftApplication: te10.cz.airbank.ams.overdraft.application.status.v1
  kafka.ab.topics.mortgageApplication: te10.cz.airbank.ams.mortgage.application.status.v1
  kafka.ab.topics.mortgageRefApplication: te10.cz.airbank.ams.mortgageref.application.status.v1
  kafka.ab.topics.splitPaymentApplication: te10.cz.airbank.ams.splitpayment.application.status.v1
  kafka.ab.topics.travelInsuranceApplication: te10.cz.airbank.ams.travelinsurance.application.status.v1
  kafka.ab.topics.accountApplicationStatus: te10.cz.airbank.ams.account.application.status.v1
  kafka.ab.topics.stockEtfApplicationStatus: te10.cz.airbank.ams.stocketf.application.status.v1
  kafka.ab.topics.transactions: te10.cz.airbank.obs.transaction.customertransaction.v2
  kafka.ab.topics.loanProductStatusChange: te10.cz.airbank.obs.loan.loanstatuschange.v1
  spring.datasource.url: ********************************************************
  kafka.ab.topics.pensionApplication: te10.cz.airbank.ams.pensionstatus.application.change.v1
  kafka.ab.topics.investmentApplication: te10.cz.airbank.ams.investmentsstatus.application.change.v1
  kafka.ab.topics.sas360messageResult: te10.cz.airbank.sas.campaign.result.v1
  airbank.kafka.sas360messageResult.topics: te10.cz.airbank.sas.campaign.result.v1
  kafka.ab.topics.successfulDevicePairing: te10.cz.airbank.rmd.devicepairing.success.v1
  kafka.ab.topics.cardDigitalization: te10.cz.airbank.cms.card.digitization.v1
  kafka.ab.topics.airbankClientUnityMemberPartyRemoved: te10.cz.airbank.o2.proxy.registration.airbankclientunitymemberpartyremoved.v1
  kafka.ab.topics.airbankClientUnityMemberPartyDeactivated: te10.cz.airbank.o2.proxy.registration.airbankclientunitymemberpartydeactivated.v1
  kafka.ab.topics.airbankClientUnityMemberDeactivated: te10.cz.airbank.o2.proxy.registration.airbankclientunitymemberdeactivated.v1
  kafka.ab.topics.plannedCallCreated: te10.cz.airbank.cml.planned.call.created.v1
  wsc.o2proxy.unity.url: http://o2-proxy.te10.ingress.np.ab/ws/sas-agent-kafka/unity
