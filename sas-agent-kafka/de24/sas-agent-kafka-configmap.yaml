apiVersion: v1
kind: ConfigMap
metadata:
  name: sas-agent-kafka-configmap
data:
  jvm.xmx: 850m
  kafka.ab.topics.generalContract: de24.cz.airbank.ams.generalcontract.application.change.v1
  kafka.ab.topics.customerRelation: de24.cz.airbank.obs.generalcontract.customerrelations.change.v1
  kafka.ab.topics.pensionApplication: de24.cz.airbank.ams.pensionstatus.application.change.v1
  kafka.ab.topics.investmentApplication: de24.cz.airbank.ams.investmentsstatus.application.change.v1
  kafka.ab.topics.successfulDevicePairing: de24.cz.airbank.rmd.devicepairing.success.v1
  kafka.ab.topics.cardDigitalization: de24.cz.airbank.cms.card.digitization.v1
  kafka.ab.topics.unity: de24.cz.unity-core.high-broadcast.from.unity-core
  kafka.ab.topics.consent: de24.cz.airbank.cml.marketing.consents.change.v1
  kafka.ab.topics.loanApplication: de24.cz.airbank.ams.cashloan.application.status.v1
  kafka.ab.topics.consolidationApplication: de24.cz.airbank.ams.consolidation.application.status.v1
  kafka.ab.topics.overdraftApplication: de24.cz.airbank.ams.overdraft.application.status.v1
  kafka.ab.topics.mortgageApplication: de24.cz.airbank.ams.mortgage.application.status.v1
  kafka.ab.topics.mortgageRefApplication: de24.cz.airbank.ams.mortgageref.application.status.v1
  kafka.ab.topics.splitPaymentApplication: de24.cz.airbank.ams.splitpayment.application.status.v1
  kafka.ab.topics.travelInsuranceApplication: de24.cz.airbank.ams.travelinsurance.application.status.v1
  kafka.ab.topics.accountApplicationStatus: de24.cz.airbank.ams.account.application.status.v1
  kafka.ab.topics.stockEtfApplicationStatus: de24.cz.airbank.ams.stocketf.application.status.v1
  kafka.ab.topics.transactions: de24.cz.airbank.obs.transaction.customertransaction.v1
  kafka.ab.topics.loanProductStatusChange: de24.cz.airbank.obs.loan.loanstatuschange.v1
  wsc.o2proxy.unity.url: http://o2-proxy.de24.ingress.np.ab/ws/sas-agent-kafka/unity
