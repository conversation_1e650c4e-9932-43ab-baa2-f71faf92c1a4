apiVersion: v1
kind: ConfigMap
metadata:
  name: sas-agent-kafka-configmap
data:
  jvm.xmx: 384m
  spring.datasource.url: **********************************************************
  kafka.ab.topics.generalContract: prod.cz.airbank.ams.generalcontract.application.change.v1
  kafka.ab.topics.customerRelation: prod.cz.airbank.obs.generalcontract.customerrelations.change.v1
  kafka.ab.topics.consent: prod.cz.airbank.cml.marketing.consents.change.v1
  kafka.ab.topics.loanApplication: prod.cz.airbank.ams.cashloan.application.status.v1
  kafka.ab.topics.consolidationApplication: prod.cz.airbank.ams.consolidation.application.status.v1
  kafka.ab.topics.overdraftApplication: prod.cz.airbank.ams.overdraft.application.status.v1
  kafka.ab.topics.mortgageApplication: prod.cz.airbank.ams.mortgage.application.status.v1
  kafka.ab.topics.mortgageRefApplication: prod.cz.airbank.ams.mortgageref.application.status.v1
  kafka.ab.topics.splitPaymentApplication: prod.cz.airbank.ams.splitpayment.application.status.v1
  kafka.ab.topics.travelInsuranceApplication: prod.cz.airbank.ams.travelinsurance.application.status.v1
  kafka.ab.topics.accountApplicationStatus: prod.cz.airbank.ams.account.application.status.v1
  kafka.ab.topics.stockEtfApplicationStatus: prod.cz.airbank.ams.stocketf.application.status.v1
  kafka.ab.topics.transactions: prod.cz.airbank.obs.transaction.customertransaction.v2
  kafka.ab.topics.loanProductStatusChange: prod.cz.airbank.obs.loan.loanstatuschange.v1
  airbank.ci360.proxy.host: proxyauth.banka.hci
  airbank.ci360.proxy.port: '3128'
  kafka.ab.topics.pensionApplication: prod.cz.airbank.ams.pensionstatus.application.change.v1
  kafka.ab.topics.investmentApplication: prod.cz.airbank.ams.investmentsstatus.application.change.v1
  kafka.ab.topics.sas360messageResult: prod.cz.airbank.sas.campaign.result.v1
  airbank.kafka.sas360messageResult.topics: prod.cz.airbank.sas.campaign.result.v1
  kafka.ab.topics.successfulDevicePairing: prod.cz.airbank.rmd.devicepairing.success.v1
  kafka.ab.topics.cardDigitalization: prod.cz.airbank.cms.card.digitization.v1
  kafka.ab.topics.airbankClientUnityMemberPartyRemoved: prod.cz.airbank.o2.proxy.registration.airbankclientunitymemberpartyremoved.v1
  kafka.ab.topics.airbankClientUnityMemberPartyDeactivated: prod.cz.airbank.o2.proxy.registration.airbankclientunitymemberpartydeactivated.v1
  kafka.ab.topics.airbankClientUnityMemberDeactivated: prod.cz.airbank.o2.proxy.registration.airbankclientunitymemberdeactivated.v1
  kafka.ab.topics.plannedCallCreated: prod.cz.airbank.cml.planned.call.created.v1
  wsc.o2proxy.unity.url: http://o2-proxy.ingress.ab.prod/ws/sas-agent-kafka/unity
