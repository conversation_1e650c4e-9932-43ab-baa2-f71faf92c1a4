apiVersion: v1
kind: ConfigMap
metadata:
  name: sas-agent-kafka-configmap
data:
  jvm.xmx: 850m
  kafka.ab.topics.generalContract: de11.cz.airbank.ams.generalcontract.application.change.v2
  kafka.ab.topics.customerRelation: de11.cz.airbank.obs.generalcontract.customerrelations.change.v1
  kafka.ab.topics.pensionApplication: de11.cz.airbank.ams.pensionstatus.application.change.v1
  kafka.ab.topics.investmentApplication: de11.cz.airbank.ams.investmentsstatus.application.change.v1
  kafka.ab.topics.successfulDevicePairing: de11.cz.airbank.rmd.devicepairing.success.v1
  kafka.ab.topics.cardDigitalization: de11.cz.airbank.cms.card.digitization.v1
  kafka.ab.topics.consent: de11.cz.airbank.cml.marketing.consents.change.v1
  kafka.ab.topics.plannedCallCreated: de11.cz.airbank.cml.planned.call.created.v1
  kafka.ab.topics.loanApplication: de11.cz.airbank.ams.cashloan.application.status.v1
  kafka.ab.topics.consolidationApplication: de11.cz.airbank.ams.consolidation.application.status.v1
  kafka.ab.topics.overdraftApplication: de11.cz.airbank.ams.overdraft.application.status.v1
  kafka.ab.topics.mortgageApplication: de11.cz.airbank.ams.mortgage.application.status.v1
  kafka.ab.topics.mortgageRefApplication: de11.cz.airbank.ams.mortgageref.application.status.v1
  kafka.ab.topics.splitPaymentApplication: de11.cz.airbank.ams.splitpayment.application.status.v1
  kafka.ab.topics.travelInsuranceApplication: de11.cz.airbank.ams.travelinsurance.application.status.v1
  kafka.ab.topics.accountApplicationStatus: de11.cz.airbank.ams.account.application.status.v1
  kafka.ab.topics.stockEtfApplicationStatus: de11.cz.airbank.ams.stocketf.application.status.v1
  kafka.ab.topics.transactions: de11.cz.airbank.obs.transaction.customertransaction.v2
  kafka.ab.topics.loanProductStatusChange: de11.cz.airbank.obs.loan.loanstatuschange.v1
  kafka.ab.topics.airbankClientUnityMemberPartyRemoved: de11.cz.airbank.o2.proxy.registration.airbankclientunitymemberpartyremoved.v1
  kafka.ab.topics.airbankClientUnityMemberPartyDeactivated: de11.cz.airbank.o2.proxy.registration.airbankclientunitymemberpartydeactivated.v1
  kafka.ab.topics.airbankClientUnityMemberDeactivated: de11.cz.airbank.o2.proxy.registration.airbankclientunitymemberdeactivated.v1
  kafka.ab.topics.sas360messageResult: de11.cz.airbank.sas.campaign.result.v1
  airbank.kafka.sas360messageResult.topics: de11.cz.airbank.sas.campaign.result.v1
  wsc.o2proxy.unity.url: http://o2-proxy.de11.ingress.np.ab/ws/sas-agent-kafka/unity
