apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: sas-agent-kafka
  name: sas-agent-kafka-deployment
spec:
  replicas: 1
  revisionHistoryLimit: 2
  selector:
    matchLabels:
      app: sas-agent-kafka
  template:
    metadata:
      labels:
        app: sas-agent-kafka
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8080"
        prometheus.io/path: /actuator/prometheus
    spec:
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchExpressions:
                  - key: app
                    operator: In
                    values:
                      - sas-agent-kafka
              topologyKey: "kubernetes.io/hostname"
      containers:
        - image: "harbor.np.ab/airbank/sas-agent-kafka/app:latest"
          name: sas-agent-kafka
          args:
            - -Xmx$(JVM_XMX)
            - -Xms$(JVM_XMX)
            - -XX:+UseG1GC
            - -XX:MaxGCPauseMillis=100
            - -XX:ParallelGCThreads=4
            - -XX:ConcGCThreads=1
            - -Djavax.xml.transform.TransformerFactory=com.sun.org.apache.xalan.internal.xsltc.trax.TransformerFactoryImpl
            - -Doracle.jdbc.implicitStatementCacheSize=10
            - -jar
            - /app/app.jar
            - -Dpodname=$(PODNAME)
          command:
            - java
          envFrom:
            - configMapRef:
                name: sas-agent-kafka-configmap
            - secretRef:
                name: sas-agent-kafka-secret
          env:
            - name: PODNAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: env_name
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: JAVA_TOOL_OPTIONS
              valueFrom:
                configMapKeyRef:
                  name: runtime-configmap
                  key: java-tool-options-no-gc
            - name: JVM_XMX
              valueFrom:
                configMapKeyRef:
                  key: jvm.xmx
                  name: sas-agent-kafka-configmap
            - name: kafka.ab.apicurioRegistryUrl
              valueFrom:
                configMapKeyRef:
                  name: kafka-cluster-configmap
                  key: REGISTRY_URL
            - name: kafka.ab.bootstrap-servers
              valueFrom:
                configMapKeyRef:
                  name: kafka-cluster-configmap
                  key: KAFKA_CLUSTER
          livenessProbe:
            httpGet:
              path: /actuator/info
              port: 8080
            periodSeconds: 10
          startupProbe:
            httpGet:
              path: /actuator/info
              port: 8080
            failureThreshold: 30
            periodSeconds: 10
          ports:
            - containerPort: 8080
              name: app
            - containerPort: 8080
              name: management
          resources:
            requests:
              memory: "630Mi"
              cpu: "100m"
          volumeMounts:
            - name: cacerts-volume
              mountPath: /java/lib/security/cacerts
              readOnly: true
              subPath: cacerts
      volumes:
        - configMap:
            name: sas-agent-kafka-cacerts-configmap
          name: cacerts-volume
