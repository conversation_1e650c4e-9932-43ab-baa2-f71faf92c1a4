apiVersion: v1
kind: ConfigMap
metadata:
  name: sas-agent-kafka-configmap
data:
  kafka.ab.topics.generalContract: te00.cz.airbank.ams.generalcontract.application.change.v1
  kafka.ab.topics.customerRelation: te00.cz.airbank.obs.generalcontract.customerrelations.change.v1
  kafka.ab.topics.successfulDevicePairing: te00.cz.airbank.rmd.devicepairing.success.v1
  kafka.ab.topics.consent: te00.cz.airbank.cml.marketing.consents.change.v1
  kafka.ab.topics.loanApplication: te00.cz.airbank.ams.cashloan.application.status.v1
  kafka.ab.topics.consolidationApplication: te00.cz.airbank.ams.consolidation.application.status.v1
  kafka.ab.topics.overdraftApplication: te00.cz.airbank.ams.overdraft.application.status.v1
  kafka.ab.topics.mortgageApplication: te00.cz.airbank.ams.mortgage.application.status.v1
  kafka.ab.topics.mortgageRefApplication: te00.cz.airbank.ams.mortgageref.application.status.v1
  kafka.ab.topics.splitPaymentApplication: te00.cz.airbank.ams.splitpayment.application.status.v1
  kafka.ab.topics.travelInsuranceApplication: te00.cz.airbank.ams.travelinsurance.application.status.v1
  kafka.ab.topics.accountApplicationStatus: te00.cz.airbank.ams.account.application.status.v1
  kafka.ab.topics.stockEtfApplicationStatus: te00.cz.airbank.ams.stocketf.application.status.v1
  kafka.ab.topics.transactions: te00.cz.airbank.obs.transaction.customertransaction.v2
  kafka.ab.topics.loanProductStatusChange: te00.cz.airbank.obs.loan.loanstatuschange.v1
  kafka.ab.topics.airbankClientUnityMemberPartyRemoved: te00.cz.airbank.o2.proxy.registration.airbankclientunitymemberpartyremoved.v1
  kafka.ab.topics.airbankClientUnityMemberPartyDeactivated: te00.cz.airbank.o2.proxy.registration.airbankclientunitymemberpartydeactivated.v1
  kafka.ab.topics.airbankClientUnityMemberDeactivated: te00.cz.airbank.o2.proxy.registration.airbankclientunitymemberdeactivated.v1
  kafka.ab.topics.plannedCallCreated: te00.cz.airbank.cml.planned.call.created.v1
  kafka.ab.topics.sas360messageResult: te00.cz.airbank.sas.campaign.result.v1
  airbank.kafka.sas360messageResult.topics: te00.cz.airbank.sas.campaign.result.v1
  wsc.o2proxy.unity.url: http://o2-proxy.te00.ingress.np.ab/ws/sas-agent-kafka/unity
