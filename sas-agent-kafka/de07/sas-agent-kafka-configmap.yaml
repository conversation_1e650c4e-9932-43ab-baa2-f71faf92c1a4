apiVersion: v1
kind: ConfigMap
metadata:
  name: sas-agent-kafka-configmap
data:
  airbank.ci360.url: http://external-services-mock.de07.ingress.np.ab/sas360/marketingGateway/events
  kafka.ab.topics.generalContract: de07.cz.airbank.ams.generalcontract.application.change.v1
  kafka.ab.topics.customerRelation: de07.cz.airbank.obs.generalcontract.customerrelations.change.v1
  kafka.ab.topics.unity: de07.cz.unity-core.high-broadcast.from.unity-core
  kafka.ab.topics.consent: de07.cz.airbank.cml.marketing.consents.change.v1
  kafka.ab.topics.loanApplication: de07.cz.airbank.ams.cashloan.application.status.v1
  kafka.ab.topics.consolidationApplication: de07.cz.airbank.ams.consolidation.application.status.v1
  kafka.ab.topics.overdraftApplication: de07.cz.airbank.ams.overdraft.application.status.v1
  kafka.ab.topics.mortgageApplication: de07.cz.airbank.ams.mortgage.application.status.v1
  kafka.ab.topics.mortgageRefApplication: de07.cz.airbank.ams.mortgageref.application.status.v1
  kafka.ab.topics.splitPaymentApplication: de07.cz.airbank.ams.splitpayment.application.status.v1
  kafka.ab.topics.travelInsuranceApplication: de07.cz.airbank.ams.travelinsurance.application.status.v1
  kafka.ab.topics.accountApplicationStatus: de07.cz.airbank.ams.account.application.status.v1
  kafka.ab.topics.stockEtfApplicationStatus: de07.cz.airbank.ams.stocketf.application.status.v1
  kafka.ab.topics.transactions: de07.cz.airbank.obs.transaction.customertransaction.v1
  kafka.ab.topics.loanProductStatusChange: de07.cz.airbank.obs.loan.loanstatuschange.v1
  wsc.o2proxy.unity.url: http://o2-proxy.de07.ingress.np.ab/ws/sas-agent-kafka/unity
