<?xml version="1.0" encoding="UTF-8" standalone="no"?><wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:sch="http://airbank.cz/sas_agent_kafka/ws" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tns="http://airbank.cz/sas_agent_kafka/ws" targetNamespace="http://airbank.cz/sas_agent_kafka/ws">
  <wsdl:types>
    <xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" targetNamespace="http://airbank.cz/sas_agent_kafka/ws">

    <xs:element name="publishContactHistoryRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="cuid" type="xs:long"/>
                <xs:element name="taskCode" type="xs:string"/>
                <xs:element minOccurs="0" name="error" type="xs:boolean"/>
                <xs:element minOccurs="0" name="result" type="xs:boolean"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="publishContactHistoryResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="status" type="xs:string" />
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="sendBHResponseRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="cuid" type="xs:long"/>
                <xs:element name="taskCode" type="xs:string"/>
                <xs:element name="executed" type="xs:string"/>
                <xs:element minOccurs="0" name="result" type="xs:boolean"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="sendBHResponseResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="status" type="xs:string" />
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
  </wsdl:types>
  <wsdl:message name="publishContactHistoryResponse">
    <wsdl:part element="tns:publishContactHistoryResponse" name="publishContactHistoryResponse">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="sendBHResponseResponse">
    <wsdl:part element="tns:sendBHResponseResponse" name="sendBHResponseResponse">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="publishContactHistoryRequest">
    <wsdl:part element="tns:publishContactHistoryRequest" name="publishContactHistoryRequest">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="sendBHResponseRequest">
    <wsdl:part element="tns:sendBHResponseRequest" name="sendBHResponseRequest">
    </wsdl:part>
  </wsdl:message>
  <wsdl:portType name="RTDMResponseWS">
    <wsdl:operation name="publishContactHistory">
      <wsdl:input message="tns:publishContactHistoryRequest" name="publishContactHistoryRequest">
    </wsdl:input>
      <wsdl:output message="tns:publishContactHistoryResponse" name="publishContactHistoryResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="sendBHResponse">
      <wsdl:input message="tns:sendBHResponseRequest" name="sendBHResponseRequest">
    </wsdl:input>
      <wsdl:output message="tns:sendBHResponseResponse" name="sendBHResponseResponse">
    </wsdl:output>
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="RTDMResponseWSSoap11" type="tns:RTDMResponseWS">
    <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="publishContactHistory">
      <soap:operation soapAction=""/>
      <wsdl:input name="publishContactHistoryRequest">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="publishContactHistoryResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="sendBHResponse">
      <soap:operation soapAction=""/>
      <wsdl:input name="sendBHResponseRequest">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="sendBHResponseResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="RTDMResponseWSService">
    <wsdl:port binding="tns:RTDMResponseWSSoap11" name="RTDMResponseWSSoap11">
      <soap:address location="http://localhost:8080/ws"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>