<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
	xmlns:tns="http://arbes.com/ib/core/ppf/ws/obsPaymentOrderWS/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
	xmlns:xsd="http://www.w3.org/2001/XMLSchema" name="obsPaymentOrderWS"
	targetNamespace="http://arbes.com/ib/core/ppf/ws/obsPaymentOrderWS/" xmlns:xsd1="http://arbes.com/ib/core/ppf/ws/common/">

  <wsdl:types>
    <xsd:schema targetNamespace="http://arbes.com/ib/core/ppf/ws/obsPaymentOrderWS/">
      <xsd:include schemaLocation="OBSPaymentOrderWS.xsd"/>
      <xsd:import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/Common.xsd"/>
    </xsd:schema>
  </wsdl:types>

	<wsdl:message name="findPaymentOrderRequest">
		<wsdl:part element="tns:findPaymentOrderRequest" name="parameters"/>
	</wsdl:message>
	<wsdl:message name="findPaymentOrderResponse1">
		<wsdl:part name="parameters" element="tns:findPaymentOrderResponse"/>
	</wsdl:message>
	<wsdl:message name="getPaymentOrdersRequest">
		<wsdl:part name="parameters" element="tns:getPaymentOrdersRequest"/>
	</wsdl:message>
	<wsdl:message name="getPaymentOrdersResponse1">
		<wsdl:part name="parameters" element="tns:getPaymentOrdersResponse"/>
	</wsdl:message>
	<wsdl:message name="deletePaymentOrderRequest">
		<wsdl:part name="parameters" element="tns:deletePaymentOrderRequest"/>
	</wsdl:message>
	<wsdl:message name="deletePaymentOrderResponse1">
		<wsdl:part name="parameters" element="tns:deletePaymentOrderResponse"/>
	</wsdl:message>
	<wsdl:message name="setPaymentOrderRequest">
		<wsdl:part name="parameters" element="tns:setPaymentOrderRequest"/>
	</wsdl:message>
	<wsdl:message name="setPaymentOrderResponse1">
		<wsdl:part name="parameters" element="tns:setPaymentOrderResponse"/>
	</wsdl:message>
	<wsdl:message name="setForeignPaymentOrderRequest">
		<wsdl:part name="parameters" element="tns:setForeignPaymentOrderRequest"/>
	</wsdl:message>
	<wsdl:message name="setForeignPaymentOrderResponse">
		<wsdl:part name="parameters" element="tns:setForeignPaymentOrderResponse"/>
	</wsdl:message>
	<wsdl:message name="getValidFromRequest">
		<wsdl:part name="parameters" element="tns:getValidFromRequest"/>
	</wsdl:message>
	<wsdl:message name="getValidFromResponse1">
		<wsdl:part name="parameters" element="tns:getValidFromResponse"/>
	</wsdl:message>
	<wsdl:message name="reCalcRequest">
		<wsdl:part name="parameters" element="tns:reCalcRequest"/>
	</wsdl:message>
	<wsdl:message name="getRemainingTransactionLimitRequest">
		<wsdl:part name="parameters" element="tns:getRemainingTransactionLimitRequest"/>
	</wsdl:message>
	<wsdl:message name="returnPaymentRequest">
		<wsdl:part name="parameters" element="tns:returnPaymentRequest"/>
	</wsdl:message>
	<wsdl:message name="returnPaymentResponse">
		<wsdl:part name="parameters" element="tns:returnPaymentResponse"/>
	</wsdl:message>
	<wsdl:message name="returnPaymentFault">
		<wsdl:part name="parameters" element="xsd1:ErrorsListType"/>
	</wsdl:message>



	<wsdl:message name="reCalcResponse">
		<wsdl:part name="parameters" element="tns:reCalcResponse"/>
	</wsdl:message>
	<wsdl:message name="getRemainingTransactionLimitResponse">
		<wsdl:part name="parameters" element="tns:getRemainingTransactionLimitResponse"/>
	</wsdl:message>
	<wsdl:message name="validateRequest">
		<wsdl:part name="parameters" element="tns:validateRequest"/>
	</wsdl:message>
	<wsdl:message name="validateResponse">
		<wsdl:part name="parameters" element="tns:validateResponse"/>
	</wsdl:message>

	<wsdl:message name="findPaymentOrderFault">
		<wsdl:part name="parameters" element="xsd1:ErrorsListType"/>
	</wsdl:message>
	<wsdl:message name="getPaymentOrdersFault">
		<wsdl:part name="parameters" element="xsd1:ErrorsListType"/>
	</wsdl:message>
	<wsdl:message name="deletePaymentOrderFault">
		<wsdl:part name="parameters" element="xsd1:ErrorsListType"/>
	</wsdl:message>
	<wsdl:message name="setPaymentOrderFault">
		<wsdl:part name="parameters" element="xsd1:ErrorsListType"/>
	</wsdl:message>
	<wsdl:message name="setForeignPaymentOrderFault">
		<wsdl:part name="parameters" element="xsd1:ErrorsListType"/>
	</wsdl:message>
	<wsdl:message name="getValidFromFault">
		<wsdl:part name="parameters" element="xsd1:ErrorsListType"/>
	</wsdl:message>
	<wsdl:message name="reCalcFault">
		<wsdl:part name="parameters" element="xsd1:ErrorsListType"/>
	</wsdl:message>
	<wsdl:message name="getRemainingTransactionLimitFault">
		<wsdl:part name="parameters" element="xsd1:ErrorsListType"/>
	</wsdl:message>

	<wsdl:message name="setVisibilityRequest">
		<wsdl:part name="parameters" element="tns:setVisibilityRequest"/>
	</wsdl:message>
	<wsdl:message name="setVisibilityResponse">
		<wsdl:part name="parameters" element="tns:setVisibilityResponse"/>
	</wsdl:message>
	<wsdl:message name="setVisibilityFault">
		<wsdl:part name="parameters" element="xsd1:ErrorsListType"/>
	</wsdl:message>
	<wsdl:message name="getForeignPaymentOrderTypeRequest">
		<wsdl:part name="parameters" element="tns:getForeignPaymentOrderTypeRequest"/>
	</wsdl:message>
	<wsdl:message name="getForeignPaymentOrderTypeResponse">
		<wsdl:part name="parameters" element="tns:getForeignPaymentOrderTypeResponse"/>
	</wsdl:message>
	<wsdl:message name="getForeignPaymentOrderTypeFault">
		<wsdl:part name="parameters" element="xsd1:ErrorsListType"/>
	</wsdl:message>
	<wsdl:message name="getFeeAmountRequest">
		<wsdl:part name="parameters" element="tns:getFeeAmountRequest"/>
	</wsdl:message>
	<wsdl:message name="getFeeAmountResponse">
		<wsdl:part name="parameters" element="tns:getFeeAmountResponse"/>
	</wsdl:message>
	<wsdl:message name="getFeeAmountFault">
		<wsdl:part name="parameters" element="xsd1:ErrorsListType"/>
	</wsdl:message>
	<wsdl:message name="validateFault">
		<wsdl:part name="parameters" element="xsd1:ErrorsListType"/>
	</wsdl:message>

	<wsdl:message name="isInstantPaymentEligibleRequest">
		<wsdl:part name="parameters" element="tns:isInstantPaymentEligibleRequest"/>
	</wsdl:message>
	<wsdl:message name="isInstantPaymentEligibleResponse">
		<wsdl:part name="parameters" element="tns:isInstantPaymentEligibleResponse"/>
	</wsdl:message>
	<wsdl:message name="isInstantPaymentEligibleFault">
		<wsdl:part name="parameters" element="xsd1:ErrorsListType"/>
	</wsdl:message>

	<wsdl:message name="getPaymentFeeDetailRequest">
		<wsdl:part name="parameters" element="tns:getPaymentFeeDetailRequest"/>
	</wsdl:message>
	<wsdl:message name="getPaymentFeeDetailResponse">
		<wsdl:part name="parameters" element="tns:getPaymentFeeDetailResponse"/>
	</wsdl:message>
	<wsdl:message name="getPaymentFeeDetailFault">
		<wsdl:part name="parameters" element="xsd1:ErrorsListType"/>
	</wsdl:message>

	<wsdl:message name="getForeignPaymentFeeDetailRequest">
		<wsdl:part name="parameters" element="tns:getForeignPaymentFeeDetailRequest"/>
	</wsdl:message>
	<wsdl:message name="getForeignPaymentFeeDetailResponse">
		<wsdl:part name="parameters" element="tns:getForeignPaymentFeeDetailResponse"/>
	</wsdl:message>
	<wsdl:message name="getForeignPaymentFeeDetailFault">
		<wsdl:part name="parameters" element="xsd1:ErrorsListType"/>
	</wsdl:message>

	<wsdl:message name="uploadBatchRequest">
		<wsdl:part name="parameters" element="tns:uploadBatchRequest"/>
	</wsdl:message>
	<wsdl:message name="uploadBatchResponse">
		<wsdl:part name="parameters" element="tns:uploadBatchResponse"/>
	</wsdl:message>
	<wsdl:message name="uploadBatchFault">
		<wsdl:part name="parameters" element="xsd1:ErrorsListType"/>
	</wsdl:message>

	<wsdl:message name="getBatchSectionsRequest">
		<wsdl:part name="parameters" element="tns:getBatchSectionsRequest"/>
	</wsdl:message>
	<wsdl:message name="getBatchSectionsResponse">
		<wsdl:part name="parameters" element="tns:getBatchSectionsResponse"/>
	</wsdl:message>
	<wsdl:message name="getBatchSectionsFault">
		<wsdl:part name="parameters" element="xsd1:ErrorsListType"/>
	</wsdl:message>

	<wsdl:message name="getBatchSectionInfoRequest">
		<wsdl:part name="parameters" element="tns:getBatchSectionInfoRequest"/>
	</wsdl:message>
	<wsdl:message name="getBatchSectionInfoResponse">
		<wsdl:part name="parameters" element="tns:getBatchSectionInfoResponse"/>
	</wsdl:message>
	<wsdl:message name="getBatchSectionInfoFault">
		<wsdl:part name="parameters" element="xsd1:ErrorsListType"/>
	</wsdl:message>

	<wsdl:message name="getBatchPaymentsRequest">
		<wsdl:part name="parameters" element="tns:getBatchPaymentsRequest"/>
	</wsdl:message>
	<wsdl:message name="getBatchPaymentsResponse">
		<wsdl:part name="parameters" element="tns:getBatchPaymentsResponse"/>
	</wsdl:message>
	<wsdl:message name="getBatchPaymentsFault">
		<wsdl:part name="parameters" element="xsd1:ErrorsListType"/>
	</wsdl:message>

	<wsdl:message name="getBatchIDsFromTrnIDsRequest">
		<wsdl:part name="parameters" element="tns:getBatchIDsFromTrnIDsRequest"/>
	</wsdl:message>
	<wsdl:message name="getBatchIDsFromTrnIDsResponse">
		<wsdl:part name="parameters" element="tns:getBatchIDsFromTrnIDsResponse"/>
	</wsdl:message>
	<wsdl:message name="getBatchIDsFromTrnIDsFault">
		<wsdl:part name="parameters" element="xsd1:ErrorsListType"/>
	</wsdl:message>

	<wsdl:message name="removeBatchPaymentsRequest">
		<wsdl:part name="parameters" element="tns:removeBatchPaymentsRequest"/>
	</wsdl:message>
	<wsdl:message name="removeBatchPaymentsResponse">
		<wsdl:part name="parameters" element="tns:removeBatchPaymentsResponse"/>
	</wsdl:message>
	<wsdl:message name="removeBatchPaymentsFault">
		<wsdl:part name="parameters" element="xsd1:ErrorsListType"/>
	</wsdl:message>

	<wsdl:message name="addOrEditBatchPaymentRequest">
		<wsdl:part name="parameters" element="tns:addOrEditBatchPaymentRequest"/>
	</wsdl:message>
	<wsdl:message name="addOrEditBatchPaymentResponse">
		<wsdl:part name="parameters" element="tns:addOrEditBatchPaymentResponse"/>
	</wsdl:message>
	<wsdl:message name="addOrEditBatchPaymentFault">
		<wsdl:part name="parameters" element="xsd1:ErrorsListType"/>
	</wsdl:message>

	<wsdl:message name="validateBatchPaymentsRequest">
		<wsdl:part name="parameters" element="tns:validateBatchPaymentsRequest"/>
	</wsdl:message>
	<wsdl:message name="validateBatchPaymentsResponse">
		<wsdl:part name="parameters" element="tns:validateBatchPaymentsResponse"/>
	</wsdl:message>
	<wsdl:message name="validateBatchPaymentsFault">
		<wsdl:part name="parameters" element="xsd1:ErrorsListType"/>
	</wsdl:message>

	<wsdl:message name="preConfirmBatchPaymentsRequest">
		<wsdl:part name="parameters" element="tns:preConfirmBatchPaymentsRequest"/>
	</wsdl:message>
	<wsdl:message name="preConfirmBatchPaymentsResponse">
		<wsdl:part name="parameters" element="tns:preConfirmBatchPaymentsResponse"/>
	</wsdl:message>
	<wsdl:message name="preConfirmBatchPaymentsFault">
		<wsdl:part name="parameters" element="xsd1:ErrorsListType"/>
	</wsdl:message>

	<wsdl:message name="confirmBatchPaymentsRequest">
		<wsdl:part name="parameters" element="tns:confirmBatchPaymentsRequest"/>
	</wsdl:message>
	<wsdl:message name="confirmBatchPaymentsResponse">
		<wsdl:part name="parameters" element="tns:confirmBatchPaymentsResponse"/>
	</wsdl:message>
	<wsdl:message name="confirmBatchPaymentsFault">
		<wsdl:part name="parameters" element="xsd1:ErrorsListType"/>
	</wsdl:message>

	<wsdl:message name="checkClientsDeferredPaymentsRequest">
		<wsdl:part name="parameters" element="tns:checkClientsDeferredPaymentsRequest"/>
	</wsdl:message>
	<wsdl:message name="checkClientsDeferredPaymentsResponse">
		<wsdl:part name="parameters" element="tns:checkClientsDeferredPaymentsResponse"/>
	</wsdl:message>
	<wsdl:message name="checkClientsDeferredPaymentsFault">
		<wsdl:part name="parameters" element="xsd1:ErrorsListType"/>
	</wsdl:message>

	<wsdl:portType name="obsPaymentOrderWS">
		<wsdl:documentation>služba pro jednorázové platby</wsdl:documentation>
		<wsdl:operation name="findPaymentOrder">
			<wsdl:documentation>filtr pro platební příkaz

možné chyby:
kód / atribut / hodnota : popis
GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
CLERR_UNSUPPORTED_FILTER / 	xx / :filtr nepodporuje daný filtrovací atribut xx</wsdl:documentation>
			<wsdl:input message="tns:findPaymentOrderRequest"/>
			<wsdl:output message="tns:findPaymentOrderResponse1"/>
            <wsdl:fault name="fault" message="tns:findPaymentOrderFault"/>
        </wsdl:operation>
		<wsdl:operation name="getPaymentOrders">
			<wsdl:documentation>vrátí údaje platebního příkazu
kód / atribut / hodnota : popis
GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
CLERR_ACCESS / GENERAL_ERROR / :  uživatel nemá právo na účet
</wsdl:documentation>
			<wsdl:input message="tns:getPaymentOrdersRequest"/>
			<wsdl:output message="tns:getPaymentOrdersResponse1"/>
            <wsdl:fault name="fault" message="tns:getPaymentOrdersFault"/>
        </wsdl:operation>
		<wsdl:operation name="deletePaymentOrder">
			<wsdl:documentation>zruší ještě nezrealizovaný platební příka

kód / atribut / hodnota : popis
GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
CLERR_ACCESS / GENERAL_ERROR / :  uživatel nemá právo na účet
CLERR_NO_DATA_FOUND / IdPaymentOrder / :  nenalezena daná platba
ERROR_ACCOUNT_BLOCKED / ACCOUNT_ERROR / : zdrojový účet je blokován
ERROR_BAD_SIGNATURE / signature / : neplatná autorizace
</wsdl:documentation>
			<wsdl:input message="tns:deletePaymentOrderRequest"/>
			<wsdl:output message="tns:deletePaymentOrderResponse1"/>
            <wsdl:fault name="fault" message="tns:deletePaymentOrderFault"/>
        </wsdl:operation>
		<wsdl:operation name="setPaymentOrder">
			<wsdl:documentation>založí nebo změní platební příkaz
kód / atribut / hodnota : popis
GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
CLERR_ACCESS / GENERAL_ERROR / :  uživatel nemá právo na účet
CLERR_CORE_PO / / :   jádro odmítlo přijmout platbu
CLERR_IS_MANDATORY / contraBankCode / : kód banky je povinný
CLERR_IS_MANDATORY / contraAccountNumber / :číslo účtu je povinné
CLERR_IS_MANDATORY / requiredCurrency / : měna je povinná
CLERR_IS_MANDATORY / amountInRequiredCurrency / : částka je povinná
CLERR_IS_MANDATORY / validFrom / :datum splatnosti je povinné
CLERR_NO_DATA_FOUND / contraBankCode / : kdyz je zeme kreditni strany v EHP, musi byt banka v ciselniku OBS
ERR_WWW_INV_ACC_FROM / / :uživatel nemá právo na převod peněz z daného účtu
WWW_INV_BANKCOUNTRY / bankCountry / :neplatná cílová země
WWW_INV_CURRENCY / accountCurrencyCredit / : neplatná měna
WWW_CZ_ACCOUNT_NUMBER / accountNumberCredit / :číslo protiúčtu neodpovídá platnému českému formátu
WWW_IBAN_NUMBER / accountNumberCredit / :neplatný IBAN protiúčtu
ERR_ACC_NUM_CREDIT / accountNumberCredit / :chyba při určení cílového účtu u tuzemské platby
ERR_BAD_VALIDITY / validFrom / :datum je v minulosti
ERROR_TRN_VALIDFROM / validFrom  / datum hodnota - Pro zadane datum nelze transakci zralizovat, v errorValue je nejblizsi mozne datum
CLERR_NO_DATA_FOUND / idEnvelopeDebit / :nenalezena zdrojová obálka
CLERR_NO_DATA_FOUND / idEnvelopeCredit / : nenalezena cílová obálka
CLERR_NO_DATA_FOUND / IdPaymentOrder / : nenalezena daná platba
ERROR_ACCOUNT_BLOCKED / ACCOUNT_ERROR / :zdrojový účet je blokován
ERROR_BAD_SIGNATURE / signature / :neplatná autorizace
CLERR_NO_DATA_FOUND / idPaymentTemplate / : ak neexisutje v systeme dana sablona
CLERR_PATTERN_ERROR / CONSTSYMBOL / : neplatný konstantní symbol
CLERR_PATTERN_ERROR / VARSYMBOL / : neplatný variabilní symbol
CLERR_PATTERN_ERROR	/ SPECSYMBOL / : neplatný specifický symbol
ERR_RQT_PAYMENTREASON_NOTENTERED / PAYMENTREASON / : důvod platby je vyžadován
CLERR_TRNLIMIT_EXCEEDED / amount / : překročen transakční limit
CLERR_TRNLIMIT_EXCEEDED_ICC / amount / : překročen transakční limit pro ICC
CLERR_NUMBER_IS_GREATER / amount / maximalni castka : byla prekrocena maximalni castka
WWW_INV_BANKCODE / contraBankCode / : neplatný kód banky
CLERR_IBAN_BIC_ERROR / contraBankCode / : neshoduje se země z IBAN a z BIC kódu, pokud neni SEPA (v budoucnu predpoklad uplne zruseni validace)
CLERR_PATTERN_ERROR / feeCoverage / : povinná položka (pouze u zahraničních plateb nebo plateb v cizí měně)
CLERR_AMOUNT_TOO_HIGH / idEnvelopeDebit / : caska je je vetsi ne je zustatek na obalce
CLERR_AMOUNT_TOO_SMALL / amount / : Payment amount converted to debit account currency is zero (or negative)
ERR_TRANS_REAL_ALR / / : transakci nelze editovat
ERR_INSUFFICIENT_PRIVILEGES / pres OpenAPI nelze editovat transakci zalozenou v jinem systemu
CLERR_STRING_TOO_LONG / accountFullNameCredit / : adresa prijemce je delsi nez dovoluje swift format 4*35x
CLERR_STRING_TOO_LONG / accountNameCredit / : identifikace prijemce je delsi nez dovoluje swift format 35x
CLERR_STRING_TOO_LONG / accountNumberCredit / : nazev protiuctu je delsi nez dovoluje swift format 35x
CLERR_STRING_TOO_LONG / bankFullNameCredit / : adresa banky je delsi nez dovoluje swift format 4*35x
CLERR_STRING_TOO_LONG / bankNameCredit / : nazev banky je delsi nez dovoluje swift format 35x
CLERR_STRING_TOO_LONG / bankCodeCredit / : kod banky je delsi nez dovoluje swift format 35x
CLERR_INVALID_CHARACTER / contraAccountName / : Illegal characters for the swift format
CLERR_INVALID_CHARACTER / contraAccountFullName / : Illegal characters for the swift format
CLERR_INVALID_CHARACTER / messageForReceiver / : Illegal characters for the swift format
CLERR_INVALID_TRUSTLEVEL / accountTrustLevel / : Invalid trust level for credit account
INVALID_CREDIT_ACCOUNT / contraAccountNumber / : chybne cislo kreditniho uctu
ERR_BLACKLISTED_ACCOUNT / accountNumberCredit / : the account is blacklisted, the transaction cannot be created
ERR_BLACKLISTED_ACCOUNT / accountNumberDebit / : the account is blacklisted, the transaction cannot be created
CLERR_INVALID_CHARACTER / EndToEnd / : Illegal characters for the swift format or illegal specific characters for EndToEnd reference field
MISMATCH_IBANBIC_BIC / contraBankCode / : The entered BIC does not match the BIC in the IBAN

soft alerts:
code / atribute / value : description
WRN_SUBOPTIMAL_CONVERSION / idBankAccountDebit / : Payment order with suboptimal conversion
WRN_CROSSBORDER_PAYMENT_IN_CZK / requiredCurrency / : Crossborder payment entred in CZK
WRN_INSUFFICIENT_FUNDS / amount / : There are insufficient funds on debit account (only for payment orders due today)
WRN_UNASSIGNED_INSUFFICIENT_FUNDS / amount / : There are unassigned insufficient funds on debit account
WRN_BLACKLISTED_ACCOUNT / accountNumberCredit / : the account is blacklisted but the transaction can be created with the client's warning
WRN_BLACKLISTED_ACCOUNT / accountNumberDebit / : the account is blacklisted but the transaction can be created with the client's warning
			</wsdl:documentation>
			<wsdl:input message="tns:setPaymentOrderRequest"/>
			<wsdl:output message="tns:setPaymentOrderResponse1"/>
            <wsdl:fault name="fault" message="tns:setPaymentOrderFault"/>
        </wsdl:operation>

		<wsdl:operation name="setForeignPaymentOrder">
			<wsdl:documentation>Validate and creates or modifies (if idPaymentOrder is specified) foreign payment order. Returns ID of the created or modified payment order.

Generated faults:
code / atribute / value : description
GENERAL_ERROR / GENERAL_ERROR / : General system error
CLERR_TIMEOUT / GENERAL_ERROR / : System was not able to process the request in time. changes were rolled back.
CLERR_ACCESS / GENERAL_ERROR / :  User is not authorized to perform the operation
CLERR_CORE_PO / / : Core refused to accept payment
ERR_WWW_INV_ACC_FROM / / : The user is not allowed to transfer money from the account
WWW_INV_BANKCOUNTRY / bankCountry / : Invalid destination country
WWW_INV_CURRENCY / accountCurrencyCredit / : Invalid currency for creating a foreign payment
ERR_BAD_VALIDITY / validFrom / : Date is in the past
ERROR_TRN_VALIDFROM / validFrom  / date value : For the specified date can not be transaction realized. in errorValue is the closest valid date.
CLERR_NO_DATA_FOUND / idPaymentOrder / : Payment order was not found
ERROR_ACCOUNT_BLOCKED / ACCOUNT_ERROR / : Debited account is blocked
ERROR_BAD_SIGNATURE / signature / : Invalid authorization
CLERR_TRNLIMIT_EXCEEDED / amount / : Transaction limit exceeded
CLERR_TRNLIMIT_EXCEEDED_ICC / amount / : Transaction limit exceeded for ICC
ERR_TRANS_REAL_ALR / / : Transaction cannot be edited
WWW_IBAN_NUMBER / accountNumberCredit / : Invalid IBAN of credit account
WWW_INV_BANKCODE / contraBankCode / : Invalid BIC
CLERR_IBAN_BIC_ERROR / contraBankCode / : Does not match the country of the IBAN and the BIC, if there is no SEPA (assuming complete cancellation of validation in the future)
CLERR_STRING_TOO_LONG / accountFullNameCredit / : Address of reciever is longer than allowed by swift format 3*35x
CLERR_STRING_TOO_LONG / accountNameCredit / : Name of reciever is longer than allowed by swift format 35x
CLERR_STRING_TOO_LONG / accountNumberCredit / : Number of credit account is longer than allowed by swift format 35x
CLERR_STRING_TOO_LONG / bankFullNameCredit / : Address of credit bank is longer than allowed by swift format 3*35x
CLERR_STRING_TOO_LONG / bankNameCredit / : Name of credit bank is longer than allowed by swift format 35x
CLERR_STRING_TOO_LONG / bankCodeCredit / : Code of credit bank is longer than allowed by swift format format 35x
CLERR_STRING_TOO_LONG / messageForBank / : Message for bank is longer than allowed by swift format 6*35x
CLERR_INVALID_CHARACTER / contraBankName / : Illegal characters for the swift format
CLERR_INVALID_CHARACTER / contraBankFullName / : Illegal characters for the swift format
CLERR_INVALID_CHARACTER / contraAccountName / : Illegal characters for the swift format
CLERR_INVALID_CHARACTER / contraAccountFullName / : Illegal characters for the swift format
CLERR_INVALID_CHARACTER / messageForSender / : Illegal characters for the swift format
CLERR_INVALID_CHARACTER / messageForReceiver / : Illegal characters for the swift format
CLERR_INVALID_CHARACTER / messageForBank / : Illegal characters for the swift format
CLERR_NO_DATA_FOUND / idPaymentTemplate / : Payment template defined in element prescription was not found
ERR_RQT_PAYMENTREASON_NOTENTERED / PAYMENTREASON / : Reason for payment is required
CLERR_NUMBER_IS_GREATER / amount / maximalni castka : Exceeding the maximum amount
CLERR_NOT_CROSSBORDER / GENERAL_ERROR / : The payment is not foreign payment (payment into a czech bank in czk). Must be entered as a domestic
CLERR_NOT_CROSSBORDER_AB / GENERAL_ERROR / : The payment is not foreign payment (payment into AirBank). Must be entered as a domestic
WRN_BLACKLISTED_ACCOUNT / accountNumberCredit / : the account is blacklisted but the transaction can be created with the client's warning
ERR_BLACKLISTED_ACCOUNT / accountNumberCredit / : the account is blacklisted, the transaction cannot be created
CLERR_INVALID_CHARACTER / EndToEnd / : Illegal characters for the swift format or illegal specific characters for EndToEnd reference field
MISMATCH_IBANBIC_BIC / contraBankCode / : The entered BIC does not match the BIC in the IBAN

Soft alerts:
code / atribute / value : description
WRN_CROSSBORDER_PAYMENT_IN_CZK / requiredCurrency / : Crossborder payment entred in CZK
WRN_INSUFFICIENT_FUNDS_FP / amount / : There are insufficient funds on debit account (only for payment orders due today)
WRN_UNASSIGNED_INSUFFICIENT_FUNDS_FP / amount / : There are unassigned insufficient funds on debit account
</wsdl:documentation>
			<wsdl:input message="tns:setForeignPaymentOrderRequest"/>
			<wsdl:output message="tns:setForeignPaymentOrderResponse"/>
      <wsdl:fault name="fault" message="tns:setForeignPaymentOrderFault"/>
   </wsdl:operation>

		<wsdl:operation name="getValidFrom">
			<wsdl:documentation>zjistí nebližší možné datum počátku platnosti platebního příkaz

kód / atribut / hodnota : popis
GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
ERR_ACC_NUM_CREDIT / accountNumberCredit / :chyba při určení cílového účtu u tuzemské platby
CLERR_NOT_CROSSBORDER / contraBankCode / : pokud se podle meny a kodu banky nejedna o zahranicni platbu
CLERR_UNSUPPORTED_TRANSACTION_TYPE / GENERAL_ERROR / : This operation is not available for foreign payment order
			</wsdl:documentation>
			<wsdl:input message="tns:getValidFromRequest"/>
			<wsdl:output message="tns:getValidFromResponse1"/>
            <wsdl:fault name="fault" message="tns:getValidFromFault"/>
        </wsdl:operation>

		<wsdl:operation name="reCalc">
			<wsdl:documentation>metoda na přepočet částky do jiné měny

Příklady:

CurrencyDebit, CurrencyCredit, AmountDebit, AmountCredit  =&gt; Amount, Rate, Currency1, Currency2, Lot1, Lot2
EUR, CZK, 10, NULL, =&gt; 250, 25, CZK, EUR, 1, 1
CZK, EUR, 270, NULL, =&gt; 10, 27, CZK, EUR, 1, 1
RUB, CZK, 100, NULL, =&gt; 70, 70, CZK, RUB, 100, 1
EUR, USD, 100, NULL, =&gt; 120, 1.2, USD, EUR, 1, 1
USD, EUR, 100, NULL, =&gt; 85, 0.85, EUR, USD, 1, 1

kód / atribut / hodnota : popis
GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
CLERR_NUMBER_IS_GREATER / amount / maximalni castka : byla prekrocena maximalni castka
CLERR_ENUM_ERROR / currencyDebit / :neplatná debitní měna
CLERR_ENUM_ERROR / currencyCredit / :neplatná kreditní měna
CLERR_IS_MANDATORY / amountDebit / :povinná debitní částka
CLERR_IS_MANDATORY / amountCredit / : povinná kreditní částka
CLERR_NUMBER_IS_LOWER / amountDebit / :debitní částka musí být větší jak 0
CLERR_NUMBER_IS_LOWER / amountCredit / :kreditní částka musí být větší jak 0
</wsdl:documentation>
			<wsdl:input message="tns:reCalcRequest"/>
			<wsdl:output message="tns:reCalcResponse"/>
            <wsdl:fault name="fault" message="tns:reCalcFault"/>
        </wsdl:operation>

		<wsdl:operation name="getRemainingTransactionLimit">
			<wsdl:documentation>metoda na ziskani zbyvajicich transakcnich limitu
				vstup: kanál (neodvodzovať z business contextu), protiúčet (číslo účtu + kód banky) – nepovinný
				pomocou údaju o protiúčte OBS rozlíši prevody medzi vlastnými účtami, na ktoré sa limit nevzťahuje; ak nie je zadaný, predpokladá, že ide o cudzí účet
			</wsdl:documentation>
			<wsdl:input message="tns:getRemainingTransactionLimitRequest"/>
			<wsdl:output message="tns:getRemainingTransactionLimitResponse"/>
            <wsdl:fault name="fault" message="tns:getRemainingTransactionLimitFault"/>
		</wsdl:operation>




		<wsdl:operation name="returnPayment">
			<wsdl:documentation>založí nebo změní platební příkaz
kód / atribut / hodnota : popis
GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
CLERR_ACCESS / GENERAL_ERROR / :  uživatel nemá právo na účet
CLERR_CORE_PO / / :   jádro odmítlo přijmout platbu
CLERR_IS_MANDATORY / contraBankCode / : kód banky je povinný
CLERR_IS_MANDATORY / contraAccountNumber / :číslo účtu je povinné
CLERR_IS_MANDATORY / requiredCurrency / : měna je povinná
CLERR_IS_MANDATORY / amountInRequiredCurrency / : částka je povinná
CLERR_IS_MANDATORY / validFrom / :datum splatnosti je povinné
CLERR_NO_DATA_FOUND / contraBankCode / : kdyz je zeme kreditni strany v EHP, musi byt banka v ciselniku OBS
ERR_WWW_INV_ACC_FROM / / :uživatel nemá právo na převod peněz z daného účtu
WWW_INV_BANKCOUNTRY / bankCountry / :neplatná cílová země
WWW_INV_CURRENCY / accountCurrencyCredit / : neplatná měna
WWW_CZ_ACCOUNT_NUMBER / accountNumberCredit / :číslo protiúčtu neodpovídá platnému českému formátu
WWW_IBAN_NUMBER / accountNumberCredit / :neplatný IBAN protiúčtu
ERR_ACC_NUM_CREDIT / accountNumberCredit / :chyba při určení cílového účtu u tuzemské platby
ERR_BAD_VALIDITY / validFrom / :datum je v minulosti
ERROR_TRN_VALIDFROM / validFrom  / datum hodnota - Pro zadane datum nelze transakci zralizovat, v errorValue je nejblizsi mozne datum
CLERR_NO_DATA_FOUND / idEnvelopeDebit / :nenalezena zdrojová obálka
CLERR_NO_DATA_FOUND / idEnvelopeCredit / : nenalezena cílová obálka
CLERR_NO_DATA_FOUND / IdPaymentOrder / : nenalezena daná platba
ERROR_ACCOUNT_BLOCKED / ACCOUNT_ERROR / :zdrojový účet je blokován
ERROR_BAD_SIGNATURE / signature / :neplatná autorizace
CLERR_NO_DATA_FOUND / idPaymentTemplate / : ak neexisutje v systeme dana sablona
CLERR_PATTERN_ERROR / CONSTSYMBOL / : neplatný konstantní symbol
CLERR_PATTERN_ERROR / VARSYMBOL / : neplatný variabilní symbol
CLERR_PATTERN_ERROR	/ SPECSYMBOL / : neplatný specifický symbol
ERR_RQT_PAYMENTREASON_NOTENTERED / PAYMENTREASON / : důvod platby je vyžadován
CLERR_TRNLIMIT_EXCEEDED / amount / : překročen transakční limit
CLERR_TRNLIMIT_EXCEEDED_ICC / amount / : překročen transakční limit pro ICC
CLERR_NUMBER_IS_GREATER / amount / maximalni castka : byla prekrocena maximalni castka
WWW_INV_BANKCODE / contraBankCode / : neplatný kód banky
CLERR_IBAN_BIC_ERROR / contraBankCode / : neshoduje se země z IBAN a z BIC kódu, pokud neni SEPA (v budoucnu predpoklad uplne zruseni validace)
CLERR_PATTERN_ERROR / feeCoverage / : povinná položka (pouze u zahraničních plateb nebo plateb v cizí měně)
CLERR_AMOUNT_TOO_HIGH / idEnvelopeDebit / : caska je je vetsi ne je zustatek na obalce
CLERR_AMOUNT_TOO_SMALL / amount / : Payment amount converted to debit account currency is zero (or negative)
ERR_TRANS_REAL_ALR / / : transakci nelze editovat
CLERR_STRING_TOO_LONG / accountFullNameCredit / : adresa prijemce je delsi nez dovoluje swift format 4*35x
CLERR_STRING_TOO_LONG / accountNameCredit / : identifikace prijemce je delsi nez dovoluje swift format 35x
CLERR_STRING_TOO_LONG / accountNumberCredit / : nazev protiuctu je delsi nez dovoluje swift format 35x
CLERR_STRING_TOO_LONG / bankFullNameCredit / : adresa banky je delsi nez dovoluje swift format 4*35x
CLERR_STRING_TOO_LONG / bankNameCredit / : nazev banky je delsi nez dovoluje swift format 35x
CLERR_STRING_TOO_LONG / bankCodeCredit / : kod banky je delsi nez dovoluje swift format 35x
CLERR_INVALID_CHARACTER / contraAccountName / : Illegal characters for the swift format
CLERR_INVALID_CHARACTER / contraAccountFullName / : Illegal characters for the swift format
CLERR_INVALID_CHARACTER / messageForReceiver / : Illegal characters for the swift format
CLERR_INVALID_TRUSTLEVEL / accountTrustLevel / : Invalid trust level for credit account
INVALID_CREDIT_ACCOUNT / contraAccountNumber / : chybne cislo kreditniho uctu
WRN_BLACKLISTED_ACCOUNT / accountNumberCredit / : the account is blacklisted but the transaction can be created with the client's warning
ERR_BLACKLISTED_ACCOUNT / accountNumberCredit / : the account is blacklisted, the transaction cannot be created
CLERR_INVALID_CHARACTER / EndToEnd / : Illegal characters for the swift format or illegal specific characters for EndToEnd reference field
MISMATCH_IBANBIC_BIC / contraBankCode / : The entered BIC does not match the BIC in the IBAN

soft alerts:
code / atribute / value : description
WRN_SUBOPTIMAL_CONVERSION / idBankAccountDebit / : Payment order with suboptimal conversion
WRN_CROSSBORDER_PAYMENT_IN_CZK / requiredCurrency / : Crossborder payment entred in CZK
WRN_INSUFFICIENT_FUNDS / amount / : There are insufficient funds on debit account (only for payment orders due today)
WRN_UNASSIGNED_INSUFFICIENT_FUNDS / amount / : There are unassigned insufficient funds on debit account
			</wsdl:documentation>
			<wsdl:input message="tns:returnPaymentRequest"/>
			<wsdl:output message="tns:returnPaymentResponse"/>
            <wsdl:fault name="fault" message="tns:returnPaymentFault"/>
        </wsdl:operation>





		<wsdl:operation name="setVisibility">
			<wsdl:documentation>metoda nastavý příznak nezobrazovat danou nezrealizovanou transakci. Po nastavení se transakce nebude nikde zobrazovat (na žádných přehledech)
Nebude se ale zobrazovat i dalším osobám k dané RS.

kód / atribut / hodnota : popis
GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
CLERR_ACCESS / IdPaymentOrder / :  uživatel nemá právo danou transakci upravovat
CLERR_NO_DATA_FOUND / IdPaymentOrder / :  nenalezena daná platba

</wsdl:documentation>
			<wsdl:input message="tns:setVisibilityRequest"/>
			<wsdl:output message="tns:setVisibilityResponse"/>
            <wsdl:fault name="fault" message="tns:setVisibilityFault"/>
        </wsdl:operation>
		<wsdl:operation name="getForeignPaymentOrderType">
			<wsdl:documentation>Get type of foreign payment order

code / atribute / value : description
GENERAL_ERROR /GENERAL_ERROR / : general system error
CLERR_TIMEOUT / GENERAL_ERROR / : system was not able to process the request in time. Changes were rolled back.
GENERAL_ERROR_ACCESS / GENERAL_ERROR / :	person is not logged
CLERR_NO_DATA_FOUND / requiredCurrency / : required currency not found in obs for foreign payment order</wsdl:documentation>
			<wsdl:input message="tns:getForeignPaymentOrderTypeRequest"/>
			<wsdl:output message="tns:getForeignPaymentOrderTypeResponse"/>
			<wsdl:fault name="fault" message="tns:getForeignPaymentOrderTypeFault"/>
		</wsdl:operation>
		<wsdl:operation name="getFeeAmount">
			<wsdl:documentation>Get amount of fees for foreign payment order

code / atribute / value : description
GENERAL_ERROR /GENERAL_ERROR / : general system error
CLERR_TIMEOUT / GENERAL_ERROR / : system was not able to process the request in time. Changes were rolled back.
CLERR_ACCESS / GENERAL_ERROR / :  User is not authorized to perform the operation
GENERAL_ERROR_ACCESS / GENERAL_ERROR / :	person is not logged
CLERR_NO_DATA_FOUND / requiredCurrency / : required currency not found in obs for foreign payment order</wsdl:documentation>
			<wsdl:input message="tns:getFeeAmountRequest"/>
			<wsdl:output message="tns:getFeeAmountResponse"/>
			<wsdl:fault name="fault" message="tns:getFeeAmountFault"/>
		</wsdl:operation>
		<wsdl:operation name="validate">
			<wsdl:documentation>Validate items of payment order

code / atribute / value : description
GENERAL_ERROR / GENERAL_ERROR / : general system error
CLERR_TIMEOUT / GENERAL_ERROR / : system was not able to process the request in time</wsdl:documentation>
			<wsdl:input message="tns:validateRequest"/>
			<wsdl:output message="tns:validateResponse"/>
			<wsdl:fault name="fault" message="tns:validateFault"/>
		</wsdl:operation>

		<wsdl:operation name="isInstantPaymentEligible">
			<wsdl:documentation>Is it possible to send instant payments to the bank in question at this moment?
				Soap faults:
				code / atribute / value : description
				CLERR_NO_DATA_FOUND / bankCode / : receiving bank not found in bank codelist
				GENERAL_ERROR / GENERAL_ERROR / : general system error
				CLERR_TIMEOUT / GENERAL_ERROR / : system was not able to process the request in time
		    </wsdl:documentation>
			<wsdl:input message="tns:isInstantPaymentEligibleRequest"/>
			<wsdl:output message="tns:isInstantPaymentEligibleResponse"/>
			<wsdl:fault name="fault" message="tns:isInstantPaymentEligibleFault"/>
		</wsdl:operation>

		<wsdl:operation name="getPaymentFeeDetail">
			<wsdl:documentation>
				Ziska detailni informace o poplatcich za pozadovanou tuzemskou platbu
				Soap faults:
				code / atribute / value : description
				ACCOUNT_IS_MANDATORY / IdBankAccountDebit / : ucet musi byt zadan
				AMOUNT_IN_REQUIRED_CURR_IS_MANDATORY / amountInRequiredCurrency /: neni zadana pozadovana castka v mene transakce
				ACCOUNT_NOT_FOUND / IdBankAccountDebit / : zadany ucet se nepodarilo dohledat
				CURRENCY_NOT_FOUND / amountInRequiredCurrency.currency / : zadanou menu se nepodarilo dohledat
				ACCOUNT_NOT_FOUND / contraAccountNumber / : zadany ucet neexistuje (nastava pouze pro kod banky 3030)

				GENERAL_ERROR / GENERAL_ERROR / : general system error
				CLERR_TIMEOUT / GENERAL_ERROR / : system was not able to process the request in time
		    </wsdl:documentation>
			<wsdl:input message="tns:getPaymentFeeDetailRequest"/>
			<wsdl:output message="tns:getPaymentFeeDetailResponse"/>
			<wsdl:fault name="fault" message="tns:getPaymentFeeDetailFault"/>
		</wsdl:operation>

		<wsdl:operation name="getForeignPaymentFeeDetail">
			<wsdl:documentation>
				Ziska detailni informace o poplatcich za pozadovanou zahranicni platbu
				Soap faults:
				code / atribute / value : description
				ACCOUNT_IS_MANDATORY / IdBankAccountDebit / : ucet musi byt zadan
				AMOUNT_IN_REQUIRED_CURR_IS_MANDATORY / amountInRequiredCurrency /: neni zadana pozadovana castka v mene transakce
				TYPE_IS_MANDATORY / type / : typ musi byt zadan
				ACCOUNT_NOT_FOUND / IdBankAccountDebit / : zadany ucet se nepodarilo dohledat
				CURRENCY_NOT_FOUND / amountInRequiredCurrency.currency / : zadanou menu se nepodarilo dohledat
				UNSUPPORTED_VALUE / feeCoverage / : hodnota mus byt SEPA, PSD, NON_PSD
				UNSUPPORTED_VALUE / type / : hodnota musi byt SHARE, OUR

				GENERAL_ERROR / GENERAL_ERROR / : general system error
				CLERR_TIMEOUT / / : system was not able to process the request in time
		    </wsdl:documentation>
			<wsdl:input message="tns:getForeignPaymentFeeDetailRequest"/>
			<wsdl:output message="tns:getForeignPaymentFeeDetailResponse"/>
			<wsdl:fault name="fault" message="tns:getForeignPaymentFeeDetailFault"/>
		</wsdl:operation>


		<wsdl:operation name="uploadBatch">
			<wsdl:documentation>
				Uploads payment batch containing payment orders.
				Data in request were parsed out of a file in one of supported formats.

				Soap faults:
				code / atribute / value : description
				GENERAL_ERROR / GENERAL_ERROR / : general system error
				CLERR_TIMEOUT / / : system was not able to process the request in time
				CLERR_NO_DATA_FOUND / gcid, cuid / : uploader not found
		    </wsdl:documentation>
			<wsdl:input message="tns:uploadBatchRequest"/>
			<wsdl:output message="tns:uploadBatchResponse"/>
			<wsdl:fault name="fault" message="tns:uploadBatchFault"/>
		</wsdl:operation>

		<wsdl:operation name="getBatchSections">
			<wsdl:documentation>
				Find and return all batch sections (uploaded in uploadBatch) according to input criteria

				Soap faults:
				code / atribute / value : description
				GENERAL_ERROR / GENERAL_ERROR / : general system error
				CLERR_TIMEOUT / / : system was not able to process the request in time
				CLERR_NO_DATA_FOUND / gcid / : general contract not found
				CLERR_NO_DATA_FOUND / profileId / : profileId not found (or not connected to gcid)
			</wsdl:documentation>
			<wsdl:input message="tns:getBatchSectionsRequest"/>
			<wsdl:output message="tns:getBatchSectionsResponse"/>
			<wsdl:fault name="fault" message="tns:getBatchSectionsFault"/>
		</wsdl:operation>

		<wsdl:operation name="getBatchSectionInfo">
			<wsdl:documentation>
				Get additional info about associated with batch section (to be shown in GUI when showing information about the batch section)

				Soap faults:
				code / atribute / value : description
				GENERAL_ERROR / GENERAL_ERROR / : general system error
				CLERR_TIMEOUT / / : system was not able to process the request in time
				CLERR_NO_DATA_FOUND / id / : id of batch section not found
				CLERR_NO_DATA_FOUND / profileId / : profileId not found
			</wsdl:documentation>
			<wsdl:input message="tns:getBatchSectionInfoRequest"/>
			<wsdl:output message="tns:getBatchSectionInfoResponse"/>
			<wsdl:fault name="fault" message="tns:getBatchSectionInfoFault"/>
		</wsdl:operation>

		<wsdl:operation name="getBatchPayments">
			<wsdl:documentation>
				Find and return all batch payments (uploaded in uploadBatch) according to input criteria

				Soap faults:
				code / atribute / value : description
				GENERAL_ERROR / GENERAL_ERROR / : general system error
				CLERR_TIMEOUT / / : system was not able to process the request in time
			</wsdl:documentation>
			<wsdl:input message="tns:getBatchPaymentsRequest"/>
			<wsdl:output message="tns:getBatchPaymentsResponse"/>
			<wsdl:fault name="fault" message="tns:getBatchPaymentsFault"/>
		</wsdl:operation>

		<wsdl:operation name="getBatchIDsFromTrnIDs">
			<wsdl:documentation>
				Return (if any) batch IDs (ID of batch, batch section, batch payment) for given transaction IDs

				Soap faults:
				code / atribute / value : description
				GENERAL_ERROR / GENERAL_ERROR / : general system error
				CLERR_TIMEOUT / / : system was not able to process the request in time
			</wsdl:documentation>
			<wsdl:input message="tns:getBatchIDsFromTrnIDsRequest"/>
			<wsdl:output message="tns:getBatchIDsFromTrnIDsResponse"/>
			<wsdl:fault name="fault" message="tns:getBatchIDsFromTrnIDsFault"/>
		</wsdl:operation>

		<wsdl:operation name="removeBatchPayments">
			<wsdl:documentation>
				Remove (i.e. change status to "removed") given batch payment or whole batch payment section

				Soap faults:
				code / atribute / value : description
				GENERAL_ERROR / GENERAL_ERROR / : general system error
				CLERR_TIMEOUT / / : system was not able to process the request in time
				CLERR_NO_DATA_FOUND / profileId / : profileId not found
				CLERR_NO_DATA_FOUND / batchPaymentId / : batchPaymentId not found
				CLERR_NO_DATA_FOUND / batchSectionId / : batchSectionId not found
				CLERR_NO_ACCESS / profileId / : not enough rights to remove batch payment
			</wsdl:documentation>
			<wsdl:input message="tns:removeBatchPaymentsRequest"/>
			<wsdl:output message="tns:removeBatchPaymentsResponse"/>
			<wsdl:fault name="fault" message="tns:removeBatchPaymentsFault"/>
		</wsdl:operation>

		<wsdl:operation name="addOrEditBatchPayment">
			<wsdl:documentation>
				Add new or edit existing batch payment (one item in payment section)

				Soap faults:
				code / atribute / value : description
				GENERAL_ERROR / GENERAL_ERROR / : general system error
				CLERR_TIMEOUT / / : system was not able to process the request in time
				CLERR_NO_DATA_FOUND / profileId / : profileId not found
				CLERR_NO_DATA_FOUND / batchPaymentId / : batchPaymentId not found
				CLERR_NO_DATA_FOUND / batchSectionId / : batchSectionId not found
				CLERR_NO_ACCESS / profileId / : not enough rights to add/edit batch payment
			</wsdl:documentation>
			<wsdl:input message="tns:addOrEditBatchPaymentRequest"/>
			<wsdl:output message="tns:addOrEditBatchPaymentResponse"/>
			<wsdl:fault name="fault" message="tns:addOrEditBatchPaymentFault"/>
		</wsdl:operation>

		<wsdl:operation name="validateBatchPayments">
			<wsdl:documentation>
				Validate batch payment batch section as whole, return problems found

				Soap faults:
				code / atribute / value : description
				GENERAL_ERROR / GENERAL_ERROR / : general system error
				CLERR_TIMEOUT / / : system was not able to process the request in time
				CLERR_NO_DATA_FOUND / profileId / : profileId not found
				CLERR_NO_DATA_FOUND / batchSectionId / : batchSectionId not found
			</wsdl:documentation>
			<wsdl:input message="tns:validateBatchPaymentsRequest"/>
			<wsdl:output message="tns:validateBatchPaymentsResponse"/>
			<wsdl:fault name="fault" message="tns:validateBatchPaymentsFault"/>
		</wsdl:operation>

		<wsdl:operation name="preConfirmBatchPayments">
			<wsdl:documentation>
				Hand over (i.e. change status to "to be confirmed") given batch payment section to confirmation

				Soap faults:
				code / atribute / value : description
				GENERAL_ERROR / GENERAL_ERROR / : general system error
				CLERR_TIMEOUT / / : system was not able to process the request in time
				CLERR_NO_DATA_FOUND / profileId / : profileId not found
				CLERR_NO_DATA_FOUND / batchSectionId / : batchSectionId not found
				CLERR_NO_ACCESS / profileId / : not enough rights to hand over given batch payment section to confirmation
			</wsdl:documentation>
			<wsdl:input message="tns:preConfirmBatchPaymentsRequest"/>
			<wsdl:output message="tns:preConfirmBatchPaymentsResponse"/>
			<wsdl:fault name="fault" message="tns:preConfirmBatchPaymentsFault"/>
		</wsdl:operation>

		<wsdl:operation name="confirmBatchPayments">
			<wsdl:documentation>
				Confirm (i.e. autorize) payments in given batch payment section, generate required transactions

				Soap faults:
				code / atribute / value : description
				GENERAL_ERROR / GENERAL_ERROR / : general system error
				CLERR_TIMEOUT / / : system was not able to process the request in time
				CLERR_NO_DATA_FOUND / profileId / : profileId not found
				CLERR_NO_DATA_FOUND / batchSection.Id / : batchSection.Id not found
				CLERR_NO_ACCESS / profileId / : not enough rights to hand over given batch payment section to confirmation
			</wsdl:documentation>
			<wsdl:input message="tns:confirmBatchPaymentsRequest"/>
			<wsdl:output message="tns:confirmBatchPaymentsResponse"/>
			<wsdl:fault name="fault" message="tns:confirmBatchPaymentsFault"/>
		</wsdl:operation>

		<wsdl:operation name="checkClientsDeferredPayments">
			<wsdl:documentation>Umožňuje zjistit že některá z plateb klienta se nachází na záchytném účtu (je odložená kvůli podezření na fraud)
				Soap faults:
				code / atribute / value : description
				GENERAL_ERROR / GENERAL_ERROR / : general system error
				CLERR_TIMEOUT / GENERAL_ERROR / : system was not able to process the request in time
			</wsdl:documentation>
			<wsdl:input message="tns:checkClientsDeferredPaymentsRequest"/>
			<wsdl:output message="tns:checkClientsDeferredPaymentsResponse"/>
			<wsdl:fault name="fault" message="tns:checkClientsDeferredPaymentsFault"/>
		</wsdl:operation>

	</wsdl:portType>
	<wsdl:binding name="obsPaymentOrderWSSOAP"
		type="tns:obsPaymentOrderWS">
		<soap:binding style="document"
			transport="http://schemas.xmlsoap.org/soap/http" />
		<wsdl:operation name="findPaymentOrder">
			<soap:operation soapAction=""/>
			<wsdl:input>
				<soap:body use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal"/>
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="getPaymentOrders">
			<soap:operation soapAction=""/>
			<wsdl:input>
				<soap:body use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal"/>
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="deletePaymentOrder">
			<soap:operation soapAction=""/>
			<wsdl:input>
				<soap:body use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal"/>
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="setPaymentOrder">
			<soap:operation soapAction=""/>
			<wsdl:input>
				<soap:body use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal"/>
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="setForeignPaymentOrder">
			<soap:operation soapAction=""/>
			<wsdl:input>
				<soap:body use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal"/>
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="getValidFrom">
			<soap:operation soapAction=""/>
			<wsdl:input>
				<soap:body use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal"/>
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="reCalc">
			<soap:operation soapAction=""/>
			<wsdl:input>
				<soap:body use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal"/>
			</wsdl:output>
		</wsdl:operation>

		<wsdl:operation name="getRemainingTransactionLimit">
			<soap:operation soapAction=""/>
			<wsdl:input>
				<soap:body use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal"/>
			</wsdl:output>
		</wsdl:operation>

		<wsdl:operation name="returnPayment">
			<soap:operation soapAction=""/>
			<wsdl:input>
				<soap:body use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal"/>
			</wsdl:output>
		</wsdl:operation>


		<wsdl:operation name="setVisibility">
			<soap:operation
				soapAction="" />
			<wsdl:input>
				<soap:body use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal"/>
			</wsdl:output>
			<wsdl:fault name="fault">
				<soap:fault use="literal" name="fault"/>
			</wsdl:fault>
		</wsdl:operation>

    <wsdl:operation name="getForeignPaymentOrderType">
      <soap:operation
        soapAction="" />
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
      <wsdl:fault name="fault">
        <soap:fault use="literal" name="fault"/>
      </wsdl:fault>
    </wsdl:operation>

    <wsdl:operation name="getFeeAmount">
      <soap:operation
        soapAction="" />
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
      <wsdl:fault name="fault">
        <soap:fault use="literal" name="fault"/>
      </wsdl:fault>
    </wsdl:operation>

    <wsdl:operation name="validate">
      <soap:operation
        soapAction="" />
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
      <wsdl:fault name="fault">
        <soap:fault use="literal" name="fault"/>
      </wsdl:fault>
    </wsdl:operation>

    <wsdl:operation name="isInstantPaymentEligible">
      <soap:operation
        soapAction="" />
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
      <wsdl:fault name="fault">
        <soap:fault use="literal" name="fault"/>
      </wsdl:fault>
    </wsdl:operation>

    <wsdl:operation name="getPaymentFeeDetail">
      <soap:operation
        soapAction="" />
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
      <wsdl:fault name="fault">
        <soap:fault use="literal" name="fault"/>
      </wsdl:fault>
    </wsdl:operation>

   <wsdl:operation name="getForeignPaymentFeeDetail">
      <soap:operation
        soapAction="" />
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
      <wsdl:fault name="fault">
        <soap:fault use="literal" name="fault"/>
      </wsdl:fault>
    </wsdl:operation>

	<wsdl:operation name="uploadBatch">
		<soap:operation
		  soapAction="" />
		<wsdl:input>
		  <soap:body use="literal"/>
		</wsdl:input>
		<wsdl:output>
		  <soap:body use="literal"/>
		</wsdl:output>
		<wsdl:fault name="fault">
		  <soap:fault use="literal" name="fault"/>
		</wsdl:fault>
	</wsdl:operation>

	<wsdl:operation name="getBatchSections">
		<soap:operation
		  soapAction="" />
		<wsdl:input>
		  <soap:body use="literal"/>
		</wsdl:input>
		<wsdl:output>
		  <soap:body use="literal"/>
		</wsdl:output>
		<wsdl:fault name="fault">
		  <soap:fault use="literal" name="fault"/>
		</wsdl:fault>
	  </wsdl:operation>

	<wsdl:operation name="getBatchSectionInfo">
		<soap:operation
		  soapAction="" />
		<wsdl:input>
		  <soap:body use="literal"/>
		</wsdl:input>
		<wsdl:output>
		  <soap:body use="literal"/>
		</wsdl:output>
		<wsdl:fault name="fault">
		  <soap:fault use="literal" name="fault"/>
		</wsdl:fault>
	  </wsdl:operation>

	  <wsdl:operation name="getBatchPayments">
		<soap:operation
		  soapAction="" />
		<wsdl:input>
		  <soap:body use="literal"/>
		</wsdl:input>
		<wsdl:output>
		  <soap:body use="literal"/>
		</wsdl:output>
		<wsdl:fault name="fault">
		  <soap:fault use="literal" name="fault"/>
		</wsdl:fault>
	  </wsdl:operation>

	  <wsdl:operation name="getBatchIDsFromTrnIDs">
		<soap:operation
		  soapAction="" />
		<wsdl:input>
		  <soap:body use="literal"/>
		</wsdl:input>
		<wsdl:output>
		  <soap:body use="literal"/>
		</wsdl:output>
		<wsdl:fault name="fault">
		  <soap:fault use="literal" name="fault"/>
		</wsdl:fault>
	  </wsdl:operation>

	  <wsdl:operation name="removeBatchPayments">
		<soap:operation
		  soapAction="" />
		<wsdl:input>
		  <soap:body use="literal"/>
		</wsdl:input>
		<wsdl:output>
		  <soap:body use="literal"/>
		</wsdl:output>
		<wsdl:fault name="fault">
		  <soap:fault use="literal" name="fault"/>
		</wsdl:fault>
	  </wsdl:operation>

	  <wsdl:operation name="addOrEditBatchPayment">
		<soap:operation
		  soapAction="" />
		<wsdl:input>
		  <soap:body use="literal"/>
		</wsdl:input>
		<wsdl:output>
		  <soap:body use="literal"/>
		</wsdl:output>
		<wsdl:fault name="fault">
		  <soap:fault use="literal" name="fault"/>
		</wsdl:fault>
	  </wsdl:operation>

	  <wsdl:operation name="validateBatchPayments">
		<soap:operation
		  soapAction="" />
		<wsdl:input>
		  <soap:body use="literal"/>
		</wsdl:input>
		<wsdl:output>
		  <soap:body use="literal"/>
		</wsdl:output>
		<wsdl:fault name="fault">
		  <soap:fault use="literal" name="fault"/>
		</wsdl:fault>
	  </wsdl:operation>

	  <wsdl:operation name="preConfirmBatchPayments">
		<soap:operation
		  soapAction="" />
		<wsdl:input>
		  <soap:body use="literal"/>
		</wsdl:input>
		<wsdl:output>
		  <soap:body use="literal"/>
		</wsdl:output>
		<wsdl:fault name="fault">
		  <soap:fault use="literal" name="fault"/>
		</wsdl:fault>
	  </wsdl:operation>

	  <wsdl:operation name="confirmBatchPayments">
		<soap:operation
		  soapAction="" />
		<wsdl:input>
		  <soap:body use="literal"/>
		</wsdl:input>
		<wsdl:output>
		  <soap:body use="literal"/>
		</wsdl:output>
		<wsdl:fault name="fault">
		  <soap:fault use="literal" name="fault"/>
		</wsdl:fault>
	  </wsdl:operation>

	  <wsdl:operation name="checkClientsDeferredPayments">
		<soap:operation
				soapAction="" />
		<wsdl:input>
			<soap:body use="literal"/>
		</wsdl:input>
		<wsdl:output>
			<soap:body use="literal"/>
		</wsdl:output>
		<wsdl:fault name="fault">
			<soap:fault use="literal" name="fault"/>
		</wsdl:fault>
	  </wsdl:operation>

	</wsdl:binding>


	<wsdl:service name="obsPaymentOrderWS">
		<wsdl:documentation>služba pro jednorázové platby</wsdl:documentation>
		<wsdl:port binding="tns:obsPaymentOrderWSSOAP" name="obsPaymentOrderWSSOAP">
			<soap:address location="http://TO-BE-CHANGED/ib/core/ppf/ws/obsPaymentOrderWS"/>
		</wsdl:port>
	</wsdl:service>
</wsdl:definitions>