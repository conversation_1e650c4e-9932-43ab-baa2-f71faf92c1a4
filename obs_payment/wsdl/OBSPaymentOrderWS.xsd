<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema"
            xmlns:Q1="http://arbes.com/ib/core/ppf/ws/common/"
            xmlns:ta="http://airbank.cz/obs/ws/common/trustedAccountsWS"
            xmlns:Q2="http://homecredit.net/manhattan-ib/ws/cz/FilterSchema"
            xmlns:Q3="http://arbes.com/ib/core/ppf/ws/obsPaymentOrderWS/"
            targetNamespace="http://arbes.com/ib/core/ppf/ws/obsPaymentOrderWS/">
  <xsd:import schemaLocation="../xsd/Filter.xsd" namespace="http://homecredit.net/manhattan-ib/ws/cz/FilterSchema"/>
  <xsd:import schemaLocation="../xsd/AutentizationAuthorization.xsd" namespace="http://arbes.com/ib/core/ppf/ws/common/"/>
  <xsd:import schemaLocation="../xsd/TransactionTO.xsd" namespace="http://arbes.com/ib/core/ppf/ws/common/"/>
  <xsd:import schemaLocation="../xsd/trustedAccounts.xsd" namespace="http://airbank.cz/obs/ws/common/trustedAccountsWS"/>
  <xsd:element name="findPaymentOrderRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="filter" type="Q2:SelectFilter"
                     maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation><![CDATA[Explicit filtering and sorting conditions (for more information about all the attributes, refer to description of PaymentOrderTO type).

Following attributes can be used for  filtering:
- IDBANKACCOUNT - internal OBS ID of the debited bank account retrieved using one of the OBSAccount services (integer, supports operation =)
- VALIDFROM - start of validity, i.e. the payment date (date, supports operations =, >, <, >=, <=)
- VALIDTO - end of validity (date, supports operations =, >, <, >=, <=)
- STATUS -  any subset of the FilterPaymentOrderStatus enumeration (enumeration, supports operations IN, =). If there is no status specified, only payment orders, which have not yet been successfully processed, are returned.
- IDCATEGORY - internal ID of a payment category as returned by one of the OBSAdvisory services (integer, supports operation =)
- TRNTYPE - a single value from the FilterRealizedTransactionType enumeration (enumeration, supports operation =)
- AMOUNT - amount of payment order (decimal, supports operations =, >, <, >=, <=)
- CURRENCY - currency of payment order (string, supports operations =)
- IDDOORDOCUMENT - identificator of document in DOOR system
- HASDOORDOCUMENT - payment order is paired with the document - value Y as yes or N as no
- IDCOUNTERPARTY - long, counterparty identifier
- IDHOMERCARD - id card of card transaction
- DIRECTION - I for incoming transactions, O for outgoing transactions
- MERCHANTNAME - merchant name of card transaction
- VS - variable symbol
- KS - constant symbol
- SS - specific symbol
- AGENDA -  any subset of the FilterPaymentAgenda enumeration (enumeration, supports operations IN, =).
The only supported logical operation is And (which is only a limitation of this method, not filter in general).

One of the following attributes can be used for ordering:
- IDCATEGORY
- VALIDFROM
- STATUS (payment orders are ordered according to the internal status ID)
- TRNTYPE (payment orders are ordered according to the type code)
- AMOUNT - payment order amount in required currency

If there is no ordering attribute specified, the result is ordered by VALIDFROM and payment order ID (both in descending order). If there is one ordering attribute specified, the payment order ID is added as the last ordering attribute and the ordering direction is the same as for the specified attribute.
            ]]></xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="findPaymentOrderResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="idRequiredTransaction" type="xsd:long"
                     maxOccurs="unbounded" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="getPaymentOrdersRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="idRequiredTransaction" type="xsd:long" maxOccurs="unbounded" minOccurs="1"/>
        <xsd:element name="includeDeleted" type="xsd:boolean" minOccurs="0" default="false">
          <xsd:annotation>
            <xsd:documentation>Include transactions deleted by deletePaymentOrder call?</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="getPaymentOrdersResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="paymentOrder" type="Q1:PaymentOrderTO"
                     maxOccurs="unbounded" minOccurs="0">
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="deletePaymentOrderRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="idRequiredTransaction"
                     type="xsd:long" maxOccurs="1" minOccurs="1">
        </xsd:element>
        <xsd:element name="authorization" type="Q1:AuthType" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="deletePaymentOrderResponse">
    <xsd:complexType>
      <xsd:sequence>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="setPaymentOrderRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="paymentOrder"
                     type="Q1:PaymentOrderTO" maxOccurs="1" minOccurs="1">
        </xsd:element>
        <xsd:element name="authorization"
                     type="Q1:AuthType" maxOccurs="1" minOccurs="0">
        </xsd:element>
        <xsd:element name="validate" type="xsd:boolean"
                     maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>
              true - OBS pouze validuje data
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="skipSoftCheck" type="xsd:string" maxOccurs="unbounded" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Soft checks that be skipped.</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="immediateOrRejected" type="xsd:boolean" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Sign whether the order shall obey My air rules</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="partnerCode" type="Q1:PartnerCode" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="paymentOrderEventId" type="xsd:string" minOccurs="0" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Payment Order Event ID</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="authLevel" type="xsd:string"  minOccurs="0" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Authorization Level (ZERO, ONE, TWO)</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="deferPayment" type="xsd:boolean" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>flag indicating whether the payment should be deferred</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="isPayment2Contact" type="xsd:boolean" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>flag indicating whether the payment is sent to contact.</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="batchPayment" type="xsd:boolean" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>flag indicating whether the payment is part of a uploaded file batch</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="isQuickPaymentOrder" type="xsd:boolean" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>flag indicating whether the payment is quick.</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="setPaymentOrderResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="idPaymentOrder" type="xsd:long" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>primární klíč nově založeného / editovaného platebního příkazu</xsd:documentation>
          </xsd:annotation></xsd:element>
        <xsd:element name="insufficientFundsFuture" type="xsd:boolean" minOccurs="0" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>there are insufficient funds on debit account for payment orders due into future</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="realizationStatus" type="Q1:RealizationStatusType" minOccurs="0" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>realization status after set/edit payment order (ON_LINE_REALIZED, ON_LINE_NOT_REALIZED_YET, PLANNED)</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="realizationStatusReason" type="Q1:RealizationStatusReason" minOccurs="0" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>realization status reason if realization status is STD_PAYMENT_FALLBACK</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="xamosStatusCode" type="Q1:XamosStatusCode" minOccurs="0" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Status of XAMOS instant payment</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="xamosErrorCode" type="Q1:XamosErrorCode" minOccurs="0" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Error code of XAMOS instant payment</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="setForeignPaymentOrderRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="paymentOrder" type="Q1:ForeignPaymentOrderTO" minOccurs="1" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>object of type SepaPaymentOrderTO, PsdPaymentOrderTO or NonPsdPaymentOrderTO
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="authorization"
                     type="Q1:AuthType" maxOccurs="1" minOccurs="0">
        </xsd:element>
        <xsd:element name="validate" type="xsd:boolean" minOccurs="1" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>true - validates data only, false - validates data and creates/updates payment order</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="skipSoftCheck" type="xsd:string" minOccurs="0" maxOccurs="unbounded">
          <xsd:annotation>
            <xsd:documentation>Soft checks that be skipped.</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="paymentOrderEventId" type="xsd:string" minOccurs="0" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Payment Order Event ID</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="authLevel" type="xsd:string"  minOccurs="0" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Authorization Level (ZERO, ONE, TWO)</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="deferPayment" type="xsd:boolean" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>flag indicating whether the payment should be deferred</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="batchPayment" type="xsd:boolean" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>flag indicating whether the payment is part of a uploaded file batch</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="setForeignPaymentOrderResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="idPaymentOrder" type="xsd:long" minOccurs="0" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>identification number of new or edited payment order</xsd:documentation>
          </xsd:annotation></xsd:element>
        <xsd:element name="insufficientFundsFuture" type="xsd:boolean" minOccurs="0" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>there are insufficient funds on debit account for payment orders due into future</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="realizationStatus" type="Q1:RealizationStatusType" minOccurs="0" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>realization status after set/edit payment order (ON_LINE_REALIZED, ON_LINE_NOT_REALIZED_YET, PLANNED)</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="getValidFromRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="contraAccountNumber"
                     type="xsd:string" maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>
              číslo protiúčtu
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="contraAccountNumberPrefix"
                     type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>
              prefix protiúčtu
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="contraBankCode"
                     type="xsd:string" maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>
              kód banky protiúčtu
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="contraBankCountry" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>kod zeme banky druhe strany</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="priority" type="xsd:boolean"
                     maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>
              true - prioritní platba
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="validFrom"
                     type="xsd:dateTime" maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>
              datum počátku platnosti platby
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="currency" type="xsd:string"
                     maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>
              kód měny transakce
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="idBankAccountDebit"
                     type="xsd:long" maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>
              id zdrojového účtu
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>

      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="getValidFromResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="result" type="xsd:boolean" maxOccurs="1"
                     minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>
              true - datum je pro zadané podmínky platné
              false -
              datum počátku není pro danou platbu
              přípustné
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="validFrom" type="xsd:dateTime" maxOccurs="1"
                     minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Nejbližší možné datum platby (pokud je result =
              false).
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="reCalcRequest">
    <xsd:complexType>
      <xsd:sequence>

        <xsd:element name="currencyCredit" type="xsd:string"
                     maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>
              cílová měna
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="currencyDebit" type="xsd:string"
                     maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>
              zdrojová měna
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="amountDebit" type="xsd:decimal"
                     maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>
              částka pro přepočet ve zdrojové měně
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="amountCredit" type="xsd:decimal"
                     maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>
              částka pro přepočet v cílové měně
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="idRealizedTransaction" type="xsd:long" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Realizovaná transakce pro kterou se chce vrátit kurz, v jakém byla zrealizována.</xsd:documentation>
          </xsd:annotation></xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="reCalcResponse">
    <xsd:complexType>
      <xsd:sequence>

        <xsd:element name="amount" type="xsd:decimal"
                     maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>
              přepočtená částka
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="rate" type="xsd:decimal"
                     maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>kurz v obvyklém tvaru. Napr. 30 kc za 1 Euro</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="currency1" type="xsd:string"
                     maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>první měna kurzu</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="currency2" type="xsd:string"
                     maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>první měna kurzu</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="lot1" type="xsd:int" maxOccurs="1"
                     minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>jednotka první měny</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="lot2" type="xsd:int" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>jednotka druhé měny</xsd:documentation>
          </xsd:annotation></xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="getRemainingTransactionLimitRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="channelCode"
                     type="Q3:PaymentOrderChannelType" minOccurs="1" maxOccurs="1">
        </xsd:element>
        <xsd:element name="contraAccountNumber" type="xsd:string" minOccurs="0" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>number of contra account</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="contraBankCode" type="xsd:string" minOccurs="0" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>code of credit bank</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="getRemainingTransactionLimitResponse">
    <xsd:complexType>
      <xsd:sequence>

        <xsd:element name="amount" type="xsd:decimal" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>
              zostávajúci limit v CZK pro cizí účet, NULL pro vlastní účet
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="returnPaymentRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="idPaymentOrder" type="xsd:long" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>
              id predepsane transakce
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="idBankAccountDb" type="xsd:long" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>
              id uctu na debetni strane
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="idBankAccountCr" type="xsd:long" maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>
              id uctu na kreditni strane
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="vs" type="xsd:string" maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>
              variabilni symbol
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="ss" type="xsd:string" maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>
              specificky symbol
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="partnerCode" type="Q1:PartnerCode" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="amount" type="xsd:decimal" minOccurs="0" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>
              amount to return
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="returnPaymentResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="idPaymentOrder" type="xsd:long" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>primární klíč nově založeného / editovaného platebního příkazu</xsd:documentation>
          </xsd:annotation></xsd:element>
        <xsd:element name="realizationStatus" type="Q1:RealizationStatusType" minOccurs="0" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>realization status after set/edit payment order (ON_LINE_REALIZED, ON_LINE_NOT_REALIZED_YET, PLANNED, ENDED)</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="setVisibilityRequest">
    <xsd:complexType>
      <xsd:sequence>

        <xsd:element name="idPaymentOrder" type="xsd:long" maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>identifikace předepsané transakce v OBS</xsd:documentation>
          </xsd:annotation></xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="setVisibilityResponse">
    <xsd:complexType>
      <xsd:sequence>

      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="setVisibilityFault">
    <xsd:complexType>
      <xsd:sequence>

        <xsd:element name="setVisibilityFault"
                     type="xsd:string">
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="getForeignPaymentOrderTypeRequest">
    <xsd:annotation>
      <xsd:documentation>Get type of foreign payment order</xsd:documentation>
    </xsd:annotation>
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="contraBankCode"
                     type="xsd:string" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>code of credit bank</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="requiredCurrency"
                     type="xsd:string" minOccurs="1" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>required currency of payment order</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="amountInRequiredCurrency"
                     type="Q1:Amount" minOccurs="0" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>amount in required currency of payment order</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="contraAccountNumber"
                     type="xsd:string" minOccurs="1" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>number of credit account</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="skipSoftCheck" type="xsd:string" minOccurs="0" maxOccurs="unbounded">
          <xsd:annotation>
            <xsd:documentation>soft checks that be skipped</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="getForeignPaymentOrderTypeResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="type" type="Q1:ForeignPaymentOrderType" minOccurs="1" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>type of foreign payment order - SEPA, PSD, NON_PSD</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="contraBank"
                     type="Q1:ContraBankTO" minOccurs="0" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>information about credit bank</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="validFrom" type="xsd:dateTime"
                     minOccurs="1" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>earliest possible date of realization transaction</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="error" type="Q1:ErrorGetForeignPaymentOrderType"
                     minOccurs="0" maxOccurs="unbounded">
          <xsd:annotation>
            <xsd:documentation>error code</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="warning" type="Q1:WarningGetForeignPaymentOrderType"
                     minOccurs="0" maxOccurs="unbounded">
          <xsd:annotation>
            <xsd:documentation>warning code</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="getFeeAmountRequest">
    <xsd:annotation>
      <xsd:documentation>Get type of foreign payment order</xsd:documentation>
    </xsd:annotation>
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="type" type="Q1:ForeignPaymentOrderType" minOccurs="1" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>type of foreign payment order - SEPA, PSD, NON_PSD</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="feeCoverage" type="Q1:NonPsdFeeCoverageType" minOccurs="0" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>cover of fees (SHARE / OUR)</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="requiredCurrency" type="xsd:string" minOccurs="1" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>required currency of payment order</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="amountInRequiredCurrency" type="xsd:decimal" minOccurs="1" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>amount in required currency of payment order</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="idBankAccountDebit" type="xsd:long" minOccurs="1" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>identification number of debit account</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="getFeeAmountResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="transactionFee" type="Q1:FeeAmount" minOccurs="1" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>amount of trasaction fee in transaction currency</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="transactionFeeInAccountCurrency" type="Q1:FeeAmount" minOccurs="1" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>amount of trasaction fee in debit account currecy</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="intermediaryFee" type="Q1:FeeAmount" minOccurs="0" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>amount of intermediary fee in transaction currency</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="intermediaryFeeInAccountCurrency" type="Q1:FeeAmount" minOccurs="0" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>amount of intermediary fee in debit account currency</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="validateRequest">
    <xsd:annotation>
      <xsd:documentation>Payment order items for validations</xsd:documentation>
    </xsd:annotation>
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="contraAccountNumberPrefix" type="xsd:string" minOccurs="0" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>prefix of contra account</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="contraAccountNumber" type="xsd:string" minOccurs="0" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>number of contra account</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="contraBankCode" type="xsd:string" minOccurs="0" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>code of credit bank</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="constantSymbol" type="xsd:string" minOccurs="0" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>constant symbol</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="variableSymbol" type="xsd:string" minOccurs="0" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>variable symbol</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="specificSymbol" type="xsd:string" minOccurs="0" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>specific symbol</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="amountInRequiredCurrency" type="xsd:decimal" minOccurs="0" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>amount in required currency</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="requiredCurrency" type="xsd:string" minOccurs="0" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>required currency</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="validFrom" type="xsd:dateTime" minOccurs="0" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>date of validity of payment order</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="paymentType" minOccurs="0" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>type of payment</xsd:documentation>
          </xsd:annotation>
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:enumeration value="PAYMENT_ORDER"/>
              <xsd:enumeration value="STANDING_ORDER"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="isPayment2Contact" type="xsd:boolean" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>flag indicating whether the payment is sent to contact. </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="validateResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="errors" type="Q1:ErrorsListType" minOccurs="0" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>collection of validation errors:

              code / attribute / value : description
              CLERR_NO_DATA_FOUND / contraBankCode / : Credit bank not found
              WWW_CZ_ACCOUNT_NUMBER / contraAccountNumberPrefix / : credit account number prefix has not valid czech account format
              WWW_CZ_ACCOUNT_NUMBER / contraAccountNumber / : credit account number has not valid czech account format
              CLERR_PATTERN_ERROR / CONSTSYMBOL / : invalid constant symbole
              CLERR_PATTERN_ERROR / VARSYMBOL / : invalid variable symbole
              CLERR_PATTERN_ERROR / SPECSYMBOL / : invalid specific symbol
              CLERR_NO_DATA_FOUND / requiredCurrency / : currency not found
              CLERR_EXCEEDED_ALLOWED_PRECISION / amountInRequiredCurrency / : amount exceeded allowed precision for given currency
              CLERR_NUMBER_IS_LOWER / amountInRequiredCurrency / : amount must be greater than or equal to zero
              ERR_BAD_VALIDITY / validFrom / : date is in the past
              ERROR_TRN_VALIDFROM / validFrom  / : for the specified date cannot be transaction realized.
              ERR_TRN_ACCDEBIT_BLACKLIST - debit account is on account blacklist
              ERR_TRN_ACCCREDIT_BLACKLIST - credit account is on account blacklist
              ERR_TRN_ACCDEBIT_ACCCREDIT_BLACKLIST - debit and credit accounts are on account blacklist
              INVALID_CREDIT_ACCOUNT / contraAccountNumber / : chybne cislo kreditniho uctu
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="isInstantPaymentEligibleRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="bankCodeCreditor" type="xsd:string">
          <xsd:annotation>
            <xsd:documentation>Numeric code of a czech bank to receive an instant payment</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="accountNumberDebtor" type="xsd:string" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Account number of debit account</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="isInstantPaymentEligibleResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="eligibilityResult" type="xsd:boolean">
          <xsd:annotation>
            <xsd:documentation>
              true:  It IS possible to send an instant payment to the receiving bank at this moment
              false: It is NOT possible to send an instant payment to the receiving bank at this moment
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="unfitReason" type="Q1:ErrorItemType" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>
              A specific reason WHY it is NOT possible to send an instant payment to the receiving bank at this moment
              code                            / attribute / value : description
              UNSUPPORTED_CREDITOR_AGENT      / bankCode  /       : The receiving bank does not support instant payments at this moment
              CREDITOR_AGENT_SCHEDULED_OUTAGE / bankCode  /       : Scheduled outage is in progress at the receiving bank at this moment
              XAMOS_SCHEDULED_OUTAGE          /           /       : Scheduled outage is in progress at the processing centre of instant payments at this moment
              UNKNOWN_BANK_CODE               / bankCode  /       : Unknown receiving bank code
              OUTGOING_PAYMENTS_DISABLED      /           /       : Outgoing instant payments were disabled/forbidden (using INSTANTPAYMENTS/OUTGOING_ENABLED global parameter)
              INHOUSE_PAYMENT_NOT_ALLOWED     /           /       : The receving bank is "our" bank and instant payments (via XAMOS) are disabled using INSTANTPAYMENTS/INHOUSE_ALLOWED global parameter.
                                                                    Instant payments between accounts in "our" bank via XAMOS are typically disabled in the production environment (there is no need to send money to the account in "our" bank via XAMOS).
                                                                    It can be useful to enable instant payments between accounts in "our" bank via XAMOS in a test environment in order to test functionality of instant payments.
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="fee" type="xsd:decimal" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>
              Amount of fee (if any) in home currency we will charge to our customer sending instant payment
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="getPaymentFeeDetailRequest">
    <xsd:complexType>
      <xsd:complexContent>
        <xsd:extension base="Q1:getPaymentFeeDetailRqCommon">
          <xsd:sequence>
            <xsd:element name="processInstantly" type="xsd:boolean" minOccurs="0">
              <xsd:annotation>
                <xsd:documentation>Zpracovat platbu okamžitě</xsd:documentation>
              </xsd:annotation>
            </xsd:element>
            <xsd:element name="contraAccountNumberPrefix" type="xsd:string" minOccurs="0">
              <xsd:annotation>
                <xsd:documentation>Předčíslí protiúčtu</xsd:documentation>
              </xsd:annotation>
            </xsd:element>
            <xsd:element name="contraAccountNumber" type="xsd:string">
              <xsd:annotation>
                <xsd:documentation>Číslo protiúčtu</xsd:documentation>
              </xsd:annotation>
            </xsd:element>
            <xsd:element name="contraBankCode" type="xsd:string">
              <xsd:annotation>
                <xsd:documentation>Kód banky protiúčtu</xsd:documentation>
              </xsd:annotation>
            </xsd:element>
          </xsd:sequence>
        </xsd:extension>
      </xsd:complexContent>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="getPaymentFeeDetailResponse">
    <xsd:complexType>
      <xsd:complexContent>
        <xsd:extension base="Q1:getPaymentFeeDetailRspCommon" />
      </xsd:complexContent>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="getForeignPaymentFeeDetailRequest">
    <xsd:complexType>
      <xsd:complexContent>
        <xsd:extension base="Q1:getPaymentFeeDetailRqCommon">
          <xsd:sequence>
            <xsd:element name="type" type="Q1:ForeignPaymentOrderType">
              <xsd:annotation>
                <xsd:documentation>typ zahranicniho platebniho prikazu - SEPA, PSD, NON_PSD</xsd:documentation>
              </xsd:annotation>
            </xsd:element>
            <xsd:element name="feeCoverage" type="Q1:NonPsdFeeCoverageType" minOccurs="0">
              <xsd:annotation>
                <xsd:documentation>kryti poplatku (SHARE / OUR)</xsd:documentation>
              </xsd:annotation>
            </xsd:element>
            <xsd:element name="contraBankCode" type="xsd:string">
              <xsd:annotation>
                <xsd:documentation>Kód banky protiúčtu</xsd:documentation>
              </xsd:annotation>
            </xsd:element>
            <xsd:element name="contraBankCountry" type="xsd:string" maxOccurs="1" minOccurs="0">
              <xsd:annotation>
                <xsd:documentation>kod zeme banky druhe strany</xsd:documentation>
              </xsd:annotation>
            </xsd:element>
            <xsd:element name="contraAccount" type="xsd:string" maxOccurs="1" minOccurs="0">
              <xsd:annotation>
                <xsd:documentation>cislo uctu nebo IBAN uctu prijemce platby</xsd:documentation>
              </xsd:annotation>
            </xsd:element>
          </xsd:sequence>
        </xsd:extension>
      </xsd:complexContent>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="getForeignPaymentFeeDetailResponse">
    <xsd:complexType>
      <xsd:complexContent>
        <xsd:extension base="Q1:getPaymentFeeDetailRspCommon">
          <xsd:sequence>
            <xsd:element name="nonPsdShaFeeIncluded" type="xsd:boolean" minOccurs="0">
              <xsd:annotation>
                <xsd:documentation>pro true je poplatek treti stranou zahrnut ve strzene castce (korespondenti mohou ponizit castku o sve poplatky)</xsd:documentation>
              </xsd:annotation>
            </xsd:element>
            <xsd:element name="nonPsdOurFeeAmountInAccountCurrency" type="Q1:AmountTO" minOccurs="0">
              <xsd:annotation>
                <xsd:documentation>poplatek za platbu zprostredkovatelske bance v mene uctu</xsd:documentation>
              </xsd:annotation>
            </xsd:element>
            <xsd:element name="nonPsdOurFeeAmount" type="Q1:AmountTO" minOccurs="0">
              <xsd:annotation>
                <xsd:documentation>poplatek za platbu zprostredkovatelske bance v mene poplatku</xsd:documentation>
              </xsd:annotation>
            </xsd:element>
          </xsd:sequence>
        </xsd:extension>
      </xsd:complexContent>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="uploadBatchRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="fileName" type="xsd:string">
          <xsd:annotation>
            <xsd:documentation>name of the file as imported by customer</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="fileFormat" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>format of the file (if recognized)</xsd:documentation>
          </xsd:annotation>
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:enumeration value="ABO"/>
              <xsd:enumeration value="XML"/>
              <xsd:enumeration value="UNKNOWN"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="fileId" type="xsd:string">
          <xsd:annotation>
            <xsd:documentation>file id (i.e. uuid returned by DMS system) in archive storage </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="fileCreatedAt" type="xsd:dateTime" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>file creation date/time</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="msgId" type="xsd:string" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>unique file identifier (generated by customer) - can be used to detect duplicate upload of the same file</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="uploader">
          <xsd:annotation>
             <xsd:documentation>Identification of a customer (and his/her role) who uploaded the file</xsd:documentation>
          </xsd:annotation>
          <xsd:complexType>
            <xsd:sequence>
              <xsd:element name="cuid" type="xsd:long">
                <xsd:annotation>
                  <xsd:documentation>customer unique id</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
              <xsd:element name="gcid" type="xsd:long">
                <xsd:annotation>
                  <xsd:documentation>general contract id</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
            </xsd:sequence>
          </xsd:complexType>
        </xsd:element>
        <xsd:choice>
          <xsd:element name="error" type="Q3:BatchError" minOccurs="0" maxOccurs="unbounded">
            <xsd:annotation>
              <xsd:documentation>Error detected "at file level"</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="payment" minOccurs="0" maxOccurs="unbounded">
            <xsd:annotation>
              <xsd:documentation>Individual payment to be created based on content of the file</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="debtorAccount" type="xsd:string">
                  <xsd:annotation>
                    <xsd:documentation>
                      Debtor account number in format corresponding to the payment domain.
                      i.e. NNNNNN-NNNNNNNNNN in domestic czech payment, IBAN in SEPA payment, etc.
                    </xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="debtorBankCode" type="xsd:string">
                  <xsd:annotation>
                    <xsd:documentation>
                      Debtor bank code in format corresponding to the payment domain.
                    </xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="creditorAccount" type="xsd:string">
                  <xsd:annotation>
                    <xsd:documentation>
                      Creditor account number in format corresponding to the payment domain.
                      i.e. NNNNNN-NNNNNNNNNN in domestic czech payment, IBAN in SEPA payment, etc.
                    </xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="creditorBankCode" type="xsd:string">
                  <xsd:annotation>
                    <xsd:documentation>
                      Creditor bank code in format corresponding to the payment domain.
                    </xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="creditorName" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>
                      Creditor name
                    </xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="creditorAddr" type="Q3:PostalAddress" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>
                      Creditor address
                    </xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="amount" type="xsd:decimal">
                  <xsd:annotation>
                    <xsd:documentation>Payment amount.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="currency" type="xsd:string">
                  <xsd:annotation>
                    <xsd:documentation>Payment currency. ISO alpha code (i.e. CZK) or ISO numeric code (i.e. 203).</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="dueDate" type="xsd:date">
                  <xsd:annotation>
                    <xsd:documentation>Payment due date.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="paymentType" type="Q3:BatchPaymentType">
                  <xsd:annotation>
                    <xsd:documentation>Type of payment</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="text" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Free text associated with payment</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="vs" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Variable symbol (czech domestic payment system specific). Up to ten digits</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="ss" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Specific symbol (czech domestic payment system specific). Up to ten digits</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="ks" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Constant symbol (czech domestic payment system specific). Up to ten digits</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="svcLvl" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Service level. SEPA for SEPA payments</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="pmtInfId" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Payment batch identification (generated by customer)</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="instrId" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Instruction identification (generated by customer)</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="endToEndId" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>End To End Identification (generated by customer)</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="purposeText" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Payment purpose (free text)</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="error" type="Q3:BatchError" minOccurs="0" maxOccurs="unbounded">
                  <xsd:annotation>
                    <xsd:documentation>Error detected "at payment level"</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
              </xsd:sequence>
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="uploadBatchResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="id" type="xsd:long">
          <xsd:annotation>
            <xsd:documentation>internal identifier of the uploaded batch</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="status">
          <xsd:annotation>
            <xsd:documentation>
              Upload status (were there some problems when processing the payments?)
              OK - no errors
              ERRORS - at least one error (at file level or at payment level)
            </xsd:documentation>
          </xsd:annotation>
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:enumeration value="OK"/>
              <xsd:enumeration value="ERRORS"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="errorOnFileLevel" type="Q3:BatchError" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>
              Additional errors (if any) detected at file level.
              It could be:
              - copy of (first) file level error from uploadBatchRequest detected by caller of uploadBatch
              - file level error detected during uploadBatch processing
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="getBatchSectionsRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:choice>
          <xsd:element name="gcid" type="xsd:long">
            <xsd:annotation>
              <xsd:documentation>general contract id the batches are connected to</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="profileId" type="xsd:long">
            <xsd:annotation>
              <xsd:documentation>profile id of logged-in frontend user</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
        </xsd:choice>
        <xsd:element name="id" type="xsd:long" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>internal identifier of the batch section. To be used when data about one specific batch section is needed</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="batchId" type="xsd:long" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>internal identifier of the batch. The batch sections to be returned were uploaded in this batch</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="uploadedAtFrom" type="xsd:dateTime" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>filter condition for batch upload datetime </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="uploadedAtTo" type="xsd:dateTime" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>filter condition for batch upload datetime </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="fileNamePattern" type="xsd:string" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>
              filter condition for a filename the batch section was uploaded in
              when not empty, will be used in condition like this: UPPER(TRIM("batch file name")) LIKE UPPER(TRIM(fileNamePattern))
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="accountNumber" type="xsd:string" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>
              filter condition for an account number the batch section is connected to
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="status" type="Q3:BatchSectionStatus" minOccurs="0" maxOccurs="unbounded">
          <xsd:annotation>
            <xsd:documentation>
              filter conditions for batch section status
              multiple statuses are linked using OR operator in the WHERE clause
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="ordering" type="Q3:Ordering" minOccurs="0" default="DESC">
          <xsd:annotation>
            <xsd:documentation>requested ordering of the result set</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="getBatchSectionsResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="batchSection" type="Q3:BatchSection" minOccurs="0" maxOccurs="unbounded"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="getBatchSectionInfoRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="id" type="xsd:long" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>internal identifier of the batch section</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="profileId" type="xsd:long">
          <xsd:annotation>
            <xsd:documentation>profile id of logged-in frontend user</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="getBatchSectionInfoResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="accountBalance" type="Q1:AmountTO" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>
              Dispo balance on the account the batch section is associated with
              It will be present only in case the caller of getBatchSectionInfo (profileId in request) has access rights to the account
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="getBatchPaymentsRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="cuid" type="xsd:long" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>cuid of the user logged in in the calling frontend</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="batchSectionId" type="xsd:long">
          <xsd:annotation>
            <xsd:documentation>internal identifier of the batch section. The batch payments to be returned are part of this batch section</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="status" type="Q3:BatchPaymentStatus"  minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>filter condition for the status of the batch payment</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="creditorAccount" type="xsd:string"  minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>
              filter condition for the credit account number of the batch payment
              IBAN in SEPA payment (optionally with bank BIC code), i.e. ****************** or ******************/AIRACZPP
              prefix-base/bankCode in domestic payment (prefix and bankcode optional), i.e. 19-522001/5100 or 19-522001 or 522001
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="creditorAccountPrefix" type="xsd:string" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>
              filter condition for creditor account number prefix in domestic payment
              value of element creditorAccount (if it contains account prefix) takes precedence before value of creditorAccountPrefix
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="creditorAccountBase" type="xsd:string" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>
              filter condition for creditor account number base in domestic payment
              value of element creditorAccount (if it contains account base) takes precedence before value of creditorAccountBase
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="creditorBankCode" type="xsd:string" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>
              filter condition for creditor bank code
              value of element creditorAccount (if it contains bank code) takes precedence before value of creditorBankCode
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="amountFrom" type="xsd:decimal"  minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>filter condition for the lower bound of the batch payment amount </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="amountTo" type="xsd:decimal"  minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>filter condition for the upper bound of the batch payment amount </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="currency" type="xsd:string"  minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>filter condition for the the currency of the batch payment amount</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="vs" type="xsd:string"  minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>filter condition for the variable symbol of the batch payment</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="fullTextPattern" type="xsd:string"  minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>
              filter condition for the fulltext search of the batch payment
              when not empty, will be used in a condition like this:
                UPPER(TRIM("text for creditor")) LIKE UPPER(TRIM(fullTextPattern))
                OR
                UPPER(TRIM("end2EndId")) LIKE UPPER(TRIM(fullTextPattern))
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="ordering" type="Q3:Ordering" minOccurs="0" default="DESC">
          <xsd:annotation>
            <xsd:documentation>requested ordering of the result set</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="getBatchIDsFromTrnIDsRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="transactionId" type="xsd:long" maxOccurs="unbounded">
          <xsd:annotation>
            <xsd:documentation>collection of transaction identifiers to find batch identifiers for</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="getBatchIDsFromTrnIDsResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="ids" type="Q3:BatchIDsForTrnIDs" maxOccurs="unbounded">
          <xsd:annotation>
            <xsd:documentation>collection of transaction and batch identifiers</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="getBatchPaymentsResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="batchPayment" type="Q3:BatchPayment" minOccurs="0" maxOccurs="unbounded"/>
        <xsd:element name="dueDatesToBeCorrected" type="xsd:boolean"  minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>true when the due date of at least one of the batch payments needs to be corrected</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="removeBatchPaymentsRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="profileId" type="xsd:long">
          <xsd:annotation>
            <xsd:documentation>profile id of logged-in frontend user</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:choice>
          <xsd:element name="batchSectionId" type="xsd:long">
            <xsd:annotation>
              <xsd:documentation>internal identifier of the batch section to be removed</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="batchPaymentId" type="xsd:long">
            <xsd:annotation>
              <xsd:documentation>internal identifier of the batch payment to be removed</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
        </xsd:choice>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="removeBatchPaymentsResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="status">
          <xsd:annotation>
            <xsd:documentation>
              Status (were there some problems when removing the payments?)
              OK - no errors
              ERRORS - at least one error
            </xsd:documentation>
          </xsd:annotation>
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:enumeration value="OK"/>
              <xsd:enumeration value="ERRORS"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="error" type="Q3:BatchError" minOccurs="0" maxOccurs="unbounded">
          <xsd:annotation>
            <xsd:documentation>Errors detected when removing payments</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="addOrEditBatchPaymentRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="profileId" type="xsd:long">
          <xsd:annotation>
            <xsd:documentation>profile id of logged-in frontend user</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="batchPayment" type="Q3:BatchPayment"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="addOrEditBatchPaymentResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="status">
          <xsd:annotation>
            <xsd:documentation>
              Status (were there some problems when adding/editing the payment?)
              OK - no errors
              ERRORS - at least one error
            </xsd:documentation>
          </xsd:annotation>
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:enumeration value="OK"/>
              <xsd:enumeration value="ERRORS"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:choice>
          <xsd:element name="batchPaymentId" type="xsd:long">
            <xsd:annotation>
              <xsd:documentation>internal identifier of the batch payment in case everything went with no problems</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="error" type="Q3:BatchError" maxOccurs="unbounded">
            <xsd:annotation>
              <xsd:documentation>Errors detected when adding/editing the payment</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
        </xsd:choice>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="validateBatchPaymentsRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="profileId" type="xsd:long">
          <xsd:annotation>
            <xsd:documentation>profile id of logged-in frontend user</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="batchSectionId" type="xsd:long">
          <xsd:annotation>
            <xsd:documentation>internal identifier of the batch section to be validated</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="validateBatchPaymentsResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="status">
          <xsd:annotation>
            <xsd:documentation>
              Status (were there some problems found during validation?)
              OK - no errors
              ERRORS - at least one error
            </xsd:documentation>
          </xsd:annotation>
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:enumeration value="OK"/>
              <xsd:enumeration value="ERRORS"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="error" type="Q3:BatchError" minOccurs="0" maxOccurs="unbounded">
          <xsd:annotation>
            <xsd:documentation>Errors detected during validation</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="preConfirmBatchPaymentsRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="profileId" type="xsd:long">
          <xsd:annotation>
            <xsd:documentation>profile id of logged-in frontend user</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="batchSectionId" type="xsd:long">
          <xsd:annotation>
            <xsd:documentation>internal identifier of the batch section to be handed over to confirmation</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="preConfirmBatchPaymentsResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="status">
          <xsd:annotation>
            <xsd:documentation>
              Status (were there some problems when handing over the payments to the confirmation?)
              OK - no errors
              ERRORS - at least one error
            </xsd:documentation>
          </xsd:annotation>
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:enumeration value="OK"/>
              <xsd:enumeration value="ERRORS"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="error" type="Q3:BatchError" minOccurs="0" maxOccurs="unbounded">
          <xsd:annotation>
            <xsd:documentation>Errors detected when handing over the payments to the confirmation</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="confirmBatchPaymentsRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="profileId" type="xsd:long">
          <xsd:annotation>
            <xsd:documentation>profile id of logged-in frontend user</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="batchSection" type="Q3:BatchSection">
          <xsd:annotation>
            <xsd:documentation>payment batch section (containing batch payments) to be confirmed</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="authorization" type="Q1:AuthType" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>authorization data</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="authLevel" type="xsd:string"  minOccurs="0" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Authorization Level (ZERO, ONE, TWO)</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="batchPaymentEventId" type="xsd:string" minOccurs="0" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Batch payment event unique identifier</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="deferBatchSectionReason" type="Q3:DeferredPaymentReason" minOccurs="0" maxOccurs="unbounded">
          <xsd:annotation>
            <xsd:documentation>collection of reasons (if any, in order of their priority) why to defer the whole batch section (all payments in the batch section)</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="deferPayment" type="Q3:DeferBatchPayment" minOccurs="0" maxOccurs="unbounded">
          <xsd:annotation>
            <xsd:documentation>collection of batch payments (from this batch section) to be deferred</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="confirmBatchPaymentsResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="status">
          <xsd:annotation>
            <xsd:documentation>
              Status (were there some problems during confirmation?)
              OK - no errors
              ERRORS - at least one error
            </xsd:documentation>
          </xsd:annotation>
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:enumeration value="OK"/>
              <xsd:enumeration value="ERRORS"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="error" type="Q3:BatchError" minOccurs="0" maxOccurs="unbounded">
          <xsd:annotation>
            <xsd:documentation>Errors detected when confirming the payments</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="checkClientsDeferredPaymentsRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="cuid" type="xsd:long">
          <xsd:annotation>
            <xsd:documentation>Numeric code of a czech bank to receive an instant payment</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="checkClientsDeferredPaymentsResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="deferredPaymentExists" type="xsd:boolean">
          <xsd:annotation>
            <xsd:documentation>
              true:  existuje platba, kterou zadala osoba s daným CUIDem a je aktuálně na záchytném účtu
              false: neexistuje zadna platba na záchytném účtu, kterou zadala osoba s daným CUIDem
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="Reason" type="Q3:DeferredPaymentReason" minOccurs="0" maxOccurs="unbounded">
          <xsd:annotation>
            <xsd:documentation>
              duvod proc platba, kterou zadala osoba s daným CUIDem je aktuálně na záchytném účtu
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:simpleType  name="DeferredPaymentReason">
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="PHISHING"/>
      <xsd:enumeration value="AML"/>
    </xsd:restriction>
  </xsd:simpleType>

  <xsd:simpleType  name="BatchSectionStatus">
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="CREATED"/>
      <xsd:enumeration value="REMOVED"/>
      <xsd:enumeration value="TO_CONFIRM"/>
      <xsd:enumeration value="IN_PROCESSING"/>
      <xsd:enumeration value="PARTIALLY_PROCESSED"/>
      <xsd:enumeration value="PROCESSED"/>
      <xsd:enumeration value="REJECTED"/>
    </xsd:restriction>
  </xsd:simpleType>

  <xsd:simpleType  name="BatchPaymentStatus">
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="CREATED"/>
      <xsd:enumeration value="TO_BE_CORRECTED"/>
      <xsd:enumeration value="REMOVED"/>
      <xsd:enumeration value="IN_PROCESSING"/>
      <xsd:enumeration value="PROCESSED"/>
      <xsd:enumeration value="TERMINATED"/>
      <xsd:enumeration value="FUTURE_DUE_DATE"/>
    </xsd:restriction>
  </xsd:simpleType>

  <xsd:simpleType  name="Ordering">
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="ASC"/>
      <xsd:enumeration value="DESC"/>
    </xsd:restriction>
  </xsd:simpleType>

  <xsd:simpleType name="BatchPaymentType">
    <xsd:annotation>
      <xsd:documentation>payment types from input batches (from ABO, XML, etc.)</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="PAYMENT"/> <!-- Domestic payment, from ABO -->
      <xsd:enumeration value="TRF"/>     <!-- Transfer, from XML -->
    </xsd:restriction>
  </xsd:simpleType>

  <xsd:complexType name="BatchSection">
    <xsd:sequence>
      <xsd:element name="id" type="xsd:long">
        <xsd:annotation>
          <xsd:documentation>internal identifier of batch section</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="batchId" type="xsd:long">
        <xsd:annotation>
          <xsd:documentation>internal identifier of batch this batch section was uploaded in</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="filename" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>name of file the batch section was uploaded in</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="uploadedAt" type="xsd:dateTime">
        <xsd:annotation>
          <xsd:documentation>point in time the file with batch section was uploaded at</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="accountNumber" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>account number the batch section is connected to</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="accountName" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>name of the account the batch section is connected to</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="amountSum" type="xsd:decimal">
        <xsd:annotation>
          <xsd:documentation>sum of amount of all payments in batch section</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="amountCurrency" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>currency of amountSum</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="paymentCount" type="xsd:long">
        <xsd:annotation>
          <xsd:documentation>number of payments in batch section</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="status" type="Q3:BatchSectionStatus">
        <xsd:annotation>
          <xsd:documentation>status of batch section</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="uploader" type="xsd:long">
        <xsd:annotation>
           <xsd:documentation>cuid of the customer who uploaded this batch section</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="preConfirmer" type="xsd:long" minOccurs="0">
        <xsd:annotation>
           <xsd:documentation>cuid of the customer who handed over this batch section to confirmation</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="confirmer" type="xsd:long" minOccurs="0">
        <xsd:annotation>
           <xsd:documentation>cuid of the customer who confirmed this batch section</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="preConfirmedAt" type="xsd:dateTime" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>point in time this batch section was handed over to confirmation</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="confirmedAt" type="xsd:dateTime" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>point in time this batch section was confirmed</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="bankCode" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>bank code of the account the batch section is connected to, i.e. "our" bank code in most (or in all?) cases</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>

  <xsd:complexType name="BatchPayment">
    <xsd:sequence>
      <xsd:element name="id" type="xsd:long" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>
            internal identifier of this batch payment
            when empty in addOrEditBatchPayment method, a new batch payment will be added to the given batch section
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="batchSectionId" type="xsd:long">
        <xsd:annotation>
          <xsd:documentation>internal identifier of the batch section this batch payment belongs to</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="transactionId" type="xsd:long" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>
            internal identifier of the transaction created from this batch payment
              - "realized transaction" when batch payment status is IN_PROCESSING
              - "required transaction" when batch payment status is PROCESSED, TERMINATED, FUTURE_DUE_DATE
              empty is other statuses
            not used in addOrEditBatchPayment method
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="debtorAccount" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>
            Debtor account number in format corresponding to the payment domain.
            i.e. NNNNNN-NNNNNNNNNN in domestic czech payment, IBAN in SEPA payment, etc.

            not used in addOrEditBatchPayment method (debtor account is defined via batchSectionId)
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="debtorBankCode" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>
            Debtor bank code in format corresponding to the payment domain.

            not used in addOrEditBatchPayment method (the bank of debtor account is defined via batchSectionId)
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="creditorAccount" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>
            Creditor account number in format corresponding to the payment domain.
            i.e. NNNNNN-NNNNNNNNNN in domestic czech payment, IBAN in SEPA payment, etc.
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="creditorAccountPrefix" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>
            Creditor account number prefix in ABO format (up to 6 digits).
            Empty when credit account has NO prefix or is NOT in ABO format
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="creditorAccountBase" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>
            Creditor account number base in ABO format (up to 10 digits).
            Empty when credit account is NOT in ABO format
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="creditorBankCode" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>
            Creditor bank code in format corresponding to the payment domain.
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="creditorBankName" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>
            creditor bank name
            not used in addOrEditBatchPayment method
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="creditorBankFullName" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>
            creditor bank full name (including address)
            not used in addOrEditBatchPayment method
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="creditorBankCountry" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>
            ISO alpha 3 code of creditor's bank (i.e. creditor's account) country
            not used in addOrEditBatchPayment method
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="creditorName" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>
            Creditor name
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="creditorAddr" type="Q3:PostalAddress" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>
            Creditor address
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="dueDate" type="xsd:date">
        <xsd:annotation>
          <xsd:documentation>batch payment due date</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="amount" type="xsd:decimal">
        <xsd:annotation>
          <xsd:documentation>batch payment amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="currency" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>ISO alpha code (i.e. CZK) of the batch payment currency</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="vs" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>variable symbol of this batch payment</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ks" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>constant symbol of this batch payment</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ss" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>specific symbol of this batch payment</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="end2EndReference" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>end to end SEPA reference</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="text" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Free text associated with payment</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="purposeCode" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Payment purpose (code list)</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="purposeText" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Payment purpose (free text)</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="paymentType" type="Q3:BatchPaymentType">
        <xsd:annotation>
          <xsd:documentation>Type of payment</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="svcLvl" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Service level. SEPA for SEPA payments</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="pmtInfId" type="xsd:string" minOccurs="0">
        <xsd:annotation>
           <xsd:documentation>Payment batch identification (generated by customer)</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="instrId" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Instruction identification (generated by customer)</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="status" type="Q3:BatchPaymentStatus" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>
            status of this batch payment
            not used in addOrEditBatchPayment method
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="feeCoverage" minOccurs="0" type="Q1:FeeCoverageType">
        <xsd:annotation>
            <xsd:documentation>Who will pay transfer charges?</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="messageForSender" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Free text (remark) for the initiator of the payment</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="transactionType" type="Q1:TransactionType">
        <xsd:annotation>
            <xsd:documentation>Transaction type</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="accountTrustLevel" type="ta:AccountTrustLevel" minOccurs="0">
        <xsd:annotation>
            <xsd:documentation>trust level of creditor account</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>

  <xsd:complexType name="DeferBatchPayment">
    <xsd:sequence>
      <xsd:element name="batchPaymentId" type="xsd:long">
        <xsd:annotation>
          <xsd:documentation>batch payment identifier</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="reason" type="Q3:DeferredPaymentReason" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation>collection of reasons (if any, in order of their priority) why to defer the batch payment</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>

  <xsd:complexType name="BatchError">
    <xsd:sequence>
      <xsd:element name="code" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>error code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="description" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>error description</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="rawData" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>part of original file where this error occured</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>

  <xsd:complexType name="BatchIDsForTrnIDs">
    <xsd:sequence>
      <xsd:element name="transactionId" type="xsd:long">
        <xsd:annotation>
          <xsd:documentation>transaction identifier</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="batchId" type="xsd:long" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>batch identifier for given transaction identifier. empty when transaction is not part of any batch</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="batchSectionId" type="xsd:long" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>batch section identifier for given transaction identifier. empty when transaction is not part of any batch</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="batchPaymentId" type="xsd:long" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>batch payment identifier for given transaction identifier. empty when transaction is not part of any batch</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>

  <xsd:simpleType name="PaymentOrderChannelType">
    <xsd:annotation>
      <xsd:documentation>channel</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="IB">
        <xsd:annotation>
          <xsd:documentation>internet banking</xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="TB">
        <xsd:annotation>
          <xsd:documentation>telephone banking</xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="BRANCH">
        <xsd:annotation>
          <xsd:documentation>branch</xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="POST">
        <xsd:annotation>
          <xsd:documentation>post</xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="MESSENGER">
        <xsd:annotation>
          <xsd:documentation>courier</xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
    </xsd:restriction>
  </xsd:simpleType>

  <xsd:complexType name="PostalAddress"> <!-- Based on PostalAddress6CZ in pain.001.001.03CZS.xsd-->
    <xsd:sequence>
      <xsd:element name="strtNm" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>street name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="bldgNb" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>building number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="pstCd" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>postal code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="twnNm" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>town name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ctry" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>country</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="adrLine" type="xsd:string" minOccurs="0" maxOccurs="2">
        <xsd:annotation>
          <xsd:documentation>address line</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="swiftFmt" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>address in SWIFT format (3x35 chars), CRLF delimits lines</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>

</xsd:schema>
