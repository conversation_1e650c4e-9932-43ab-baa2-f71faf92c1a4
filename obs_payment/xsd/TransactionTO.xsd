<?xml version="1.0" encoding="UTF-8"?>
<schema
    xmlns="http://www.w3.org/2001/XMLSchema"
    xmlns:com="http://arbes.com/ib/core/ppf/ws/common/"
    xmlns:ta="http://airbank.cz/obs/ws/common/trustedAccountsWS"
    xmlns:tns="http://arbes.com/ib/core/ppf/ws/common/"
    targetNamespace="http://arbes.com/ib/core/ppf/ws/common/"
    elementFormDefault="qualified"
    version="1.0">

    <include schemaLocation="PaymentReasonTO.xsd" />
    <include schemaLocation="Common.xsd" />
    <include schemaLocation="CountryTO.xsd" />
    <import schemaLocation="trustedAccounts.xsd" namespace="http://airbank.cz/obs/ws/common/trustedAccountsWS"/>

    <complexType name="CommonTransactionTO">
        <sequence>
            <element name="validFrom" type="dateTime">
                <annotation>
                    <documentation>
                        Počáteční datum platnosti předepsané transakce
                    </documentation>
                </annotation>
            </element>
            <element name="statusCode" type="tns:TransactionStatusType" minOccurs="0">
                <annotation>
                    <documentation>kód stavu transakce</documentation>
                </annotation>
            </element>
            <element name="statusName" type="string" minOccurs="0">
                <annotation>
                    <documentation>název stavu transakce</documentation>
                </annotation>
            </element>
            <element name="amountInRequiredCurrency" type="decimal">
                <annotation>
                    <documentation>požadovaná částka</documentation>
                </annotation>
            </element>
            <element name="feeCoverage" minOccurs="0" type="tns:FeeCoverageType">
                <annotation>
                    <documentation>
                        Krytí poplatků - zahraniční platby
                    </documentation>
                </annotation>
            </element>
            <element name="priority" type="boolean">
                <annotation>
                    <documentation>
                        true - prioritní platba
                    </documentation>
                </annotation>
            </element>
            <element name="categoryID" type="long" minOccurs="0">
                <annotation>
                    <documentation>id kategorie platby</documentation>
                </annotation>
            </element>
            <element name="contraEnvelopeID" type="long" minOccurs="0">
                <annotation>
                    <documentation>
                        identifikátor obálky proti-strany (když je
                        odchozí platba, je to kreditní obálka, když
                        příchozí tak debetní obálka)
                    </documentation>
                </annotation>
            </element>
            <!-- TODO use CZSpecificFields -->
            <element name="specificFields" type="tns:CommonSpecificFields" minOccurs="0">
                <annotation>
                    <documentation>
                        specifické atributy pro danou zemi
                    </documentation>
                </annotation>
            </element>
            <element name="requiredCurrency" type="string">
                <annotation>
                    <documentation>kód požadované měny</documentation>
                </annotation>
            </element>
            <element name="contraAccountNumberPrefix" type="string" minOccurs="0">
                <annotation>
                    <documentation>předčíslí protiúčtu</documentation>
                </annotation>
            </element>
            <element name="contraAccountNumber" type="string">
                <annotation>
                    <documentation>číslo protiúčtu</documentation>
                </annotation>
            </element>
            <element name="contraAccountName" type="string" minOccurs="0">
                <annotation>
                    <documentation>
                        jméno protiúčtu. Pozor u zahraničních plateb je
                        při zadávání povinný.
                    </documentation>
                </annotation>
            </element>
            <element name="contraBankCode" type="string">
                <annotation>
                    <documentation>
                        kód banky protiúčtu. U zahraničních SWIFT kód.
                    </documentation>
                </annotation>
            </element>
            <element name="contraBankName" type="string" minOccurs="0">
                <annotation>
                    <documentation>
                        jméno banky protiúčtu.

                        Pozor u zahraničních plateb je při zadávání
                        povinný.
                    </documentation>
                </annotation>
            </element>
            <element name="transactionType" type="tns:TransactionType">
                <annotation>
                    <documentation>Typ transakce</documentation>
                </annotation>
            </element>
            <element name="prescription" type="tns:PrescriptionType" minOccurs="0">
                <annotation>
                    <documentation>
                        předpisu - vazba na předpis transakce -atribut
                        pouze pro čtení
                    </documentation>
                </annotation>
            </element>
            <element name="counterPartyId" type="long" minOccurs="0">
                <annotation>
                    <documentation>
                        ID protistrany
                    </documentation>
                </annotation>
            </element>
            <element name="contraIBAN" type="string" minOccurs="0">
                <annotation>
                    <documentation>IBAN protiúčtu</documentation>
                </annotation>
            </element>
            <element name="extSource" type="string" minOccurs="0">
                <annotation>
                    <documentation>Externí zdroj transakce</documentation>
                </annotation>
            </element>
            <element name="extTransactionId" type="string" minOccurs="0">
                <annotation>
                    <documentation>Id transakce v primárním systému</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <simpleType name="TransactionStatusType">
        <restriction base="string">
            <enumeration value="RTS_EDITED">
                <annotation>
                    <documentation>V editaci</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RTS_NOT_REALISED">
                <annotation>
                    <documentation>Připraveno ke zpracování</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RTS_NOT_FULLY_REALISED">
                <annotation>
                    <documentation>Částečně zpracováno</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RTS_REALISED">
                <annotation>
                    <documentation>Plně zpracováno</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RTS_SUSPENDED">
                <annotation>
                    <documentation>Suspendováno</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RTS_ENDED">
                <annotation>
                    <documentation>Zrušeno</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RTS_WAIT_FOR_AUTHORISATION">
                <annotation>
                    <documentation>K verifikaci supervisorem</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RTS_FAULTY_PARAMS">
                <annotation>
                    <documentation>Chybná</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RTS_READY_TO_SEND">
                <annotation>
                    <documentation>Připraveno k odeslání</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RTS_SENT">
                <annotation>
                    <documentation>Odeslán</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RTS_REFUSED_BY_COUNTERPARTY">
                <annotation>
                    <documentation>Odmítnuto protistranou</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RTS_REFUSED_ERROR">
                <annotation>
                    <documentation>Chyba při zpracování</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RTS_INPROC">
                <annotation>
                    <documentation>Právě odesíláno</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RTS_WAITS_FOR_APPROVAL">
                <annotation>
                    <documentation>čeká na schválení</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RTS_PARTLYSIGNED">
                <annotation>
                    <documentation>Částečně podepsáno</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RTS_SIGNED">
                <annotation>
                    <documentation>Podepsáno</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RTS_PARTLYEDITED">
                <annotation>
                    <documentation>V omezené editaci</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RTS_CANCELLED">
                <annotation>
                    <documentation>Stornováno</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RTS_FOR_EXT_PROCESSING">
                <annotation>
                    <documentation>pro externí zpracování</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RTS_WAIT_FOR_CNDPRECEDENT">
                <annotation>
                    <documentation>Čeká na odkládací podmínky</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="InternalTransactionType">
        <restriction base="string">
            <enumeration value="REQUIRED">
                <annotation>
                    <documentation>požadovaná platba</documentation>
                </annotation>
            </enumeration>
            <enumeration value="REALIZED">
                <annotation>
                    <documentation>realizovaná platba</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CARDHOLD">
                <annotation>
                    <documentation>karetní blokace</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="FeeCoverageType">
        <restriction base="string">
            <enumeration value="BEN">
                <annotation>
                    <documentation>na vrub cizi banky</documentation>
                </annotation>
            </enumeration>
            <enumeration value="OUR">
                <annotation>
                    <documentation>na vrub nasi banky (klienta)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="SHARE">
                <annotation>
                    <documentation>na vrub cizi i nasi banky (klienta)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="NA">
                <annotation>
                    <documentation>Not applicable</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <complexType name="CommonSpecificFields" abstract="true"></complexType>

    <complexType name="CZSpecificFields">
        <annotation>
            <documentation>specifické atributy platby pro CZ</documentation>
        </annotation>
        <complexContent>
            <extension base="tns:CommonSpecificFields">
                <sequence>
                    <element name="constantSymbol" minOccurs="0">
                        <simpleType>
                            <restriction base="string">
                                <minLength value="0"></minLength>
                                <maxLength value="4"></maxLength>
                            </restriction>
                        </simpleType>
                    </element>
                    <element name="variableSymbol" minOccurs="0">
                        <simpleType>
                            <restriction base="string">
                                <minLength value="0"></minLength>
                                <maxLength value="10"></maxLength>
                            </restriction>
                        </simpleType>
                    </element>
                    <element name="specificSymbol" minOccurs="0">
                        <simpleType>
                            <restriction base="string">
                                <minLength value="0"></minLength>
                                <maxLength value="10"></maxLength>
                            </restriction>
                        </simpleType>
                    </element>
                    <element name="paymentReasonCode" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                důvod platby, pokud jsou nad určitou částku
                                stanovenou ČNB.
                                Kód z číselníku CodeList.PaymentReason
                            </documentation>
                        </annotation>
                    </element>
                    <element name="paymentReasonText" type="string" minOccurs="0">
                        <annotation>
                            <documentation>důvod platby, pokud jsou nad určitou částku
                                stanovenou ČNB. Free text.</documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="PaymentOrderTO">
        <annotation>
            <documentation>požadovaná (předepsaná transakce)</documentation>
        </annotation>
        <complexContent>
            <extension base="tns:CommonTransactionTO">
                <sequence>
                    <element name="idRequiredTransaction" type="long" minOccurs="0">
                        <annotation>
                            <documentation>
                                primární klíč požadované transakce
                            </documentation>
                        </annotation>
                    </element>
                    <element name="editableByUser" type="boolean">
                        <annotation>
                            <documentation>
                                true - uživatel může danou transakci
                                upravovat
                            </documentation>
                        </annotation>
                    </element>
                    <element name="messageForSender" type="string" minOccurs="0">
                        <annotation>
                            <documentation>zpráva pro mě</documentation>
                        </annotation>
                    </element>
                    <element name="messageForReceiver" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                zpráva pro příjemce
                            </documentation>
                        </annotation>
                    </element>
                    <element name="validTo" type="date" minOccurs="0">
                        <annotation>
                            <documentation>
                                platnost do požadované transakce
                            </documentation>
                        </annotation>
                    </element>
                    <element name="emailNotif" type="boolean">
                        <annotation>
                            <documentation>
                                zda se má při zrealizování poslat
                                emailová notifikace
                            </documentation>
                        </annotation>
                    </element>
                    <element name="paymentReasonCode" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                kód důvodu platby
                            </documentation>
                        </annotation>
                    </element>
                    <element name="paymentReasonText" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                pokud je kód definovaný jako (TODO:
                                doplnit) potom lze důvod platby uvést
                                jako free text v tomto atributu
                            </documentation>
                        </annotation>
                    </element>
                    <element name="errorCode" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                kód chyby při realizaci transakce - např
                                -20109 je "Nedostatek prostředků"
                            </documentation>
                        </annotation>
                    </element>
                    <element name="errorText" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                textový popis chyby při realizaci
                                transakce - např. "Nedostatek
                                prostředků".
                            </documentation>
                        </annotation>
                    </element>
                    <element name="contraBankCountry" type="string" minOccurs="0">
                        <annotation>
                            <documentation>stát banky</documentation>
                        </annotation>
                    </element>
                    <element name="contraAccountCountry" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                kód země příjemce - na straně OBS bude
                                imlementováno tak, že se přidá do
                                položky ContraAccountName
                            </documentation>
                        </annotation>
                    </element>
                    <element name="idBankAccountDebit" type="long">
                        <annotation>
                            <documentation>
                                Primární klíč účtu na debitní straně.
                            </documentation>
                        </annotation>
                    </element>
                    <element name="additionalInfo" type="com:AdditionalInfoTO" minOccurs="0">
                        <annotation>
                            <documentation>Additional information for mobile application frontend purposes.</documentation>
                        </annotation>
                    </element>
                    <element name="debitEnvelopeID" type="long" minOccurs="0">
                        <annotation>
                            <documentation>
                                identifikátor zdrojové obálky
                            </documentation>
                        </annotation>
                    </element>
                    <element name="doorDocumentID" type="long" minOccurs="0">
                        <annotation>
                            <documentation>
                                Identifikátor dokumentu v DOOR
                            </documentation>
                        </annotation>
                    </element>
                    <element name="createdAt" type="dateTime" minOccurs="0">
                        <annotation>
                            <documentation>datum zadání</documentation>
                        </annotation>
                    </element>
                    <element name="channel" type="tns:ChannelType" minOccurs="0">
                        <annotation>
                            <documentation>kanal zadani</documentation>
                        </annotation>
                    </element>
                    <element name="paymentAuthorizedBy" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                jmeno a prijmeni toho kdo autorizoval
                                platbu (pokud se jedná o systémovou
                                platbu, je zde název banky)
                            </documentation>
                        </annotation>
                    </element>
                    <element name="addToWhitelist" type="boolean" minOccurs="0">
                        <annotation>
                            <documentation>add contra bank account to whitelist</documentation>
                        </annotation>
                    </element>
                    <element name="accountTrustLevel" type="ta:AccountTrustLevel" minOccurs="0">
                        <annotation>
                            <documentation>trust level of bank account</documentation>
                        </annotation>
                    </element>
                    <element name="endToEnd" type="string" minOccurs="0">
                        <annotation>
                            <documentation>Identifikace SEPA platby - EndToEnd reference</documentation>
                        </annotation>
                    </element>
                    <element name="consumingAppId" type="string" minOccurs="0">
                        <annotation>
                            <documentation>Payment Initiation Service Providers (PISP) pro Open API</documentation>
                        </annotation>
                    </element>
                    <element name="developerId" type="string" minOccurs="0">
                        <annotation>
                            <documentation>Payment Initiation Service Providers (PISP) pro Open API</documentation>
                        </annotation>
                    </element>
                    <element name="processInstantly" type="boolean" minOccurs="0">
                        <annotation>
                            <documentation>Zpracovat platbu okamžitě</documentation>
                        </annotation>
                    </element>
                    <element name="xamosStatusCode" type="tns:XamosStatusCode" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Status of XAMOS instant payment</documentation>
                        </annotation>
                    </element>
                    <element name="xamosErrorCode" type="tns:XamosErrorCode" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Error code of XAMOS instant payment</documentation>
                        </annotation>
                    </element>
                    <element name="isContraAccountInternal" type="boolean">
                        <annotation>
                            <documentation>true - protiucet je interni</documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="AmountTO">
        <sequence>
            <element name="amount" type="decimal">
                <annotation>
                    <documentation>amount</documentation>
                </annotation>
            </element>
            <element name="currency" type="string">
                <annotation>
                    <documentation>ISO currency code</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="RealizedTransactionTO">
        <annotation>
            <documentation>realizovaná transakce</documentation>
        </annotation>
        <complexContent>
            <extension base="tns:CommonTransactionTO">
                <sequence>
                    <element name="idTransactionOrig" type="long" minOccurs="0">
                        <annotation>
                            <documentation>
                                původní identifikátor transakce
                                - id blokace pokud tato transakce vznikla z blokace
                                - totéž co idRealizedTransaction pokud tato transakce NEvznikla z blokace
                            </documentation>
                        </annotation>
                    </element>
                    <element name="idRealizedTransaction" type="long" minOccurs="0">
                        <annotation>
                            <documentation>
                                primární klíč realizované transakce
                            </documentation>
                        </annotation>
                    </element>
                    <element name="realizationDate" type="date">
                        <annotation>
                            <documentation>
                                datum realizace
                            </documentation>
                        </annotation>
                    </element>
                    <element name="fee" type="decimal">
                        <annotation>
                            <documentation>
                                suma transakčních poplatků k dané
                                transakci
                            </documentation>
                        </annotation>
                    </element>
                    <element name="actionName" type="string">
                        <annotation>
                            <documentation>
                                popis typu realizované platby. Např
                                platba, inkaso, poplatek za výpis,
                                sankční úrok atd.
                            </documentation>
                        </annotation>
                    </element>
                    <element name="messageToReceiver" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                popis transakce od / k uživateli - nelze
                                měnit
                            </documentation>
                        </annotation>
                    </element>
                    <element name="messageToSender" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                popis transakce od / k uživateli - nelze
                                měnit
                            </documentation>
                        </annotation>
                    </element>
                    <element name="messageToBank" type="string" minOccurs="0">
                        <annotation>
                            <documentation>message for bank</documentation>
                        </annotation>
                    </element>
                    <element name="currencyInAccount" type="string">
                        <annotation>
                            <documentation>
                                kód měny realizované částky
                            </documentation>
                        </annotation>
                    </element>
                    <element name="amountInAccountCurrency" type="decimal" minOccurs="0">
                        <annotation>
                            <documentation>
                                částka v měně účtu
                            </documentation>
                        </annotation>
                    </element>
                    <element name="feeCanceled" type="boolean">
                        <annotation>
                            <documentation>
                                true - poplatky k dané transakci byly
                                odpuštěny
                            </documentation>
                        </annotation>
                    </element>
                    <element name="feeCanCancel" type="boolean">
                        <annotation>
                            <documentation>
                                true - poplatky k dané transakci lze
                                odpustit
                            </documentation>
                        </annotation>
                    </element>
                    <element name="dateFeeCanceled" type="date" minOccurs="0">
                        <annotation>
                            <documentation>
                                datum zrušení poplatku
                            </documentation>
                        </annotation>
                    </element>
                    <element name="contraEnvelopeName" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                název obálky proti strany (když je
                                odchozí platba, je to kreditní obálka,
                                když příchozí tak debetní obálka)
                            </documentation>
                        </annotation>
                    </element>
                    <element name="selfEnvelopeName" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                název vlastní obálky (když je odchozí
                                platba, je to debetní obálka, když
                                příchozí tak je to kreditní obálka)
                            </documentation>
                        </annotation>
                    </element>
                    <element name="selfEnvelopeID" type="long" minOccurs="0">
                        <annotation>
                            <documentation>
                                identifikátor vlastní obálky (když je
                                odchozí platba, je to debetní obálka,
                                když příchozí tak je to kreditní obálka)
                            </documentation>
                        </annotation>
                    </element>
                    <element name="userNote" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                uživatelská poznámka - lze editovat
                            </documentation>
                        </annotation>
                    </element>
                    <element name="merchantName" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                jméno obchodníka (karetní operace)
                            </documentation>
                        </annotation>
                    </element>
                    <element name="merchantAddress" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                adresa obchodníka (karetní operace)
                            </documentation>
                        </annotation>
                    </element>
                    <element name="mcc" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                MCC (karetní operace)
                            </documentation>
                        </annotation>
                    </element>
                    <element name="contraAccountFullName" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                Adresa příjemce/odesílatele protiúčtu
                            </documentation>
                        </annotation>
                    </element>
                    <element name="contraBankFullName" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                adresa banky příjemce/odesílatele
                                protiúčtu
                            </documentation>
                        </annotation>
                    </element>
                    <element name="contraAccountCountry" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                zěmě příjemce/odesílate protiúčtu
                            </documentation>
                        </annotation>
                    </element>
                    <element name="contraBankCountry" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                země banky příjemce/odesílate protiúčtu
                            </documentation>
                        </annotation>
                    </element>
                    <element name="isContraAccountInternal" type="boolean">
                        <annotation>
                            <documentation>
                                true - protiucet je interni
                            </documentation>
                        </annotation>
                    </element>
                    <element name="myAccountId" type="long" minOccurs="0">
                        <annotation>
                            <documentation>ID klientova účtu</documentation>
                        </annotation>
                    </element>
                    <element name="createdAt" type="dateTime" minOccurs="0">
                        <annotation>
                            <documentation>datum zadání</documentation>
                        </annotation>
                    </element>
                    <element name="valuta" type="date" minOccurs="0">
                        <annotation>
                            <documentation>datum valuty</documentation>
                        </annotation>
                    </element>
                    <element name="approvalDate" type="date" minOccurs="0">
                        <annotation>
                            <documentation>
                                datum povoleni inkasa
                            </documentation>
                        </annotation>
                    </element>
                    <element name="channel" type="tns:ChannelType" minOccurs="0">
                        <annotation>
                            <documentation>kanal zadani</documentation>
                        </annotation>
                    </element>
                    <element name="paymentAuthorizedBy" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                jmeno a prijmeni toho kdo autorizoval
                                platbu (pokud se jedná o systémovou
                                platbu, je zde název banky)
                            </documentation>
                        </annotation>
                    </element>
                    <element name="cardName" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                uživatelský názav karty
                            </documentation>
                        </annotation>
                    </element>
                    <element name="cardID" type="long" minOccurs="0">
                        <annotation>
                            <documentation>
                                identifikace karty v systemu homer
                            </documentation>
                        </annotation>
                    </element>
                    <element name="cardAtmCode" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                posledních 9 čísel karty - maskované
                                např. *****1234
                            </documentation>
                        </annotation>
                    </element>
                    <element name="cardHolder" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                držitel - jméno fyzické osoby, která má
                                vztah držitele k rámcové smlouvě
                            </documentation>
                        </annotation>
                    </element>
                    <element name="sazka" type="tns:SazkaTransType" minOccurs="0" />
                    <element name="idChargedTransaction" type="long" minOccurs="0">
                        <annotation>
                            <documentation>For a charging transaction this is the charged transaction ID</documentation>
                        </annotation>
                    </element>
                    <element name="accountBalanceAfter" type="decimal" minOccurs="0">
                        <annotation>
                            <documentation>Account balance after the transaction</documentation>
                        </annotation>
                    </element>
                    <element name="direction" type="tns:DirectionType" minOccurs="0">
                        <annotation>
                            <documentation>Incoming/Outgoing/In-account transfer</documentation>
                        </annotation>
                    </element>
                    <element name="accounted" type="tns:AccountedType" minOccurs="0">
                        <annotation>
                            <documentation>Is filled for payment card/Sazka code transactions only
                                Y = transaction is already accounted
                                N = transaction (blocking) has not been accounted yet
                                NA = not applicable for this type of transaction (not payment card/Sazka code)
                            </documentation>
                        </annotation>
                    </element>
                    <element name="instructedAmount" type="tns:AmountTO" minOccurs="0">
                        <annotation>
                            <documentation>Amount of the instruction. It occurs only on incoming PSD or non-PSD payment.</documentation>
                        </annotation>
                    </element>
                    <element name="sendersCharges" type="tns:AmountTO" minOccurs="0" maxOccurs="unbounded">
                        <annotation>
                            <documentation>Transaction charges deducted by the Sender and by previous banks in the transaction chain. It occurs only on incoming PSD or non-PSD payment.</documentation>
                        </annotation>
                    </element>
                    <element name="endToEnd" type="string" minOccurs="0">
                        <annotation>
                            <documentation>Identifikace SEPA platby - EndToEnd reference</documentation>
                        </annotation>
                    </element>
                    <element name="rmdName" type="string" minOccurs="0">
                        <annotation>
                            <documentation>Jmeno aplikace z RMD, z ktere byla provedena NFC platba</documentation>
                        </annotation>
                    </element>
                    <element name="transactionDate" type="dateTime" minOccurs="0">
                        <annotation>
                            <documentation>Čas provedení transakce</documentation>
                        </annotation>
                    </element>
                    <element name="bookingDate" type="dateTime" minOccurs="0">
                        <annotation>
                            <documentation>Datum a čas zaúčtování transakce</documentation>
                        </annotation>
                    </element>
                    <element name="consumingAppId" type="string" minOccurs="0">
                        <annotation>
                            <documentation>Payment Initiation Service Providers (PISP) pro Open API</documentation>
                        </annotation>
                    </element>
                    <element name="developerId" type="string" minOccurs="0">
                        <annotation>
                            <documentation>Payment Initiation Service Providers (PISP) pro Open API</documentation>
                        </annotation>
                    </element>
                    <element name="xamosStatusCode" type="tns:XamosStatusCode" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Status of XAMOS instant payment</documentation>
                        </annotation>
                    </element>
                    <element name="xamosErrorCode" type="tns:XamosErrorCode" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Error code of XAMOS instant payment</documentation>
                        </annotation>
                    </element>
                    <element name="conversion" type="boolean" minOccurs="0">
                        <annotation>
                            <documentation>true = jde o konverzni platbu</documentation>
                        </annotation>
                    </element>
                    <element name="batchFileName" type="string" minOccurs="0">
                        <annotation>
                            <documentation>jmeno souboru davky, byla-li platba jeji soucasti</documentation>
                        </annotation>
                    </element>
                    <element name="reversalIndicator" type="boolean" minOccurs="0">
                        <annotation>
                            <documentation>true = jde o storno</documentation>
                        </annotation>
                    </element>
                    <element name="creditDebitIndicator" type="tns:CreditDebitIndicator" minOccurs="0">
                        <annotation>
                            <documentation>určení zda jde o debetní nebo o kreditní transakci</documentation>
                        </annotation>
                    </element>
                    <element name="ultimateCreditor" type="tns:StructAddress" minOccurs="0">
                        <annotation>
                            <documentation>informace o konecnem prijemci</documentation>
                        </annotation>
                    </element>
                    <element name="ultimateDebtor" type="tns:StructAddress" minOccurs="0">
                        <annotation>
                            <documentation>informace o puvodnim platci</documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <simpleType name="ChannelType">
        <restriction base="string">
            <enumeration value="SYSTEM">
                <annotation>
                    <documentation>SYSTEM OBS</documentation>
                </annotation>
            </enumeration>
            <enumeration value="BRANCH">
                <annotation>
                    <documentation>BRANCH - pobocka</documentation>
                </annotation>
            </enumeration>
            <enumeration value="OPER">
                <annotation>
                    <documentation>OPER - operator banky</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ICC">
                <annotation>
                    <documentation>ICC - Interni call centrum</documentation>
                </annotation>
            </enumeration>
            <enumeration value="BO">
                <annotation>
                    <documentation>BO - backOffice</documentation>
                </annotation>
            </enumeration>
            <enumeration value="IB">
                <annotation>
                    <documentation>IB / WWW - internet banka</documentation>
                </annotation>
            </enumeration>
            <enumeration value="SPB">
                <annotation>
                    <documentation>smart phone banking</documentation>
                </annotation>
            </enumeration>
            <enumeration value="OPENAPI">
                <annotation>
                    <documentation>pristup pres Open Api</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="TransactionType">
        <restriction base="string">
            <enumeration value="ACCTRN">
                <annotation>
                    <documentation>převod v rámci účtu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="BALANCETRANSFER">
                <annotation>
                    <documentation>Převod zůstatku</documentation>
                </annotation>
            </enumeration>
            <enumeration value="BALANCETRANSFERCO">
                <annotation>
                    <documentation>Převod zůstatku - konverzní</documentation>
                </annotation>
            </enumeration>
            <enumeration value="BBLOCK">
                <annotation>
                    <documentation>bankovní blokace</documentation>
                </annotation>
            </enumeration>
            <enumeration value="BUSINTPAYFULL">
                <annotation>
                    <documentation>Odchozí nekonverzní splátka obch. úroku</documentation>
                </annotation>
            </enumeration>
            <enumeration value="BUSINTPAYPART">
                <annotation>
                    <documentation>Odchozí nekonverzní částečná splátka obch. úroku</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CACCBLK">
                <annotation>
                    <documentation>Blokace účtu na žádost klienta</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CACCDBLK">
                <annotation>
                    <documentation>Deblokace účtu na žádost klienta</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CARDCOMPLAINT">
                <annotation>
                    <documentation>Karetní reklamace - proplacení reklamované platby / výběru hotovosti</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CARDCOMPLAINTCNCL">
                <annotation>
                    <documentation>Storno karetní reklamace</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CASHBACKBENEFIT">
                <annotation>
                    <documentation>Odměna</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CASHBACKPAY">
                <annotation>
                    <documentation>Výplata peněz ušetřených díky slevám</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CASHBACKPAYCO">
                <annotation>
                    <documentation>Výplata peněz ušetřených díky slevám-konverzní</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CASHBACKPAYO2">
                <annotation>
                    <documentation>Odměna za spolupráci s O2</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CASHBACKPAYRETURN">
                <annotation>
                    <documentation>Vrácení peněz získaných díky slevám</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CASHBACKPAYRETURNCO">
                <annotation>
                    <documentation>Vrácení peněz získaných díky slevám-konverzní</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CBACHRG">
                <annotation>
                    <documentation>Měsíční poplatek za tarif</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CBACHRGSTORNO">
                <annotation>
                    <documentation>Storno poplatku za tarif kvůli nepoužívání</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CCARDDBL">
                <annotation>
                    <documentation>Odblokování karty</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CCARDHTVDK">
                <annotation>
                    <documentation>Vydání podpisové karty - z účtu v CZ</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CCARDHTVDKCO">
                <annotation>
                    <documentation>Vydání podpisové karty - z účtu v CM</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CCARDHTVNK">
                <annotation>
                    <documentation>Vydání náhradní podpisové karty - z účtu v CZ</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CCARDHTVNKCO">
                <annotation>
                    <documentation>Vydání náhradní podpisové karty - z účtu v CM</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CCARDHTVOK">
                <annotation>
                    <documentation>Vydání obnovené podpisové karty - z účtu v CZ</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CCARDHTVOKCO">
                <annotation>
                    <documentation>Vydání obnovené podpisové karty - z účtu v CM</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CCARDMNG">
                <annotation>
                    <documentation>Měsíční vedení karty</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CCARDVDK">
                <annotation>
                    <documentation>Vydání platební karty</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CCARDVNK">
                <annotation>
                    <documentation>Vydání náhradní karty</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CCARDVNN">
                <annotation>
                    <documentation>Poplatek za vydání náhradní platební nálepky</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CCARDVPN">
                <annotation>
                    <documentation>Poskytnutí další debetní karty / platební nálepky</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CCARDVSTIC">
                <annotation>
                    <documentation>Poplatek za vydání platební nálepky</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CCARDVVK">
                <annotation>
                    <documentation>Vydání výběrového kódu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CCARDYR">
                <annotation>
                    <documentation>Roční vedení karty</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CCLAIM">
                <annotation>
                    <documentation>Neoprávněná reklamace</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CCNCL">
                <annotation>
                    <documentation>Storno platby</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CCNCLCZ">
                <annotation>
                    <documentation>Storno platby</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CCNCLREF">
                <annotation>
                    <documentation>Vrácení poplatku</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CCNCLSG">
                <annotation>
                    <documentation>Vrácení poplatku v rámci SG</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CDOCEL">
                <annotation>
                    <documentation>Zaslání potvrzení na žádost klienta elektronicky</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CDOCPOST">
                <annotation>
                    <documentation>Zaslání potvrzení na žádost klienta poštou</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CFIXINT">
                <annotation>
                    <documentation>Opravné zúčtování</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CFOREIGNCORRESPCHRG">
                <annotation>
                    <documentation>Poplatek za zahraniční korespondenci</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CLMTCHNG">
                <annotation>
                    <documentation>Změna nastavení transakčních limitů na kartě</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CMLHEDGECOST">
                <annotation>
                    <documentation>Poplatek - náklady vynaložené za garanci sazby</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CNEGINT">
                <annotation>
                    <documentation>debetní úrok</documentation>
                </annotation>
            </enumeration>
            <enumeration value="COMPLAINT">
                <annotation>
                    <documentation>Reklamace</documentation>
                </annotation>
            </enumeration>
            <enumeration value="COMPLAINTCO">
                <annotation>
                    <documentation>Reklamace - konverzní</documentation>
                </annotation>
            </enumeration>
            <enumeration value="COMPLETECOMMITMENT">
                <annotation>
                    <documentation>Doplacení závazku</documentation>
                </annotation>
            </enumeration>
            <enumeration value="COMPLETECOMMITMENTCO">
                <annotation>
                    <documentation>Doplacení závazku - konverzní</documentation>
                </annotation>
            </enumeration>
            <enumeration value="COTHNTRN">
                <annotation>
                    <documentation>manuálně zadaný název poplatku</documentation>
                </annotation>
            </enumeration>
            <enumeration value="COTHTRN">
                <annotation>
                    <documentation>manuálně zadaný název poplatku</documentation>
                </annotation>
            </enumeration>
            <enumeration value="COURRBI">
                <annotation>
                    <documentation>OUR poplatky RBI (zahraniční platby)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="COURTFEE">
                <annotation>
                    <documentation>Soudní poplatky</documentation>
                </annotation>
            </enumeration>
            <enumeration value="COURTFEECO">
                <annotation>
                    <documentation>Soudní poplatky - konverzní</documentation>
                </annotation>
            </enumeration>
            <enumeration value="COWD">
                <annotation>
                    <documentation>Zaslání upomínky při přečerpání účtu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CPINEL">
                <annotation>
                    <documentation>Zaslání PIN elektronicky (IB)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CPINCHNG">
                <annotation>
                    <documentation>Změna PIN v bankomatu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CPOSINT">
                <annotation>
                    <documentation>kreditní úrok</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CPRINCBACK">
                <annotation>
                    <documentation>Vrácení úroků</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CPWDMAIL">
                <annotation>
                    <documentation>opakované zaslání hesla</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CREPPIN">
                <annotation>
                    <documentation>Znovu zaslání PIN poštou</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CREPPINEL">
                <annotation>
                    <documentation>Znovu zaslání PIN elektronicky (IB)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CRETD">
                <annotation>
                    <documentation>Vrácená platba tuzemská</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CRETI">
                <annotation>
                    <documentation>Vrácená platba zahraniční</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CSMS">
                <annotation>
                    <documentation>Zaslání notifikačních SMS</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CSTATEL">
                <annotation>
                    <documentation>Zaslání elektronického výpisu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CSTATMAIL">
                <annotation>
                    <documentation>Zaslání výpisu poštou</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CTHIRD">
                <annotation>
                    <documentation>poplatky třetích stran (zahraniční platby)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CTRMINS">
                <annotation>
                    <documentation>Nastavení trvalého příkazu, inkasa nebo SIPO</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CTRMUPD">
                <annotation>
                    <documentation>Změna trvalého příkazu, inkasa nebo SIPO</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CWITHTAX">
                <annotation>
                    <documentation>srážková daň</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DCOLL">
                <annotation>
                    <documentation>Inkasní platba</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DCOLLBM">
                <annotation>
                    <documentation>Inkasní platba - Bill Manager</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DREPTRN">
                <annotation>
                    <documentation>Trvalý příkaz</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DSAVING">
                <annotation>
                    <documentation>Pravidelné spoření - Forced Savings</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DSIPO">
                <annotation>
                    <documentation>SIPO</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DTRNCLI">
                <annotation>
                    <documentation>Převod mezi účty klienta příchozí</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DTRNCLO">
                <annotation>
                    <documentation>Převod mezi účty klienta odchozí</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DTRNIMMHOLD">
                <annotation>
                    <documentation>Blokace - tuzemská okamžitá platba (do jiné banky v ČR)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DTRNIMMI">
                <annotation>
                    <documentation>Tuzemská prichozi okamžitá platba (z jiné banky v ČR)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DTRNIMMO">
                <annotation>
                    <documentation>Tuzemská odchozi okamžitá platba(do jiné banky v ČR)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DTRNINTI">
                <annotation>
                    <documentation>Platba  v rámci banky příchozí</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DTRNINTO">
                <annotation>
                    <documentation>Platba v rámci banky odchozí</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DTRNOUTI">
                <annotation>
                    <documentation>Platba mimo banku příchozí</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DTRNOUTO">
                <annotation>
                    <documentation>Platba mimo banku odchozí</documentation>
                </annotation>
            </enumeration>
            <enumeration value="EXEMPTEDINCOME">
                <annotation>
                    <documentation>Výplata nepostižitelného příjmu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="EXTCHRGFRGPAY">
                <annotation>
                    <documentation>Poplatek partnera za zahraniční platbu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="EXTCHRGFRGPAYCO">
                <annotation>
                    <documentation>Poplatek partnera za zahraniční platbu - konverní</documentation>
                </annotation>
            </enumeration>
            <enumeration value="EXTRAPAYOUT">
                <annotation>
                    <documentation>Výplata mimořádná</documentation>
                </annotation>
            </enumeration>
            <enumeration value="EXTRAPAYOUTCO">
                <annotation>
                    <documentation>Výplata mimořádná (konverzní)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="EXTRAPAYOUTVCH">
                <annotation>
                    <documentation>Výplata mimořádná složenkou</documentation>
                </annotation>
            </enumeration>
            <enumeration value="EXTRAPAYOUTVCHCO">
                <annotation>
                    <documentation>Výplata mimořádná složenkou(konverzní)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="FIXDEPCAP">
                <annotation>
                    <documentation>Peníze získané úročením termínovaného vkladu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="FIXDEPCLOSE">
                <annotation>
                    <documentation>Vložení peněz na termínovaný vklad</documentation>
                </annotation>
            </enumeration>
            <enumeration value="FIXDEPCREATE">
                <annotation>
                    <documentation>Vrácení peněz z termínovaného vkladu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="FEEPKG_ATM">
                <annotation>
                    <documentation>Výběry hotovosti ze všech bankomatů u nás i ve světě</documentation>
                </annotation>
            </enumeration>
            <enumeration value="FEEPKG_CURACC">
                <annotation>
                    <documentation>Vedení všech dalších běžných účtů (až do celkového počtu deseti účtů)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="FEEPKG_SMS">
                <annotation>
                    <documentation>Neomezená zasílání informačních SMS o pohybech na účtu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="FEEPKG_TRAVELPROP">
                <annotation>
                    <documentation>Zahraničí karta</documentation>
                </annotation>
            </enumeration>
            <enumeration value="FREETRIAL">
                <annotation>
                    <documentation>Vrácení poplatku 3 měsíce na zkoušku</documentation>
                </annotation>
            </enumeration>
            <enumeration value="I2ABPRCTRNSFR">
                <annotation>
                    <documentation>Investice do Air Bank - převod</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INCOFFSET">
                <annotation>
                    <documentation>Příchozí konverzní automatický zápočet pohled. mezi účty klienta - deposita</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INCOFFSETLOA">
                <annotation>
                    <documentation>Příchozí konverzní automatický zápočet pohled. mezi účty klienta - půjčky</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INNOFFSET">
                <annotation>
                    <documentation>Příchozí nekonverzní automatický zápočet pohled. mezi účty klienta - deposita</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INNOFFSETLOA">
                <annotation>
                    <documentation>Příchozí nekonverzní automatický zápočet pohled. mezi účty klienta - půjčky</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INSURANCE">
                <annotation>
                    <documentation>Pojistné</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INSURANCECO">
                <annotation>
                    <documentation>Pojistné konverzní</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INSURANCEPIP">
                <annotation>
                    <documentation>Pojistné</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INSURANCEPIPCO">
                <annotation>
                    <documentation>Pojistné konverzní</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INSURANCEPIPREF">
                <annotation>
                    <documentation>Přeplatek pojistného</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INSURANCEPIPREFCO">
                <annotation>
                    <documentation>Přeplatek pojistného konverzní</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INSURANCEPPI">
                <annotation>
                    <documentation>Pojistné PPI</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INSURANCEPPICO">
                <annotation>
                    <documentation>Pojistné PPI konverzní</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INSURANCEPPIREF">
                <annotation>
                    <documentation>Přeplatek pojistného PPI</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INSURANCEPPIREFCO">
                <annotation>
                    <documentation>Přeplatek pojistného PPI konverzní</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INSURANCEREF">
                <annotation>
                    <documentation>Přeplatek pojistného</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INSURANCEREFCO">
                <annotation>
                    <documentation>Přeplatek pojistného konverzní</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INVBUY">
                <annotation>
                    <documentation>Nákup cenného papíru</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INVBUYCO">
                <annotation>
                    <documentation>Nákup cenného papíru konverzní</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INVBUYFEE">
                <annotation>
                    <documentation>Poplatek za nákup cenného papíru</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INVBUYFEECO">
                <annotation>
                    <documentation>Poplatek za nákup cenného papíru konverzní</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INVBUYSTOCK">
                <annotation>
                    <documentation>Nákup akcií</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INVCOUPONPAY">
                <annotation>
                    <documentation>Výplata kupónu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INVCOUPONPAYCO">
                <annotation>
                    <documentation>Výplata kupónu konverzní</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INVDIVIDENDPAY">
                <annotation>
                    <documentation>Výplata dividendy</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INVDIVIDENDPAYCO">
                <annotation>
                    <documentation>Výplata dividendy konverzní</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INVDIVIDENDTAX">
                <annotation>
                    <documentation>Srážková daň z dividendy</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INVDIVIDENDTAXCO">
                <annotation>
                    <documentation>Srážková daň z dividendy konverzní</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INVMATURITY">
                <annotation>
                    <documentation>Výplata dluhopisu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INVMATURITYCO">
                <annotation>
                    <documentation>Výplata dluhopisu konverzní</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INVMGMFEE">
                <annotation>
                    <documentation>Poplatek za správu portfolia</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INVMGMFEECO">
                <annotation>
                    <documentation>Poplatek za správu portfolia konverzní</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INVREALDATAFEE">
                <annotation>
                    <documentation>Poplatek za poskytnutí realtime dat</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INVREFUNDBUY">
                <annotation>
                    <documentation>Vrácení přeplatku z nákupu cenného papíru</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INVREFUNDBUYCO">
                <annotation>
                    <documentation>Vrácení přeplatku z nákupu cenného papíru konverzní</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INVREFUNDBUYFEE">
                <annotation>
                    <documentation>Vrácení poplatku za nákup cenného papíru</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INVREFUNDBUYFEECO">
                <annotation>
                    <documentation>Vrácení poplatku za nákup cenného papíru konverzní</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INVSELL">
                <annotation>
                    <documentation>Prodej cenného papíru</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INVSELLCO">
                <annotation>
                    <documentation>Prodej cenného papíru konverzní</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INVSELLFEE">
                <annotation>
                    <documentation>Poplatek za prodej cenného papíru</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INVSELLFEECO">
                <annotation>
                    <documentation>Poplatek za prodej cenného papíru konverzní</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INVSELLSTOCK">
                <annotation>
                    <documentation>Prodej akcií</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INVTRANSFERFEE">
                <annotation>
                    <documentation>Poplatek za převod cenného papíru na jiný majetkový účet</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INVTRANSFERFEECO">
                <annotation>
                    <documentation>Poplatek za převod cenného papíru na jiný majetkový účet konverzní</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INVXSTMTFEE">
                <annotation>
                    <documentation>Poplatek za výpis</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INVXSTMTFEECO">
                <annotation>
                    <documentation>Poplatek za výpis s měnovou konverzí</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ITRNCLI">
                <annotation>
                    <documentation>Konverzní převod mezi účty klienta příchozí</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ITRNCLO">
                <annotation>
                    <documentation>Konverzní převod mezi účty klienta odchozí</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ITRNCZI">
                <annotation>
                    <documentation>Konverzní/nekonverzní platba v rámci ČR příchozí</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ITRNCZO">
                <annotation>
                    <documentation>Konverzní/nekonverzní platba v rámci ČR odchozí</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ITRNEEAI">
                <annotation>
                    <documentation>Konverzní/nekonverzní platba v rámci EHP mimo ČR příchozí</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ITRNEEAO">
                <annotation>
                    <documentation>Konverzní/nekonverzní platba v rámci EHP mimo ČR odchozí</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ITRNEURI">
                <annotation>
                    <documentation>Konverzní/nekonverzní Euro platba příchozí</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ITRNEURO">
                <annotation>
                    <documentation>Konverzní/nekonverzní Euro platba odchozí</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ITRNINTI">
                <annotation>
                    <documentation>Konverzní/nekonverzní platba v rámci banky příchozí</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ITRNINTO">
                <annotation>
                    <documentation>Konverzní/nekonverzní platba v rámci banky odchozí</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ITRNNPSDI">
                <annotation>
                    <documentation>Zahraniční platba - nonPSD příchozí</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ITRNNPSDO">
                <annotation>
                    <documentation>Zahraniční platba - nonPSD odchozí</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ITRNOUTI">
                <annotation>
                    <documentation>Konverzní/nekonverzní platba mimo EHP příchozí</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ITRNOUTO">
                <annotation>
                    <documentation>Konverzní/nekonverzní platba mimo EHP odchozí</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ITRNPSDI">
                <annotation>
                    <documentation>Zahraniční platba - PSD příchozí</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ITRNPSDO">
                <annotation>
                    <documentation>Zahraniční platba - PSD odchozí</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ITRNSEPI">
                <annotation>
                    <documentation>Zahraniční platba - SEPA příchozí</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ITRNSEPO">
                <annotation>
                    <documentation>Zahraniční platba - SEPA odchozí</documentation>
                </annotation>
            </enumeration>
            <enumeration value="KATMCODE">
                <annotation>
                    <documentation>výběr hotovosti výběrovým kódem</documentation>
                </annotation>
            </enumeration>
            <enumeration value="KATMCUR">
                <annotation>
                    <documentation>výběr hotovosti z ATM v měně účtu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="KATMOTH">
                <annotation>
                    <documentation>výběr hotovosti z ATM v jiné měně</documentation>
                </annotation>
            </enumeration>
            <enumeration value="KBLOCK">
                <annotation>
                    <documentation>karetní blokace</documentation>
                </annotation>
            </enumeration>
            <enumeration value="KCASHCUR">
                <annotation>
                    <documentation>výplata hotovosti v měně účtu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="KCASHOTH">
                <annotation>
                    <documentation>výplata hotovosti v jiné měně</documentation>
                </annotation>
            </enumeration>
            <enumeration value="KDEPCUR">
                <annotation>
                    <documentation>vklad hotovosti v ATM v měně účtu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="KDEPOTH">
                <annotation>
                    <documentation>vklad hotovosti v ATM v jiné měně</documentation>
                </annotation>
            </enumeration>
            <enumeration value="KPAYCUR">
                <annotation>
                    <documentation>platba kartou u obchodníka v měně účtu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="KPAYCURCB">
                <annotation>
                    <documentation>Platba kartou včetně Cash Back v měně účtu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="KPAYOTH">
                <annotation>
                    <documentation>platba kartou u obchodníka v jiné měně</documentation>
                </annotation>
            </enumeration>
            <enumeration value="KPAYOTHCB">
                <annotation>
                    <documentation>Platba kartou včetně Cash Back v jiné měně</documentation>
                </annotation>
            </enumeration>
            <enumeration value="KREFCUR">
                <annotation>
                    <documentation>refundace platby kartou v měně účtu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="KREFOTH">
                <annotation>
                    <documentation>refundace platby kartou v jiné měně</documentation>
                </annotation>
            </enumeration>
            <enumeration value="LIVINGWAGE">
                <annotation>
                    <documentation>Výplata životního minima</documentation>
                </annotation>
            </enumeration>
            <enumeration value="LIVINGWAGEACC">
                <annotation>
                    <documentation>Výplata životního minima klientovi bezhotovostně</documentation>
                </annotation>
            </enumeration>
            <enumeration value="LIVINGWAGEACCCO">
                <annotation>
                    <documentation>Výplata životního minima klientovi bezhotovostně - konverzní</documentation>
                </annotation>
            </enumeration>
            <enumeration value="LIVINGWAGECO">
                <annotation>
                    <documentation>Výplata životního minima konverzni</documentation>
                </annotation>
            </enumeration>
            <enumeration value="LOADRAWDOWNFEE">
                <annotation>
                    <documentation>Poplatek za čerpání hypotéky</documentation>
                </annotation>
            </enumeration>
            <enumeration value="LOAEXCEEDINGLTVFEE">
                <annotation>
                    <documentation>Poplatek za čerpání hypotéky</documentation>
                </annotation>
            </enumeration>
            <enumeration value="LOAEXINSTFINAL">
                <annotation>
                    <documentation>Předčasné splacení půjčky</documentation>
                </annotation>
            </enumeration>
            <enumeration value="LOAEXPAY">
                <annotation>
                    <documentation>Mimořádná splátka půjčky</documentation>
                </annotation>
            </enumeration>
            <enumeration value="LOAPAYSUMFULL">
                <annotation>
                    <documentation>Splátka půjčky</documentation>
                </annotation>
            </enumeration>
            <enumeration value="LOAPAYSUMPART">
                <annotation>
                    <documentation>Částečná splátka půjčky</documentation>
                </annotation>
            </enumeration>
            <enumeration value="LOAPENALTYRECALC">
                <annotation>
                    <documentation>Úrok z prodlení - doúčtování (ze servis. BÚ na vnitř. účet)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="LOAPENALTYRECALCPART">
                <annotation>
                    <documentation>Doúčtování obchodního úroku - část. úhrada (při nedost. prostředků)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="LOAPENALTYRETURN">
                <annotation>
                    <documentation>Úrok z prodlení - vratka (z vnitř. účtu na  servis. BÚ)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="LOAPENINTRECALC">
                <annotation>
                    <documentation>Obchodní úrok - doúčtování (ze servis. BÚ na vnitř. účet)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="LOAPENINTRECALCPART">
                <annotation>
                    <documentation>Doúčtování úroku z prodlení - část. úhrada (při nedost. prostředků)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="LOAPENINTRETURN">
                <annotation>
                    <documentation>Obchodní úrok - vratka (z vnitř. účtu na  servis. BÚ)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="LOAREDEM">
                <annotation>
                    <documentation>Zesplatnění úvěru - plná úhrada</documentation>
                </annotation>
            </enumeration>
            <enumeration value="LOAREDEMPART">
                <annotation>
                    <documentation>Zesplatnění úvěru - část. úhrada (při nedost. prostředků)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="LOAREFIN">
                <annotation>
                    <documentation>Refinancování úvěru</documentation>
                </annotation>
            </enumeration>
            <enumeration value="LOAREFINDEADI">
                <annotation>
                    <documentation>Refinancování úvěru dědictví</documentation>
                </annotation>
            </enumeration>
            <enumeration value="LOAREFINDEADO">
                <annotation>
                    <documentation>Refinancování úvěru dědictví</documentation>
                </annotation>
            </enumeration>
            <enumeration value="LOAREFININTI">
                <annotation>
                    <documentation>Doplacení interního závazku</documentation>
                </annotation>
            </enumeration>
            <enumeration value="LOAREFININTO">
                <annotation>
                    <documentation>Doplacení interního závazku</documentation>
                </annotation>
            </enumeration>
            <enumeration value="LOAUNDRAWNAMOUNTFEE">
                <annotation>
                    <documentation>Poplatek za čerpání hypotéky</documentation>
                </annotation>
            </enumeration>
            <enumeration value="LOAUTIL">
                <annotation>
                    <documentation>Načerpání půjčky</documentation>
                </annotation>
            </enumeration>
            <enumeration value="MLOAEXINSTLFINAL">
                <annotation>
                    <documentation>Předčasné úplné splacení hypotéky</documentation>
                </annotation>
            </enumeration>
            <enumeration value="MLOAEXPREPAY">
                <annotation>
                    <documentation>Úložka do OTB (mimořádná platba navíc)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="MLOAPAYSUMFULL">
                <annotation>
                    <documentation>Splátka hypotéky</documentation>
                </annotation>
            </enumeration>
            <enumeration value="MLOAPAYSUMPART">
                <annotation>
                    <documentation>Splátka hypotéky - částečná (při nedost. prostředků)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="MLOAPREPAY">
                <annotation>
                    <documentation>Úložka do OTB - pravidelná (pravidelná mimořádná platba navíc)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="MLOAPREPAYTOPRC">
                <annotation>
                    <documentation>Převod peněz z Chytré rezervy na jistinu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="MLOAREDEM">
                <annotation>
                    <documentation>Zesplatnění hypotéky - plná úhrada</documentation>
                </annotation>
            </enumeration>
            <enumeration value="MLOAREDEMPART">
                <annotation>
                    <documentation>Zesplatnění hypotéky - část. úhrada (při nedost. prostředků)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="MLOAREFIN">
                <annotation>
                    <documentation>Doplacení závazku při refinanc. hypotéky provádíme-li za klienta (do jiné banky v ČR)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="MLOARETURNPREPAY">
                <annotation>
                    <documentation>Čerpání z OTB (na BÚ pro splácení půjčky/hypo)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="MLOAUTIL">
                <annotation>
                    <documentation>Načerpání hypotéky (na BÚ pro splácení půjčky/hypo)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="MLOOTHFEE">
                <annotation>
                    <documentation>Poplatek za změnu hypoteční smlouvy</documentation>
                </annotation>
            </enumeration>
            <enumeration value="MOBILEPAYREWARD">
                <annotation>
                    <documentation>Odměna za platby a výběry mobilem</documentation>
                </annotation>
            </enumeration>
            <enumeration value="MOBILEPAYREWARDCO">
                <annotation>
                    <documentation>Odměna za platby a výběry mobilem (konverzní)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="MYAIRPARTNERACCCHRG">
                <annotation>
                    <documentation>Poplatek za platební styk a zúčtování na technickém účtu, vytváří se jednou měsíčně</documentation>
                </annotation>
            </enumeration>
            <enumeration value="MYAIRPARTNERPAY">
                <annotation>
                    <documentation>Převod z technického účtu partnera na účet partnera, vytváří se jednou denně</documentation>
                </annotation>
            </enumeration>
            <enumeration value="MYAIRPAY">
                <annotation>
                    <documentation>Platba z aplikace My Air z klientského účtu na transportní účet partnera</documentation>
                </annotation>
            </enumeration>
            <enumeration value="MYAIRPAYCO">
                <annotation>
                    <documentation>Platba z aplikace My Air z klientského účtu na transportní účet partnera-konverzní</documentation>
                </annotation>
            </enumeration>
            <enumeration value="MYAIRPAYRETURN">
                <annotation>
                    <documentation>Vratka platby z aplikace My Air z transportního účtu partnera na klientský účet</documentation>
                </annotation>
            </enumeration>
            <enumeration value="MYAIRPAYRETURNCO">
                <annotation>
                    <documentation>Vratka platby z aplikace My Air z transportního účtu partnera na klientský účet-konverzní</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ODBTCLR">
                <annotation>
                    <documentation>Úhrada debetního zůstatku na účtu klienta z vnitřního účtu banky</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ODTBUSINTPAY">
                <annotation>
                    <documentation>Obchodní úrok kontokorentu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ODTPENINTPAY">
                <annotation>
                    <documentation>Úrok z prodlení kontokorentu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ODTREDEM">
                <annotation>
                    <documentation>Splátka převedeného kontokorentu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ORNDUP">
                <annotation>
                    <documentation>Haléřové vyrovnání</documentation>
                </annotation>
            </enumeration>
            <enumeration value="OTRMTRN">
                <annotation>
                    <documentation>Převod konečného zůstatku</documentation>
                </annotation>
            </enumeration>
            <enumeration value="OUTCOFFSET">
                <annotation>
                    <documentation>Odchozí konverzní automatický zápočet pohled. mezi účty klienta - deposita</documentation>
                </annotation>
            </enumeration>
            <enumeration value="OUTCOFFSETLOA">
                <annotation>
                    <documentation>Odchozí konverzní automatický zápočet pohled. mezi účty klienta - půjčky</documentation>
                </annotation>
            </enumeration>
            <enumeration value="OUTNOFFSET">
                <annotation>
                    <documentation>Odchozí nekonverzní automatický zápočet pohled. mezi účty klienta - deposita</documentation>
                </annotation>
            </enumeration>
            <enumeration value="OUTNOFFSETLOA">
                <annotation>
                    <documentation>Odchozí nekonverzní automatický zápočet pohled. mezi účty klienta - půjčky</documentation>
                </annotation>
            </enumeration>
            <enumeration value="OVDRINSTL">
                <annotation>
                    <documentation>Splátka kontokorentu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="OVDRRETURN">
                <annotation>
                    <documentation>Vrácení čerpání kontokorentu na běžný účet při ukončení poslední exekuce</documentation>
                </annotation>
            </enumeration>
            <enumeration value="OVERDRTOLOAN">
                <annotation>
                    <documentation>Vyvedení čerpání kontokorentu při exekuci na běžném účtu/ukončení běžného účtu/zesplatnění</documentation>
                </annotation>
            </enumeration>
            <enumeration value="OVERPAYOUTCONSOLID">
                <annotation>
                    <documentation>Přeplatek z odchozí konsolidace</documentation>
                </annotation>
            </enumeration>
            <enumeration value="OVERPAYOUTCONSOLIDCO">
                <annotation>
                    <documentation>Přeplatek z odchozí konsolidace - konverzní</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PAYINHERITANCE">
                <annotation>
                    <documentation>Výplata dědictví</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PAYINHERITANCECO">
                <annotation>
                    <documentation>Výplata dědictví - konverzní</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PENINTPAYFULL">
                <annotation>
                    <documentation>Odchozí nekonverzní splátka úroku z prodlení</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PENINTPAYPART">
                <annotation>
                    <documentation>Odchozí nekonverzní částečná splátka úroku z prodlení</documentation>
                </annotation>
            </enumeration>
            <enumeration value="POSTALORDER">
                <annotation>
                    <documentation>Výplata složenkou</documentation>
                </annotation>
            </enumeration>
            <enumeration value="POSTALORDERCO">
                <annotation>
                    <documentation>Výplata složenkou konverzní</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RESTRAINT">
                <annotation>
                    <documentation>Výplata exekuce</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RESTRAINTCO">
                <annotation>
                    <documentation>Výplata exekuce konverzni</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RETIREMENT">
                <annotation>
                    <documentation>Vrácení důchodu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RETIREMENTCO">
                <annotation>
                    <documentation>Vrácení důchodu - konverzní</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RETURNFEE">
                <annotation>
                    <documentation>Vrácení poplatku</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RETURNFEECO">
                <annotation>
                    <documentation>Vrácení poplatku - konverní</documentation>
                </annotation>
            </enumeration>
            <enumeration value="REWARD">
                <annotation>
                    <documentation>Odměna obecná</documentation>
                </annotation>
            </enumeration>
            <enumeration value="REWARDCO">
                <annotation>
                    <documentation>Odměna obecná (konverzní)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="SAZKABLD">
                <annotation>
                    <documentation>Blokace Sazka</documentation>
                </annotation>
            </enumeration>
            <enumeration value="SAZKABLF">
                <annotation>
                    <documentation>Blokace Sazka - cizoměnová</documentation>
                </annotation>
            </enumeration>
            <enumeration value="SAZKABLUND">
                <annotation>
                    <documentation>Blokace Sazka (nepoužitá)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="SAZKABLUNF">
                <annotation>
                    <documentation>Blokace Sazka - cizoměnová (nepoužitá)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="SAZKAPAYD">
                <annotation>
                    <documentation>Výběr Sazka kódem</documentation>
                </annotation>
            </enumeration>
            <enumeration value="SAZKAPAYF">
                <annotation>
                    <documentation>Výběr Sazka kódem - cizoměnový</documentation>
                </annotation>
            </enumeration>
            <enumeration value="SPLITPAYMENTFEE">
                <annotation>
                    <documentation>Poplatek za rozložení platby</documentation>
                </annotation>
            </enumeration>
            <enumeration value="TDINTRETURN">
                <annotation>
                    <documentation>Vrácení úroku z terminovaného vkladu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="TDINTTRNSFR">
                <annotation>
                    <documentation>Výnos z terminovaného vkladu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="TOPUPPHONECREDIT">
                <annotation>
                    <documentation>Dobití kreditu na předplacené SIM kartě</documentation>
                </annotation>
            </enumeration>
            <enumeration value="TRVINSURCANCEL">
                <annotation>
                    <documentation>Vratka cestovního pojištění na CZ účet při stornu cesty</documentation>
                </annotation>
            </enumeration>
            <enumeration value="TRVINSURCANCELCO">
                <annotation>
                    <documentation>Vratka cestovního pojištění na CM účet při stornu cesty</documentation>
                </annotation>
            </enumeration>
            <enumeration value="TRVINSURPAY">
                <annotation>
                    <documentation>Úhrada cestovního pojištění z CZ účtu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="TRVINSURPAYCO">
                <annotation>
                    <documentation>Úhrada cestovního pojištění z CM účtu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="TRVINSURREFUND">
                <annotation>
                    <documentation>Vratka přeplatku cestovního pojištění na CZ účet</documentation>
                </annotation>
            </enumeration>
            <enumeration value="TRVINSURREFUNDCO">
                <annotation>
                    <documentation>Vratka přeplatku cestovního pojištění na CM účet</documentation>
                </annotation>
            </enumeration>
            <enumeration value="TRAVELINSURANCEUNITYREWARD">
                <annotation>
                    <documentation>Unity sleva na pojištění z účtu Air Bank</documentation>
                </annotation>
            </enumeration>
            <enumeration value="UNITYREWARD">
                <annotation>
                    <documentation>Odměna Unity</documentation>
                </annotation>
            </enumeration>
            <enumeration value="WAGEPAY">
                <annotation>
                    <documentation>Výplata mzdy zaměstnanců</documentation>
                </annotation>
            </enumeration>
            <enumeration value="WAGEPAYACC">
                <annotation>
                    <documentation>Výplata mzdy zaměstnanců bezhotovostní</documentation>
                </annotation>
            </enumeration>
            <enumeration value="WAGEPAYACCCO">
                <annotation>
                    <documentation>Výplata mzdy zaměstnanců bezhotovostní s konverzí</documentation>
                </annotation>
            </enumeration>
            <enumeration value="WAGEPAYCO">
                <annotation>
                    <documentation>Výplata mzdy zaměstnanců konverzní</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ZKNCHRG">
                <annotation>
                    <documentation>Poplatek za zápis do KN</documentation>
                </annotation>
            </enumeration>
            <!--POZOR do seznamu se přidává podle abecedy, přímo nad tento řádek tedy přidávej pouze tehdy, pokud SEM nový transakční typ opravdu patří, tzn. spadá dle abecedního řazení až úplně na konec -->
        </restriction>
    </simpleType>

    <complexType name="StandingOrderTO">
        <complexContent>
            <extension base="tns:PaymentOrderTO">
                <sequence>
                    <element name="periodicityUnit" type="string">
                        <annotation>
                            <documentation>jednotka opakování</documentation>
                        </annotation>
                    </element>
                    <element name="periodicity" type="int">
                        <annotation>
                            <documentation>
                                Např. pokud je periodicityUnit nastaveno na
                                měsíčně a periodicity je 2, potom to znamená, že
                                jednou za 2 měsíce se provede platba.
                            </documentation>
                        </annotation>
                    </element>
                    <element name="standingOrderName" type="string">
                        <annotation>
                            <documentation>jméno trvalého příkazu</documentation>
                        </annotation>
                    </element>
                    <element name="validFromChainSO" type="date" minOccurs="0">
                        <annotation>
                            <documentation>datum počátku platnosti řetězce trvalých příkazů</documentation>
                        </annotation>
                    </element>
                    <element name="standingOrderStatus" minOccurs="0">
                        <annotation>
                            <documentation>status trvalého příkazu</documentation>
                        </annotation>
                        <simpleType>
                            <restriction base="string">
                                <enumeration value="ACTIVE">
                                    <annotation>
                                        <documentation>aktivní trvalý příkaz (zahrnuje i autorizované příkazy s dopředným počátkem platnosti)</documentation>
                                    </annotation>
                                </enumeration>
                                <enumeration value="SUSPENDED">
                                    <annotation>
                                        <documentation>pozastavený trvalý příkaz, během pozastavení nejsou na jeho základě vytvářeny žádné transakce</documentation>
                                    </annotation>
                                </enumeration>
                                <enumeration value="ENDED">
                                    <annotation>
                                        <documentation>ukončený trvalý příkaz, buďto úplně zrušený nebo nahrazený novější instancí v rámci modifikace předpisu TP</documentation>
                                    </annotation>
                                </enumeration>
                                <enumeration value="EDITED">
                                    <annotation>
                                        <documentation>trvalý příkaz v režimu modifikace</documentation>
                                    </annotation>
                                </enumeration>
                            </restriction>
                        </simpleType>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <simpleType name="PeriodicityUnitType">
        <restriction base="string">
            <enumeration value="DAY"/>
            <enumeration value="WEEK"/>
            <enumeration value="MONTH"/>
            <enumeration value="QUARTER"/>
            <enumeration value="HALF_YEAR"/>
            <enumeration value="YEAR"/>
        </restriction>
    </simpleType>

    <complexType name="CrossBorderPaymentOrderTO">
        <complexContent>
            <extension base="tns:PaymentOrderTO">
                <sequence>
                    <element name="contraAccountFullName" type="string" minOccurs="0">
                        <annotation>
                            <documentation>adresa příjemce</documentation>
                        </annotation>
                    </element>
                    <element name="contraBankFullName" type="string" minOccurs="0">
                        <annotation>
                            <documentation>adresa banky příjemce</documentation>
                        </annotation>
                    </element>
                    <element name="messageForBank" type="string" minOccurs="0">
                        <annotation>
                            <documentation>zpráva pro banku</documentation>
                        </annotation>
                    </element>
                    <element name="countryCode" type="string" minOccurs="1" />
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <simpleType name="CollectionStatusType">
        <restriction base="string">
            <enumeration value="REALIZED">
                <annotation>
                    <documentation>povolení realizace inkasa</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CANCELED">
                <annotation>
                    <documentation>zrušení realizace inkasa</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="FilterPaymentOrderStatus">
        <annotation>
            <documentation>výčet stavů pro filtr na požadovaných platbách (PaymentOrder)</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="RTS_NOT_REALIZED">
                <annotation>
                    <documentation>čěká na realizaci</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RTS_SUSPENDED">
                <annotation>
                    <documentation>stav inkasa pro manuální schválení</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RTS_ENDED">
                <annotation>
                    <documentation>ukoncena nebo zrusena</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RTS_ENDED_COLLECTION">
                <annotation>
                    <documentation>pro zamítnuté inkaso</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="FilterRealizedTransactionType">
        <restriction base="string">
            <enumeration value="PAY">
                <annotation>
                    <documentation>tuzemská platba</documentation>
                </annotation>
            </enumeration>
            <enumeration value="MYPAY">
                <annotation>
                    <documentation>platba v rámci svých účtů</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INTLPAY">
                <annotation>
                    <documentation>zahraniční platba</documentation>
                </annotation>
            </enumeration>
            <enumeration value="STNDPAY">
                <annotation>
                    <documentation>trvalý příkaz</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INKPAY">
                <annotation>
                    <documentation>inkaso </documentation>
                </annotation>
            </enumeration>
            <enumeration value="SAVEPAY">
                <annotation>
                    <documentation>pravidelné spoření</documentation>
                </annotation>
            </enumeration>
            <enumeration value="SIPOPAY">
                <annotation>
                    <documentation>sipo</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CARDPAY">
                <annotation>
                    <documentation>platba kartou </documentation>
                </annotation>
            </enumeration>
            <enumeration value="CASHPAY">
                <annotation>
                    <documentation>hotovostní operace</documentation>
                </annotation>
            </enumeration>
            <enumeration value="OTHERPAY">
                <annotation>
                    <documentation>úroky a poplatky</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ENVPAY">
                <annotation>
                    <documentation>jen převody v rámci jednoho účtu mezi obálkami přip. “bezobálky”</documentation>
                </annotation>
            </enumeration>
            <enumeration value="LOANPAY">
                <annotation>
                    <documentation>úvěrové transakce</documentation>
                </annotation>
            </enumeration>
            <enumeration value="SAZKAPAY">
                <annotation>
                    <documentation>úvěrové transakce</documentation>
                </annotation>
            </enumeration>
            <enumeration value="TOPUPPHONECREDIT">
                <annotation>
                    <documentation>dobíjení kreditu na předplacených SIM kartách</documentation>
                </annotation>
            </enumeration>
            <enumeration value="TRAVELINSURANCE">
                <annotation>
                    <documentation>transakce cestovního pojištění</documentation>
                </annotation>
            </enumeration>
            <enumeration value="BPIINSURANCE">
                <annotation>
                    <documentation>transakce pojištění pravidelných výdajů</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PPIINSURANCE">
                <annotation>
                    <documentation>transakce pojištění schopnosti splácet</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <complexType name="ForgivenFeeTO">
        <sequence>
            <element name="amountFee" type="decimal">
                <annotation>
                    <documentation>
                        částka (přepočtená) v Kč.
                    </documentation>
                </annotation>
            </element>
            <element name="dateFeeCanceled" type="date">
                <annotation>
                    <documentation>
                        datum odpuštění poplatku
                    </documentation>
                </annotation>
            </element>
            <element name="descriptionFee" type="string">
                <annotation>
                    <documentation>textový popis poplatku</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="TransactionCategeryTO">
        <sequence>
            <element name="idTransaction" type="long">
                <annotation>
                    <documentation>
                        id realizované nebo předepsané transakce
                    </documentation>
                </annotation>
            </element>
            <element name="idBankAccountDebit" type="long" />
            <element name="internalTransactionType" type="tns:InternalTransactionType">
                <annotation>
                    <documentation>
                        atribut pro rozlišení zda se jedná o
                        realizovanou nebo předepsanou transakci
                    </documentation>
                </annotation>
            </element>
            <element name="idCategory" type="long" minOccurs="0">
                <annotation>
                    <documentation>id kategorie platby</documentation>
                </annotation>
            </element>
            <element name="date" type="date">
                <annotation>
                    <documentation>
                        datum splatnosti u poředepsaných a datum
                        realizace u realizovaných
                    </documentation>
                </annotation>
            </element>
            <element name="contraAccountNumber" type="string">
                <annotation>
                    <documentation>
                        číslo protiúčtu (i spojovací číslo v případě
                        inkasa)
                    </documentation>
                </annotation>
            </element>
            <element name="contraAccountNumberPrefix" type="string" minOccurs="0">
                <annotation>
                    <documentation>prefix protiúčtu</documentation>
                </annotation>
            </element>
            <element name="contraBankCode" type="string">
                <annotation>
                    <documentation>kód bank protiúčtu</documentation>
                </annotation>
            </element>
            <element name="amount" type="decimal">
                <annotation>
                    <documentation>
                        u předepsaných transakcí se jedná o požadovanou
                        částku a u realizovaných o částku v měně účtu
                    </documentation>
                </annotation>
            </element>
            <element name="currencyCode" type="string">
                <annotation>
                    <documentation>kód měny</documentation>
                </annotation>
            </element>
            <element name="userNote" type="string" minOccurs="0">
                <annotation>
                    <documentation>poznámka pro mě</documentation>
                </annotation>
            </element>
            <element name="transactionType" type="tns:TransactionType">
                <annotation>
                    <documentation>typ transakce</documentation>
                </annotation>
            </element>
            <element name="merchantName" type="string" minOccurs="0">
                <annotation>
                    <documentation>
                        jméno obchodníka (karetní operace)
                    </documentation>
                </annotation>
            </element>
            <element name="mcc" type="string" minOccurs="0">
                <annotation>
                    <documentation>
                        MCC (karetní operace)
                    </documentation>
                </annotation>
            </element>
            <element name="prescription" type="tns:PrescriptionType" minOccurs="0">
                <annotation>
                    <documentation>
                        informace o předpisu platby (náza
                    </documentation>
                </annotation>
            </element>
            <element name="cardName" type="string" minOccurs="0">
                <annotation>
                    <documentation>uživatelský název karty</documentation>
                </annotation>
            </element>
            <element name="cardAtmCode" type="string" minOccurs="0">
                <annotation>
                    <documentation>posledních 9  čísel karty - maskované např. *****1234</documentation>
                </annotation>
            </element>
            <element name="contraAccountName" type="string" minOccurs="0">
                <annotation>
                    <documentation>název protiúčtu</documentation>
                </annotation>
            </element>
            <element name="contraEnvelopeName" type="string" minOccurs="0">
                <annotation>
                    <documentation>název obálky proti strany (když je odchozí platba, je to kreditní obálka, když příchozí tak debetní obálka)</documentation>
                </annotation>
            </element>
            <element name="selfEnvelopeName" type="string" minOccurs="0">
                <annotation>
                    <documentation>název vlastní obálky (když je odchozí platba, je to debetní obálka, když příchozí tak je to kreditní obálka)</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="PrescriptionType">
        <annotation>
            <documentation></documentation>
        </annotation>
        <sequence>
            <element name="idPrescription" type="long">
                <annotation>
                    <documentation>id předpisu</documentation>
                </annotation>
            </element>
            <element name="type" type="tns:PrescriptionEnumType">
                <annotation>
                    <documentation>typ předpisu</documentation>
                </annotation>
            </element>
            <element name="name" type="string" minOccurs="0">
                <annotation>
                    <documentation>název předpisu</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <simpleType name="PrescriptionEnumType">
        <restriction base="string">
            <enumeration value="STANDING_ORDER">
                <annotation>
                    <documentation>trvalý příkaz</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ENABLE_COLLECTION">
                <annotation>
                    <documentation>povolení inkasa</documentation>
                </annotation>
            </enumeration>
            <enumeration value="SIPO">
                <annotation>
                    <documentation>sipo</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PAYMENT_ORDER">
                <annotation>
                    <documentation>
                        šablona jednorázového platebního příkazu
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="LOAN">
                <annotation>
                    <documentation>na zakladě jakého úvěru transakce vznikla</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <complexType name="SazkaTransType">
        <sequence>
            <element name="status" type="tns:SazkaStatusType">
                <annotation>
                    <documentation>stav sazka kódu</documentation>
                </annotation>
            </element>
            <element name="sazkaCode" type="string">
                <annotation>
                    <documentation>použité číslo SK</documentation>
                </annotation>
            </element>
            <element name="sazkaID" type="string">
                <annotation>
                    <documentation>Externí id sazka kódu</documentation>
                </annotation>
            </element>
            <element name="cuid" type="string" minOccurs="0">
                <annotation>
                    <documentation>CUID subjektu, který si vygeneroval SK</documentation>
                </annotation>
            </element>
            <element name="fullName" type="string" minOccurs="0">
                <annotation>
                    <documentation>Jméno subjektu, který si vygeneroval SK</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <simpleType name="SazkaStatusType">
        <annotation>
            <documentation>výčet stavů sazka kódu</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="w">
                <annotation>
                    <documentation>použitý SK</documentation>
                </annotation>
            </enumeration>
            <enumeration value="a">
                <annotation>
                    <documentation>aktivní SK</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="Amount">
        <annotation>
            <documentation>amount of payment order - positive number, maximum 2 decimal places</documentation>
        </annotation>
        <restriction base="decimal">
            <minExclusive value="0"/>
            <fractionDigits value="2"/>
        </restriction>
    </simpleType>

    <simpleType name="ForeignPaymentOrderType">
        <annotation>
            <documentation>type of foreign payment order</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="SEPA">
                <annotation>
                    <documentation>SEPA payment</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PSD">
                <annotation>
                    <documentation>PSD payment</documentation>
                </annotation>
            </enumeration>
            <enumeration value="NON_PSD">
                <annotation>
                    <documentation>NON_PSD payment</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="ErrorGetForeignPaymentOrderType">
        <annotation>
            <documentation>error for GetForeignPaymentOrderType</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="IBAN_INVALID">
                <annotation>
                    <documentation>invalid iban</documentation>
                </annotation>
            </enumeration>
            <enumeration value="BIC_NOT_FOUND">
                <annotation>
                    <documentation>BIC for IBAN was not found</documentation>
                </annotation>
            </enumeration>
            <enumeration value="BANK_NOT_FOUND">
                <annotation>
                    <documentation>Bank for BIC was not found or is invalid.</documentation>
                </annotation>
            </enumeration>
            <enumeration value="BIC_REQUIRED">
                <annotation>
                    <documentation>BIC is required for non-SEPA payment</documentation>
                </annotation>
            </enumeration>
            <enumeration value="BANK_CODE_INVALID">
                <annotation>
                    <documentation>invalid bank code</documentation>
                </annotation>
            </enumeration>
            <enumeration value="COUNTRY_MISMATCH">
                <annotation>
                    <documentation>country code in IBAN and BIC are different</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CLERR_NOT_CROSSBORDER">
                <annotation>
                    <documentation>the payment is not foreign payment (payment into a czech bank in czk). must be entered as a domestic</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CLERR_NOT_CROSSBORDER_AB">
                <annotation>
                    <documentation>the payment is not foreign payment (payment into AirBank). must be entered as a domestic</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ERR_CROSSBORDER_PAYMENT_IN_CZK">
                <annotation>
                    <documentation>non PSD crossborder payment IN CZK</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ERR_CURRENCY_NOT_SUPPORTED_BY_COUNTERPARTY">
                <annotation>
                    <documentation>Counterparty (bank) does not support payments in requested currency</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CURRENCY_NOT_FOUND">
                <annotation>
                    <documentation>The requested currency is not allowed for outgoing payments</documentation>
                </annotation>
            </enumeration>
            <enumeration value="MISMATCH_IBANBIC_BIC">
                <annotation>
                    <documentation>The entered BIC does not match the BIC in the IBAN</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ERR_BLACKLISTED_ACCOUNT">
                <annotation>
                    <documentation>The entered account is on BlackList</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="WarningGetForeignPaymentOrderType">
        <annotation>
            <documentation>warning for GetForeignPaymentOrderType</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="IBAN_INVALID">
                <annotation>
                    <documentation>invalid iban</documentation>
                </annotation>
            </enumeration>
            <enumeration value="WRN_CROSSBORDER_PAYMENT_IN_CZK">
                <annotation>
                    <documentation>crossborder payment entred in CZK</documentation>
                </annotation>
            </enumeration>
             <enumeration value="WRN_BLACKLISTED_ACCOUNT">
                <annotation>
                    <documentation>The entered account is on BlackList</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <complexType name="ContraBankTO">
        <annotation>
            <documentation>name and address of contra bank in foreign payment order</documentation>
        </annotation>
        <sequence>
            <element name="name" type="string">
                <annotation>
                    <documentation>name of bank</documentation>
                </annotation>
            </element>
            <element name="address" type="string">
                <annotation>
                    <documentation>address of bank</documentation>
                </annotation>
            </element>
            <element name="country" type="string">
                <annotation>
                    <documentation>textual code of the country of bank</documentation>
                </annotation>
            </element>
            <element name="bic" type="string">
                <annotation>
                    <documentation>BIC code of bank</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <simpleType name="NonPsdFeeCoverageType">
        <restriction base="string">
            <enumeration value="OUR">
                <annotation>
                    <documentation>charged to our bank (the client)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="SHARE">
                <annotation>
                    <documentation>charged to foreign and our bank (client)</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>


    <simpleType name="Swift1x35">
        <annotation>
            <documentation>swift message item 1x35</documentation>
        </annotation>
        <restriction base="string">
            <maxLength value="35"/>
        </restriction>
    </simpleType>

    <simpleType name="Swift3x35">
        <annotation>
            <documentation>swift message item 3x35</documentation>
        </annotation>
        <restriction base="string">
            <maxLength value="105"/>
        </restriction>
    </simpleType>

    <simpleType name="Swift4x35">
        <annotation>
            <documentation>swift message item 4x35</documentation>
        </annotation>
        <restriction base="string">
            <maxLength value="140"/>
        </restriction>
    </simpleType>

    <simpleType name="Swift6x35">
        <annotation>
            <documentation>swift message item 6x35</documentation>
        </annotation>
        <restriction base="string">
            <maxLength value="210"/>
        </restriction>
    </simpleType>

    <complexType name="ForeignPaymentOrderTO" abstract="true">
        <sequence>
            <element name="idPaymentOrder" type="long" minOccurs="0">
                <annotation>
                    <documentation>identification number of edited payment order. when it is filled, editing is an existing payment</documentation>
                </annotation>
            </element>
            <element name="idBankAccountDebit" type="long">
                <annotation>
                    <documentation>identification number of debit account</documentation>
                </annotation>
            </element>
            <element name="additionalInfo" type="com:AdditionalInfoTO" minOccurs="0">
                <annotation>
                    <documentation>Additional information for mobile application frontend purposes.</documentation>
                </annotation>
            </element>
            <element name="debitEnvelopeID" type="long" minOccurs="0">
                <annotation>
                    <documentation>identifikátor zdrojové obálky</documentation>
                </annotation>
            </element>
            <element name="amountInRequiredCurrency" type="tns:Amount">
                <annotation>
                    <documentation>amount in required currency of payment order</documentation>
                </annotation>
            </element>
            <element name="requiredCurrency" type="string">
                <annotation>
                    <documentation>required currency of payment order</documentation>
                </annotation>
            </element>
            <element name="categoryID" type="long" minOccurs="0">
                <annotation>
                    <documentation>id of payment category</documentation>
                </annotation>
            </element>
            <element name="contraAccountNumber" type="string">
                <annotation>
                    <documentation>number of credit account</documentation>
                </annotation>
            </element>
            <element name="contraBankCode" type="string">
                <annotation>
                    <documentation>code of credit bank</documentation>
                </annotation>
            </element>
            <element name="contraAccountName" type="tns:Swift1x35">
                <annotation>
                    <documentation>name of credit account</documentation>
                </annotation>
            </element>
            <element name="contraAccountFullName" type="tns:Swift3x35">
                <annotation>
                    <documentation>address of reciever (credit account)</documentation>
                </annotation>
            </element>
            <element name="contraAccountCountry" type="string">
                <annotation>
                    <documentation>country code of reciever (credit account)</documentation>
                </annotation>
            </element>
            <element name="messageForSender" type="tns:Swift4x35" minOccurs="0">
                <annotation>
                    <documentation>message for sender</documentation>
                </annotation>
            </element>
            <element name="messageForReceiver" type="tns:Swift4x35" minOccurs="0">
                <annotation>
                    <documentation>message for receiver</documentation>
                </annotation>
            </element>
            <element name="validFrom" type="dateTime">
                <annotation>
                    <documentation>date of validity of payment order</documentation>
                </annotation>
            </element>
            <element name="emailNotif" type="boolean">
                <annotation>
                    <documentation>whether it will send e-mail notifications for realizing transactions</documentation>
                </annotation>
            </element>
            <element name="prescription" type="tns:PrescriptionType" minOccurs="0">
                <annotation>
                    <documentation>binding prescription transactions</documentation>
                </annotation>
            </element>
            <element name="paymentReasonCode" type="string" minOccurs="0">
                <annotation>
                    <documentation>payment reason code</documentation>
                </annotation>
            </element>
            <element name="paymentReasonText" type="string" minOccurs="0">
                <annotation>
                    <documentation>description of payment reason</documentation>
                </annotation>
            </element>
            <element name="endToEnd" type="string" minOccurs="0">
                <annotation>
                    <documentation>Identifikace SEPA platby - EndToEnd reference</documentation>
                </annotation>
            </element>
            <element name="consumingAppId" type="string" minOccurs="0">
                <annotation>
                    <documentation>Payment Initiation Service Providers (PISP) pro Open API</documentation>
                </annotation>
            </element>
            <element name="developerId" type="string" minOccurs="0">
                <annotation>
                    <documentation>Payment Initiation Service Providers (PISP) pro Open API</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="SepaPaymentOrderTO">
        <annotation>
            <documentation>SEPA payment order type</documentation>
        </annotation>
        <complexContent>
            <extension base="tns:ForeignPaymentOrderTO">
                <sequence></sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="PsdPaymentOrderTO">
        <annotation>
            <documentation>PSD payment order type</documentation>
        </annotation>
        <complexContent>
            <extension base="tns:ForeignPaymentOrderTO">
                <sequence></sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="NonPsdPaymentOrderTO">
        <annotation>
            <documentation>NON_PSD payment order type</documentation>
        </annotation>
        <complexContent>
            <extension base="tns:ForeignPaymentOrderTO">
                <sequence>
                    <element name="contraBankName">
                        <annotation>
                            <documentation>name ob credit bank</documentation>
                        </annotation>
                        <simpleType>
                            <restriction base="string">
                                <maxLength value="150"/>
                            </restriction>
                        </simpleType>
                    </element>
                    <element name="contraBankFullName" type="tns:Swift3x35">
                        <annotation>
                            <documentation>address of credit bank</documentation>
                        </annotation>
                    </element>
                    <element name="contraBankCountry" type="string">
                        <annotation>
                            <documentation>country code of credit bank</documentation>
                        </annotation>
                    </element>
                    <element name="feeCoverage" type="tns:NonPsdFeeCoverageType">
                        <annotation>
                            <documentation>Cover of fees (SHARE / OUR)</documentation>
                        </annotation>
                    </element>
                    <element name="messageForBank" type="tns:Swift6x35" minOccurs="0">
                        <annotation>
                            <documentation>message for bank</documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="FeeAmount">
        <annotation>
            <documentation></documentation>
        </annotation>
        <sequence>
            <element name="amount" type="decimal">
                <annotation>
                    <documentation>amount of fee</documentation>
                </annotation>
            </element>
            <element name="currency" type="string">
                <annotation>
                    <documentation>currency of fee</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <simpleType name="RealizationStatusType">
        <annotation>
            <documentation>realization status after set/edit payment order</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="ON_LINE_REALIZED">
                <annotation>
                    <documentation>payment order was realized online</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ON_LINE_NOT_REALIZED_YET">
                <annotation>
                    <documentation>payment order was not realized online (runs closing job, insufficient means, etc.)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PLANNED">
                <annotation>
                    <documentation>payment order will be realized in the future</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ENDED">
                <annotation>
                    <documentation>payment order was ended</documentation>
                </annotation>
            </enumeration>
            <enumeration value="IN_DOUBT">
                <annotation>
                    <documentation>Instant payment order is in doubt</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ON_LINE_REALIZED_IN_DELIVERY">
                <annotation>
                    <documentation>Instant payment order was delivered and not confirmed</documentation>
                </annotation>
            </enumeration>
            <enumeration value="REJECTED">
                <annotation>
                    <documentation>Instant payment order was rejected</documentation>
                </annotation>
            </enumeration>
            <enumeration value="STD_PAYMENT_FALLBACK">
                <annotation>
                    <documentation>Instant payment cannot process. Can be processed standardly.</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="RealizationStatusReason">
        <annotation>
            <documentation>realization status reason if realization status is STD_PAYMENT_FALLBACK</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="AML_CHECK_FAILED">
                <annotation>
                    <documentation>AML check failed</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PHISHING_CHECK_FAILED">
                <annotation>
                    <documentation>Phishing check failed</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="DirectionType">
        <annotation>
            <documentation>Direction of transaction in relation to the client's account</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="IN">
                <annotation>
                    <documentation>Incoming transaction</documentation>
                </annotation>
            </enumeration>
            <enumeration value="OUT">
                <annotation>
                    <documentation>Outgoing transaction</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ACC">
                <annotation>
                    <documentation>Transfer inside account</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="AccountedType">
        <annotation>
            <documentation>Information on payment card/Sazka code transaction that the payment is already accounted</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="Y">
                <annotation>
                    <documentation>Yes</documentation>
                </annotation>
            </enumeration>
            <enumeration value="N">
                <annotation>
                    <documentation>No</documentation>
                </annotation>
            </enumeration>
            <enumeration value="NA">
                <annotation>
                    <documentation>Not applicable</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="CreditDebitIndicator">
        <restriction base="string">
            <enumeration value="DBIT">
                <annotation>
                    <documentation>DBIT - debetní pohyb na účtu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CRDT">
                <annotation>
                    <documentation>CRDT - kreditní pohyb na účtu</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <complexType name="RealTrnKey">
        <annotation>
            <documentation>contextual identificator of realized transaction</documentation>
        </annotation>
        <sequence>
            <element name="idRealizedTransaction" type="long">
                <annotation>
                    <documentation>id of realized transaction</documentation>
                </annotation>
            </element>
            <element name="idBankAccount" type="long">
                <annotation>
                    <documentation>context</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="StandingOrderInstanceInfoTO">
        <sequence>
            <element name="idStandingOrder" type="long" />
            <element name="firstRealizedTransactionDate" type="date" minOccurs="0">
                <annotation>
                    <documentation>datum (nejstarší) první realizované transakce z celého řetězce trvalých příkazů do kterého patří daný předpis</documentation>
                </annotation>
            </element>
            <element name="realizedTransactionCount" type="long">
                <annotation>
                    <documentation>počet již realizovaných transakcí pro celý řetězec trvalých příkazů do kterého patří daný předpis</documentation>
                </annotation>
            </element>
            <element name="lastRealizedTransactionDate" type="date" minOccurs="0">
                <annotation>
                    <documentation>datum (nejnovější) poslední realizované transakce z celého řetězce trvalých příkazů do kterého patří daný předpis</documentation>
                </annotation>
            </element>
            <element name="nextRequiredTransactionDate" type="date" minOccurs="0">
                <annotation>
                    <documentation>datum nejbližší následující předepsané transakce pro celý řetězec trvalých příkazů do kterého patří daný předpis</documentation>
                </annotation>
            </element>
            <element name="recommendedValidFromDate" type="date" minOccurs="0">
                <annotation>
                    <documentation>vypočítané datum "Opakovat od", které má být použito do nového předpisu při modifikaci trvalého příkazu</documentation>
                </annotation>
            </element>
            <element name="currentRepeatPeriodStatus" minOccurs="0">
                <annotation>
                    <documentation>status trvalého příkazu v momentě jeho modifikace, který bere v potaz datum otočky a stav poslední předepsané transakce</documentation>
                </annotation>
                <simpleType>
                    <restriction base="string">
                        <enumeration value="WAITING_FOR_REQUIRED_PAYMENT_DAY">
                            <annotation>
                                <documentation>datum otočky je v budoucnu a předchozí předepsané transakce jsou již realizovány</documentation>
                            </annotation>
                        </enumeration>
                        <enumeration value="REQUIRED_PAYMENT_DAY_IN_PROCESS">
                            <annotation>
                                <documentation>je právě datum otočky, přičemž předepsaná transakce ještě není zrealizována</documentation>
                            </annotation>
                        </enumeration>
                        <enumeration value="REQUIRED_PAYMENT_DAY_DONE">
                            <annotation>
                                <documentation>je právě datum otočky, přičemž předepsaná transakce již je zrealizována</documentation>
                            </annotation>
                        </enumeration>
                        <enumeration value="IN_PROCESS_AFTER_REQUIRED_PAYMENT_DAY">
                            <annotation>
                                <documentation>datum otočky je v budoucnu, ale předepsaná transakce z předchozí otočky ještě není zrealizována</documentation>
                            </annotation>
                        </enumeration>
                        <enumeration value="REQUIRED_PAYMENT_DAY_CANCELLED">
                            <annotation>
                                <documentation>je právě datum otočky, přičemž předepsaná transakce byla zrušena (relevantní pouze pro pozastavené TP)</documentation>
                            </annotation>
                        </enumeration>
                    </restriction>
                </simpleType>
            </element>
        </sequence>
    </complexType>

    <simpleType name="FilterPaymentAgenda">
        <annotation>
            <documentation>výčet typů agend na požadovaných platbách (PaymentOrder)</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="MYPAY">
                <annotation>
                    <documentation>platba mezi účty klienta</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INTLPAY">
                <annotation>
                    <documentation>zahraniční platby mimo evropský hospodářský prostor</documentation>
                </annotation>
            </enumeration>
            <enumeration value="EUPAY">
                <annotation>
                    <documentation>platby v rámci evropského hospodářského prostoru</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOMPAY">
                <annotation>
                    <documentation>platba mezi tuyemskými účty</documentation>
                </annotation>
            </enumeration>
            <enumeration value="STNDPAY">
                <annotation>
                    <documentation>trvalá platba</documentation>
                </annotation>
            </enumeration>
            <enumeration value="SAVEPAY">
                <annotation>
                    <documentation>pravidelné spoření</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INKPAY">
                <annotation>
                    <documentation>povolení k inkasu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="SIPOPAY">
                <annotation>
                    <documentation>SIPO</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="PartnerCode">
        <restriction base="string">
            <enumeration value="ROHLIK">
                <annotation>
                    <documentation>Kod Partnera Rohlik</documentation>
                </annotation>
            </enumeration>
            <enumeration value="SLEVOMAT">
                <annotation>
                    <documentation>Kod Partnera Slevomat</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="XamosErrorCode">
        <restriction base="string">
            <enumeration value="10_CERT_AUTHENTICATION_ERROR"/>
            <enumeration value="12_INVALID_FORMAT"/>
            <enumeration value="13_XID_DUPLICITY"/>
            <enumeration value="14_INVALID_SEAL"/>
            <enumeration value="15_UNEXPECTED_ERROR"/>
            <enumeration value="23_INVALID_DATA"/>
            <enumeration value="24_BANNED_DEBTOR_AGENT"/>
            <enumeration value="25_UNSUPPORTED_CREDITOR_AGENT"/>
            <enumeration value="26_BLACKLISTED_DEBTOR"/>
            <enumeration value="27_BLACKLISTED_CREDITOR"/>
            <enumeration value="28_A_LIMIT_EXCEEDED"/>
            <enumeration value="29_CREDITOR_AGENT_SCHEDULED_OUTAGE"/>
            <enumeration value="31_CREDITOR_AGENT_UNREACHABLE"/>
            <enumeration value="32_TKO_LIMIT_EXCEEDED"/>
            <enumeration value="40_CREDITOR_AGENT_TEMPORALLY_OFFLINE"/>
            <enumeration value="41_NONEXISTENT_CREDITOR_ACCOUNT"/>
            <enumeration value="43_ALL_CREDIT_DISABLED_ACCOUNT"/>
            <enumeration value="44_INSTANT_CREDIT_DISABLED_ACCOUNT"/>
            <enumeration value="45_CREDIT_REJECTED_LEGAL_REASON"/>
            <enumeration value="46_CREDIT_REJECTED_NO_REASON"/>
            <enumeration value="47_CREDIT_REJECTED_TECHNICAL_REASON"/>
            <enumeration value="48_INSTANT_PAYMENT_LIMIT_EXCEEDED"/>
            <enumeration value="60_RACE_CONDITION"/>
            <enumeration value="61_UNKNOWN_XID"/>
        </restriction>
    </simpleType>

    <simpleType name="XamosStatusCode">
        <restriction base="string">
            <enumeration value="IN_PROCESSING"/>
            <enumeration value="REJECTED"/>
            <enumeration value="RECEIVED"/>
            <enumeration value="IN_DELIVERY"/>
            <enumeration value="NEVER_EXISTED"/>
        </restriction>
    </simpleType>

    <complexType name="RealizedPayment">
        <sequence>
            <element name="transactionIdentification" type="long">
                <annotation>
                    <documentation>ID realizované transakce</documentation>
                </annotation>
            </element>
            <element name="dueDate" type="date" minOccurs="0">
                <annotation>
                    <documentation>datum splatnosti transakce</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

  <complexType name="getPaymentFeeDetailRqCommon" abstract="true">
    <annotation>
      <documentation>detail informaci o poplatcich za pozadovanou platbu - request</documentation>
    </annotation>
    <sequence>
      <element name="amountInRequiredCurrency" type="tns:AmountTO">
        <annotation>
          <documentation>castka transakce v mene pozadovane transakce (iso alpha3 code)</documentation>
        </annotation>
      </element>
      <element name="idBankAccountDebit" type="long">
        <annotation>
          <documentation>identifikace debetniho uctu platby</documentation>
        </annotation>
      </element>
    </sequence>
  </complexType>

  <complexType name="getPaymentFeeDetailRspCommon" abstract="true">
    <annotation>
      <documentation>rsp detail informaci o poplatcich za pozadovanou platbu - response</documentation>
    </annotation>
    <sequence>
      <element name="amountInRequiredCurrency" type="tns:AmountTO">
        <annotation>
          <documentation>castka transakce v mene pozadovane transakce (iso alpha3 code)</documentation>
        </annotation>
      </element>
      <element name="amountInAccountCurrency" type="tns:AmountTO">
        <annotation>
          <documentation>castka transakce v mene debetniho uctu (iso alpha3 code)</documentation>
        </annotation>
      </element>
      <element name="exchangeFeeInAccountCurrency" type="tns:AmountTO">
        <annotation>
          <documentation>poplatek za smenu v mene debetniho uctu (iso alpha3 code)</documentation>
        </annotation>
      </element>
      <element name="paymentFeeInAccountCurrency" type="tns:AmountTO">
        <annotation>
          <documentation>poplatek za platbu v mene debetniho uctu (iso alpha3 code)</documentation>
        </annotation>
      </element>
      <element name="totalDebitedAmountInAccountCurrency" type="tns:AmountTO">
        <annotation>
          <documentation>celkova prevedena castka v cetne poplatku v mene debetniho uctu (iso alpha3 code)</documentation>
        </annotation>
      </element>
    </sequence>
  </complexType>

    <complexType name="PocketMoneyPaymentTO">
        <sequence>
            <element name="nextPayment" type="date">
                <annotation>
                    <documentation>Datum příští platby kapesného.</documentation>
                </annotation>
            </element>
            <element name="amount" type="tns:AmountTO">
                <annotation>
                    <documentation>Částka příští platby kapesného.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="StructAddress">
        <sequence>
            <element name="name" type="string" minOccurs="0"/>
            <element name="streetName" type="string" minOccurs="0"/>
            <element name="buildingNumber" type="string" minOccurs="0"/>
            <element name="townName" type="string" minOccurs="0"/>
            <element name="postalCode" type="string" minOccurs="0"/>
            <element name="Country" type="string" minOccurs="0"/>
            <element name="CtrySubDvsn" type="string" minOccurs="0"/>
        </sequence>
    </complexType>

</schema>