<?xml version="1.0" encoding="UTF-8"?>
<schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" targetNamespace="http://arbes.com/ib/core/ppf/ws/common/"
    xmlns:tns="http://arbes.com/ib/core/ppf/ws/xsd/" xmlns="http://www.w3.org/2001/XMLSchema"
    elementFormDefault="qualified" version="1.0"
    xmlns:Q1="http://arbes.com/ib/core/ppf/ws/common/">

    <xsd:simpleType name="AuthResultType">
        <xsd:annotation>
            <xsd:documentation>Result of authorization</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
      <xsd:enumeration value="SUCCEEDED">
        <xsd:annotation>
          <xsd:documentation>authentication was okay</xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="FAILED">
        <xsd:annotation>
          <xsd:documentation>authentication was failed</xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="LOCKED">
        <xsd:annotation>
          <xsd:documentation>authorization type was locked</xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:simpleType name="LoginResult">
        <xsd:annotation>
            <xsd:documentation>Result of log on client</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="OK">
                <xsd:annotation>
                    <xsd:documentation>Přihlášení proběhlo úspešně</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NOK_ALREADY_SIGNED">
                <xsd:annotation>
                    <xsd:documentation>Uživatele nebylo možné přihlásit z důvodu, že RS již byla podepsána</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NOK_BAD_DOC_STATUS">
                <xsd:annotation>
                    <xsd:documentation>Uživatele nebylo možné přihlásit z důvodu nenalezení RS ve stavu k podpisu</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NOK_CHANNEL_BLOCKED">
                <xsd:annotation>
                    <xsd:documentation>Uživatele nebylo možné přihlásit z důvodu zablokovaného přístupu do IB</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NOK_NOT_WALKIN">
                <xsd:annotation>
                    <xsd:documentation>Uživatele nebylo možné přihlásit, protože se nejedná o Walkina</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NOK_USER_PWD_NOT_SET">
                <xsd:annotation>
                    <xsd:documentation>Uživatele nebylo možné přihlásit z důvodu zablokovaného přístupu do IB požadovaným kanálem</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:complexType name="SendAuthSMSType">
        <xsd:sequence>
            <xsd:element name="operationDesc" type="xsd:string">
                <xsd:annotation>
                    <xsd:documentation>unikátní řetězec identifikující operaci
                        (nejlépe serializovaná data z formuláře)</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="smsText" type="xsd:string" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>Text, který se bude posílat klientovi. Tento
                        text musí obsahovat zástupku #SMSCODE#, která je nahrazena
                        vygenerovaným SMS kódem.
                        Pokud není text vyplněn, bere se
                        standardní text uložený v DB.
                        Text SMS může obsahovat následující nepovinné zástupky, které před jejím odesláním budou zpracovány následovně:
                        #ANDROIDHASH# - tato zástupka bude nahrazena relevantní hodnotou z globálního parametru OBS pro dané prostředí (PROD/NONPROD)
                        #NEWLINE# - tato zástupka bude nahrazena znakem CHR(10) vloženým bezprostředně mezi předcházející a následující řetězec znaků
         </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>


    <complexType name="AuthType">
        <annotation>
            <documentation>Autorizační záznam, který se použije ve všech metodách
                vyžadující autorizaci.</documentation>
        </annotation>
        <sequence>
            <element name="password" type="xsd:base64Binary" minOccurs="0">
                <annotation>
                    <documentation>statické heslo - viz wiki https://wiki.apedie.abank.cz/pages/viewpage.action?pageId=*********</documentation>
                </annotation>
            </element>
            <element name="authCodeType" minOccurs="0">
                <annotation>
                    <documentation>
                        Typ autorizacniho kodu, rikajici o jaky typ se jedna SMS, callID
                    </documentation>
                </annotation>
                <simpleType>
                    <restriction base="xsd:string">
                        <!--hodnoty jsem dodal pouze ty co potrebuji a vychazel jsem z IB.-->
                        <!--Jelikoz se jedna o pouzivany complex type, jedna se o takovy kockopes, kdy cas atributu je na pevno a cast dynamicky.-->
                        <!--Pokud je potraba pridat novy typ autorizace, tak nevymyslet svou zkratku, ale pouzit stavajici, bohuzel ikdyz nedavaji smysl.-->
                        <!--Pavel Vit-->
                        <!-- Authentication by SMS and password -->
                        <enumeration value="PWD_OTP"/>
                        <!-- Authentication by SMS -->
                        <enumeration value="OTP"/>
                        <!-- Authentication password in IBS prolonged mode -->
                        <enumeration value="PWD"/>
                        <!-- Authentication by security questions -->
                        <enumeration value="QUESTION"/>
                        <!-- No authentication required -->
                        <enumeration value="NO_AUTH"/>
                        <!-- Authentication by e-sign -->
                        <enumeration value="SIGNPAD"/>
                        <!-- Authentication by general sign-->
                        <enumeration value="BLUE_SIGN"/>
                        <!-- Customer login authentication - special case -->
                        <enumeration value="PWD_PPF"/>
                        <!-- Authentication by customer agree and operator authentication -->
                        <enumeration value="CALL_ID"/>
                        <!-- Authentication by new password and password -->
                        <enumeration value="PWDCMD"/>
                        <enumeration value="PWD_SPB"/>
                    </restriction>
                </simpleType>
            </element>
            <element name="authCode" type="xsd:string" minOccurs="0">
                <annotation>
                    <documentation>
                        Jednorázový kód (Digipass, SMS, idHovoru, atd. )
                    </documentation>
                </annotation>
            </element>
            <element name="operationDesc" type="xsd:string" minOccurs="0">
                <annotation>
                    <documentation>
                        unikátní řetězec identifikující operaci (nejlépe
                        serializovaná data z formuláře) Používá se ve
                        spojení s autorizací pomocí SMS
                    </documentation>
                </annotation>
            </element>
            <element name="authId" type="xsd:string" minOccurs="0">
                <annotation>
                    <documentation>id of external authorization</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="LoginAuthType">
        <annotation>
            <documentation>autentizační záznam pro login</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:AuthType">
                <sequence>
                    <element name="userName" type="xsd:string">
                        <annotation>
                            <documentation>
                                uživatelské jméno
                            </documentation>
                        </annotation>
                    </element>
                    <element name="parameter" type="xsd:string" minOccurs="0">
                        <annotation>
                            <documentation>
                                třetí autentizační parametr (datum
                                narození) -- datum bude zadáno ve
                                fromátu yyyy-mmm-dd
                            </documentation>
                        </annotation>
                    </element>
                    <element name="ipAddress" type="xsd:string" minOccurs="0">
                        <annotation>
                            <documentation>ip adresa prihlasovaneho uzivatele</documentation>
                        </annotation></element>
                    <element name="eventId" type="xsd:string" minOccurs="0">
                      <annotation>
                            <documentation>frontend event id</documentation>
                      </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="SendAuthSMSResultType">
        <annotation>
            <documentation>

            </documentation>
        </annotation>
        <sequence>
            <element name="nextSMSinSecond" type="xsd:int">
                <annotation>
                    <documentation>
                        Za kolik sekund lze poslat další SMS. Možné návratové
                        hodnoty:
                        X = -1 - sms nelze poslat.
                        X = 0 - sms lze poslat
                        X &gt; 0 - sms lze poslat až za X sekund
            </documentation>
                </annotation>
            </element>
            <element name="result">
                <annotation>
                    <documentation>SEND - sms byla poslána
                        NOT_SEND - sms nebyla odeslána</documentation>
                </annotation>
                <simpleType>
                    <restriction base="xsd:string">
                        <enumeration value="SEND"/>
                        <enumeration value="NOT_SEND"/>
                    </restriction>
                </simpleType>
            </element>
        </sequence>
    </complexType>

    <simpleType name="AuthChannelType">
        <annotation>
            <documentation>typ kanálu pro podpis smluvních dokumentů</documentation>
        </annotation>
        <restriction base="xsd:string">
            <enumeration value="IB">
                <annotation>
                    <documentation>internet banking</documentation>
                </annotation></enumeration>
            <enumeration value="ICC">
                <annotation>
                    <documentation>interní call centrum</documentation>
                </annotation></enumeration>
            <enumeration value="BRANCH">
                <annotation>
                    <documentation>pobočka</documentation>
                </annotation>
			</enumeration>
            <enumeration value="MAIL">
                <annotation>
                    <documentation>pošta</documentation>
                </annotation>
			</enumeration>
            <enumeration value="MESSENGER">
                <annotation>
                    <documentation>kurýr</documentation>
                </annotation>
			</enumeration>
        </restriction>
    </simpleType>

    <complexType name="AccessStatusType">
        <annotation>
            <documentation>Stavy přístupů do banky pro klienta na vstupu</documentation>
        </annotation>
        <sequence>
            <element name="accessChannel">
                <annotation>
                    <documentation>Přístup</documentation>
                </annotation>
                <simpleType>
                    <restriction base="xsd:string">
                        <enumeration value="IB"/>
                        <enumeration value="MA"/>
                        <enumeration value="ICC"/>
                        <enumeration value="BRANCH"/>
                        <enumeration value="OPENAPI"/>
                    </restriction>
                </simpleType>
            </element>
            <element name="status">
                <annotation>
                    <documentation>Stav přístupu.
                        OK se vrací se i pro osoby, které se do banky vůbec přihlásit nemohou. Vrácení OK pro daný způsob přístupu tedy spíše znamená, že přístup není blokován.
                    </documentation>
                </annotation>
                <simpleType>
                    <restriction base="xsd:string">
                        <enumeration value="OK"/>
                        <enumeration value="BLOCKED"/>
                    </restriction>
                </simpleType>
            </element>
            <element name="blockingBusinessProcess" minOccurs="0">
                <annotation>
                    <documentation>Pokud je status BLOCKED vrací i obchodní proces, který blokaci provedl.</documentation>
                </annotation>
                <simpleType>
                    <restriction base="xsd:string">
                        <enumeration value="ANTIPHISHING"/>
                    </restriction>
                </simpleType>
            </element>
        </sequence>
    </complexType>

</schema>
