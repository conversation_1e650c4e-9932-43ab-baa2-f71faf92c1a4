<?xml version="1.0" encoding="UTF-8"?>
<schema xmlns="http://www.w3.org/2001/XMLSchema"
        xmlns:cp="http://airbank.cz/obs/ws/counterparty"
        targetNamespace="http://airbank.cz/obs/ws/common/trustedAccountsWS">

    <import schemaLocation="counterparty.xsd" namespace="http://airbank.cz/obs/ws/counterparty" />

    <simpleType name="AccountTrustLevel">
        <annotation>
            <documentation>trust level of account</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="OWN">
                <annotation>
                    <documentation>Owner or disponent of account</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CUSTOM_WHITELISTED">
                <annotation>
                    <documentation>Account is on the custom whitelist</documentation>
                </annotation>
            </enumeration>
            <enumeration value="UNKNOWN">
                <annotation>
                    <documentation>Unknown account number</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <complexType name="WhiteListItemTO">
        <annotation>
            <documentation>detail of whitelist item</documentation>
        </annotation>
        <sequence>
            <element name="id" type="long">
                <annotation>
                    <documentation>identifiers of whitelist items</documentation>
                </annotation>
            </element>
            <element name="bankAccount" type="cp:CounterpartyAccount">
                <annotation>
                    <documentation>Whitelisted bank account</documentation>
                </annotation>
            </element>
            <element name="creationDate" type="dateTime">
                <annotation>
                    <documentation>creation date of record in white list</documentation>
                </annotation>
            </element>
            <element name="idRequiredTransaction" type="long">
                <annotation>
                    <documentation>id required transaction which created record in white list</documentation>
                </annotation>
            </element>
            <element name="idCounterparty" type="long" minOccurs="0" />
        </sequence>
    </complexType>
</schema>
