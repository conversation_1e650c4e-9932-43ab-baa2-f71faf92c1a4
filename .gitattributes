# Handle line endings automatically for files detected as text
# and leave all files detected as binary untouched.
* text=auto

#
# The above will handle all files NOT found below
#
# These files are text and should be normalized (Convert crlf => lf)
*.md            text
*.adoc          text
*.textile       text
*.mustache      text
*.csv           text
*.tab           text
*.tsv           text
*.css           text
*.df            text
*.htm           text
*.html          text
*.java          text
*.js            text
*.json          text
*.jsp           text
*.jspf          text
*.properties    text
*.sh            text
*.sql           text
*.svg           text
*.tld           text
*.txt           text
*.xml           text
*.sql           text
*.xsd           text
*.wsdl          text

# Shell scripts needs always LF, never perform conversion between crlf and lf
*.sh		-text
commit-msg	-text


# These files are binary and should be left untouched
# (binary is a macro for -text -diff)
*.class         binary
*.dll           binary
*.ear           binary
*.gif           binary
*.ico           binary
*.jar           binary
*.jpg           binary
*.jpeg          binary
*.png           binary
*.so            binary
*.war           binary

# Documents
*.doc	 diff=astextplain
*.DOC	 diff=astextplain
*.docx diff=astextplain
*.DOCX diff=astextplain
*.dot  diff=astextplain
*.DOT  diff=astextplain
*.pdf  diff=astextplain
*.PDF	 diff=astextplain
*.rtf	 diff=astextplain
*.RTF	 diff=astextplain
*.md text
*.adoc text
*.textile text
*.mustache text
*.csv text
*.tab text

# Graphics
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.svg text

# $Id:$ expansion
*.xsd ident
*.wsdl ident

