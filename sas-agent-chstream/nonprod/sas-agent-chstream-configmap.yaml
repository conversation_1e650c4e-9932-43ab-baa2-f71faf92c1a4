apiVersion: v1
kind: ConfigMap
metadata:
  name: sas-agent-chstream-configmap
data:
  kafka.ab.topics.sentCampaignEmail: nonprod.cz.airbank.sas.campaign.email.sent.v1
  kafka.ab.topics.sendPromoPushEvent: nonprod.cz.airbank.sas.campaign.push.send.v1
  kafka.ab.topics.sendMaPromoAd: nonprod.cz.airbank.sas.campaign.ma.promoad.send.v3
  kafka.ab.topics.sendIbPromoAd: nonprod.cz.airbank.sas.campaign.ib.promoad.send.v3
  kafka.ab.topics.storeCampaignPlannedCall: nonprod.cz.airbank.sas.campaign.planned.call.v1
  airbank.kafka.topics.ma-promo-ad: nonprod.cz.airbank.sas.campaign.ma.promoad.send.v3
  airbank.kafka.topics.ib-promo-ad: nonprod.cz.airbank.sas.campaign.ib.promoad.send.v3
  airbank.kafka.topics.campaign-planned-call: nonprod.cz.airbank.sas.campaign.planned.call.v1
  spring.datasource.url: ************************************************************
