apiVersion: v1
kind: ConfigMap
metadata:
  name: sas-agent-chstream-configmap
data:
  kafka.ab.topics.sentCampaignEmail: perf.cz.airbank.sas.campaign.email.sent.v1
  kafka.ab.topics.sendPromoPushEvent: perf.cz.airbank.sas.campaign.push.send.v1
  kafka.ab.topics.sendMaPromoAd: perf.cz.airbank.sas.campaign.ma.promoad.send.v2
  kafka.ab.topics.sendIbPromoAd: perf.cz.airbank.sas.campaign.ib.promoad.send.v2
  kafka.ab.topics.storeCampaignPlannedCall: perf.cz.airbank.sas.campaign.planned.call.v1
  airbank.kafka.topics.ma-promo-ad: perf.cz.airbank.sas.campaign.ma.promoad.send.v2
  airbank.kafka.topics.ib-promo-ad: perf.cz.airbank.sas.campaign.ib.promoad.send.v2
  airbank.kafka.topics.campaign-planned-call: perf.cz.airbank.sas.campaign.planned.call.v1
  spring.datasource.url: ********************************************************
