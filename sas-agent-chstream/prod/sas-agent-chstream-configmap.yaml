apiVersion: v1
kind: ConfigMap
metadata:
  name: sas-agent-chstream-configmap
data:
  jvm.xmx: 384m
  kafka.ab.topics.sentCampaignEmail: prod.cz.airbank.sas.campaign.email.sent.v1
  kafka.ab.topics.sendPromoPushEvent: prod.cz.airbank.sas.campaign.push.send.v1
  kafka.ab.topics.sendMaPromoAd: prod.cz.airbank.sas.campaign.ma.promoad.send.v3
  kafka.ab.topics.sendIbPromoAd: prod.cz.airbank.sas.campaign.ib.promoad.send.v3
  kafka.ab.topics.storeCampaignPlannedCall: prod.cz.airbank.sas.campaign.planned.call.v1
  airbank.kafka.topics.ma-promo-ad: prod.cz.airbank.sas.campaign.ma.promoad.send.v3
  airbank.kafka.topics.ib-promo-ad: prod.cz.airbank.sas.campaign.ib.promoad.send.v3
  airbank.kafka.topics.campaign-planned-call: prod.cz.airbank.sas.campaign.planned.call.v1
  spring.datasource.url: **********************************************************
  airbank.ci360.proxy.host: proxyauth.banka.hci
  airbank.ci360.proxy.port: '3128'
  airbank.ci360.gateway: 'extapigwservice-eu-prod.ci360.sas.com'
  airbank.ci360.url: 'https://extapigwservice-eu-prod.ci360.sas.com/'
  airbank.ci360.proxy.non-proxy-hosts: 'app01-pdcab1sasoracle.ab.prod'
