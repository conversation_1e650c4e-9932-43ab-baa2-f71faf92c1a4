topics:
  - name: cz.cvak.idport.audit.v1
    description:
      brief: "Topic for IdPort audit log events"
      url: "https://wiki.airbank.cz/display/KPA/XR-11787+%28cvak%29+v1.1+-+Onboarding+Merchanta"
    partitions: 10
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: unknown
    schema:
      - name: Schema for IdPort log events
        artifactId: cz.monetplus.mep.messaging.api.avro.v2_1_2.AvroStringParam
        groupId: default
        description: Schema of all idport audit events - avro string param
        version: 1
        schemaRef: schema/idport-audit-log-event-avro-string-param-schema_2.1.2.avsc
      - name: Schema for IdPort log events
        artifactId: cz.monetplus.mep.messaging.api.avro.v2_1_2.GenericMessage
        groupId: default
        description: Schema of all idport audit events - generic message
        version: 1
        schemaRef: schema/idport-audit-log-event-generic-message-schema_2.1.2.avsc
    acl:
      read:
        - principal: "User:CVAK_MAS_CVAK_KAFKA_USER"
          name: mas-cvak
          group: mas-cvak
          generateDlt: true
          description:
            brief: "read idport audit log event"
        - principal: "User:CVAK_PAM_KAFKA_USER"
          name: pam
          group: pam
          generateDlt: true
          description:
            brief: "read idport audit log event"
        - principal: "User:CVAK_PCM_KAFKA_USER"
          name: pcm
          group: pcm
          generateDlt: true
          description:
            brief: "read idport audit log event"
        - principal: "User:CVAK_IDPORT_KAFKA_USER"
          name: idport
          group: idport
          generateDlt: true
          description:
            brief: "read idport audit log event"
      write:
        - principal: "User:CVAK_IDPORT_KAFKA_USER"
          name: idport
          description:
            brief: "write idport audit log event"
  - name: cz.cvak.idport.revoke.v1
    description:
      brief: "Topic for IdPort revoke log events"
      url: "https://jira.abank.cz/browse/PAY-1205"
    partitions: 10
    replicationFactor: 3
    config:
      "retention.ms": "*********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: unknown
    schema:
      - name: Schema for IdPort revoke events
        artifactId: cz.monetplus.mep.messaging.api.avro.v2_1_2.AvroStringParam
        groupId: default
        description: Schema of all idport revoke events - avro string param
        version: 1
        schemaRef: schema/idport-revoke-log-event-avro-string-param-schema_2.1.2.avsc
      - name: Schema for IdPort revoke events
        artifactId: cz.monetplus.mep.messaging.api.avro.v2_1_2.GenericMessage
        groupId: default
        description: Schema of all idport revoke events - generic message
        version: 1
        schemaRef: schema/idport-revoke-log-event-generic-message-schema_2.1.2.avsc
    acl:
      read:
        - principal: "User:CVAK_IDPORT_KAFKA_USER"
          name: idport
          group: idport
          generateDlt: true
          description:
            brief: "read idport revoke log event"
      write:
        - principal: "User:CVAK_IDPORT_KAFKA_USER"
          name: idport
          description:
            brief: "write idport revoke log event"