{"type": "record", "name": "CvakTransactionNotificationEvent", "namespace": "cz.cvak.transactions.notification.v2", "fields": [{"name": "transactionId", "type": "string"}, {"name": "merchantId", "type": "long"}, {"name": "transactionResultType", "type": {"type": "enum", "name": "TransactionResultType", "symbols": ["FINISHED", "CANCELLED"]}}, {"name": "realizedOrderResultCode", "type": ["null", {"type": "enum", "name": "RealizedOrderResultCode", "symbols": ["ALREADY_CANCELLED", "ALREADY_FINISHED", "CANCELLED_FINISHED", "DIFFERENT_ACCOUNT", "ERROR", "ERROR_FINISHED", "EXPIRED_FINISHED", "FINISHED", "NOT_FOUND"]}], "default": null}]}