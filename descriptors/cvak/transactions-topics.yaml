topics:
  - name: cz.cvak.transactions.notification.v1
    description:
      brief: "Topic for cvak transaction notification events"
      url: "https://wiki.airbank.cz/display/KPA/XR-11239+Cvak+-+API+modul+pro+retail#XR-11239Cvak-APImodulproretail-Paymentresultnotification"
    partitions: 10
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: string
    schema:
      - name: Schema for cvak transaction notification event
        artifactId: cz.cvak.transactions.notification.CvakTransactionNotificationEvent
        groupId: default
        description: Schema for cvak transaction notification event
        version: 1
        schemaRef: schema/transactions-notification-event.avsc
    acl:
      read:
        - principal: "User:CVAK_PARTNER_API_KAFKA_USER"
          name: partner-api
          group: partner-api
          generateDlt: true
          description:
            brief: "read transaction notification event"
      write:
        - principal: "User:CVAK_TRANSACTIONS_KAFKA_USER"
          name: transactions
          description:
            brief: "write transaction notification event"
  - name: cz.cvak.transactions.notification.v2
    description:
      brief: "Topic for cvak transaction notification events"
      url: "https://wiki.airbank.cz/display/SA/finishTransactionNotification+topic"
    partitions: 10
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: string
    schema:
      - name: Schema for cvak transaction notification event
        artifactId: cz.cvak.transactions.notification.v2.CvakTransactionNotificationEvent
        groupId: default
        description: Schema for cvak transaction notification event
        version: 1
        schemaRef: schema/transactions-notification-event-v2.avsc
    acl:
      read:
        - principal: "User:CVAK_PARTNER_API_KAFKA_USER"
          name: partner-api
          group: partner-api
          generateDlt: true
          description:
            brief: "read transaction notification event"
      write:
        - principal: "User:CVAK_TRANSACTIONS_KAFKA_USER"
          name: transactions
          description:
            brief: "write transaction notification event"
  - name: cz.cvak.transactions.notification.v2-retry-cvak_partner_api_kafka_user
    description:
      brief: "Retry topic for Topic for cz.cvak.transactions.notification.v2"
      url: "https://wiki.airbank.cz/display/KPA/XR-11239+Cvak+-+API+modul+pro+retail#XR-11239Cvak-APImodulproretail-Paymentresultnotification"
    partitions: 1
    replicationFactor: 3
    config:
      "retention.ms": "********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: string
    schema:
      - name: Schema for cvak transaction notification event
        artifactId: cz.cvak.transactions.notification.v2.CvakTransactionNotificationEvent
        groupId: default
        description: Schema for cvak transaction notification event
        version: 1
        schemaRef: schema/transactions-notification-event-v2.avsc
    acl:
      read:
        - principal: "User:CVAK_PARTNER_API_KAFKA_USER"
          name: partner-api
          group: partner-api
          generateDlt: false
          description:
            brief: "read event from retry topic"
      write:
        - principal: "User:CVAK_PARTNER_API_KAFKA_USER"
          name: partner-api
          description:
            brief: "write event to retry topic"
  - name: cz.cvak.transactions.notification.v3
    description:
      brief: "Topic for cvak transaction notification events"
      url: "https://wiki.airbank.cz/display/SA/finishTransactionNotification+topic"
    partitions: 6
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: string
    schema:
      - name: Schema for cvak transaction notification event
        artifactId: cz.cvak.transactions.notification.v3.CvakTransactionNotificationEvent
        groupId: default
        description: Schema for cvak transaction notification event
        version: 1
        schemaRef: schema/transactions-notification-event-v3.avsc
    acl:
      read:
        - principal: "User:CVAK_PARTNER_API_KAFKA_USER"
          name: partner-api
          group: partner-api
          generateDlt: true
          description:
            brief: "read transaction notification event"
      write:
        - principal: "User:CVAK_TRANSACTIONS_KAFKA_USER"
          name: transactions
          description:
            brief: "write transaction notification event"
  - name: cz.cvak.transactions.notification.v3-retry-cvak_partner_api_kafka_user
    description:
      brief: "Retry topic for Topic for cz.cvak.transactions.notification.v3"
      url: "https://wiki.airbank.cz/display/KPA/XR-11239+Cvak+-+API+modul+pro+retail#XR-11239Cvak-APImodulproretail-Paymentresultnotification"
    partitions: 1
    replicationFactor: 3
    config:
      "retention.ms": "********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: string
    schema:
      - name: Schema for cvak transaction notification event
        artifactId: cz.cvak.transactions.notification.v3.CvakTransactionNotificationEvent
        groupId: default
        description: Schema for cvak transaction notification event
        version: 1
        schemaRef: schema/transactions-notification-event-v3.avsc
    acl:
      read:
        - principal: "User:CVAK_PARTNER_API_KAFKA_USER"
          name: partner-api
          group: partner-api
          generateDlt: false
          description:
            brief: "read event from retry topic"
      write:
        - principal: "User:CVAK_PARTNER_API_KAFKA_USER"
          name: partner-api
          description:
            brief: "write event to retry topic"