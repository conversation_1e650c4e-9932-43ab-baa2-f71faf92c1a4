topics:
  - name: cz.airbank.o2.proxy.registration.airbankclientunitymemberpartyremoved.v1
    description:
      brief: "Topic s propagující zprávy MemberPartyRemoved z Unity Core do AB"
      url: "https://wiki.airbank.cz/display/SA/AirbankClientUnityMemberPartyRemoved"
    partitions: 6
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: unknown
    schema:
      - name: AirbankClientUnityMemberPartyRemovedEvent
        artifactId: cz.airbank.o2.proxy.registration.AirbankClientUnityMemberPartyRemovedEvent
        groupId: default
        description: Schema for AirBank client Unity member party removed events
        version: 1
        schemaRef: schemas/airbankclient-unitymemberparty-removed-event.avsc
    acl:
      read:
        - principal: "User:SAS_KAFKA_USER"
          name: sas
          group: sas-agent-kafka
          generateDlt: true
          description:
            brief: "p<PERSON><PERSON><PERSON><PERSON><PERSON> HIGH-IDENT události pro zpracovani s SAS360"
      write:
        - principal: "User:O2PROXY_KAFKA_USER"
          name: o2-proxy
          description:
            brief: "o2-proxy writes AirbankClientUnityMemberPartyRemoved event"

  - name: cz.airbank.o2.proxy.registration.airbankclientunitymemberpartydeactivated.v1
    description:
      brief: "Topic s propagující zprávy MemberPartyDeactivated z Unity Core do AB"
      url: "https://wiki.airbank.cz/pages/viewpage.action?spaceKey=SA&title=AirbankClientUnityMemberPartyDeactivated"
    partitions: 6
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: unknown
    schema:
      - name: AirbankClientUnityMemberPartyDeactivatedEvent
        artifactId: cz.airbank.o2.proxy.registration.AirbankClientUnityMemberPartyDeactivatedEvent
        groupId: default
        description: Schema for AirBank client Unity member party deactivated events
        version: 1
        schemaRef: schemas/airbankclient-unitymemberparty-deactivated-event.avsc
    acl:
      read:
        - principal: "User:SAS_KAFKA_USER"
          name: sas
          group: sas-agent-kafka
          generateDlt: true
          description:
            brief: "příchozí HIGH-IDENT události pro zpracovani s SAS360"
      write:
        - principal: "User:O2PROXY_KAFKA_USER"
          name: o2-proxy
          description:
            brief: "o2-proxy writes AirbankClientUnityMemberPartyDeactivated event"

  - name: cz.airbank.o2.proxy.registration.airbankclientunitymemberdeactivated.v1
    description:
      brief: "Topic s propagující zprávy MemberDeactivated z Unity Core do AB"
      url: "https://wiki.airbank.cz/pages/viewpage.action?spaceKey=SA&title=AirbankClientUnityMemberDeactivated"
    partitions: 6
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: unknown
    schema:
      - name: AirbankClientUnityMemberDeactivatedEvent
        artifactId: cz.airbank.o2.proxy.registration.AirbankClientUnityMemberDeactivatedEvent
        groupId: default
        description: Schema for AirBank client Unity member deactivated events
        version: 1
        schemaRef: schemas/airbankclient-unitymember-deactivated-event.avsc
    acl:
      read:
        - principal: "User:SAS_KAFKA_USER"
          name: sas
          group: sas-agent-kafka
          generateDlt: true
          description:
            brief: "příchozí HIGH-IDENT události pro zpracovani s SAS360"
      write:
        - principal: "User:O2PROXY_KAFKA_USER"
          name: o2-proxy
          description:
            brief: "o2-proxy writes AirbankClientUnityMemberDeactivated event"
