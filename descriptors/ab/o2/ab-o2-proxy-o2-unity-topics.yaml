topics:
  - name: cz.unity-core.o2-proxy.to.unity-core
    description:
      brief: "dedi<PERSON><PERSON><PERSON><PERSON> topics pro partnery – INBOUND (z pohledu Unity Core). airbank.inbound"
      url: "https://wiki.airbank.cz/pages/viewpage.action?pageId=*********#link-talk-60028"
    partitions: 50
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "min.insync.replicas": "2"
    keyType: unknown
    acl:
      read:
        - principal: "User:BRIDGE_KAFKA_USER"
          name: kafka-bridge
          group: unity-airbank
          generateDlt: false
          description:
            brief: "kafka bridge from ab to unity - od<PERSON><PERSON><PERSON> ud<PERSON>"
      write:
        - principal: "User:O2PROXY_KAFKA_USER"
          name: o2-proxy
          description:
            brief: "od<PERSON><PERSON><PERSON> ud<PERSON>"
  - name: cz.unity-core.o2-proxy.from.unity-core
    description:
      brief: "dedikovaných Kafka topics pro partnery – OUTBOUND (z pohledu Unity Core). airbank.outbound"
      url: "https://wiki.airbank.cz/pages/viewpage.action?pageId=*********#link-talk-60028"
    partitions: 50
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "min.insync.replicas": "2"
    keyType: unknown
    acl:
      read:
        - principal: "User:O2PROXY_KAFKA_USER"
          name: o2-proxy
          group: o2-proxy
          generateDlt: false
          description:
            brief: "příchozí události"
      write:
        - principal: "User:BRIDGE_KAFKA_USER"
          name: kafka-bridge
          description:
            brief: "kafka bridge from unity to ab - příchozí události"
  - name: cz.unity-core.o2-proxy.from.unity-core-dlt
    description:
      brief: "dedikovaných Kafka topics pro partnery – OUTBOUND (z pohledu Unity Core). airbank.outbound-dlt (DeadLetterTopic)"
      url: "https://wiki.airbank.cz/pages/viewpage.action?pageId=*********#link-talk-60028"
    partitions: 50
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "min.insync.replicas": "2"
    keyType: unknown
    acl:
      read:
        - principal: "User:BRIDGE_KAFKA_USER"
          name: kafka-bridge
          group: unity-airbank
          generateDlt: false
          description:
            brief: "kafka bridge from unity to ab - odchozí dlt události"
      write:
        - principal: "User:O2PROXY_KAFKA_USER"
          name: o2-proxy
          description:
            brief: "odchozí dlt události"
  - name: cz.unity-core.high-broadcast.from.unity-core
    description:
      brief: "broadcastový topic, ze kterého jsou eventy vyčítány partnery s ohledem na rozdělění event dle sensitivity obsahu (LOW-IDENT a HIGH-IDENT topic). high.outbound"
      url: "https://wiki.airbank.cz/pages/viewpage.action?pageId=*********#link-talk-60028"
    partitions: 50
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "min.insync.replicas": "2"
    keyType: unknown
    acl:
      read:
        - principal: "User:O2PROXY_KAFKA_USER"
          name: o2-proxy
          group: o2-proxy
          generateDlt: false
          description:
            brief: "příchozí HIGH-IDENT události"
        - principal: "User:AIRFLOW_KAFKA_USER"
          name: dwh
          group: dwh-airflow
          generateDlt: false
          description:
            brief: "příchozí HIGH-IDENT události pro zpracování na dwh"
      write:
        - principal: "User:BRIDGE_KAFKA_USER"
          name: kafka-bridge
          description:
            brief: "kafka bridge from unity-core to ab - příchozí broadcast události"
  - name: cz.unity-core.high-broadcast.from.unity-core-dlt
    description:
      brief: "broadcastový topic, ze ktereho jsou eventy vyčítány partnery s ohledem na rozdělění event dle sensitivity obsahu (LOW-IDENT a HIGH-IDENT topic). high.outbound-dlt (DeadLetterTopic)"
      url: "https://wiki.airbank.cz/pages/viewpage.action?pageId=*********#link-talk-60028"
    partitions: 50
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "min.insync.replicas": "2"
    keyType: unknown
    acl:
      read:
        - principal: "User:BRIDGE_KAFKA_USER"
          name: kafka-bridge
          group: unity-airbank
          generateDlt: false
          description:
            brief: "odchozí broadcast dlt události"
      write:
        - principal: "User:O2PROXY_KAFKA_USER"
          name: o2-proxy
          description:
            brief: "odchozí broadcast dlt události"
  - name: cz.unity-core.proposition.from.unity-core
    description:
      brief: "proposition topic, do které unity eviduje informace o plnění podmínek za stranu o2"
      url: "https://wiki.airbank.cz/pages/viewpage.action?pageId=*********"
    partitions: 50
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "min.insync.replicas": "2"
    keyType: unknown
    acl:
      read:
        - principal: "User:O2PROXY_KAFKA_USER"
          name: o2-proxy
          group: o2-proxy
          generateDlt: false
          description:
            brief: "příchozí proposition události"
        - principal: "User:AIRFLOW_KAFKA_USER"
          name: dwh
          group: dwh-airflow
          generateDlt: false
          description:
            brief: "příchozí proposition události pro zpracování na dwh"
      write:
        - principal: "User:BRIDGE_KAFKA_USER"
          name: kafka-bridge
          description:
            brief: "kafka bridge from unity-core to ab - příchozí broadcast události"
  - name: cz.unity-core.proposition.from.unity-core-dlt
    description:
      brief: "Proposition topic, kam padaji pripadne zpravy nezpracovane z proposition v o2-proxy"
      url: "https://wiki.airbank.cz/pages/viewpage.action?pageId=*********"
    partitions: 50
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "min.insync.replicas": "2"
    keyType: unknown
    acl:
      read:
        - principal: "User:BRIDGE_KAFKA_USER"
          name: kafka-bridge
          group: unity-airbank
          generateDlt: false
          description:
            brief: "odchozí proposition dlt události pro prenos to unity"
      write:
        - principal: "User:O2PROXY_KAFKA_USER"
          name: o2-proxy
          description:
            brief: "odchozí proposition dlt události"
  - name: cz.unity-core.o2-proxy.to.proposition
    description:
      brief: "dedikovaný kafka topics pro vyhodnecení benefitů za AB"
      url: "https://wiki.airbank.cz/pages/viewpage.action?pageId=*********"
    partitions: 50
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "min.insync.replicas": "2"
    keyType: unknown
    acl:
      read:
        - principal: "User:BRIDGE_KAFKA_USER"
          name: kafka-bridge
          group: unity-airbank
          generateDlt: false
          description:
            brief: "odchozí proposition žádosti za AB"
      write:
        - principal: "User:O2PROXY_KAFKA_USER"
          name: o2-proxy
          description:
            brief: "odchozí proposition události pro přenos do unity"
  - name: cz.unity-core.high-broadcast-proposition.from.unity-core
    description:
      brief: "dedikovaný kafka topics pro vyhodnecení benefitů pro všechny partnery"
      url: "https://wiki.airbank.cz/display/SA/Unity+Core+-+Unity+Common+Propositions+Outbound+HIGH-IDENT"
    partitions: 50
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "min.insync.replicas": "2"
    keyType: unknown
    acl:
      read:
        - principal: "User:O2PROXY_KAFKA_USER"
          name: o2-proxy
          group: o2-proxy
          generateDlt: false
          description:
            brief: "příchozí HIGH-IDENT proposition události"
        - principal: "User:AIRFLOW_KAFKA_USER"
          name: dwh
          group: dwh-airflow
          generateDlt: false
          description:
            brief: "příchozí HIGH-IDENT proposition události pro zpracování na dwh"
      write:
        - principal: "User:BRIDGE_KAFKA_USER"
          name: kafka-bridge
          description:
            brief: "kafka bridge from unity-core to ab - příchozí broadcast proposition události"
  - name: cz.unity-core.high-broadcast-proposition.from.unity-core-dlt
    description:
      brief: "dedikovaný kafka topics pro vyhodnecení benefitů pro všechny partnery a sem padaji vschny zpravy co nedokazeme z o2/unity zpraovat"
      url: "https://wiki.airbank.cz/display/SA/Unity+Core+-+Unity+Common+Propositions+Outbound+HIGH-IDENT"
    partitions: 50
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "min.insync.replicas": "2"
    keyType: unknown
    acl:
      read:
        - principal: "User:BRIDGE_KAFKA_USER"
          name: kafka-bridge
          group: unity-airbank
          generateDlt: false
          description:
            brief: "kafka bridge from unity to ab - odchozí dlt události"
      write:
        - principal: "User:O2PROXY_KAFKA_USER"
          name: o2-proxy
          description:
            brief: "odchozí dlt události"
