topics:
  - name: cz.airbank.o2.proxy.unity.benefit.entitlement.obtained.v1
    description:
      brief: "<PERSON>ic s propaguj<PERSON><PERSON><PERSON> zprávy UnityBenefitEntitlementObtained z o2-proxy, ukaz<PERSON><PERSON><PERSON><PERSON><PERSON>, že klient obdr<PERSON><PERSON> nárok na výplatu odměny."
      url: "https://wiki.airbank.cz/pages/viewpage.action?spaceKey=SA&title=O2Proxy+-+UnityBenefitEntitlementObtained"
    partitions: 6
    replicationFactor: 3
    config:
      # ********** ms == 30 days
      "retention.ms": "**********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: unknown
    schema:
      - name: UnityBenefitEntitlementObtainedEvent
        artifactId: cz.airbank.o2.proxy.unity.benefit.UnityBenefitEntitlementObtainedEvent
        groupId: default
        description: Schema for Unity BenefitEntitlementObtain events
        version: 1
        schemaRef: schemas/unity-benefit-entitlement-obtained-event.avsc
    acl:
      read:
        - principal: "User:OBS_KAFKA_USER"
          name: obs
          group: obs
          generateDlt: true
          description:
            brief: "OBS rozhoduje a vyplácí odměny na základě zpráv z tohoto topicu."
      write:
        - principal: "User:O2PROXY_KAFKA_USER"
          name: o2-proxy
          description:
            brief: "o2-proxy zapisuje cz.airbank.o2.backend.db.domain.BenefitEntitlement (UnityBenefitEntitlementObtained) zprávy na základě unity.PropositionBenefitGranted nebo jiných podmínek"

  - name: cz.airbank.o2.proxy.unity.benefit.entitlement.lost.v1
    description:
      brief: "Topic s propagující zprávy UnityBenefitEntitlementLost z o2-proxy, ukazující, že klient utratil nárok na výplatu odměny."
      url: "https://wiki.airbank.cz/pages/viewpage.action?spaceKey=SA&title=O2Proxy+-+UnityBenefitEntitlementLost"
    partitions: 6
    replicationFactor: 3
    config:
      # ********** ms == 30 days
      "retention.ms": "**********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: unknown
    schema:
      - name: UnityBenefitEntitlementLostEvent
        artifactId: cz.airbank.o2.proxy.unity.benefit.UnityBenefitEntitlementLostEvent
        groupId: default
        description: Schema for AirBank client Unity member party deactivated events
        version: 1
        schemaRef: schemas/unity-benefit-entitlement-lost-event.avsc
    acl:
      read:
        - principal: "User:OBS_KAFKA_USER"
          name: obs
          group: obs
          generateDlt: true
          description:
            brief: "OBS odvolává rozhodnutí a vyplácení odměn na základě zpráv z tohoto topicu."
      write:
        - principal: "User:O2PROXY_KAFKA_USER"
          name: o2-proxy
          description:
            brief: "o2-proxy zapisuje cz.airbank.o2.backend.db.domain.BenefitEntitlement (UnityBenefitEntitlementLost) zprávy na základě unity.PropositionBenefitGranted nebo jiných podmínek"