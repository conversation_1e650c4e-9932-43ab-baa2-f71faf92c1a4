topics:
  - name: cz.airbank.benman.benefit.obtained.v1
    description:
      brief: "Topic informující o nároku RS na konkrétní benefit"
      url: "https://wiki.airbank.cz/display/SA/BenefitEntitlementObtained"
    partitions: 10
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: string
    schema:
      - name: BenefitEntitlementObtainedEvent
        artifactId: cz.airbank.benman.benefit.BenefitEntitlementObtainedEvent
        groupId: default
        description: Schema for information about GC's entitlement to a specific benefit
        version: 1
        schemaRef: schemas/benefit-entitlement-obtained-event.avsc
    acl:
      read:
        - principal: "User:OBS_KAFKA_USER"
          name: obs
          group: obs
          generateDlt: true
          description:
            brief: "příchozí notifikace o získání nároku na benefit"
        - principal: "User:DISGRAM_KAFKA_USER"
          name: disgram
          group: disgram
          generateDlt: true
          description:
            brief: "Disgram decides about rewards at our partners based on BenefitEntitlementObtained events"
        - principal: "User:DISGRAM_PERSONALIZER_KAFKA_USER"
          name: disgram_personalizer
          group: disgram
          generateDlt: true
          description:
            brief: "Disgram decides about rewards at our partners based on BenefitEntitlementObtained events"
      write:
        - principal: "User:BENMAN_KAFKA_USER"
          name: benman
          description:
            brief: "outbound events"
  - name: cz.airbank.benman.benefit.header.obtained.v1
    description:
      brief: "Topic informující o nároku RS na konkrétní hlavičku (skupinu) benefitů."
      url: "https://wiki.airbank.cz/display/SA/BenefitHeaderEntitlementObtained"
    partitions: 10
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: string
    schema:
        - name: BenefitHeaderEntitlementObtainedEvent
          artifactId: cz.airbank.benman.benefit.BenefitHeaderEntitlementObtainedEvent
          groupId: default
          description: Schema for information about GC's entitlement to a specific benefit header
          version: 1
          schemaRef: schemas/benefit-header-entitlement-obtained-event.avsc
    acl:
      read:
        - principal: "User:FES_KAFKA_USER"
          name: fes
          group: fes
          generateDlt: true
          description:
            brief: "FES decides which client badges to display from BenefitHeaderEntitlementObtained events"
      write:
        - principal: "User:BENMAN_KAFKA_USER"
          name: benman
          description:
            brief: "odchozí notifikace o získání nároku na hlavičku benefitů"
