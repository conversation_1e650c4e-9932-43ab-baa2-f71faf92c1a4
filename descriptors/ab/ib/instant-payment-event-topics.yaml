topics:
  - name: cz.airbank.frontend.event.instantpayment.status.v1
    description:
      brief: "Topic s eventy pro status okamzite platby. nahrada JMS"
      url: "https://wiki.airbank.cz/pages/viewpage.action?pageId=*********"
    partitions: 6
    replicationFactor: 3
    config:
      "retention.ms": "600000"
      "delete.retention.ms": "500000"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
      "segment.ms": "600000"
      "segment.bytes": "********"
    keyType: unknown
    schema:
      - name: InstantPaymentStatusEvent
        artifactId: cz.airbank.frontend.event.instantpayment.InstantPaymentStatusEvent
        groupId: default
        description: Schema for instant payment status
        version: 1
        schemaRef: schemas/instant-payment-status-event.avsc
    acl:
      read:
        - principal: "User:IB_KAFKA_USER"
          name: ib
          group: ib-frontend
          generateDlt: true
          description:
            brief: "IB consumes notification about instant payment status."
      write:
        - principal: "User:IB_WS_KAFKA_USER"
          name: ib-ws
          description:
            brief: "IB WS send notification about instant payment status."
