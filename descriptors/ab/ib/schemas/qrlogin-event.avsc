{"type": "record", "namespace": "cz.airbank.frontend.event.qrlogin", "name": "QrLoginEvent", "doc": "Avro message about a QR login event. Produced by MAS when QR login is initiated (<PERSON> takes a photo and calls MAS to initialize it) and consumed by IB to trigger the authentication flow via WebSocket.", "fields": [{"name": "loginToken", "type": "string", "doc": "Its for token value which is use to connect MA with IB."}, {"name": "cuid", "type": "long", "doc": "Bank customer id."}]}