{"type": "record", "namespace": "cz.airbank.frontend.event.instantpayment", "name": "InstantPaymentStatusEvent", "doc": "Avro message for notification about instant payment status from ib-ws to frontends.", "fields": [{"name": "idPaymentOrder", "type": "long", "doc": "Payment order id from OBS."}, {"name": "status", "type": {"type": "enum", "name": "InstantPaymentStatus", "symbols": ["IN_PROCESSING", "RECEIVED", "TIMEOUT", "REJECTED", "IN_DELIVERY", "REJECTED_AND_CAN_BE_STANDARD"]}}, {"name": "errorCode", "type": ["null", "string"], "default": null, "doc": "Payment error code from OBS"}]}