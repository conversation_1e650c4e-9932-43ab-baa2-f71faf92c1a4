topics:
  - name: cz.airbank.ams.splitpayment.application.status.v1
    description:
      brief: "Topic se změnami stavu žádostí o rozložení platby."
      url: "https://wiki.airbank.cz/x/AsoHGQ"
    partitions: 6
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: unknown
    schema:
      - name: SplitPaymentApplicationStatus
        artifactId: cz.airbank.ams.splitpayment.application.SplitPaymentApplicationStatus
        groupId: default
        description: Schema for split payment application status
        version: 1
        schemaRef: schemas/split-payment-application-status.avsc
    acl:
      read:
        - principal: "User:SAS_KAFKA_USER"
          name: sas
          group: sas-agent-kafka
          generateDlt: true
          description:
            brief: "SAS360 consumes events for further processing in CRM"
        - principal: "User:OM_KAFKA_USER"
          name: om-loan-bin
          group: om
          generateDlt: true
          description:
            brief: "consumes event to trigger loanbin re-computation when needed"
            wiki: "https://wiki.airbank.cz/display/KPA/XR-11072+Online+LoanBiny"
      write:
        - principal: "User:AMS_KAFKA_USER"
          name: ams
          transactionalId: ams
          description:
            brief: "ASM produce event when split payment application moves to some new states"

