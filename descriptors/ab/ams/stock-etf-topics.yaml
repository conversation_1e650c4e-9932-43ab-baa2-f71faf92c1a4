topics:
  - name: cz.airbank.ams.stocketf.application.status.v1
    description:
      brief: "Topic se změnami stavu žádostí o investice do akcií/etf."
      url: "https://wiki.airbank.cz/x/dQBPGQ"
    partitions: 6
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: unknown
    schema:
      - name: StockEtfApplicationStatus
        artifactId: cz.airbank.ams.stocketf.application.StockEtfApplicationStatus
        groupId: default
        description: Schema for stock etf application status
        version: 1
        schemaRef: schemas/stock-etf-application-status.avsc
    acl:
      read:
        - principal: "User:SAS_KAFKA_USER"
          name: sas
          group: sas-agent-kafka
          generateDlt: true
          description:
            brief: "SAS360 consumes events for further processing in CRM"
      write:
        - principal: "User:AMS_KAFKA_USER"
          name: ams
          transactionalId: ams
          description:
            brief: "AMS produce event when stock ETF application moves to some new states"

