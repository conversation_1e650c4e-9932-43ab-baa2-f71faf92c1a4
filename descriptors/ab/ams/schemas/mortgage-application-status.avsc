{"type": "record", "name": "MortgageApplicationStatus", "namespace": "cz.airbank.ams.mortgage.application", "fields": [{"name": "cuid", "type": "long"}, {"name": "envelopeId", "type": "long"}, {"name": "applicationId", "type": "long"}, {"name": "applicationType", "type": "string", "doc": "MORTGAGE"}, {"name": "applicationStatus", "type": "string", "doc": "https://wiki.airbank.cz/x/8YgkBQ"}, {"name": "approveStatus", "type": ["null", "string"], "default": null, "doc": "https://wiki.airbank.cz/x/tLMCBw"}, {"name": "originateSystem", "type": "string"}, {"name": "generalContractNumber", "type": ["null", "string"], "default": null}, {"name": "generalContractType", "type": ["null", "string"], "default": null, "doc": "https://wiki.airbank.cz/x/jI32F"}, {"name": "mortgageStage", "type": "string"}]}