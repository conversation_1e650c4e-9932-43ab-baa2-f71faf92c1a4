{"type": "record", "name": "ConditionFulfilledEvent", "namespace": "cz.airbank.tor.condition", "fields": [{"name": "generalContractNumber", "type": "string"}, {"name": "evaluatedMonth", "type": {"type": "string", "logicalType": "iso-local-date"}}, {"name": "amount", "type": {"type": "bytes", "logicalType": "decimal", "precision": 38, "scale": 10}}, {"name": "currencyCode", "type": "string"}, {"name": "conditionType", "type": "string"}, {"name": "conditionFulfillmentDate", "type": {"type": "string", "logicalType": "iso-zoned-date-time"}}]}