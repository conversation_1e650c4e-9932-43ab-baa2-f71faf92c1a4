topics:
  - name: cz.airbank.sas.campaign.email.sent.v1
    description:
      brief: "Topic s <PERSON><PERSON> o<PERSON> emaily k registraci v CML v rámci kampaně"
      url: "https://wiki.airbank.cz/x/rZLQG"
    partitions: 6
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: long
    schema:
      - name: SentCampaignEmailEvent
        artifactId: cz.airbank.sas.campaign.email.SentCampaignEmailEvent
        groupId: default
        description: Schema for sent campaign email events
        version: 1
        schemaRef: schemas/campaign-email-sent-event.avsc
    acl:
      read:
        - principal: "User:SASADP_KAFKA_USER"
          name: sasadp
          group: sasadp
          generateDlt: true
          description:
            brief: "SASADP reads realized emails"
      write:
        - principal: "User:SAS_KAFKA_USER"
          name: sas
          description:
            brief: "SAS writes realized email events"
  - name: cz.airbank.sas.campaign.result.v1
    description:
      brief: "Topic s výsledky odeslaných emailů v rámci kampaně"
      url: "https://wiki.airbank.cz/x/rZLQG"
    partitions: 6
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: unknown
    schema:
      - name: SAS360MessageResultEvent
        artifactId: cz.airbank.sas.campaign.SAS360MessageResultEvent
        groupId: default
        description: Schema for campaign email result events
        version: 1
        schemaRef: schemas/campaign-result-event.avsc
    acl:
      read:
        - principal: "User:SAS_KAFKA_USER"
          name: sas-agent-kafka
          group: sas-agent-kafka
          generateDlt: true
          description:
            brief: "SAS reads campaign results"
      write:
        - principal: "User:SASADP_KAFKA_USER"
          name: sasadp
          description:
            brief: "SASADP writes result of campaign process"
  - name: cz.airbank.sas.campaign.push.send.v1
    description:
      brief: "Topic s požadavkem na odeslání push promo AD nabídky"
      url: "https://wiki.airbank.cz/x/pRvqG"
    partitions: 6
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: long
    schema:
      - name: SendPushPromoEvent
        artifactId: cz.airbank.sas.campaign.push.SendPushPromoEvent
        groupId: default
        description: Schema for campaign push event
        version: 1
        schemaRef: schemas/campaign-push-send-event.avsc
    acl:
      read:
        - principal: "User:SASADP_KAFKA_USER"
          name: sasadp
          group: sasadp
          generateDlt: true
          description:
            brief: "read SAS360 campaign push promo ad event"
      write:
        - principal: "User:SAS_KAFKA_USER"
          name: sas-agent-kafka
          description:
            brief: "write SAS360 message result"

  - name: cz.airbank.sas.campaign.push.send.v2
    description:
      brief: "Topic s požadavkem na odeslání push promo AD nabídky"
      url: "https://wiki.airbank.cz/x/pRvqG"
    partitions: 6
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: long
    schema:
      - name: SendPushPromoEvent_V2
        artifactId: cz.airbank.sas.campaign.push.v2.SendPushPromoEvent
        groupId: default
        description: Schema for campaign push event
        version: 1
        schemaRef: schemas/campaign-push-send-event-v2.avsc
    acl:
      read:
        - principal: "User:SASADP_KAFKA_USER"
          name: sasadp
          group: sasadp
          generateDlt: true
          description:
            brief: "read SAS360 campaign push promo ad event"
      write:
        - principal: "User:SAS_KAFKA_USER"
          name: sas-agent-kafka
          description:
            brief: "write SAS360 message result"

  - name: cz.airbank.sas.campaign.ib.promoad.send.v1
    description:
      brief: "Topic s požadavkem na odeslání ib promo AD nabídky"
      url: "https://wiki.airbank.cz/x/thtPGQ"
    partitions: 6
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: unknown
    schema:
      - name: SendIbPromoAdEvent
        artifactId: cz.airbank.sas.campaign.ibpromoad.SendIbPromoAdEvent
        groupId: default
        description: Schema for campaign ib event
        version: 1
        schemaRef: schemas/campaign-ib-promoad-send-event.avsc
    acl:
      read:
        - principal: "User:SASADP_KAFKA_USER"
          name: sasadp
          group: sasadp
          generateDlt: true
          description:
            brief: "read SAS360 campaign ib promo ad event"
      write:
        - principal: "User:SAS_KAFKA_USER"
          name: sas-agent-kafka
          description:
            brief: "write SAS360 message result"

  - name: cz.airbank.sas.campaign.ib.promoad.send.v2
    description:
      brief: "Topic s požadavkem na odeslání ib promo AD nabídky - version 2"
      url: "https://wiki.airbank.cz/x/thtPGQ"
    partitions: 6
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: unknown
    schema:
      - name: SendIbPromoAdEvent_V2
        artifactId: cz.airbank.sas.campaign.ibpromoad.v2.SendIbPromoAdEvent
        groupId: default
        description: Schema for campaign ib event - version 2
        version: 1
        schemaRef: schemas/campaign-ib-promoad-send-event-v2.avsc
    acl:
      read:
        - principal: "User:SASADP_KAFKA_USER"
          name: sasadp
          group: sasadp
          generateDlt: true
          description:
            brief: "read SAS360 campaign ib promo ad event"
      write:
        - principal: "User:SAS_KAFKA_USER"
          name: sas-agent-kafka
          description:
            brief: "write SAS360 message result"

  - name: cz.airbank.sas.campaign.ib.promoad.send.v3
    description:
      brief: "Topic s požadavkem na odeslání ib promo AD nabídky - version 3"
      url: "https://wiki.airbank.cz/x/thtPGQ"
    partitions: 6
    replicationFactor: 3
    config:
      "retention.ms": "*********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: long
    schema:
      - name: SendIbPromoAdEvent_V3
        artifactId: cz.airbank.sas.campaign.ibpromoad.v3.SendIbPromoAdEvent
        groupId: default
        description: Schema for campaign ib event - version 3
        version: 1
        schemaRef: schemas/campaign-ib-promoad-send-event-v3.avsc
    acl:
      read:
        - principal: "User:SASADP_KAFKA_USER"
          name: sasadp
          group: sasadp
          generateDlt: true
          description:
            brief: "read SAS360 campaign ib promo ad event"
      write:
        - principal: "User:SAS_KAFKA_USER"
          name: sas-agent-kafka
          description:
            brief: "write SAS360 message result"

  - name: cz.airbank.sas.campaign.ma.promoad.send.v1
    description:
      brief: "Topic s požadavkem na odeslání ma promo AD nabídky"
      url: "https://wiki.airbank.cz/x/vBnqG"
    partitions: 6
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: unknown
    schema:
      - name: SendMaPromoAdEvent
        artifactId: cz.airbank.sas.campaign.mapromoad.SendMaPromoAdEvent
        groupId: default
        description: Schema for campaign ma event
        version: 1
        schemaRef: schemas/campaign-ma-promoad-send-event.avsc
    acl:
      read:
        - principal: "User:SASADP_KAFKA_USER"
          name: sasadp
          group: sasadp
          generateDlt: true
          description:
            brief: "read SAS360 campaign ma promo ad event"
      write:
        - principal: "User:SAS_KAFKA_USER"
          name: sas-agent-kafka
          description:
            brief: "write SAS360 message result"

  - name: cz.airbank.sas.campaign.ma.promoad.send.v2
    description:
      brief: "Topic s požadavkem na odeslání ma promo AD nabídky - version 2"
      url: "https://wiki.airbank.cz/x/vBnqG"
    partitions: 6
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: unknown
    schema:
      - name: SendMaPromoAdEvent_V2
        artifactId: cz.airbank.sas.campaign.mapromoad.v2.SendMaPromoAdEvent
        groupId: default
        description: Schema for campaign ma event - version 2
        version: 1
        schemaRef: schemas/campaign-ma-promoad-send-event-v2.avsc
    acl:
      read:
        - principal: "User:SASADP_KAFKA_USER"
          name: sasadp
          group: sasadp
          generateDlt: true
          description:
            brief: "read SAS360 campaign ma promo ad event"
      write:
        - principal: "User:SAS_KAFKA_USER"
          name: sas-agent-kafka
          description:
            brief: "write SAS360 message result"

  - name: cz.airbank.sas.campaign.ma.promoad.send.v3
    description:
      brief: "Topic s požadavkem na odeslání ma promo AD nabídky - version 3"
      url: "https://wiki.airbank.cz/x/vBnqG"
    partitions: 6
    replicationFactor: 3
    config:
      "retention.ms": "*********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: long
    schema:
      - name: SendMaPromoAdEvent_V2
        artifactId: cz.airbank.sas.campaign.mapromoad.v3.SendMaPromoAdEvent
        groupId: default
        description: Schema for campaign ma event - version 3
        version: 1
        schemaRef: schemas/campaign-ma-promoad-send-event-v3.avsc
    acl:
      read:
        - principal: "User:SASADP_KAFKA_USER"
          name: sasadp
          group: sasadp
          generateDlt: true
          description:
            brief: "read SAS360 campaign ma promo ad event"
      write:
        - principal: "User:SAS_KAFKA_USER"
          name: sas-agent-kafka
          description:
            brief: "write SAS360 message result"

  - name: cz.airbank.sas.campaign.ma.promoad.send.v4
    description:
      brief: "Topic s požadavkem na odeslání ma promo AD nabídky - version 4"
      url: "https://wiki.airbank.cz/x/vBnqG"
    partitions: 6
    replicationFactor: 3
    config:
      "retention.ms": "*********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: long
    schema:
      - name: SendMaPromoAdEvent_V4
        artifactId: cz.airbank.sas.campaign.mapromoad.v4.SendMaPromoAdEvent
        groupId: default
        description: Schema for campaign ma event - version 4
        version: 1
        schemaRef: schemas/campaign-ma-promoad-send-event-v4.avsc
    acl:
      read:
        - principal: "User:SASADP_KAFKA_USER"
          name: sasadp
          group: sasadp
          generateDlt: true
          description:
            brief: "read SAS360 campaign ma promo ad event"
      write:
        - principal: "User:SAS_KAFKA_USER"
          name: sas-agent-kafka
          description:
            brief: "write SAS360 message result"

  - name: cz.airbank.sas.campaign.ma.promoad.send.v5
    description:
      brief: "Topic s požadavkem na odeslání ma promo AD nabídky - version 5"
      url: "https://wiki.airbank.cz/x/vBnqG"
    partitions: 6
    replicationFactor: 3
    config:
      "retention.ms": "*********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: long
    schema:
      - name: SendMaPromoAdEvent_V5
        artifactId: cz.airbank.sas.campaign.mapromoad.v5.SendMaPromoAdEvent
        groupId: default
        description: Schema for campaign ma event - version 5
        version: 1
        schemaRef: schemas/campaign-ma-promoad-send-event-v5.avsc
    acl:
      read:
        - principal: "User:SASADP_KAFKA_USER"
          name: sasadp
          group: sasadp
          generateDlt: true
          description:
            brief: "read SAS360 campaign ma promo ad event"
      write:
        - principal: "User:SAS_KAFKA_USER"
          name: sas-agent-kafka
          description:
            brief: "write SAS360 message result"

  - name: cz.airbank.sas.campaign.revoke.promoad.v1
    description:
      brief: "Topic s požadavkem na revoke promo AD nabídky"
      url: "https://wiki.airbank.cz/x/4xlsGQ"
    partitions: 6
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: unknown
    schema:
      - name: RevokePromoAdEvent
        artifactId: cz.airbank.sas.campaign.revoke.RevokePromoAdEvent
        groupId: default
        description: Schema for revoke promo ad event
        version: 1
        schemaRef: schemas/campaign-revoke-promoad-event.avsc
    acl:
      read:
        - principal: "User:SASADP_KAFKA_USER"
          name: sasadp
          group: sasadp
          generateDlt: true
          description:
            brief: "request for revoke promo ad (IB, MA, PUSH)"
      write:
        - principal: "User:SAS_KAFKA_USER"
          name: sas-agent-kafka
          description:
            brief: "result of revoking promo ad"

  - name: cz.airbank.sas.campaign.planned.call.v1
    description:
      brief: "Topic s požadavkem na odeslání plánovaného hovoru"
      url: "https://wiki.airbank.cz/x/98HaGQ"
    partitions: 6
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: unknown
    schema:
      - name: CampaignPlannedCallEvent
        artifactId: cz.airbank.sas.campaign.planned.call.CampaignPlannedCallEvent
        groupId: default
        description: Schema for campaign ma event
        version: 1
        schemaRef: schemas/campaign-planned-call-event.avsc
    acl:
      read:
        - principal: "User:SASADP_KAFKA_USER"
          name: sasadp
          group: sasadp
          generateDlt: true
          description:
            brief: "read SAS360 campaign planned call event"
      write:
        - principal: "User:SAS_KAFKA_USER"
          name: sas-agent-kafka
          description:
            brief: "write SAS360 message result"