topics:
  - name: cz.airbank.sas.campaign.launched.v1
    description:
      brief: "<PERSON><PERSON> se spuštěn<PERSON>mi kamp<PERSON> pro propozici"
      url: "https://wiki.airbank.cz/x/nHsLGw"
    partitions: 6
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: none
    schema:
      - name: CampaignLaunched
        artifactId: cz.airbank.sas.campaign.CampaignLaunched
        groupId: default
        description: Schema for launched campaigns for proposition
        version: 1
        schemaRef: schemas/campaign-launched.avsc
    acl:
      read:
        - principal: "User:BENMAN_KAFKA_USER"
          name: benman
          group: benman
          generateDlt: true
          description:
            brief: "BENMAN reads launched campaigns"
      write:
        - principal: "User:SASADP_KAFKA_USER"
          name: sasadp
          description:
            brief: "SASADP writes reads launched campaigns"