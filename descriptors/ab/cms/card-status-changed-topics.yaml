topics:
  - name: cz.airbank.cms.card.status.v1
    description:
      brief: "Topic o zmenach stavu platevni karty"
      url: "https://wiki.airbank.cz/x/barSGg"
    partitions: 6
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: unknown
    schema:
      - name: CardStatusChangedEvent
        artifactId: cz.airbank.cms.card.status.v1.CardStatusChangedEvent
        groupId: default
        description: Schema representing an event that captures a payment card status change
        version: 1
        schemaRef: schemas/card-status-changed-event.avsc
    acl:
      read:
        - principal: "User:SAS_KAFKA_USER"
          name: sas-agent-kafka
          group: sas-agent-kafka
          generateDlt: true
          description:
            brief: "SAS360 reads topic and determinates events about payment card's activation to disable offer"
      write:
        - principal: "User:CMS_KAFKA_USER"
          name: cms
          description:
            brief: "CMS writes payment card status changes"