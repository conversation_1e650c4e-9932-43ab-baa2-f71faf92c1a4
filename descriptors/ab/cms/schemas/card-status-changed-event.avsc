{"type": "record", "name": "CardStatusChangedEvent", "namespace": "cz.airbank.cms.card.status.v1", "fields": [{"name": "id", "type": "long", "doc": "paymentCard.id - identifier in CMS"}, {"name": "cardHolderCuid", "type": "long"}, {"name": "accountOwnerCuid", "type": "long"}, {"name": "accountContractNumber", "type": ["null", "string"], "default": null}, {"name": "cardTypeTechnology", "type": {"type": "enum", "name": "PaymentCardTechnologyType", "symbols": ["CHIP", "CTLS", "MAG", "VIRT", "STIC"]}, "doc": "https://wiki.airbank.cz/x/PTvZBg"}, {"name": "statusBefore", "type": {"type": "enum", "name": "PaymentCardStatusType", "symbols": ["ACTIVE", "BLOCKED", "KEEP_BACK", "NOT_ACTIVE", "RENEWAL", "STOLEN", "CANCELED", "LOST"]}, "doc": "https://wiki.airbank.cz/x/OQDnBg"}, {"name": "statusAfter", "type": {"type": "enum", "name": "PaymentCardStatusType", "symbols": ["ACTIVE", "BLOCKED", "KEEP_BACK", "NOT_ACTIVE", "RENEWAL", "STOLEN", "CANCELED", "LOST"]}, "doc": "https://wiki.airbank.cz/x/OQDnBg"}, {"name": "numberCard", "type": "string", "doc": "Anonymized card number https://wiki.airbank.cz/x/y4A6BQ"}, {"name": "blockId", "type": ["null", "long"], "default": null, "doc": "This identifier is set only in case of a card status change where the card is blocked"}, {"name": "statusChangeTime", "type": {"type": "string", "logicalType": "iso-zoned-date-time"}, "doc": "Time when the event was made in CMS"}]}