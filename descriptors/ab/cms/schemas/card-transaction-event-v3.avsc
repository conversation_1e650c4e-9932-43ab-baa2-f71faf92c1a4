{
  "type": "record",
  "name": "CardTransactionEvent",
  "namespace": "cz.airbank.cms.card.transaction.v3",
  "fields": [
      { "name": "idAccMove", "type": "long",    "doc": "idAccmove - ID of transaction, id of account move in OBS"},
      { "name": "cardTransactionType",  "type": {
                                        "type": "enum",
                                        "name": "CardTransactionType",
                                        "symbols": [
                                            "AUTHORIZATION",
                                            "PRESENTMENT"
                                        ],
                                       "default": "PRESENTMENT"},
        "doc": "Enum if the card transaction is authorization or presentment"
      },
      { "name": "authorizationId", "type": ["null", "long"], "default": null,
        "doc": "authorizationId, used for disgram, if is null the presentment is offline transaction without authorization"},

    { "name": "presentmentId", "type": ["null", "long"], "default": null,
      "doc": "presentmentId - id of mc incoming" },

    { "name": "ard", "type": ["null", "string"], "default": null,  "doc": "ARD (ARN) acquirer reference number of presentment"},

      //Amounts and currencies
      { "name": "accountAmount",
        "type": {
          "type": "bytes",
          "logicalType": "decimal",
          "precision": 38,   // Adjust precision as needed
          "scale": 10,        // Adjust scale as needed
          "doc": "AccountAmount - for non CZK account is converted"
        }
      },
      { "name": "accountCurrency", "type": "string",  "doc": "Account amount currency"},

    { "name": "accountAmountCzk",
      "type": {
        "type": "bytes",
        "logicalType": "decimal",
        "precision": 38,   // Adjust precision as needed
        "scale": 10,        // Adjust scale as needed
        "doc": "AccountAmount in CZK - for non CZK accounts is amount from GPE"
      }
    },
    { "name": "cashBackAmount",
      "type": {
        "type": "bytes",
        "logicalType": "decimal",
        "precision": 38,   // Adjust precision as needed
        "scale": 10,        // Adjust scale as needed
        "doc": "CashBackAmount in CZK -  cash Back amount in case of POS RTL"
      }
    },

     { "name": "originalAmount",
              "type": {
                "type": "bytes",
                "logicalType": "decimal",
                "precision": 38,   // Adjust precision as needed
                "scale": 10, // Adjust scale as needed
                "doc": "Original amount"
              }
            },
    { "name": "originalCurrency", "type": "string",  "doc": "Original amount currency for. eg. USD, EUR, PLN,.." },

    { "name": "transactionType", "type": ["null", "string"], "default": null, "doc": "Authorization Transaction Type in OBS" },

    { "name": "transactionDate",  "type": "string",
        "doc": "ISO string representation  of transaction Date"
    },

    {"name": "terminalTransactionDate", "type": "string",
      "doc": "ISO string representation of Terminal transaction date - may be different to system time (for. e.g. in future, ...)"
    },

    { "name": "transactionDescription", "type": ["null", "string"], "default": null,  "doc": "Used for client notifications in disgram"},

    { "name": "reversedAuthIdAccMove", "type": ["null", "long"], "default": null,
      "doc": "id of the original authorization for this authorization is reversal for "},

    { "name": "preAuthIdAccMove", "type": ["null", "long"], "default": null,
      "doc": "id of the original authorization for this authorization is preauthorization completion  for "},

    { "name": "adjustedAuthIdAccMove", "type": ["null", "long"], "default": null,
      "doc": "id of the original authorization for this authorization is adjustment  for "},

    //tranaction authentication information
    { "name": "paymentDataInputMode", "type": ["null", "string"], "default": null,  "doc": "CHIP,CTLS,MAGN_STRIPE,MANUAL,ECOMMERCE,OTHER"},
    { "name": "paymentAuthenticationMethod", "type": ["null", "string"], "default": null,  "doc": "PIN,3DS,OTHER"},

    //card, card holder, account owner and contract identification
    { "name": "cardId", "type": "long"},
    { "name": "maskedCardNumber", "type": "string", "doc": "Used for client notifications, e.g disgram push"},
    { "name": "cuid", "type": "long"},
    { "name": "accountOwnerCuid", "type": "long"},
    { "name": "accountNumber", "type": "long"},
    { "name": "generalContractNumber", "type": "string"},

    //token informations in case of token payment
    { "name": "tokenUniqueReference", "type": ["null", "string"], "default": null,
      "doc": "Token unique reference in case of authorization payed via token"
    },
    { "name": "tokenWalletCode", "type": ["null", "string"], "default": null,
      "doc": "Token wallet in case of authorization payed via token"},

    // fields used for e.g. digram and transaction pairing - used mainly for authorization
    { "name": "authorizationCode",  "type": ["null", "string"], "default": null },
    { "name": "referenceNumber", "type": ["null", "string"], "default": null },

    //Merchant and terminal information, used for e.g. in digram or dateio pairing rules
    { "name": "terminalId", "type": ["null", "string"], "default": null },
    { "name": "merchantId", "type": ["null", "string"], "default": null },
    { "name": "merchantCategoryCode", "type": ["null", "string"], "default": null },
    { "name": "merchantCategoryGroup", "type": ["null", "string"], "default": null,
      "doc": "MDM of merchant categories - group for e.g. O2 merchant, mandatory for O2 Proxy"
    },
    { "name": "merchantName", "type": ["null", "string"], "default": null },
    { "name": "shopCountry", "type": ["null", "string"], "default": null },
    { "name": "shopPostalCode", "type": ["null", "string"], "default": null },
    { "name": "shopTown", "type": ["null", "string"], "default": null },

    { "name": "isAdjustment", "type": "boolean"},
    { "name": "isEcommerce", "type": "boolean"},
    { "name": "is3DS", "type": ["null", "string"], "default": null },
    { "name": "channelCode", "type": ["null", "string"], "default": null ,
      "doc": "ChannelCode char representation used for disgram or dateio"
    }

  ]
}
