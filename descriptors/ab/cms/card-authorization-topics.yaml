topics:
  - name: cz.airbank.cms.card.transaction.v1
    description:
      brief: "Topic s karetnimi transakcemi - autorizacemi i presentmenty 155"
      url: "https://wiki.airbank.cz/display/SA/CardTransaction+topic"
    partitions: 10
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: unknown
    schema:
      - name: CardTransactionEvent
        artifactId:  cz.airbank.cms.card.transaction.CardTransactionEvent
        groupId: default
        description: Schema for card transaction event
        version: 1
        schemaRef: schemas/card-transaction-event.avsc
    acl:
      read:
        - principal: "User:CTC_KAFKA_USER"
          name: cardtransaction
          group: ctc
          generateDlt: true
          description:
            brief: "CTC reads card transactions"
      write:
        - principal: "User:CMS_KAFKA_USER"
          name: cms
          description:
            brief: "CMS writes card transactions"
  - name: cz.airbank.cms.card.transaction.v2
    description:
      brief: "Topic s karetnimi transakcemi - autorizacemi i presentmenty 155"
      url: "https://wiki.airbank.cz/display/SA/CardTransaction+topic"
    partitions: 10
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: unknown
    schema:
      - name: CardTransactionEvent V2
        artifactId:  cz.airbank.cms.card.transaction.v2.CardTransactionEvent
        groupId: default
        description: Schema for card transaction event v2
        version: 1
        schemaRef: schemas/card-transaction-event-v2.avsc
    acl:
      read:
        - principal: "User:CTC_KAFKA_USER"
          name: cardtransaction
          group: ctc
          generateDlt: true
          description:
            brief: "CTC reads card transactions"
      write:
        - principal: "User:CMS_KAFKA_USER"
          name: cms
          description:
            brief: "CMS writes card transactions"
  - name: cz.airbank.cms.card.transaction.v3
    description:
      brief: "Topic s karetnimi transakcemi - autorizacemi i presentmenty 155"
      url: "https://wiki.airbank.cz/display/SA/CardTransaction+topic"
    partitions: 10
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: unknown
    schema:
      - name: CardTransactionEvent V3
        artifactId: cz.airbank.cms.card.transaction.v3.CardTransactionEvent
        groupId: default
        description: Schema for card transaction event v3
        version: 1
        schemaRef: schemas/card-transaction-event-v3.avsc
    acl:
      read:
        - principal: "User:CTC_KAFKA_USER"
          name: cardtransaction
          group: ctc
          generateDlt: true
          description:
            brief: "CTC reads card transactions"
        - principal: "User:DISGRAM_PERSONALIZER_KAFKA_USER"
          name: disgram_personalizer
          group: disgram
          generateDlt: true
          description:
            brief: "Disgram reads card transactions for benefit entitlements"
        - principal: "User:DISGRAM_DETECTOR_KAFKA_USER"
          name: disgram_detector
          group: disgram
          generateDlt: true
          description:
            brief: "Disgram reads card transactions"
      write:
        - principal: "User:CMS_KAFKA_USER"
          name: cms
          description:
            brief: "CMS writes card transactions"
  - name: cz.airbank.cms.card.transaction.v4
    description:
      brief: "Topic s karetnimi transakcemi - autorizacemi i presentmenty 155"
      url: "https://wiki.airbank.cz/display/SA/CardTransaction+topic"
    partitions: 10
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: unknown
    schema:
      - name: CardTransactionEvent V4
        artifactId: cz.airbank.cms.card.transaction.v4.CardTransactionEvent
        groupId: default
        description: Schema for card transaction event v4
        version: 1
        schemaRef: schemas/card-transaction-event-v4.avsc
    acl:
      read:
        - principal: "User:CTC_KAFKA_USER"
          name: cardtransaction
          group: ctc
          generateDlt: true
          description:
            brief: "CTC reads card transactions"
        - principal: "User:DISGRAM_DETECTOR_KAFKA_USER"
          name: disgram_detector
          group: disgram
          generateDlt: true
          description:
            brief: "Disgram reads card transactions"
      write:
        - principal: "User:CMS_KAFKA_USER"
          name: cms
          description:
            brief: "CMS writes card transactions"

