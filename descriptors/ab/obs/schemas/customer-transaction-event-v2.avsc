{"type": "record", "name": "CustomerTransactionEvent", "namespace": "cz.airbank.obs.transaction.v2", "fields": [{"name": "id", "type": "string", "doc": "unique id of this message, transactionId + dbcrIndicator + active"}, {"name": "transactionId", "type": "long", "doc": "ID of transaction, same as in OBSRealizedTransactionWS.getRealizedTransaction method"}, {"name": "accounted", "type": "boolean", "doc": "true - accounted transaction, false - hold"}, {"name": "active", "type": "boolean", "doc": "accounted transaction: always true, hold: true when hold becomes active, false when hold is deactivated"}, {"name": "amount", "type": {"type": "bytes", "logicalType": "decimal", "precision": 38, "scale": 10}, "doc": "amount in account currency, negative in case of reversal"}, {"name": "currency", "type": "string", "doc": "ISO alpha 3 code of account currency"}, {"name": "homeAmount", "type": {"type": "bytes", "logicalType": "decimal", "precision": 38, "scale": 10}, "doc": "amount in home currency (CZK), negative in case of reversal"}, {"name": "payAmount", "type": ["null", {"type": "bytes", "logicalType": "decimal", "precision": 38, "scale": 10}], "doc": "amount in payment currency, negative in case of reversal"}, {"name": "payCurrency", "type": ["null", "string"], "doc": "ISO alpha 3 code of payment currency"}, {"name": "origAmount", "type": ["null", {"type": "bytes", "logicalType": "decimal", "precision": 38, "scale": 10}], "default": null, "doc": "amount in original currency, negative in case of reversal"}, {"name": "origCurrency", "type": ["null", "string"], "default": null, "doc": "ISO alpha 3 code of original currency"}, {"name": "bookingDate", "type": {"type": "string", "logicalType": "iso-local-date-time"}, "doc": "accounted transaction: booking date(time), hold: date(time) of hold activation/deactivation"}, {"name": "valueDate", "type": ["null", {"type": "string", "logicalType": "iso-local-date-time"}], "default": null, "doc": "accounted transaction: value date(time), not used if same as booking date, hold: not used"}, {"name": "dueDate", "type": ["null", {"type": "string", "logicalType": "iso-local-date-time"}], "default": null, "doc": "accounted transaction: original due date(time), hold: not used"}, {"name": "accountNumber", "type": "string", "doc": "account number transaction was debited/credited on"}, {"name": "envelopeId", "type": ["null", "long"], "default": null, "doc": "ID of savings envelope (if any) transaction was debited/credited on"}, {"name": "generalContractNumber", "type": "string", "doc": "General contract number accountNumber relates to"}, {"name": "ownerCuid", "type": "long", "doc": "cuid of the owner of the accountNumber (and of the generalContractNumber)"}, {"name": "dbcrIndicator", "type": "string", "doc": "DB - debit on accountNumber, CR - credit on accountNumber"}, {"name": "contraAccountNumber", "type": ["null", "string"], "default": null, "doc": "contraaccount number"}, {"name": "contraEnvelopeId", "type": ["null", "long"], "default": null, "doc": "contra savings envelope ID"}, {"name": "contraAccountName", "type": ["null", "string"], "default": null, "doc": "contraaccount name"}, {"name": "contraBankCode", "type": ["null", "string"], "default": null, "doc": "contrabank code, i.e. 0100, CHASUS33, etc."}, {"name": "contraGeneralContractNumber", "type": ["null", "string"], "default": null, "doc": "General contract number contraAccountNumber relates to"}, {"name": "contraOwnerCuid", "type": ["null", "long"], "default": null, "doc": "cuid of the owner of the contraAccountNumber (and of the contraGeneralContractNumber)"}, {"name": "transactionType", "type": ["null", "string"], "default": null, "doc": "transaction type from 'devil's xls'"}, {"name": "varSymbol", "type": ["null", "string"], "default": null, "doc": "variable symbol"}, {"name": "specSymbol", "type": ["null", "string"], "default": null, "doc": "specific symbol"}, {"name": "constSymbol", "type": ["null", "string"], "default": null, "doc": "constant symbol"}, {"name": "end2EndRef", "type": ["null", "string"], "default": null, "doc": "end to end reference"}, {"name": "msg", "type": ["null", "string"], "default": null, "doc": "message for accountNumber"}, {"name": "contraMsg", "type": ["null", "string"], "default": null, "doc": "message for contraAccountNumber"}, {"name": "merchantName", "type": ["null", "string"], "default": null, "doc": "merchant name (typicaly used for payment card transactions)"}, {"name": "merchantAddr", "type": ["null", "string"], "default": null, "doc": "merchant address (typicaly used for payment card transactions)"}, {"name": "merchantId", "type": ["null", "string"], "default": null, "doc": "merchant ID (typicaly used for payment card transactions)"}, {"name": "channel", "type": ["null", "string"], "default": null, "doc": "channel the transaction was originated on"}, {"name": "extSource", "type": ["null", "string"], "default": null, "doc": "external source the transaction was originated on"}, {"name": "extTransactionId", "type": ["null", "string"], "default": null, "doc": "transaction ID in external source"}, {"name": "loanNumber", "type": ["null", "string"], "default": null, "doc": "loan number (if any) the transaction relates to"}, {"name": "termDepositNumber", "type": ["null", "string"], "default": null, "doc": "term deposit number (if any) the transaction relates to"}, {"name": "standingOrderId", "type": ["null", "long"], "default": null, "doc": "standing order ID (if any) the transaction relates to"}, {"name": "paymentCardId", "type": ["null", "long"], "default": null, "doc": "payment card ID (if any) from CMS the transaction relates to"}, {"name": "paymentOrderEventId", "type": ["null", "string"], "default": null, "doc": "event ID of the payment order"}, {"name": "batchPaymentEventId", "type": ["null", "string"], "default": null, "doc": "event ID of the batch payment order this transaction is part of"}, {"name": "purposeCode", "type": ["null", "string"], "default": null, "doc": "payment purpose code"}, {"name": "purposeText", "type": ["null", "string"], "default": null, "doc": "payment purpose free text"}, {"name": "pispAppId", "type": ["null", "string"], "default": null, "doc": "third party application ID this transaction was originated in"}, {"name": "pispDeveloperId", "type": ["null", "string"], "default": null, "doc": "developer ID of the pispAppId"}, {"name": "matchedHoldId", "type": ["null", "long"], "default": null, "doc": "transactionId of the hold this transaction was matched to"}, {"name": "splitable", "type": ["null", "boolean"], "default": null, "doc": "true - transaction can be used in 'Rozlozeni platby' product"}]}