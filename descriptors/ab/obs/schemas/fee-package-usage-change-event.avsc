{"type": "record", "name": "FeePackageUsageChangeEvent", "namespace": "cz.airbank.obs.feepackage.v1", "fields": [{"name": "messageId", "type": "long", "doc": "unique ID"}, {"name": "generalContractNumber", "type": "string", "doc": "General contract number"}, {"name": "generalContractType", "type": {"type": "enum", "name": "GeneralContractType", "symbols": ["RETAIL", "ENTREPRENEUR", "LEGAL_ENTITY"]}}, {"name": "code", "type": "string", "doc": "Package code (OBS.BC_FEEPACKAGE.CODE)"}, {"name": "action", "type": {"type": "enum", "name": "FeePackageAction", "symbols": ["ACTIVATION", "DEACTIVATION"]}}, {"name": "changedOn", "type": {"type": "string", "logicalType": "iso-zoned-date-time"}}, {"name": "usedFrom", "type": {"type": "string", "logicalType": "iso-local-date"}}, {"name": "usedTo", "type": {"type": "string", "logicalType": "iso-local-date"}}, {"name": "benefitEntitlementFrom", "type": {"type": "string", "logicalType": "iso-local-date"}}, {"name": "benefitEntitlementTo", "type": {"type": "string", "logicalType": "iso-local-date"}}]}