{"type": "record", "name": "GeneralContractStatusChangedEvent", "namespace": "cz.airbank.obs.generalcontract.statuschanged.v1", "fields": [{"name": "messageId", "type": "long", "doc": "unique ID"}, {"name": "generalContractNumber", "type": "string", "doc": "General contract number"}, {"name": "generalContractType", "type": {"type": "enum", "name": "GeneralContractType", "symbols": ["RETAIL", "ENTREPRENEUR", "LEGAL_ENTITY"]}}, {"name": "statusNew", "type": {"type": "enum", "name": "GeneralContractStatus", "symbols": ["PROPOSAL", "ACTIVE", "ENDED", "CANCELLED"]}}, {"name": "statusOld", "type": ["null", "GeneralContractStatus"]}, {"name": "statusChangedOn", "type": {"type": "string", "logicalType": "iso-zoned-date-time"}}, {"name": "changeReason", "type": ["null", "string"], "doc": "Reason of status change"}, {"name": "ownerCuid", "type": "long"}, {"name": "entitledCuid", "type": ["null", "long"]}]}