{"type": "record", "name": "GeneralContractCustomerRelationsChangeEvent", "namespace": "cz.airbank.obs.generalcontract.customerrelations", "fields": [{"name": "eventId", "type": "long"}, {"name": "event", "type": {"type": "enum", "name": "EventType", "symbols": ["CREATED", "DELETED"]}}, {"name": "generalContractNumber", "type": "long"}, {"name": "generalContractId", "type": "long"}, {"name": "generalContractType", "type": {"type": "enum", "name": "GeneralContractType", "symbols": ["RETAIL", "ENTREPRENEUR", "LEGAL_ENTITY"]}}, {"name": "legalSegment", "type": {"type": "enum", "name": "LegalSegment", "symbols": ["CUSTOMER", "ENTREPRENEUR", "LEGAL_ENTITY"]}}, {"name": "relationToContract", "type": {"type": "enum", "name": "RelationToContractType", "symbols": ["OWNER", "DISPONENT", "CARD_HOLDER", "ENTITLED"]}}, {"name": "generalContractStatus", "type": "string"}]}