topics:
  - name: cz.airbank.obs.transaction.customertransaction.v2
    description:
      brief: "All transactions on customer accounts, both accounting transactions and holds"
      url: "https://wiki.airbank.cz/display/SA/Transactions+topic"
    partitions: 12
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: string
    schema:
      - name: CustomerTransactionEvent_V2
        artifactId: cz.airbank.obs.transaction.v2.CustomerTransactionEvent
        groupId: default
        description: Schema for customer transactions events
        version: 1
        schemaRef: schemas/customer-transaction-event-v2.avsc
    acl:
      read:
        - principal: "User:SAS_KAFKA_USER"
          name: sas
          group: sas-agent-kafka
          generateDlt: true
          description:
            brief: "inbound events"
        - principal: "User:TOR_KAFKA_USER"
          name: tor
          group: tor
          generateDlt: true
          description:
            brief: "replacement of IncomeWS.addIncome() (TOR calculates turnover from incoming transactions and sends events to another topic)"
        - principal: "User:RDR_KAFKA_USER"
          name: rdr
          group: rdr
          generateDlt: true
          description:
            brief: "Saves transaction to database for use during approval process"
        - principal: "User:CTC_KAFKA_USER"
          name: ctc
          group: ctc
          generateDlt: true
          description:
            brief: "Processes transactions and count them by general contract"
        - principal: "User:O2PROXY_KAFKA_USER"
          name: o2-proxy
          group: o2-proxy
          generateDlt: true
          description:
            brief: "Consumes transactions to detect payments to O2 for Unity benefit cashback 300"
      write:
        - principal: "User:OBS_KAFKA_USER"
          name: obs
          description:
            brief: "outbound events"
