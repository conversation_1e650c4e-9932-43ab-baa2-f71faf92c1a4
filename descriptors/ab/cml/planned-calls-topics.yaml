topics:
  - name: cz.airbank.cml.planned.call.created.v1
    description:
      brief: "Topic for planned call creation - version 1"
      url: "https://wiki.airbank.cz/x/1horGg"
    partitions: 6
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: string
    schema:
      - name: PlannedCallCreatedEvent_V1
        artifactId: cz.airbank.cml.planned.call.PlannedCallCreatedEvent
        groupId: default
        description: Schema for planned call creation - version 1
        version: 1
        schemaRef: schemas/planned-call-created-event.avsc
    acl:
      read:
        - principal: "User:SAS_KAFKA_USER"
          name: sas
          group: sas-agent-kafka
          generateDlt: true
          description:
            brief: "planned call creation event"
      write:
        - principal: "User:CML_KAFKA_USER"
          name: cml
          description:
            brief: "planned call creation event"
