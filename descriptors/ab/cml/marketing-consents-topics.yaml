topics:
  - name: cz.airbank.cml.marketing.consents.change.v1
    description:
      brief: "Topic for clients marketing consent changes"
      url: "https://wiki.airbank.cz/x/oO4HGQ"
    partitions: 10
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: unknown
    schema:
      - name: MarketingConsentsChangeEvent
        artifactId: cz.airbank.cml.marketing.consents.MarketingConsentsChangeEvent
        groupId: default
        description: Schema for marketing consents change events
        version: 1
        schemaRef: schemas/marketing-consents-change-event.avsc
    acl:
      read:
        - principal: "User:SAS_KAFKA_USER"
          name: sas
          group: sas-agent-kafka
          generateDlt: true
          description:
            brief: "inbound events"
      write:
        - principal: "User:CML_KAFKA_USER"
          name: cml
          description:
            brief: "outbound events"
  - name: cz.airbank.cml.marketing.consents.change.v2
    description:
      brief: "Topic for clients marketing consent changes - version 2"
      url: "https://wiki.airbank.cz/x/oO4HGQ"
    partitions: 6
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: unknown
    schema:
      - name: MarketingConsentsChangeEvent_V2
        artifactId: cz.airbank.cml.marketing.consents.v2.MarketingConsentsChangeEvent
        groupId: default
        description: Schema for marketing consents change events - version 2
        version: 1
        schemaRef: schemas/marketing-consents-change-event-v2.avsc
    acl:
      read:
        - principal: "User:SAS_KAFKA_USER"
          name: sas
          group: sas-agent-kafka
          generateDlt: true
          description:
            brief: "client consents change"
      write:
        - principal: "User:CML_KAFKA_USER"
          name: cml
          description:
            brief: "actual status of all clients consents"
