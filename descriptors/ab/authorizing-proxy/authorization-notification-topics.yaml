topics:
  - name: cz.airbank.authorizing.proxy.notification.v1
    description:
      brief: "Topic s Authorizacnima zpravama. nahrada JMS"
      url: "https://wiki.airbank.cz/pages/viewpage.action?pageId=*********"
    partitions: 6
    replicationFactor: 3
    config:
      "retention.ms": "600000"
      "delete.retention.ms": "500000"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
      "segment.ms": "600000"
      "segment.bytes": "********"
    keyType: string
    schema:
      - name: AuthMessage
        artifactId: cz.airbank.authorizing.proxy.AuthMessage
        groupId: default
        description: Schema for Auhtorization message
        version: 1
        schemaRef: schemas/authorization-notification.avsc
    acl:
      read:
        - principal: "User:IB_KAFKA_USER"
          name: ib
          group: ib-frontend
          generateDlt: true
          description:
            brief: "IB consumes authorization message."
        - principal: "User:MAS_KAFKA_USER"
          name: mas
          group: mas
          generateDlt: true
          description:
            brief: "MAS consumes authorization message."
        - principal: "User:APA_KAFKA_USER"
          name: apa
          group: apa
          generateDlt: true
          description:
            brief: "Apa consume authorization message."
      write:
        - principal: "User:APX_KAFKA_USER"
          name: auth-proxy
          description:
            brief: "Authorizing proxy send authorization message"
