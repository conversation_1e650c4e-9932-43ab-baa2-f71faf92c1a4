<assembly xmlns="http://maven.apache.org/ASSEMBLY/2.2.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/ASSEMBLY/2.2.0 http://maven.apache.org/xsd/assembly-2.2.0.xsd">
<id>distribution</id>
    <formats>
        <format>tar</format>
    </formats>
    <includeBaseDirectory>false</includeBaseDirectory>

    <fileSets>
        <fileSet>
            <outputDirectory>/kafka-descriptors</outputDirectory>
            <includes>
                <include>**/*</include>
            </includes>
            <directory>../../descriptors/</directory>
        </fileSet>
    </fileSets>
    <files>
        <file>
            <source>${project.basedir}/src/container/Containerfile</source>
            <outputDirectory>/</outputDirectory>
            <filtered>false</filtered>
            <destName>Containerfile</destName>
        </file>
        <file>
            <source>${project.basedir}/src/container/manifest.yaml</source>
            <outputDirectory>/</outputDirectory>
            <filtered>false</filtered>
            <destName>manifest.yaml</destName>
        </file>
    </files>
</assembly>