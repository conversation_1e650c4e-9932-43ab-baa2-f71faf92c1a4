<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions name="NotificationWsResult"
                  targetNamespace="http://airbank.cz/ib/ws/notification/"
                  xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns="http://airbank.cz/ib/ws/notification/"
                  xmlns:xsd="http://www.w3.org/2001/XMLSchema">
    <wsdl:types>
        <xsd:schema>
            <xsd:import schemaLocation="NotificationWs.xsd" namespace="http://airbank.cz/ib/ws/notification/"/>
        </xsd:schema>
    </wsdl:types>

    <wsdl:message name="AuthorizationFinishedRequest">
        <wsdl:part element="AuthorizationFinishedRequest" name="AuthorizationFinishedRequest"/>
    </wsdl:message>

    <wsdl:message name="AuthorizationFinishedResponse">
        <wsdl:part element="AuthorizationFinishedResponse" name="AuthorizationFinishedResponse"/>
    </wsdl:message>


    <wsdl:message name="PairingFinishedRequest">
        <wsdl:part element="PairingFinishedRequest" name="PairingFinishedRequest"/>
    </wsdl:message>

    <wsdl:message name="PairingFinishedResponse">
        <wsdl:part element="PairingFinishedResponse" name="PairingFinishedResponse"/>
    </wsdl:message>

    <wsdl:portType name="NotificationWsPort">
        <wsdl:operation name="AuthorizationFinished">
            <wsdl:input message="AuthorizationFinishedRequest"/>
            <wsdl:output message="AuthorizationFinishedResponse"/>
        </wsdl:operation>

        <wsdl:operation name="PairingFinished">
            <wsdl:input message="PairingFinishedRequest"/>
            <wsdl:output message="PairingFinishedResponse"/>
        </wsdl:operation>
    </wsdl:portType>


    <wsdl:binding name="NotificationWsSOAP" type="NotificationWsPort">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

        <wsdl:operation name="AuthorizationFinished">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>

        <wsdl:operation name="PairingFinished">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="NotificationWs">
        <wsdl:port binding="NotificationWsSOAP" name="NotificationWsSOAP">
            <soap:address location="http://TO-BE-SPECIFIED/ib/NotificationWs/"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>
