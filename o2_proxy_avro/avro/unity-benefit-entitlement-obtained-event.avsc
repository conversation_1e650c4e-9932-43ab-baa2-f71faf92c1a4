{"type": "record", "name": "UnityBenefitEntitlementObtainedEvent", "namespace": "cz.airbank.o2.proxy.unity.benefit", "fields": [{"name": "unityBenefitEntitlementId", "type": "string"}, {"name": "generalContractOwnerCuid", "type": "long"}, {"name": "amount", "type": {"type": "bytes", "logicalType": "decimal", "precision": 38, "scale": 10}}, {"name": "benefitCode", "type": "string"}, {"name": "entitledFrom", "type": {"type": "string", "logicalType": "iso-local-date"}}, {"name": "entitledTo", "type": {"type": "string", "logicalType": "iso-local-date"}}, {"name": "evaluatedMonth", "type": {"type": "string", "logicalType": "iso-local-date"}}, {"name": "evaluatedOn", "type": {"type": "string", "logicalType": "iso-zoned-date-time"}}]}