<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:commonFault="http://airbank.cz/common/ws/fault"
                  xmlns="http://airbank.cz/sasadprealtime/ws/customeridentification"
                  targetNamespace="http://airbank.cz/sasadprealtime/ws/customeridentification">

    <wsdl:types>
        <xsd:schema targetNamespace="http://airbank.cz/sasadprealtime/ws/customeridentification">
            <xsd:include schemaLocation="../xsd/CustomerIdentificationWS.xsd"/>
        </xsd:schema>
        <xsd:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xsd:include schemaLocation="../xsd/commonSoapFault.xsd"/>
        </xsd:schema>
    </wsdl:types>

    <wsdl:message name="GetCustomerHashRequest">
        <wsdl:part element="GetCustomerHashRequest" name="GetCustomerHashRequest"/>
    </wsdl:message>
    <wsdl:message name="GetCustomerHashResponse">
        <wsdl:part element="GetCustomerHashResponse" name="GetCustomerHashResponse"/>
    </wsdl:message>
    <wsdl:message name="GetCustomerHashFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetCustomerHashFault"/>
    </wsdl:message>

    <wsdl:portType name="CustomerIdentificationPort">
        <wsdl:operation name="GetCustomerHash">
            <wsdl:documentation>
                Returns hash for CUID
            </wsdl:documentation>
            <wsdl:input message="GetCustomerHashRequest"/>
            <wsdl:output message="GetCustomerHashResponse"/>
            <wsdl:fault name="GetCustomerHashFault" message="GetCustomerHashFault"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="CustomerIdentificationBinding" type="CustomerIdentificationPort">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

        <wsdl:operation name="GetCustomerHash">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="CustomerIdentificationService">
        <wsdl:documentation>
            Customer Identification service
        </wsdl:documentation>
        <wsdl:port name="CustomerIdentificationPort" binding="CustomerIdentificationBinding">
            <soap:address location="http://localhost:8000/ws"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>