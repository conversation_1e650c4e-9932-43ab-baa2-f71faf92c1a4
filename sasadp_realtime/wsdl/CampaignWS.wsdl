<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:fault="http://airbank.cz/common/ws/fault"
                  xmlns="http://airbank.cz/sasadprealtime/ws/campaign"
                  targetNamespace="http://airbank.cz/sasadprealtime/ws/campaign">

    <wsdl:types>
        <xsd:schema targetNamespace="http://airbank.cz/sasadprealtime/ws/campaign">
            <xsd:include schemaLocation="../xsd/CampaignWS.xsd"/>
        </xsd:schema>
        <xsd:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xsd:include schemaLocation="../xsd/commonSoapFault.xsd"/>
        </xsd:schema>
    </wsdl:types>

    <wsdl:message name="ReceiveCommunicationEntityChangesRequest">
        <wsdl:part element="ReceiveCommunicationEntityChangesRequest" name="ReceiveCommunicationEntityChangesRequest"/>
    </wsdl:message>
    <wsdl:message name="ReceiveCommunicationEntityChangesResponse">
        <wsdl:part element="ReceiveCommunicationEntityChangesResponse" name="ReceiveCommunicationEntityChangesResponse"/>
    </wsdl:message>
    <wsdl:message name="ReceiveCommunicationEntityChangesFaultMessage">
        <wsdl:part element="fault:CoreFaultElement" name="ReceiveCommunicationEntityChangesFault"/>
    </wsdl:message>

    <wsdl:message name="RevokeEntitiesRequest">
        <wsdl:part element="RevokeEntitiesRequest" name="RevokeEntitiesRequest"/>
    </wsdl:message>
    <wsdl:message name="RevokeEntitiesResponse">
        <wsdl:part element="RevokeEntitiesResponse" name="RevokeEntitiesResponse"/>
    </wsdl:message>
    <wsdl:message name="RevokeEntitiesFaultMessage">
        <wsdl:part element="fault:CoreFaultElement" name="RevokeEntitiesFault"/>
    </wsdl:message>


    <wsdl:portType name="CampaignPort">
        <wsdl:operation name="ReceiveCommunicationEntityChanges">
            <wsdl:documentation>
                Receive communication entity changes and resend them to kafka
            </wsdl:documentation>
            <wsdl:input message="ReceiveCommunicationEntityChangesRequest" name="ReceiveCommunicationEntityChangesRequest"/>
            <wsdl:output message="ReceiveCommunicationEntityChangesResponse" name="ReceiveCommunicationEntityChangesResponse"/>
            <wsdl:fault message="ReceiveCommunicationEntityChangesFaultMessage" name="ReceiveCommunicationEntityChangesFault"/>
        </wsdl:operation>
        <wsdl:operation name="RevokeEntities">
            <wsdl:documentation>
                Revoke realtime campaign entities coming from SAS360
            </wsdl:documentation>
            <wsdl:input message="RevokeEntitiesRequest" name="RevokeEntitiesRequest"/>
            <wsdl:output message="RevokeEntitiesResponse" name="RevokeEntitiesResponse"/>
            <wsdl:fault message="RevokeEntitiesFaultMessage" name="RevokeEntitiesFault"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="CampaignBinding" type="CampaignPort">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

        <wsdl:operation name="ReceiveCommunicationEntityChanges">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ReceiveCommunicationEntityChangesFault">
                <soap:fault name="ReceiveCommunicationEntityChangesFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="RevokeEntities">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="RevokeEntitiesFault">
                <soap:fault name="RevokeEntitiesFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="CampaignService">
        <wsdl:documentation>
            Campaign service
        </wsdl:documentation>
        <wsdl:port name="CampaignPort" binding="CampaignBinding">
            <soap:address location="http://localhost:8000/ws"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>