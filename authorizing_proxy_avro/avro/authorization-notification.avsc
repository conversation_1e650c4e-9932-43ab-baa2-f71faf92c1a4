{"type": "record", "namespace": "cz.airbank.authorizing.proxy", "name": "AuthMessage", "doc": "Avro message about authorization status. There will be two Kafka messages sent to IB per each authorization. The first will come when the backend operation starts. It will have the BackendOperationStatus.AUTHORIZED. The IB frontend should react by spinning the 'waiting wheel' if it is not spun yet. The second Kafka message will come when the backend operation finishes. It will have the BackendOperationStatus.DONE. The IB frontend should react by fetching the result of the operation from IB backend.", "fields": [{"name": "authId", "type": "string", "doc": "ID of the authorization this Kafka message is about."}, {"name": "backendOperationStatus", "type": {"type": "enum", "name": "BackendOperationStatus", "doc": "Status of the backend operation progress.", "symbols": ["AUTHORIZED", "DONE", "SWT_ONLINE_ATTEMPT_DONE", "FORCE_LOGOUT"]}, "doc": "Status of the backend operation progress."}, {"name": "channel", "type": "string", "doc": "Channel of authorized operation initialization."}]}