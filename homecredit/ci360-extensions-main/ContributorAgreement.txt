Contributor Agreement

Version 1.1

Contributions to this software are accepted only when they are
properly accompanied by a Contributor Agreement. The Contributor
Agreement for this software is the Developer's Certificate of Origin
1.1 (DCO) as provided with and required for accepting contributions
to the Linux kernel.

In each contribution proposed to be included in this software, the
developer must include a "sign-off" that denotes consent to the
terms of the Developer's Certificate of Origin.  The sign-off is
a line of text in the description that accompanies the change,
certifying that you have the right to provide the contribution
to be included.  For changes provided in source code control (for
example, via a Git pull request) the sign-off must be included in
the commit message in source code control.  For changes provided
in email or issue tracking, the sign-off must be included in the
email or the issue, and the sign-off will be incorporated into the
permanent commit message if the contribution is accepted into the
official source code.

If you can certify the below:

        Dev<PERSON>per's Certificate of Origin 1.1

        By making a contribution to this project, I certify that:

        (a) The contribution was created in whole or in part by me and I
            have the right to submit it under the open source license
            indicated in the file; or

        (b) The contribution is based upon previous work that, to the best
            of my knowledge, is covered under an appropriate open source
            license and I have the right under that license to submit that
            work with modifications, whether created in whole or in part
            by me, under the same open source license (unless I am
            permitted to submit under a different license), as indicated
            in the file; or

        (c) The contribution was provided directly to me by some other
            person who certified (a), (b) or (c) and I have not modified
            it.

        (d) I understand and agree that this project and the contribution
            are public and that a record of the contribution (including all
            personal information I submit with it, including my sign-off) is
            maintained indefinitely and may be redistributed consistent with
            this project or the open source license(s) involved.

then you just add a line saying

        Signed-off-by: Random J Developer <<EMAIL>>

using your real name (sorry, no pseudonyms or anonymous contributions.)
