# Standard agent config - CI360 settings
ci360.gatewayHost=extapigwservice-demo.cidemo.sas.com
ci360.tenantID=
ci360.clientSecret=

# Standard agent config 
agent.keepaliveInterval=300
agent.runInteractiveConsole=false
agent.monitorOutputInterval=600
agent.maxRetries=3
agent.retryInterval=300
agent.lastEventOutput=


# Custom agent settings here
scg.consumerKey=
scg.consumerSecret=
scg.accessToken=
scg.apiUrl=https://api.syniverse.com
scg.defaultSender=
scg.defaultChannel=SMS
scg.message.encoding=UTF-8

agent.event.statusMethod=WEBHOOK
agent.http.port=8080
agent.http.webhookContextRoot=/scgWebhook
agent.messageStatusInterval=60
agent.cache.messageCacheName=cache1
agent.cache.cacheDirectory=cacheData
agent.cache.messageCacheHeap=5000
agent.cache.messageCacheOffHeapMB=0
agent.cache.messageCacheDiskMB=10
agent.cache.messageCacheTTLMin=60
agent.event.statusEventNames=DELIVERED:SMS Delivered,FAILED:SMS Failed,CLICKTHRU:SMS Clicked
agent.event.moEventName=Inbound SMS
agent.event.moIdentityField=subject_id
agent.event.moFromField=from_address
agent.event.moMessageBodyField=message_body
agent.event.moMessageBodyUpperCase=false
agent.creative.format=JSON
agent.event.TTL=0
