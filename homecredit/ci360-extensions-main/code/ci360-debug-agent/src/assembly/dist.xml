<assembly xmlns="http://maven.apache.org/ASSEMBLY/2.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/ASSEMBLY/2.0.0 http://maven.apache.org/xsd/assembly-2.0.0.xsd">
	<id>dist</id>
	<formats>
		<format>zip</format>
	</formats>
	<fileSets>
		<fileSet>
			<directory>${project.basedir}</directory>
			<outputDirectory>./</outputDirectory>
			<includes>
				<include>README*</include>
				<include>logback.xml</include>
			</includes>
		</fileSet>
		<fileSet>
			<directory>${project.build.directory}</directory>
			<outputDirectory>./</outputDirectory>
			<includes>
				<include>*.jar</include>
			</includes>
		</fileSet>
		<fileSet>
			<directory>${project.build.directory}/dependency</directory>
			<outputDirectory>./dependency</outputDirectory>
		</fileSet>
		<fileSet>
			<directory>${project.basedir}/scripts</directory>
			<outputDirectory>./</outputDirectory>
			<filtered>true</filtered>
		</fileSet>
		<fileSet>
			<directory>${project.basedir}/logs</directory>
			<outputDirectory>./logs</outputDirectory>
			<excludes>
				<exclude>*/**</exclude>
			</excludes>
		</fileSet>
	</fileSets>
</assembly>
