body {
  background-color: #012036;
  color           : white;
  scroll-behavior : smooth;
}

.body {
  background-color: #012036;
}

html {
  overflow : auto;
  min-width: 625px;
}

.header-container {
  height: 30px;
}

.header-menu {
  font-size       : small;
  text-align      : right;
  float           : right;
  height          : 25px;
  background-color: #86134e;
  border-top      : 0px !important;
}

.pull-down {
  background-color: #02385f;
  margin-right    : 10px;
  height          : 25px;
  border          : 1px solid #034574;

  padding-left       : 5px;
  padding-right      : 5px;
  position           : relative;
  border-top         : 0px !important;
  transition-property: height, overflow-y;
  transition-duration: 1s;
  transition-delay   : 0.5s;
  z-index            : 1;
  overflow           : hidden;
  float              : none;
  width              : auto
}

.pull-down:hover {
  height    : 200px;
  overflow-y: auto;

}

.header-menu-item {
  cursor: pointer;
  width : 25%;
  height: 100%;
  float : left;

}

.header-menu-item>img {
  width : 20px;
  height: 20px;
  margin: 2px;
}

.header-menu-item>img:hover {
  width : 21px;
  height: 21px;
  margin: 1px;
}

.header-menu-item-selected {
  width           : 25%;
  height          : 100%;
  background-color: #9471ff;
  border          : 1px solid #9471ff;

}

.header-menu-item-selected>img {
  width : 20px;
  height: 20px;
  margin: 1px;
}

.event-stream-main {
  border: 0px solid red;
  ;
}

.settings-main {
  padding: 10px;
}

.debugging-main {
  padding: 10px;
}

.border_red {
  border          : 2px solid rgb(255, 92, 92) !important;
  background-color: #ffdddd !important;
}

.example {
  font-size : smaller;
  font-style: italic;
}

.console {
  width     : 100%;
  font-size : smaller;
  max-height: 250px;
  overflow-y: scroll;
  overflow-x: hidden;
  margin-top: 5px;
  display   : none;
}

.warning {
  color    : orangered;
  font-size: smaller;
}

.round {

  float    : left;
  margin   : 0px;
  font-size: x-small;
  cursor   : pointer;
  padding  : 0px;
}

.left-margin {
  margin-left: 3px;
}

.filter-details {
  float      : left;
  margin-left: 3px;
  padding-top: 8px;
}

.filter-details-network {
  float      : left;
  margin-left: 3px;
  padding-top: -2px;
}

#gateway_info {
  display: none;
}

.profile-table-container {
  float: left;
  width: 100%;

}

.profile-table {
  border: 0px;
  width : 100%;
}

.profile-table th {
  font-weight     : normal;
  background-color: #0a2b42;
  border-bottom   : 1px solid #1b6194;
  border-top      : 1px solid #1b6194;
  padding         : 5px;
}

.profile-table td {
  font-weight  : normal;
  border-bottom: 1px solid #1b6194;
  padding      : 2px;
  padding-left : 5px;

}

.profile-table tr {
  transition-property: color;
  transition-duration: 1s;
  transition-delay   : 0s;
}

.profile-table tr:hover {
  color: #ff9800;
}

.profile-table tbody tr:last-child {
  display: none;
}

.profile-table tbody tr:first-child {
  display: table-row;
}

.accordion {

  transition: 0.4s;
  cursor    : pointer;
  padding   : 5px;
  border    : 0px;
  width     : 100%;
  text-align: left;
  margin-top: 1em;
}

.active,
.accordion:hover,
.accordion:focus {

  color  : #ff9800 !important;
  cursor : pointer;
  padding: 10px;

}

.panel {
  padding            : 0 18px;
  max-height         : 0px;
  display            : none;
  transition-property: max-height, overflow-y, display;
  transition-duration: 1s;
  transition-delay   : 0.2s;
  
  z-index            : 1;
  overflow           : hidden;
}

.panel-active {
  display   : block;
  max-height: 5000px;
}

.event-stream-settings {
  background-color: auto;
}

.plugin-settings {
  background-color: auto;
}

.tag-settings {
  background-color: auto;
}

.setting-sub-section-1 {
  background-color: #012f50;
  color           : white;
}

.setting-sub-section-2 {
  background-color: #073c61;
  color           : white;
}

.setting-sub-section-3 {
  background-color: #0f4972;
  color           : white;
}

.meta-link-txt {
  cursor      : pointer;
  padding-left: 3px;
}

.status-feedback {
  position           : fixed;
  bottom             : 0;
  width              : 100%;
  max-height         : 0px;
  overflow-y         : hidden;
  text-align         : center;
  padding            : 0px;
  background         : linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
  background-size    : 400% 400%;
  animation          : gradient 15s ease infinite;
  transition-property: max-height, overflow-y, padding;
  transition-duration: 1s;
  transition-delay   : 0.2s;

}

.status-feedback-active {
  max-height: 20em;
  overflow-y: auto;
  padding   : .5em;
}

.status-version {
  position           : fixed;
  bottom             : 0;
  width              : 100%;
  max-height         : 0px;
  overflow-y         : hidden;
  text-align         : center;
  padding            : 0px;
  background         : linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
  background-size    : 400% 400%;
  animation          : gradient 15s ease infinite;
  transition-property: max-height, overflow-y, padding;
  transition-duration: 1s;
  transition-delay   : 0.2s;

}

.status-version-active {
  max-height: 20em;
  overflow-y: auto;
  padding   : .5em;
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}
.output-line {
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: monospace;
  margin: 2px 0;
}
.output-line span:last-child {
  color: rgb(243, 243, 243);
  float: right;
  margin-right: 0.5rem;
}