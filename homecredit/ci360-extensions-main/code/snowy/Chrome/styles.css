html {
    height: 100%;
    width: 100%;
    scroll-behavior: smooth;
}

body {
    background-color: white;
    color: black;
    font-family: Arial, Helvetica, sans-serif;
    font-size: smaller;
    margin: 0px;
}


td.details-control {
    background: url('images/details_open.png') no-repeat center center;
    cursor: pointer;
    width: 10px;
}

tr.shown td.details-control {
    background: url('images/details_close.png') no-repeat center center;
}

pre {
    outline: 1px solid #ccc;
    padding: 5px;
    margin: 5px;
}

.string {
    color: green;
}

.number {
    color: darkorange;
}

.boolean {
    color: blue;
}

.null {
    color: magenta;
}

.key {
    color: red;
}

