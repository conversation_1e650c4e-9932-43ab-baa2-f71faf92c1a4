/*
Copyright © 2021, SAS Institute Inc., Cary, NC, USA.  All Rights Reserved.
SPDX-License-Identifier: Apache-2.0
*/

DROP TABLE <PREFIX>_ab_test_path_assignment;
DROP TABLE <PREFIX>_activity_conversion;
DROP TABLE <PREFIX>_activity_flow_in;
DROP TABLE <PREFIX>_activity_start;
DROP TABLE <PREFIX>_asset_details;
DROP TABLE <PREFIX>_asset_details_custom_prop;
DROP TABLE <PREFIX>_asset_folder_details;
DROP TABLE <PREFIX>_asset_rendition_details;
DROP TABLE <PREFIX>_asset_revision;
DROP TABLE <PREFIX>_business_process_details;
DROP TABLE <PREFIX>_cart_activity_details;
DROP TABLE <PREFIX>_cc_budget_breakup;
DROP TABLE <PREFIX>_cc_budget_breakup_ccbdgt;
DROP TABLE <PREFIX>_commitment_details;
DROP TABLE <PREFIX>_commitment_line_items;
DROP TABLE <PREFIX>_commitment_line_items_ccbdgt;
DROP TABLE <PREFIX>_contact_history;
DROP TABLE <PREFIX>_conversion_milestone;
DROP TABLE <PREFIX>_custom_attributes;
DROP TABLE <PREFIX>_custom_events;
DROP TABLE <PREFIX>_custom_events_ext;
DROP TABLE <PREFIX>_daily_usage;
DROP TABLE <PREFIX>_data_view_details;
DROP TABLE <PREFIX>_direct_contact;
DROP TABLE <PREFIX>_document_details;
DROP TABLE <PREFIX>_email_bounce;
DROP TABLE <PREFIX>_email_click;
DROP TABLE <PREFIX>_email_complaint;
DROP TABLE <PREFIX>_email_open;
DROP TABLE <PREFIX>_email_optout;
DROP TABLE <PREFIX>_email_optout_details;
DROP TABLE <PREFIX>_email_reply;
DROP TABLE <PREFIX>_email_send;
DROP TABLE <PREFIX>_email_view;
DROP TABLE <PREFIX>_external_event;
DROP TABLE <PREFIX>_fiscal_cc_budget;
DROP TABLE <PREFIX>_form_details;
DROP TABLE <PREFIX>_goal_details;
DROP TABLE <PREFIX>_goal_details_ext;
DROP TABLE <PREFIX>_identity_attributes;
DROP TABLE <PREFIX>_identity_map;
DROP TABLE <PREFIX>_impression_delivered;
DROP TABLE <PREFIX>_impression_spot_viewable;
DROP TABLE <PREFIX>_in_app_failed;
DROP TABLE <PREFIX>_in_app_message;
DROP TABLE <PREFIX>_in_app_send;
DROP TABLE <PREFIX>_in_app_targeting_request;
DROP TABLE <PREFIX>_invoice_details;
DROP TABLE <PREFIX>_invoice_line_items;
DROP TABLE <PREFIX>_invoice_line_items_ccbdgt;
DROP TABLE <PREFIX>_md_activity;
DROP TABLE <PREFIX>_md_activity_abtestpath;
DROP TABLE <PREFIX>_md_activity_custom_prop;
DROP TABLE <PREFIX>_md_activity_node;
DROP TABLE <PREFIX>_md_activity_x_activity_node;
DROP TABLE <PREFIX>_md_activity_x_task;
DROP TABLE <PREFIX>_md_asset;
DROP TABLE <PREFIX>_md_bu;
DROP TABLE <PREFIX>_md_business_context;
DROP TABLE <PREFIX>_md_cost_category;
DROP TABLE <PREFIX>_md_costcenter;
DROP TABLE <PREFIX>_md_creative;
DROP TABLE <PREFIX>_md_creative_custom_prop;
DROP TABLE <PREFIX>_md_creative_x_asset;
DROP TABLE <PREFIX>_md_cust_attrib;
DROP TABLE <PREFIX>_md_custattrib_table_values;
DROP TABLE <PREFIX>_md_dataview;
DROP TABLE <PREFIX>_md_dataview_x_event;
DROP TABLE <PREFIX>_md_event;
DROP TABLE <PREFIX>_md_fiscal_period;
DROP TABLE <PREFIX>_md_grid_attr_defn;
DROP TABLE <PREFIX>_md_message;
DROP TABLE <PREFIX>_md_message_custom_prop;
DROP TABLE <PREFIX>_md_message_x_creative;
DROP TABLE <PREFIX>_md_object_type;
DROP TABLE <PREFIX>_md_occurrence;
DROP TABLE <PREFIX>_md_picklist;
DROP TABLE <PREFIX>_md_rtc;
DROP TABLE <PREFIX>_md_segment;
DROP TABLE <PREFIX>_md_segment_custom_prop;
DROP TABLE <PREFIX>_md_segment_map;
DROP TABLE <PREFIX>_md_segment_map_custom_prop;
DROP TABLE <PREFIX>_md_segment_map_x_segment;
DROP TABLE <PREFIX>_md_segment_x_event;
DROP TABLE <PREFIX>_md_spot;
DROP TABLE <PREFIX>_md_target_assist;
DROP TABLE <PREFIX>_md_task;
DROP TABLE <PREFIX>_md_task_custom_prop;
DROP TABLE <PREFIX>_md_task_x_creative;
DROP TABLE <PREFIX>_md_task_x_dataview;
DROP TABLE <PREFIX>_md_task_x_event;
DROP TABLE <PREFIX>_md_task_x_message;
DROP TABLE <PREFIX>_md_task_x_segment;
DROP TABLE <PREFIX>_md_task_x_spot;
DROP TABLE <PREFIX>_md_vendor;
DROP TABLE <PREFIX>_md_wf_process_def;
DROP TABLE <PREFIX>_md_wf_process_def_attr_grp;
DROP TABLE <PREFIX>_md_wf_process_def_categories;
DROP TABLE <PREFIX>_md_wf_process_def_task_assg;
DROP TABLE <PREFIX>_md_wf_process_def_tasks;
DROP TABLE <PREFIX>_media_activity_details;
DROP TABLE <PREFIX>_media_details;
DROP TABLE <PREFIX>_media_details_ext;
DROP TABLE <PREFIX>_mobile_focus_defocus;
DROP TABLE <PREFIX>_mobile_spots;
DROP TABLE <PREFIX>_monthly_usage;
DROP TABLE <PREFIX>_notification_failed;
DROP TABLE <PREFIX>_notification_opened;
DROP TABLE <PREFIX>_notification_send;
DROP TABLE <PREFIX>_notification_targeting_request;
DROP TABLE <PREFIX>_order_details;
DROP TABLE <PREFIX>_order_summary;
DROP TABLE <PREFIX>_outbound_system;
DROP TABLE <PREFIX>_page_details;
DROP TABLE <PREFIX>_page_details_ext;
DROP TABLE <PREFIX>_page_errors;
DROP TABLE <PREFIX>_planning_hierarchy_defn;
DROP TABLE <PREFIX>_planning_info;
DROP TABLE <PREFIX>_planning_info_custom_prop;
DROP TABLE <PREFIX>_product_views;
DROP TABLE <PREFIX>_promotion_displayed;
DROP TABLE <PREFIX>_promotion_used;
DROP TABLE <PREFIX>_response_history;
DROP TABLE <PREFIX>_search_results;
DROP TABLE <PREFIX>_search_results_ext;
DROP TABLE <PREFIX>_session_details;
DROP TABLE <PREFIX>_session_details_ext;
DROP TABLE <PREFIX>_spot_clicked;
DROP TABLE <PREFIX>_spot_requested;
DROP TABLE <PREFIX>_tag_details;
DROP TABLE <PREFIX>_visit_details;
DROP TABLE <PREFIX>_wf_process_details;
DROP TABLE <PREFIX>_wf_process_details_custom_prop;
DROP TABLE <PREFIX>_wf_process_tasks;
DROP TABLE <PREFIX>_wf_tasks_user_assignment;
