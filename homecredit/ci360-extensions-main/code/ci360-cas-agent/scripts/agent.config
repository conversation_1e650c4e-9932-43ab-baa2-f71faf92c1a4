# Standard agent config - CI360 settings
ci360.gatewayHost=
ci360.tenantID=
ci360.clientSecret=

# Standard agent config 
agent.keepaliveInterval=300000
agent.runInteractiveConsole=false
agent.monitorOutputInterval=600000
agent.batchInterval=5000
agent.lastEventOutput=

# Custom agent settings here
cas.host=
cas.port=5570
cas.username=
cas.password=
cas.tableName=CI360EVENTS
cas.caslib=
cas.createTable=true
cas.tableColumns=[{"name":"timestamp", "type":"VARCHAR"},{"name":"eventName", "type":"VARCHAR"},{"name":"channelType", "type":"VARCHAR"},{"name":"identityId", "type":"VARCHA<PERSON>"},{"name":"session", "type":"VARCHA<PERSON>"},{"name":"page_title", "type":"VARCHA<PERSON>"},{"name":"spot_id", "type":"VARCHAR"}]
cas.commitRowCount=1000
cas.maxBatchSize=0
