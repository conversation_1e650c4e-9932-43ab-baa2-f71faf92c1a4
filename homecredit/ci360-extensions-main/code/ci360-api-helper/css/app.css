body {
    line-height: 1;
}


.select2-container .select2-selection--single {
    height: 33px !important;
}

.select2-selection__rendered {
    line-height: 32px !important;
    max-height: 500px !important;
}

.select2-container--default .select2-results>.select2-results__options {
    max-height: 400px !important;
}

.select2-results {
    height: 400px;
}


.mybtn, .btn {
    cursor: pointer;
}

.input-group-addon {
        background-color: rgba(0,0,0,.15) !important;
        border: 0px !important;
        color: black !important;
}

.greycolor{
    color: lightgrey;
}

.collapsing{
    position:relative;
    height:0;
    overflow:hidden;
    /*transition:height .35s ease*/
    transition:height .0001s !important;
}

.container {
    max-width: 99.99% !important;
}

a.nav-link {
    color: #3a7999;
}

.modal-dialog {
    max-width: 60% !important;
}

label {
    margin-top: 4px;
}

dt {
    font-size: 0.8rem !important;
    font-weight: 740 !important;
}

.card-header {
    padding: .1rem 1rem !important;
}

.marginbottom {
    margin-bottom: 0px;
    min-height: 32px;
}

.title {
	color: #f5f5f5;
	text-align:center
}

.control-label {
    padding-top: 5px;
    }

body {
	height: 100%;
	min-height: 100%;
}

.btn-send {
	color: #6a6f6f;
}

.btn-primary {
    background-color: #468baf !important;
    border-color: #468baf !important;
}

.btn-danger {
    background-color: #e45e54 !important;
    border-color: #e45e54!important;
}

.btn-default {
    border-color: #b6b6b6 !important;
}

#btn_GetExternalEvents {
    height: 20px;
    font-size: 10px;
    padding: 3px;
}

.alert-primary {
    color: #e4ebee !important;
    background-color: #468baf !important;
    border-color: #468baf !important;
}

.whiteBox {
    background-color: #fff;
    border: 1px solid rgba(0,0,0,.15);
    border-radius: 5px;
    box-shadow: 0 5px 0 rgba(255,255,255,0.7) inset, 0 4px 4px rgba(0,0,0,0.2);
    margin: 1em auto;
    margin-top: -1em;
    padding-top: 1.5em;
    padding-left: 1.5em;
    padding-right: 1.5em;
    padding-bottom: .5em;
    /*max-width: 300px;
    width: 50%;*/
    font-family: 'AvenirNextRegular', Helvetica, Arial, sans-serif;
}

.bg {
	width: 100%;
	height: 100%;
	background: -webkit-radial-gradient(#5fb0da, #154158); /* Safari 5.1 to 6.0 */
  	background: -o-radial-gradient(#5fb0da, #154158); /* For Opera 11.6 to 12.0 */
  	background: -moz-radial-gradient(#5fb0da, #154158); /* For Firefox 3.6 to 15 */
  	background: radial-gradient(#5fb0da, #154158); /* Standard syntax */
  	/*color: #f5f5f5;*/
}

.btn-submit {
    border-radius: 5px;
    border: 1px solid rgb( 186, 186, 186 );
    color: #fff;
    max-width: 100%;
    text-align: center;
    background: linear-gradient(#287eab, #236f96);
    padding: 6px 12px;
}

.grey-box {
    border: 1px solid gainsboro;
    background: #efefef;
    border-radius: .2rem;
}

.eventBtn {
    margin-top: 5px;
}