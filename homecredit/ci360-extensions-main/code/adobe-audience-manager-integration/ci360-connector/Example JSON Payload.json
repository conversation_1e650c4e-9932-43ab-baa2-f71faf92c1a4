{
  "guid": "a217d4c4-34f9-460a-afb7-0b7ade117c7e",
  "apiEventKey": null,
  "eventDesignedId": "9155c61d-dd69-4fbb-9b49-c6f73490b900",
  "eventDesignedName": "Facebook: Event Pixel",
  "eventName": "load",
  "customName": "fb_profile",
  "eventType": "customEvent",
  "sessionId": "e105513fe064112b551dccc3",
  "channelId": "6ffeb2f866cb3044264262ab",
  "channelType": "web",
  "ipAddress": null,
  "date": {
    "generatedTimestamp": 1597647111774,
    "sentTimestamp": null,
    "receivedTimestamp": 1597647111774,
    "utcOffset": 28800000
  },
  "externalTenantId": "abcdef0123456789",
  "internalTenantId": 0000,
  "identityId": "2d25b302-6ee9-35fc-ba41-8c3033f90266",
  "page": {
    "loadId": "9df574667a3a4c587dca6ec7",
    "viewSequenceNum": 0,
    "uri": null,
    "pageTitle": null,
    "referrerUri": null,
    "pageCategoryMap": null,
    "pageLoadTime": null,
    "pageId": null,
    "pageName": null,
    "errorDescription": null,
    "errorLocation": null,
    "pageLanguage": null,
    "charset": null,
    "contentSize": null,
    "imagesCompleteTime": null,
    "actions": null,
    "pageActiveTimeMillis": null,
    "pageViewTimeMillis": null
  },
  "ipInfo": null,
  "browser": null,
  "visit": {
    "landingPageTitle": null,
    "landingPageUrl": null,
    "referrer": null,
    "responseCode": null,
    "searchEngine": null,
    "searchEngineDomain": null,
    "searchEngineTerm": null,
    "subjectId": null,
    "trafficSourceName": null,
    "trafficSourceType": null,
    "trafficSourcePlacement": null,
    "trafficSourceCreative": null,
    "trafficSourceTrackingCode": null,
    "visitId": "930981fa7e3a4c58623bc80d",
    "visitorGroup": null,
    "visitorState": "returning",
    "visitSequenceNum": null
  },
  "domain": null,
  "properties": {
    "fb_em": "<EMAIL>",
    "fbq": "1231231151234",
    "fb_ts": 1597647111
  },
  "app": {
    "sdkName": null,
    "sdkVersion": null,
    "appName": null,
    "appVersion": null,
    "appLanguage": null,
    "appId": null
  },
  "identity": {
    "identityId": "2d25b302-6ee9-35fc-ba41-8c3033f90266",
    "identityType": null,
    "identitySource": null,
    "identityEventName": null,
    "identityAttribute": null,
    "identityAssociation": null,
    "userId": null,
    "visitId": null,
    "ipAddress": null,
    "sessionId": null,
    "visitorId": null,
    "loginEventType": null,
    "loginType": null,
    "loginValue": null
  },
  "geofence": null,
  "deviceInfo": null,
  "serviceProvider": null,
  "stagedOffer": null,
  "beacon": null,
  "obfuscateIpAddress": null,
  "eventCategory": "unifiedAndEngage",
  "eventSource": "eventGen",
  "download": null,
  "eCommerce": null,
  "form": null,
  "internalSearch": null,
  "media": null,
  "product": null,
  "promotion": null,
  "customGroupName": "track",
  "customRevenueDbl": null,
  "extendedCustomEventWithRevenueFlag": false,
  "journey": null,
  "session": null,
  "parentEventUid": null,
  "activity": null,
  "contactResponse": null,
  "contentChange": null,
  "impression": null,
  "email": null,
  "linkTracking": null,
  "trace": {
    "traceId": "",
    "contributingEventGuids": ["9df574667a3a4c587dca6ec7194589443068"],
    "tracePoints": [{
      "tracePointName": "mkt-events-ingest",
      "tracePointIpAddress": "*************",
      "tracePointTimestamp": 1597647114300
    }, {
      "tracePointName": "mkt-events",
      "tracePointIpAddress": "**********",
      "tracePointTimestamp": 1597647114608
    }, {
      "tracePointName": "mkt-eventgen",
      "tracePointIpAddress": "*************",
      "tracePointTimestamp": 1597647114670
    }]
  },
  "trigger": null,
  "ruleVersion": "v2008-2020.***********.32.254"
} 
