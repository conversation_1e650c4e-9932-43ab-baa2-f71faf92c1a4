{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "type": "java",
      "name": "Launch Current File",
      "request": "launch",
      "mainClass": "${file}"
    },
    {
      "type": "java",
      "name": "Launch CustomAgent",
      "request": "launch",
      "mainClass": "com.sas.ci360.agent.CustomAgent",
      "projectName": "ci360-debug-agent",
      "args": ["-DconfigFile= C:/Users/<USER>/OneDrive - SAS/Sudheesh_ext/swMarvel/ci360-snowflake-agent/agent.config"]
    }
  ]
}
