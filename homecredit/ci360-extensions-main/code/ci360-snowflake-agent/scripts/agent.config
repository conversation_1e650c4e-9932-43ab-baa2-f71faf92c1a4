# Standard agent config - CI360 settings
ci360.gatewayHost=extapigwservice-demo.cidemo.sas.com
ci360.tenantID=
ci360.clientSecret=

# Standard agent config 
agent.keepaliveInterval=300000
agent.runInteractiveConsole=false
agent.monitorOutputInterval=600000
agent.lastEventOutput=last_event_json.txt



# Snowflake details
sf.connction_url=************************************************************************
sf.PRIVATE_KEY_FILE=
sf.user=
sf.database=
sf.schema=
sf.warehouse=
sf.role=
sf.table_name=
sf.columns=eventname,sessionId,channelType,timestamp,datahub_id,rowKey,visitor_state,page_title,searchTerm,page_name,EVENT_JSON
