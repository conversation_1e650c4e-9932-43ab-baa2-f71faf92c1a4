{"type": "record", "name": "CampaignLaunched", "namespace": "cz.airbank.sas.campaign", "fields": [{"name": "generalContractNumber", "type": "string"}, {"name": "campaignType", "type": "string"}, {"name": "runId", "type": "long"}, {"name": "entitledFrom", "type": {"type": "string", "logicalType": "iso-zoned-date-time"}}, {"name": "entitledTo", "type": {"type": "string", "logicalType": "iso-zoned-date-time"}}, {"name": "campaignStartedOn", "type": {"type": "string", "logicalType": "iso-zoned-date-time"}}]}