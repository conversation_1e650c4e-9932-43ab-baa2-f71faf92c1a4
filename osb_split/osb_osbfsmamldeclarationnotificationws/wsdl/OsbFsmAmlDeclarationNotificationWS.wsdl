<?xml version="1.0" encoding="UTF-8"?>
    <wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns="http://airbank.cz/osb/ws/amldeclarationnotif" targetNamespace="http://airbank.cz/osb/ws/amldeclarationnotif">

        <wsdl:types>
            <xsd:schema targetNamespace="http://airbank.cz/osb/ws/amldeclarationnotif">
                <xsd:include schemaLocation="../xsd/OsbFsmAmlDeclarationNotificationWS.xsd" />
            </xsd:schema>
        </wsdl:types>

        <wsdl:message name="ProcessCustomerAmlDeclarationEventsRequest">
            <wsdl:part element="ProcessCustomerAmlDeclarationEventsRequest" name="ProcessCustomerAmlDeclarationEventsRequest" />
        </wsdl:message>
        <wsdl:message name="ProcessCustomerAmlDeclarationEventsResponse">
            <wsdl:part element="ProcessCustomerAmlDeclarationEventsResponse" name="ProcessCustomerAmlDeclarationEventsResponse" />
        </wsdl:message>

        <wsdl:portType name="AmlDeclarationNotificationPort">
            <wsdl:operation name="ProcessCustomerAmlDeclarationEvents">
                <wsdl:input message="ProcessCustomerAmlDeclarationEventsRequest" />
                <wsdl:output message="ProcessCustomerAmlDeclarationEventsResponse" />
            </wsdl:operation>
        </wsdl:portType>

        <wsdl:binding name="AmlDeclarationNotificationBinding" type="AmlDeclarationNotificationPort">
            <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />

            <wsdl:operation name="ProcessCustomerAmlDeclarationEvents">
                <soap:operation soapAction="" />
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
            </wsdl:operation>
        </wsdl:binding>

        <wsdl:service name="AmlDeclarationNotificationService">
            <wsdl:port name="AmlDeclarationNotificationPort" binding="AmlDeclarationNotificationBinding">
                <soap:address location="http://localhost:8000/ws" />
            </wsdl:port>
        </wsdl:service>
    </wsdl:definitions>
