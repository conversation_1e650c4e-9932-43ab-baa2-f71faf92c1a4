<?xml version="1.0" encoding="UTF-8"?>
    <xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ad="http://airbank.cz/osb/ws/amldeclaration" targetNamespace="http://airbank.cz/osb/ws/amldeclarationnotif" xmlns="http://airbank.cz/osb/ws/amldeclarationnotif" elementFormDefault="qualified">

        <xsd:import namespace="http://airbank.cz/osb/ws/amldeclaration" schemaLocation="AmlDeclarationWS.xsd" />

        <xsd:element name="ProcessCustomerAmlDeclarationEventsRequest">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="customerAmlDeclarationEvents" type="CustomerAmlDeclarationEvent" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>

        <xsd:element name="ProcessCustomerAmlDeclarationEventsResponse">
            <xsd:complexType/>
        </xsd:element>

        <xsd:complexType name="CustomerAmlDeclarationEvent">
            <xsd:sequence>
                <xsd:element name="id" type="xsd:long" />
                <xsd:element name="customerAmlDeclaration" type="ad:CustomerAmlDeclaration" />
            </xsd:sequence>
        </xsd:complexType>
    </xsd:schema>
