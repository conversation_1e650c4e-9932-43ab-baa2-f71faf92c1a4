<?xml version="1.0" encoding="UTF-8"?>
    <xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" targetNamespace="http://airbank.cz/osb/ws/amldeclaration" xmlns="http://airbank.cz/osb/ws/amldeclaration" elementFormDefault="qualified">

        <xsd:element name="GetCustomerAmlDeclarationRequest">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="cuid" type="xsd:long" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>

        <xsd:element name="GetCustomerAmlDeclarationResponse">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="customerAmlDeclaration" type="CustomerAmlDeclaration" minOccurs="0" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>

        <xsd:element name="SetCustomerAmlDeclarationRequest">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="customerAmlDeclaration" type="CustomerAmlDeclaration" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>

        <xsd:element name="SetCustomerAmlDeclarationResponse">
            <xsd:complexType/>
        </xsd:element>

        <xsd:complexType name="CustomerAmlDeclaration">
            <xsd:sequence>
                <xsd:element name="cuid" type="xsd:long" />
                <xsd:element name="amlDeclaration" type="AmlDeclaration" />
                <xsd:element name="event" type="Event" />
            </xsd:sequence>
        </xsd:complexType>

        <xsd:complexType name="AmlDeclaration">
            <xsd:sequence>
                <xsd:element name="pepOpinion" type="xsd:boolean" />
            </xsd:sequence>
        </xsd:complexType>

        <xsd:complexType name="Event">
            <xsd:sequence>
                <xsd:element name="created" type="xsd:dateTime" />
                <xsd:element name="businessKey" type="xsd:string" />
                <xsd:element name="identification" type="xsd:string" minOccurs="0" />
                <xsd:element name="operatorEmployeeNumber" type="xsd:string" minOccurs="0" />
            </xsd:sequence>
        </xsd:complexType>
    </xsd:schema>
