<?xml version="1.0" encoding="UTF-8"?>
    <wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tns="http://airbank.cz/osb/ws/gpeClients" targetNamespace="http://airbank.cz/osb/ws/gpeClients">

        <wsdl:types>
            <xsd:schema targetNamespace="http://airbank.cz/osb/ws/gpeClients">
                <xsd:include schemaLocation="../xsd/ProcessCustomerNotificationList.xsd" />
            </xsd:schema>
        </wsdl:types>

        <wsdl:message name="PatchClientsRequest">
            <wsdl:part element="tns:PatchClientsRequest" name="PatchClientsRequest" />
        </wsdl:message>
        <wsdl:message name="PatchClientsResponse">
            <wsdl:part element="tns:PatchClientsResponse" name="PatchClientsResponse" />
        </wsdl:message>


        <wsdl:portType name="GpeClientsWS">

            <wsdl:operation name="PatchClients">
                <wsdl:input message="tns:PatchClientsRequest" />
                <wsdl:output message="tns:PatchClientsResponse" />
            </wsdl:operation>

        </wsdl:portType>



        <wsdl:binding name="GpeClientsBinding" type="tns:GpeClientsWS">
            <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />

            <wsdl:operation name="PatchClients">
                <soap:operation soapAction="" />
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
            </wsdl:operation>

        </wsdl:binding>


        <wsdl:service name="GpeClientsWS">
            <wsdl:port binding="tns:GpeClientsBinding" name="GpeClientsBinding">
                <soap:address location="https://https://external-services.np.ab/cps-gw/api" />
            </wsdl:port>
        </wsdl:service>
    </wsdl:definitions>
