<?xml version="1.0" encoding="UTF-8"?>
<!-- $Id: 1eeb452c11d4e15a9044cb505c8dfd1ce8923a2d $ -->
<wsdl:definitions name="EmailManagementWS" targetNamespace="http://osb.airbank.cz/ms/ws/EmailManagement"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tns="http://osb.airbank.cz/ms/ws/EmailManagement"
                  xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <wsdl:types>
    <xsd:schema targetNamespace="http://osb.airbank.cz/ms/ws/EmailManagement">
      <xsd:include schemaLocation="osbEmailManagementWS.xsd"/>
    </xsd:schema>
  </wsdl:types>
  <wsdl:message name="sendEmailRequest">
    <wsdl:part element="tns:sendEmailRequest" name="sendEmailRequest"/>
  </wsdl:message>
  <wsdl:message name="sendEmailResponse">
    <wsdl:part element="tns:sendEmailResponse" name="sendEmailResponse"/>
  </wsdl:message>
  <wsdl:message name="sendEmailFault">
    <wsdl:part name="sendEmailFault" element="tns:sendEmailFault"/>
  </wsdl:message>
  <wsdl:portType name="EmailManagementWS">
    <wsdl:operation name="sendEmail">
      <wsdl:input message="tns:sendEmailRequest" name="sendEmailRequest"/>
      <wsdl:output message="tns:sendEmailResponse" name="sendEmailResponse"/>
      <wsdl:fault message="tns:sendEmailFault" name="sendEmailFault"/>
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="EmailManagementWSSOAP" type="tns:EmailManagementWS">
    <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="sendEmail">
      <soap:operation soapAction=""/>
      <wsdl:input name="sendEmailRequest">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="sendEmailResponse">
        <soap:body use="literal"/>
      </wsdl:output>
      <wsdl:fault name="sendEmailFault">
        <soap:fault use="literal" name="sendEmailFault"/>
      </wsdl:fault>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="EmailManagementWS">
    <wsdl:port binding="tns:EmailManagementWSSOAP" name="EmailManagementWSSOAP">
      <soap:address location="http://www.example.org/"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>
