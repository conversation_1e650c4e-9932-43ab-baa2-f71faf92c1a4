<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- $Id: d2dfa3a484e5816bf32b0a2ad7674a24d2d025e4 $ -->
<xsd:schema targetNamespace="http://osb.airbank.cz/ms/ws/core" xmlns:xsd="http://www.w3.org/2001/XMLSchema"
	xmlns:tns="http://osb.airbank.cz/ms/ws/core" elementFormDefault="qualified">


	<xsd:complexType name="MUID">
		<xsd:annotation>
			<xsd:documentation>Identificaion of the message</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="idMessage" type="xsd:string" minOccurs="1" maxOccurs="1">
				<xsd:annotation>
					<xsd:documentation>ID of the message in the channel
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="system" type="xsd:string" minOccurs="1" maxOccurs="1">
				<xsd:annotation>
					<xsd:documentation>Identification of the system that keeps the
						message?
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="channel" type="xsd:string" minOccurs="1" maxOccurs="1">
				<xsd:annotation>
					<xsd:documentation>Communication channel</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>

	<xsd:complexType name="result">
		<xsd:annotation>
			<xsd:documentation> this element contains return values for send email
				methods
			</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="resultCode" type="tns:resultCodeType" minOccurs="1" maxOccurs="1" />
			<xsd:element name="failedMessageList" minOccurs="0" maxOccurs="1">
				<xsd:annotation>
					<xsd:documentation>List of messages that were not accepted by message server
					</xsd:documentation>
				</xsd:annotation>
				<xsd:complexType>
					<xsd:sequence>
						<xsd:element name="failedMessage" type="tns:failedMessageType" minOccurs="1" maxOccurs="unbounded" />
					</xsd:sequence>
				</xsd:complexType>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>

	<xsd:simpleType name="resultCodeType">
		<xsd:restriction base="xsd:string">
			<xsd:annotation>
				<xsd:documentation>Values - meaning:
					0 - all messages were accepted by
					Message Server
					1 - all messages were
					rejected
					(service unavailable)
					2 -
					some of the messages were not accepted because of an error
				</xsd:documentation>
			</xsd:annotation>
			<xsd:enumeration value="0" />
			<xsd:enumeration value="1" />
			<xsd:enumeration value="2" />
		</xsd:restriction>
	</xsd:simpleType>

	<xsd:complexType name="failedMessageType">
		<xsd:sequence>
			<xsd:element name="MUID" type="tns:MUID" minOccurs="1" maxOccurs="1">
				<xsd:annotation>
					<xsd:documentation>Identification of error message
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="errorMessage" type="xsd:string" minOccurs="1" maxOccurs="1">
				<xsd:annotation>
					<xsd:documentation>Human-readable description of the error
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
</xsd:schema>
