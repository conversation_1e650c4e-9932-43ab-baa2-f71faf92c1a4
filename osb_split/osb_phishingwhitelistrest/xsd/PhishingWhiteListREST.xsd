<?xml version="1.0" encoding="UTF-8"?>
    <xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:jxb="http://java.sun.com/xml/ns/jaxb" targetNamespace="http://airbank.cz/osb/ws/phishingWhiteList" xmlns="http://airbank.cz/osb/ws/phishingWhiteList" jxb:version="2.1" xmlns:data="http://osb.abank.cz/digitization/rest" elementFormDefault="qualified">

        <xsd:element name="GetClientsVPNIPsRequest">
            <xsd:annotation>
                <xsd:documentation>Načtení seznamu položek a jejich naplnění do seznamu výstupních elementů ip:string.</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType/>
        </xsd:element>

        <xsd:element name="GetReferersRequest">
            <xsd:annotation>
                <xsd:documentation>Načtení seznamu z výstupu REST služby a jeho naplnění do výstupních položek SOAP služby</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType/>
        </xsd:element>

        <xsd:element name="GetClientsVPNIPsResponse" type="response">
            <xsd:annotation>
                <xsd:documentation>Načtení seznamu z výstupu REST služby a jeho naplnění do výstupních položek SOAP služby</xsd:documentation>
            </xsd:annotation>
        </xsd:element>

        <xsd:element name="GetReferersResponse" type="responseRef">
            <xsd:annotation>
                <xsd:documentation>Načtení seznamu z výstupu REST služby a jeho naplnění do výstupních položek SOAP služby</xsd:documentation>
            </xsd:annotation>
        </xsd:element>


        <xsd:element name="GetBrowserLanguagesRequest">
            <xsd:annotation>
                <xsd:documentation>Načtení seznamu z výstupu REST služby a jeho naplnění do výstupních položek SOAP služby</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType/>
        </xsd:element>

        <xsd:element name="GetBrowserLanguagesResponse" type="responseRef">
            <xsd:annotation>
                <xsd:documentation>Načtení seznamu z výstupu REST služby a jeho naplnění do výstupních položek SOAP služby</xsd:documentation>
            </xsd:annotation>
        </xsd:element>

        <xsd:element name="GetCountriesRequest">
            <xsd:annotation>
                <xsd:documentation>Načtení seznamu z výstupu REST služby a jeho naplnění do výstupních položek SOAP služby</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType/>
        </xsd:element>

        <xsd:element name="GetCountriesResponse" type="responseRef">
            <xsd:annotation>
                <xsd:documentation>Načtení seznamu z výstupu REST služby a jeho naplnění do výstupních položek SOAP služby</xsd:documentation>
            </xsd:annotation>
        </xsd:element>

        <xsd:element name="GetIPsRequest">
            <xsd:annotation>
                <xsd:documentation>Načtení seznamu z výstupu REST služby a jeho naplnění do výstupních položek SOAP služby</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType/>
        </xsd:element>

        <xsd:element name="GetIPsResponse" type="responseRef">
            <xsd:annotation>
                <xsd:documentation>Načtení seznamu z výstupu REST služby a jeho naplnění do výstupních položek SOAP služby</xsd:documentation>
            </xsd:annotation>
        </xsd:element>

        <xsd:element name="GetCuidsRequest">
            <xsd:annotation>
                <xsd:documentation>Načtení seznamu z výstupu REST služby a jeho naplnění do výstupních položek SOAP služby</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType/>
        </xsd:element>

        <xsd:element name="GetCuidsResponse" type="responseRef">
            <xsd:annotation>
                <xsd:documentation>Načtení seznamu z výstupu REST služby a jeho naplnění do výstupních položek SOAP služby</xsd:documentation>
            </xsd:annotation>
        </xsd:element>

        <xsd:element name="GetCookiesRequest">
            <xsd:annotation>
                <xsd:documentation>Načtení seznamu z výstupu REST služby a jeho naplnění do výstupních položek SOAP služby</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType/>
        </xsd:element>

        <xsd:element name="GetCookiesResponse" type="responseRef">
            <xsd:annotation>
                <xsd:documentation>Načtení seznamu z výstupu REST služby a jeho naplnění do výstupních položek SOAP služby</xsd:documentation>
            </xsd:annotation>
        </xsd:element>



        <xsd:complexType name="response">
            <xsd:sequence>
                <xsd:element name="data" type="dataMap" />
                <xsd:element name="timeToLive" type="xsd:string" minOccurs="0" />
                <xsd:element name="timeout_type" type="xsd:string" minOccurs="0" />
                <xsd:element name="number_of_elements" type="xsd:long" minOccurs="0" />
                <xsd:element name="creation_time" type="xsd:long" minOccurs="0" />
                <xsd:element name="name" type="xsd:string" minOccurs="0" />
                <xsd:element name="element_type" type="xsd:string" minOccurs="0" />
            </xsd:sequence>
        </xsd:complexType>

        <xsd:complexType name="dataMap">
            <xsd:sequence>
                <xsd:any minOccurs="0" maxOccurs="unbounded" />
            </xsd:sequence>
        </xsd:complexType>

        <xsd:complexType name="data">
            <xsd:sequence>
                <xsd:element minOccurs="0" maxOccurs="unbounded" name="last_seen" type="xsd:long" />
                <xsd:element minOccurs="0" maxOccurs="unbounded" name="first_seen" type="xsd:long" />
                <xsd:element minOccurs="0" maxOccurs="unbounded" name="source" type="xsd:string" />
                <xsd:element minOccurs="0" maxOccurs="unbounded" name="value" type="xsd:string" />
            </xsd:sequence>
        </xsd:complexType>

        <xsd:complexType name="responseRef">
            <xsd:sequence>
                <xsd:element name="data" type="data" />
                <xsd:element name="timeToLive" type="xsd:string" minOccurs="0" />
                <xsd:element name="timeout_type" type="xsd:string" minOccurs="0" />
                <xsd:element name="number_of_elements" type="xsd:long" minOccurs="0" />
                <xsd:element name="creation_time" type="xsd:long" minOccurs="0" />
                <xsd:element name="name" type="xsd:string" minOccurs="0" />
                <xsd:element name="element_type" type="xsd:string" minOccurs="0" />
            </xsd:sequence>
        </xsd:complexType>

    </xsd:schema>
