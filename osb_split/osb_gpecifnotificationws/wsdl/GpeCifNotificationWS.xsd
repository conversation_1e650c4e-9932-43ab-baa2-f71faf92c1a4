<?xml version="1.0" encoding="UTF-8" ?>
    <!-- $Id: 74566b9cacd1d9c52659046f5f261d81866f6e1a $ -->
    <xsd:schema targetNamespace="http://osb.abank.cz/customer/notification" xmlns:tns="http://osb.abank.cz/customer/notification" xmlns:xsd="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified">


        <xsd:complexType name="ModificationNotification">
            <xsd:sequence>
                <xsd:element name="action" type="tns:CustomerAction" minOccurs="1" maxOccurs="1" />
                <xsd:element name="cuid" type="xsd:long" minOccurs="1" maxOccurs="1" />
                <xsd:element name="cuid2" type="xsd:long" minOccurs="0" maxOccurs="1" />
                <xsd:element name="eventOriginator" type="xsd:string" minOccurs="1" maxOccurs="1" />
                <xsd:element name="externalParameters" type="tns:Vector" minOccurs="1" maxOccurs="1" />
                <xsd:element name="notification" type="tns:CustomerNotification" minOccurs="1" maxOccurs="1" />
                <xsd:element name="resolvedManually" type="xsd:boolean" minOccurs="1" maxOccurs="1" />
                <xsd:element name="timeOfChange" type="xsd:dateTime" minOccurs="1" maxOccurs="1" />
            </xsd:sequence>
        </xsd:complexType>

        <xsd:complexType name="CustomerNotification">
            <xsd:sequence>
                <xsd:element name="action" type="tns:CustomerFeatureNotification" minOccurs="1" maxOccurs="1" />
                <xsd:element name="addresses" type="tns:CustomerFeatureNotification" minOccurs="0" />
                <xsd:element name="birth" type="tns:CustomerFeatureNotification" minOccurs="0" maxOccurs="1" />
                <xsd:element name="contacts" type="tns:CustomerFeatureNotification" minOccurs="0" maxOccurs="unbounded" />
                <xsd:element name="undesirableCustomer" type="tns:CustomerFeatureNotification" minOccurs="0" maxOccurs="1" />
                <xsd:element name="anonymization" type="tns:CustomerFeatureNotification" minOccurs="0" maxOccurs="1" />
                <xsd:element name="additionalData" type="tns:CustomerFeatureNotification" minOccurs="0" maxOccurs="unbounded" />
            </xsd:sequence>
        </xsd:complexType>

        <xsd:complexType name="CustomerFeatureNotification">
            <xsd:sequence>
                <xsd:element name="action" type="tns:NotificationAction" minOccurs="1" maxOccurs="1" />
                <xsd:element name="businessKey" type="xsd:string" minOccurs="0" maxOccurs="1" />
            </xsd:sequence>
        </xsd:complexType>

        <xsd:complexType name="Vector">
            <xsd:annotation>
                <xsd:documentation>Vector composed of key-value structure</xsd:documentation>
            </xsd:annotation>
            <xsd:sequence minOccurs="1" maxOccurs="1">
                <xsd:element name="vector" type="tns:Pair" minOccurs="1" maxOccurs="unbounded" />
            </xsd:sequence>
        </xsd:complexType>

        <xsd:complexType name="Pair">
            <xsd:annotation>
                <xsd:documentation>Key-value structure (one item in vector)</xsd:documentation>
            </xsd:annotation>
            <xsd:sequence minOccurs="1" maxOccurs="1">
                <xsd:element name="key" type="xsd:string" minOccurs="1" maxOccurs="1" />
                <xsd:element name="value" type="xsd:string" minOccurs="1" maxOccurs="1" nillable="true" />
            </xsd:sequence>
        </xsd:complexType>


        <xsd:simpleType name="CustomerAction">
            <xsd:restriction base="xsd:string">
                <xsd:enumeration value="ANONYMIZED" />
                <xsd:enumeration value="MERGE" />
                <xsd:enumeration value="NEW" />
                <xsd:enumeration value="SPLIT" />
                <xsd:enumeration value="UPDATE" />
            </xsd:restriction>
        </xsd:simpleType>

        <xsd:simpleType name="NotificationAction">
            <xsd:restriction base="xsd:string">
                <xsd:enumeration value="ADDED" />
                <xsd:enumeration value="DELETED" />
                <xsd:enumeration value="MODIFIED" />
                <xsd:enumeration value="NONE" />
            </xsd:restriction>
        </xsd:simpleType>

    </xsd:schema>
