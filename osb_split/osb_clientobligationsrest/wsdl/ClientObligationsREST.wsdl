<?xml version="1.0" encoding="UTF-8"?>
    <wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:phi="http://airbank.cz/osb/ws/ClientObligations" targetNamespace="http://airbank.cz/osb/ws/ClientObligations">

        <wsdl:types>
            <xsd:schema targetNamespace="http://airbank.cz/osb/ws/ClientObligations">
                <xsd:include schemaLocation="../xsd/ClientObligationsREST.xsd" />
            </xsd:schema>
        </wsdl:types>

        <wsdl:message name="GetCreditExposureRequest">
            <wsdl:part element="phi:GetCreditExposureRequest" name="GetCreditExposureRequest" />
        </wsdl:message>
        <wsdl:message name="GetCreditExposureResponse">
            <wsdl:part element="phi:GetCreditExposureResponse" name="GetCreditExposureResponse" />
        </wsdl:message>



        <wsdl:portType name="ClientObligationsWS">

            <wsdl:operation name="GetCreditExposure">
                <wsdl:input message="phi:GetCreditExposureRequest" />
                <wsdl:output message="phi:GetCreditExposureResponse" />
            </wsdl:operation>

        </wsdl:portType>



        <wsdl:binding name="ClientObligationsBinding" type="phi:ClientObligationsWS">
            <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />

            <wsdl:operation name="GetCreditExposure">
                <soap:operation soapAction="" />
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
            </wsdl:operation>

        </wsdl:binding>

        <wsdl:service name="ClientObligationsWS">
            <wsdl:port binding="phi:ClientObligationsBinding" name="ClientObligationsBinding">
                <soap:address location="/zonky/ext/ClientObligationsProcessWS" />
            </wsdl:port>
        </wsdl:service>
    </wsdl:definitions>
