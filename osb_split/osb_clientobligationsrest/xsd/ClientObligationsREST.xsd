<?xml version="1.0" encoding="UTF-8"?>
    <xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:jxb="http://java.sun.com/xml/ns/jaxb" targetNamespace="http://airbank.cz/osb/ws/ClientObligations" xmlns="http://airbank.cz/osb/ws/ClientObligations" jxb:version="2.1" elementFormDefault="qualified" xmlns:nxsd="http://xmlns.oracle.com/pcbpel/nxsd" nxsd:version="JSON" nxsd:encoding="UTF-8">

        <xsd:element name="GetCreditExposureRequest">
            <xsd:annotation>
                <xsd:documentation>Načtení seznamu položek a jejich naplnění podle CUIDu.</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType/>
        </xsd:element>


        <xsd:element name="GetCreditExposureResponse" type="responseRef">
            <xsd:annotation>
                <xsd:documentation>Načtení seznamu z výstupu REST služby a jeho naplnění do výstupních položek SOAP služby</xsd:documentation>
            </xsd:annotation>
        </xsd:element>



        <xsd:complexType name="loanRequests">
            <xsd:sequence>
                <xsd:element minOccurs="0" maxOccurs="unbounded" name="status" type="xsd:string" />
                <xsd:element minOccurs="0" maxOccurs="unbounded" name="contractNumber" type="xsd:decimal" />
                <xsd:element minOccurs="0" maxOccurs="unbounded" name="requestedAmount" type="amount" />
                <xsd:element minOccurs="0" maxOccurs="unbounded" name="appliedAt" type="xsd:date" />
                <xsd:element minOccurs="0" maxOccurs="unbounded" name="signedAt" type="xsd:date" />
                <xsd:element minOccurs="0" maxOccurs="unbounded" name="rejectionReasonCode" type="xsd:string" />
                <xsd:element minOccurs="0" maxOccurs="unbounded" name="terminatedAt" type="xsd:date" />
            </xsd:sequence>
        </xsd:complexType>

        <xsd:complexType name="amount">
            <xsd:sequence>
                <xsd:element name="value" type="xsd:decimal" minOccurs="0" />
                <xsd:element name="currency" type="xsd:string" minOccurs="0" />
            </xsd:sequence>
        </xsd:complexType>

        <xsd:complexType name="rateType">
            <xsd:sequence>
                <xsd:element name="rate" type="xsd:decimal" minOccurs="1" />
                <xsd:element name="type" type="InterestRateType" minOccurs="1" />
            </xsd:sequence>
        </xsd:complexType>

        <xsd:complexType name="loans">
            <xsd:sequence>
                <xsd:element minOccurs="0" maxOccurs="unbounded" name="status" type="xsd:string" />
                <xsd:element minOccurs="0" maxOccurs="unbounded" name="contractNumber" type="xsd:decimal" />
                <xsd:element minOccurs="0" maxOccurs="unbounded" name="requestedAmount" type="amount" />
                <xsd:element minOccurs="0" maxOccurs="unbounded" name="appliedAt" type="xsd:date" />
                <xsd:element minOccurs="0" maxOccurs="unbounded" name="signedAt" type="xsd:date" />
                <xsd:element minOccurs="0" maxOccurs="unbounded" name="terminatedAt" type="xsd:date" />
                <xsd:element minOccurs="0" maxOccurs="unbounded" name="amount" type="amount" />
                <xsd:element minOccurs="0" maxOccurs="unbounded" name="annuity" type="amount" />
                <xsd:element minOccurs="1" maxOccurs="2" name="interestRate" type="rateType" />
                <xsd:element minOccurs="0" maxOccurs="unbounded" name="insuranceAmount" type="amount" />
                <xsd:element minOccurs="0" maxOccurs="unbounded" name="dpd" type="xsd:decimal" />
                <xsd:element minOccurs="0" maxOccurs="unbounded" name="remainingPrincipalAmount" type="amount" />
                <xsd:element minOccurs="0" maxOccurs="unbounded" name="dueAmount" type="amount" />
            </xsd:sequence>
        </xsd:complexType>

        <xsd:complexType name="responseRef">
            <xsd:sequence>
                <xsd:element name="snapshotDate" type="xsd:string" minOccurs="0" />
                <xsd:element name="loans" type="loans" />
                <xsd:element name="loanRequests" type="loanRequests" />
            </xsd:sequence>
        </xsd:complexType>

        <xsd:simpleType name="InterestRateType">
            <xsd:restriction base="xsd:string">
                <xsd:enumeration value="STDRATE" />
                <xsd:enumeration value="PRIMERATE" />
                <xsd:enumeration value="FIXRATE" />
                <xsd:enumeration value="FLOATRATE" />
                <xsd:enumeration value="FLOATRATECAP" />
            </xsd:restriction>
        </xsd:simpleType>

    </xsd:schema>
