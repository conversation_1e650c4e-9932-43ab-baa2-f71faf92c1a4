<?xml version="1.0" encoding="UTF-8"?>
    <xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:jxb="http://java.sun.com/xml/ns/jaxb" targetNamespace="http://airbank.cz/osb/bisnode/SearchPerson" xmlns="http://airbank.cz/osb/bisnode/SearchPerson" jxb:version="2.1" elementFormDefault="qualified">

        <xsd:element name="SearchPersonRequest">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="first_name" type="xsd:string" maxOccurs="1" minOccurs="1">
                        <xsd:annotation>
                            <xsd:documentation>Client's first_name</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="last_name" type="xsd:string" maxOccurs="1" minOccurs="1">
                        <xsd:annotation>
                            <xsd:documentation>Client's last_name</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="born_date" type="xsd:string" maxOccurs="1" minOccurs="1">
                        <xsd:annotation>
                            <xsd:documentation>Client's born_date</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>

        <xsd:element name="SearchPersonResponse">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="report" type="Report" minOccurs="1" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:documentation>Client's BisNode complex info</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>

        <xsd:complexType name="Report">
            <xsd:sequence>
                <xsd:element name="subject" maxOccurs="unbounded" minOccurs="0">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="office_information">
                                <xsd:complexType>
                                    <xsd:all>
                                        <xsd:element type="xsd:long" name="ent_id" maxOccurs="1" minOccurs="1" />
                                        <xsd:element name="reg_nbr" maxOccurs="1" minOccurs="1">
                                          <xsd:simpleType>
                                            <xsd:restriction base="xsd:string">
                                              <xsd:minLength value="1" />
                                            </xsd:restriction>
                                          </xsd:simpleType>
                                        </xsd:element>                                        
                                        <xsd:element type="xsd:string" name="vat_nbr" maxOccurs="1" minOccurs="0" />
                                        <xsd:element type="xsd:string" name="duns" maxOccurs="1" minOccurs="0" />
                                        <xsd:element name="name" maxOccurs="1" minOccurs="1">
                                          <xsd:simpleType>
                                            <xsd:restriction base="xsd:string">
                                              <xsd:minLength value="1" />
                                            </xsd:restriction>
                                          </xsd:simpleType>
                                        </xsd:element>                                        
                                        <xsd:element name="date_of_establishment" maxOccurs="1" minOccurs="1">
                                          <xsd:simpleType>
                                            <xsd:restriction base="xsd:string">
                                              <xsd:minLength value="1" />
                                            </xsd:restriction>
                                          </xsd:simpleType>
                                        </xsd:element>                                        
                                        <xsd:element type="xsd:string" name="date_of_termination" maxOccurs="1" minOccurs="0" />
                                        <xsd:element type="xsd:string" name="court_txt" maxOccurs="1" minOccurs="0" />
                                        <xsd:element type="xsd:string" name="file_nbr" maxOccurs="1" minOccurs="0" />
                                        <xsd:element type="xsd:string" name="lei_txt" maxOccurs="1" minOccurs="0" />
                                        <xsd:element type="xsd:string" name="data_box_txt" maxOccurs="1" minOccurs="0" />
                                        <xsd:element name="legal_form" maxOccurs="1" minOccurs="1">
                                          <xsd:simpleType>
                                            <xsd:restriction base="xsd:string">
                                              <xsd:minLength value="1" />
                                            </xsd:restriction>
                                          </xsd:simpleType>
                                        </xsd:element>                                        
                                        <xsd:element type="xsd:string" name="esa2010" maxOccurs="1" minOccurs="0" />
                                    </xsd:all>
                                </xsd:complexType>
                            </xsd:element>
                            <xsd:element name="address">
                                <xsd:complexType>
                                    <xsd:sequence>
                                        <xsd:element type="xsd:string" name="ruian_id" maxOccurs="1" minOccurs="0" />
                                        <xsd:element name="full_txt" maxOccurs="1" minOccurs="1">
                                          <xsd:simpleType>
                                            <xsd:restriction base="xsd:string">
                                              <xsd:minLength value="1" />
                                            </xsd:restriction>
                                          </xsd:simpleType>
                                        </xsd:element>                                        
                                        <xsd:element type="xsd:string" name="gps_x" maxOccurs="1" minOccurs="0" />
                                        <xsd:element type="xsd:string" name="gps_y" maxOccurs="1" minOccurs="0" />
                                        <xsd:element type="xsd:string" name="district" maxOccurs="1" minOccurs="0" />
                                        <xsd:element type="xsd:string" name="region" maxOccurs="1" minOccurs="0" />
                                        <xsd:element name="city" maxOccurs="1" minOccurs="1">
                                          <xsd:simpleType>
                                            <xsd:restriction base="xsd:string">
                                              <xsd:minLength value="1" />
                                            </xsd:restriction>
                                          </xsd:simpleType>
                                        </xsd:element>                                        
                                        <xsd:element type="xsd:string" name="city_part" maxOccurs="1" minOccurs="0" />
                                        <xsd:element type="xsd:string" name="street" maxOccurs="1" minOccurs="0" />
                                        <xsd:element type="xsd:string" name="house_nbr" maxOccurs="1" minOccurs="0" />
                                        <xsd:element type="xsd:string" name="ori_nbr" maxOccurs="1" minOccurs="0" />
                                        <xsd:element name="zip" maxOccurs="1" minOccurs="1">
                                          <xsd:simpleType>
                                            <xsd:restriction base="xsd:string">
                                              <xsd:minLength value="1" />
                                            </xsd:restriction>
                                          </xsd:simpleType>
                                        </xsd:element>                                        
                                        <xsd:element name="country" maxOccurs="1" minOccurs="1">
                                          <xsd:simpleType>
                                            <xsd:restriction base="xsd:string">
                                              <xsd:minLength value="1" />
                                            </xsd:restriction>
                                          </xsd:simpleType>
                                        </xsd:element>                                        
                                        <xsd:element name="state_iso_2" maxOccurs="1" minOccurs="1">
                                          <xsd:simpleType>
                                            <xsd:restriction base="xsd:string">
                                              <xsd:minLength value="1" />
                                            </xsd:restriction>
                                          </xsd:simpleType>
                                        </xsd:element>                                        
                                        <xsd:element name="state_iso_3" maxOccurs="1" minOccurs="1">
                                          <xsd:simpleType>
                                            <xsd:restriction base="xsd:string">
                                              <xsd:minLength value="1" />
                                            </xsd:restriction>
                                          </xsd:simpleType>
                                        </xsd:element>                                        
                                        <xsd:element type="xsd:string" name="strt_dt" maxOccurs="1" minOccurs="0" />
                                    </xsd:sequence>
                                </xsd:complexType>
                            </xsd:element>
                            <xsd:element name="naces">
                                <xsd:complexType>
                                    <xsd:sequence>
                                        <xsd:element maxOccurs="1" minOccurs="0" name="main_nace">
                                            <xsd:complexType>
                                                <xsd:sequence>
                                                    <xsd:element type="xsd:string" name="code" maxOccurs="1" minOccurs="0" />
                                                    <xsd:element type="xsd:string" name="text" maxOccurs="1" minOccurs="0" />
                                                </xsd:sequence>
                                            </xsd:complexType>
                                        </xsd:element>
                                        <xsd:element minOccurs="0" maxOccurs="unbounded" name="secondary_nace">
                                            <xsd:complexType>
                                                <xsd:sequence>
                                                    <xsd:element type="xsd:string" name="code" maxOccurs="1" minOccurs="0" />
                                                    <xsd:element type="xsd:string" name="text" maxOccurs="1" minOccurs="0" />
                                                </xsd:sequence>
                                            </xsd:complexType>
                                        </xsd:element>
                                    </xsd:sequence>
                                </xsd:complexType>
                            </xsd:element>
                            <xsd:element name="individual" maxOccurs="1" minOccurs="0">
                                <xsd:complexType>
                                    <xsd:sequence>
                                        <xsd:element name="full_name" maxOccurs="1" minOccurs="1">
                                          <xsd:simpleType>
                                            <xsd:restriction base="xsd:string">
                                              <xsd:minLength value="1" />
                                            </xsd:restriction>
                                          </xsd:simpleType>
                                        </xsd:element>                                        
                                        <xsd:element type="xsd:string" name="title_pfx" maxOccurs="1" minOccurs="0" />
                                        <xsd:element name="first_name" maxOccurs="1" minOccurs="1">
                                          <xsd:simpleType>
                                            <xsd:restriction base="xsd:string">
                                              <xsd:minLength value="1" />
                                            </xsd:restriction>
                                          </xsd:simpleType>
                                        </xsd:element>                                        
                                        <xsd:element name="last_name" maxOccurs="1" minOccurs="1">
                                          <xsd:simpleType>
                                            <xsd:restriction base="xsd:string">
                                              <xsd:minLength value="1" />
                                            </xsd:restriction>
                                          </xsd:simpleType>
                                        </xsd:element>                                        
                                        <xsd:element type="xsd:string" name="title_sfx" maxOccurs="1" minOccurs="0" />
                                        <xsd:element name="born_dt" maxOccurs="1" minOccurs="1">
                                          <xsd:simpleType>
                                            <xsd:restriction base="xsd:string">
                                              <xsd:minLength value="1" />
                                            </xsd:restriction>
                                          </xsd:simpleType>
                                        </xsd:element>                                        
                                    </xsd:sequence>
                                </xsd:complexType>
                            </xsd:element>
                            <xsd:element name="trade_licenses">
                                <xsd:complexType>
                                    <xsd:sequence>
                                        <xsd:element minOccurs="0" maxOccurs="unbounded" name="business_subject">
                                            <xsd:complexType>
                                                <xsd:sequence>
                                                    <xsd:element type="xsd:long" name="trd_nbr" maxOccurs="1" minOccurs="0" />
                                                    <xsd:element type="xsd:string" name="trd_type_cd" maxOccurs="1" minOccurs="0" />
                                                    <xsd:element type="xsd:string" name="trd_type_txt" maxOccurs="1" minOccurs="0" />
                                                    <xsd:element type="xsd:string" name="value_txt" maxOccurs="1" minOccurs="0" />
                                                    <xsd:element type="xsd:string" name="strt_dt" maxOccurs="1" minOccurs="0" />
                                                    <xsd:element type="xsd:string" name="end_dt" maxOccurs="1" minOccurs="0" />
                                                </xsd:sequence>
                                            </xsd:complexType>
                                        </xsd:element>
                                        <xsd:element minOccurs="0" maxOccurs="unbounded" name="field_of_activity">
                                            <xsd:complexType>
                                                <xsd:sequence>
                                                    <xsd:element type="xsd:long" name="trd_nbr" maxOccurs="1" minOccurs="0" />
                                                    <xsd:element type="xsd:string" name="value_txt" maxOccurs="1" minOccurs="0" />
                                                    <xsd:element type="xsd:string" name="strt_dt" maxOccurs="1" minOccurs="0" />
                                                </xsd:sequence>
                                            </xsd:complexType>
                                        </xsd:element>
                                        <xsd:element minOccurs="0" maxOccurs="unbounded" name="business_interruption">
                                            <xsd:complexType>
                                                <xsd:sequence>
                                                    <xsd:element type="xsd:long" name="trd_nbr" maxOccurs="1" minOccurs="0" />
                                                    <xsd:element type="xsd:string" name="strt_dt" maxOccurs="1" minOccurs="0" />
                                                    <xsd:element type="xsd:string" name="end_dt" maxOccurs="1" minOccurs="0" />
                                                </xsd:sequence>
                                            </xsd:complexType>
                                        </xsd:element>
                                    </xsd:sequence>
                                </xsd:complexType>
                            </xsd:element>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>

    </xsd:schema>
