<?xml version="1.0" encoding="UTF-8"?>
    <wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:commonFault="http://airbank.cz/common/ws/fault" xmlns="http://airbank.cz/osb/bisnode/SearchPerson" targetNamespace="http://airbank.cz/osb/bisnode/SearchPerson">

        <wsdl:types>
            <xsd:schema targetNamespace="http://airbank.cz/osb/bisnode/SearchPerson">
                <xsd:include schemaLocation="../xsd/BisNodeSearchPersonWS.xsd" />
            </xsd:schema>
            <xsd:schema targetNamespace="http://airbank.cz/common/ws/fault">
                <xsd:include schemaLocation="../xsd/commonSoapFault.xsd" />
            </xsd:schema>
        </wsdl:types>


        <wsdl:message name="SearchPersonRequest">
            <wsdl:part element="SearchPersonRequest" name="SearchPersonRequest" />
        </wsdl:message>
        <wsdl:message name="SearchPersonResponse">
            <wsdl:part element="SearchPersonResponse" name="SearchPersonResponse" />
        </wsdl:message>
        <wsdl:message name="SearchPersonFault">
            <wsdl:part element="commonFault:CoreFaultElement" name="SearchPersonFault" />
        </wsdl:message>

        <wsdl:portType name="SearchPersonWS">

            <wsdl:operation name="SearchPerson">
                <wsdl:input message="SearchPersonRequest" />
                <wsdl:output message="SearchPersonResponse" />
                <wsdl:fault name="SearchPersonFault" message="SearchPersonFault" />
            </wsdl:operation>

        </wsdl:portType>

        <wsdl:binding name="SearchPersonBinding" type="SearchPersonWS">
            <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />

            <wsdl:operation name="SearchPerson">
                <soap:operation soapAction="" />
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
                <wsdl:fault name="SearchPersonFault">
                    <soap:fault name="SearchPersonFault" use="literal" />
                </wsdl:fault>
            </wsdl:operation>

        </wsdl:binding>

        <wsdl:service name="SearchPersonWSBinding">
            <wsdl:port binding="SearchPersonBinding" name="SearchPersonBinding">
                <soap:address location="/ws/SearchPersonWS" />
            </wsdl:port>
        </wsdl:service>
    </wsdl:definitions>
