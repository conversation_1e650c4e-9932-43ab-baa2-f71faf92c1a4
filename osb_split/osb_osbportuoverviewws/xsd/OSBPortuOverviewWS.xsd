<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://airbank.cz/osb/portu/portuProxy" elementFormDefault="qualified" targetNamespace="http://airbank.cz/osb/portu/portuProxy">


    <xs:element name="GetRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Vraci pro dany cuid a ramcovku seznam portfolii daneho uzivatele</xs:documentation>
            </xs:annotation>
            <xs:sequence>
                <xs:element name="cuid" type="xs:long" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Jednoznačný identifikátor osoby</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="generalContractId" type="xs:long" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Číslo ramcove smlouvy</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="clientId" type="xs:string" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Jednoznačný identifikátor osoby</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="canBeDisplayed" type="xs:boolean" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Jednoznačný identifikátor osoby</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="portfolioDashboardViewModelWS" type="portfolioDashboardViewModelWS" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Typ a cas validity souhlasu</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="portuConsent" type="PortuConsent" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>Id operatora</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="clientId" type="xs:string" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Číslo ramcove smlouvy</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>


    <xs:complexType name="PortuConsent">
        <xs:sequence>
            <xs:element name="consentType" type="consentType" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Typ souhlasu muze byt hodnota PORTU_DATA nebo PORTU_ONBOARDING</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="validFrom" type="xs:dateTime" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Id operatora</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="validTo" type="xs:dateTime" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Id operatora</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>


    <xs:complexType name="portfolioDashboardViewModelWS">
        <xs:sequence>
            <xs:element name="id" type="xs:string" minOccurs="0" />
            <xs:element name="portfolioGraphs" type="portfolioGraphViewModel" nillable="true" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="portfolioSummary" type="portfolioSummaryViewModel" minOccurs="0" />
            <xs:element name="portfolioSummaryGraph" type="portfolioGraphViewModel" minOccurs="0" />
            <xs:element name="portfolios" type="portfolioViewModel" nillable="true" minOccurs="0" maxOccurs="unbounded" />
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="portfolioGraphViewModel">
        <xs:sequence>
            <xs:element name="id" type="xs:string" minOccurs="0" />
            <xs:element name="investmentInTime" minOccurs="0" maxOccurs="unbounded">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="key" minOccurs="0" type="xs:string" />
                        <xs:element name="value" minOccurs="0" type="xs:decimal" />
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="moneyReturn" type="xs:decimal" minOccurs="0" />
            <xs:element name="percentageProfitInTime" minOccurs="0" maxOccurs="unbounded">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="key" minOccurs="0" type="xs:string" />
                        <xs:element name="value" minOccurs="0" type="xs:decimal" />
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="timeReturn" type="xs:decimal" minOccurs="0" />
            <xs:element name="valueInTime" minOccurs="0" maxOccurs="unbounded">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="key" minOccurs="0" type="xs:string" />
                        <xs:element name="value" minOccurs="0" type="xs:decimal" />
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="portfolioSummaryViewModel">
        <xs:sequence>
            <xs:element name="actualBalance" type="xs:decimal" minOccurs="0" />
            <xs:element name="actualProfit" type="xs:decimal" minOccurs="0" />
            <xs:element name="id" type="xs:string" minOccurs="0" />
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="portfolioViewModel">
        <xs:sequence>
            <xs:element name="actualBalance" type="xs:decimal" minOccurs="0" />
            <xs:element name="actualProfit" type="xs:decimal" minOccurs="0" />
            <xs:element name="availableForWithdrawal" type="xs:decimal" minOccurs="0" />
            <xs:element name="createdOn" type="xs:dateTime" minOccurs="0" />
            <xs:element name="currency" type="xs:string" minOccurs="0" />
            <xs:element name="goalAmount" type="xs:int" minOccurs="0" />
            <xs:element name="hasToBeValidatedMoneyDeposit" type="xs:boolean" minOccurs="0" />
            <xs:element name="hedgingEnabled" type="xs:boolean" minOccurs="0" />
            <xs:element name="id" type="xs:string" minOccurs="0" />
            <xs:element name="initialInvestment" type="xs:int" minOccurs="0" />
            <xs:element name="investmentInProgress" type="xs:boolean" minOccurs="0" />
            <xs:element name="investmentYears" type="xs:int" minOccurs="0" />
            <xs:element name="lastPmpUpdate" type="xs:dateTime" minOccurs="0" />
            <xs:element name="missingRegularDeposit" type="xs:boolean" minOccurs="0" />
            <xs:element name="modelPortfolio" type="modelPortfolio" minOccurs="0" />
            <xs:element name="name" type="xs:string" minOccurs="0" />
            <xs:element name="ownerId" type="xs:string" minOccurs="0" />
            <xs:element name="pendingWithdrawals" type="xs:decimal" minOccurs="0" />
            <xs:element name="periodicalInvestmentPeriod" type="xs:int" minOccurs="0" />
            <xs:element name="periodicalInvestmentValue" type="xs:int" minOccurs="0" />
            <xs:element name="portfolioStatus" type="portfolioStatusEnum" minOccurs="0" />
            <xs:element name="portfolioStatusChange" type="xs:dateTime" minOccurs="0" />
            <xs:element name="portfolioType" type="portfolioTypeEnum" minOccurs="0" />
            <xs:element name="rebalanceEnabled" type="xs:boolean" minOccurs="0" />
            <xs:element name="riskScore" type="xs:int" minOccurs="0" />
            <xs:element name="subProduct" type="subProductEnum" minOccurs="0" />
            <xs:element name="timestamp" type="xs:dateTime" minOccurs="0" />
            <xs:element name="totalDeposit" type="xs:decimal" minOccurs="0" />
            <xs:element name="variableSymbol" type="xs:string" minOccurs="0" />
            <xs:element name="waitingForInvestment" type="xs:boolean" minOccurs="0" />
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="modelPortfolio">
        <xs:sequence>
            <xs:element name="description" type="xs:string" minOccurs="0" />
            <xs:element name="isActive" type="xs:boolean" minOccurs="0" />
            <xs:element name="isTemplate" type="xs:boolean" minOccurs="0" />
            <xs:element name="level" type="xs:int" minOccurs="0" />
            <xs:element name="name" type="xs:string" minOccurs="0" />
            <xs:element name="order" type="xs:int" minOccurs="0" />
            <xs:element name="positions" type="positionBase" nillable="true" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="profit" type="xs:decimal" minOccurs="0" />
            <xs:element name="subProduct" type="subProductEnum" minOccurs="0" />
            <xs:element name="validFrom" type="xs:dateTime" minOccurs="0" />
            <xs:element name="validTo" type="xs:dateTime" minOccurs="0" />
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="positionBase">
        <xs:sequence>
            <xs:element name="id" type="xs:string" minOccurs="0" />
            <xs:element name="refCurrency" type="xs:string" minOccurs="0" />
            <xs:element name="refCurrencyFxRate" type="xs:decimal" minOccurs="0" />
            <xs:element name="timestamp" type="xs:dateTime" minOccurs="0" />
            <xs:element name="weight" type="xs:decimal" minOccurs="0" />
        </xs:sequence>
    </xs:complexType>



    <xs:simpleType name="consentType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="PORTU_DATA" />
            <xs:enumeration value="PORTU_ONBOARDING" />
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="portfolioStatusEnum">
        <xs:annotation>
            <xs:documentation>ReadyForInvestment = 1</xs:documentation>
            <xs:documentation>Active = 2</xs:documentation>
            <xs:documentation>InLiquidation = 3</xs:documentation>
            <xs:documentation>Liquidated = 4</xs:documentation>
            <xs:documentation>InProgress = 5</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="NUMBER_1" />
            <xs:enumeration value="NUMBER_2" />
            <xs:enumeration value="NUMBER_3" />
            <xs:enumeration value="NUMBER_4" />
            <xs:enumeration value="NUMBER_5" />
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="portfolioTypeEnum">
        <xs:annotation>
            <xs:documentation>Standard = 1</xs:documentation>
            <xs:documentation> OnlineAssetManagement = 2</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="NUMBER_1" />
            <xs:enumeration value="NUMBER_2" />
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="subProductEnum">
        <xs:annotation>
            <xs:documentation>None = 1</xs:documentation>
            <xs:documentation>PortuRobo = 2</xs:documentation>
            <xs:documentation>PortuExpert = 3</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="NUMBER_1" />
            <xs:enumeration value="NUMBER_2" />
            <xs:enumeration value="NUMBER_3" />
        </xs:restriction>
    </xs:simpleType>

</xs:schema>
