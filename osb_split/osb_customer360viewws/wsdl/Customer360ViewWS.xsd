<?xml version="1.0" encoding="UTF-8" ?>
    <!-- $Id: b0f62b6233007ac9a5ee0dc212b53edb67c3a54a $ -->
    <xsd:schema targetNamespace="http://osb.abank.cz/customer/view" xmlns:tns="http://osb.abank.cz/customer/view" xmlns:xsd="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified">
        <xsd:element name="getCustomerBasicProfileRequest">
            <xsd:complexType>
                <xsd:choice>
                    <xsd:element name="cuid" type="xsd:long" />
                    <xsd:element name="phone" type="tns:phoneNumber" />
                </xsd:choice>
            </xsd:complexType>
        </xsd:element>
        <xsd:element name="getCustomerBasicProfileResponse">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element maxOccurs="1" name="customerBasicProfile" type="tns:customerBasicProfile" minOccurs="0" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>
        <xsd:element name="identifyCustomersRequest">
            <xsd:complexType>
                <xsd:choice>
                    <xsd:element name="birthNumber" type="xsd:string" />
                    <xsd:element name="phone" type="tns:phoneNumber" />
                    <xsd:element name="email" type="xsd:string" />
                    <xsd:element name="name" type="tns:name" />
                    <xsd:element name="cardNumber" type="xsd:string" />
                    <xsd:element name="applicationId" type="xsd:int" />
                    <xsd:element name="username" type="xsd:string" />
                    <xsd:element name="loanNumber" type="xsd:string" />
                    <xsd:element name="accountNumber" type="xsd:string" />
                    <xsd:element name="documentNumber" type="xsd:string" />
                    <xsd:element name="cuid" type="xsd:long" />
                    <xsd:element name="identificationDocument" type="tns:IdentificationDocument" />
                    <xsd:element name="identificationNumber" type="xsd:string" />
                </xsd:choice>
            </xsd:complexType>
        </xsd:element>
        <xsd:element name="identifyCustomersResponse">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="customer" type="tns:customerSimple" minOccurs="0" maxOccurs="unbounded" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>
        <xsd:element name="getCustomerDetailRequest">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="cuid" type="xsd:long" minOccurs="1" maxOccurs="1" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>
        <xsd:element name="getCustomerDetailResponse">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="customer" type="tns:customer" minOccurs="0" maxOccurs="1" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>
        <xsd:element name="getCustomerServicesRequest">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="cuid" type="xsd:long" minOccurs="1" maxOccurs="1" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>
        <xsd:element name="getCustomerServicesResponse">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="customerService" type="tns:customerService" minOccurs="0" maxOccurs="unbounded" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>
        <xsd:element name="getCustomerToBankRelationsRequest">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="cuid" type="xsd:long" minOccurs="1" maxOccurs="1" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>
        <xsd:element name="getCustomerToBankRelationsResponse">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="toBankRelation" type="tns:relationType" minOccurs="1" maxOccurs="unbounded" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>

        <xsd:element name="getCustomerWelcomeFlagRequest">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="cuid" type="xsd:long" minOccurs="1" maxOccurs="1" />
                    <xsd:element name="contactKind" type="tns:OnlineContactKind" minOccurs="1" maxOccurs="1" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>
        <xsd:element name="getCustomerWelcomeFlagResponse">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="welcomeFlag" type="xsd:boolean" minOccurs="1" maxOccurs="1" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>

        <xsd:element name="searchCustomerToGeneralContractRelationsRequest">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="cuid" type="xsd:long" minOccurs="1" maxOccurs="1" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>
        <xsd:element name="searchCustomerToGeneralContractRelationsResponse">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="toGeneralContractRelation" type="tns:toGeneralContractRelation" minOccurs="0" maxOccurs="unbounded" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>


        <xsd:complexType name="toGeneralContractRelation">
            <xsd:sequence>
                <xsd:element name="personToBankRelation" type="tns:relationType" minOccurs="1" maxOccurs="1" />
                <xsd:element name="generalContractId" type="xsd:long" minOccurs="1" maxOccurs="1" />
                <xsd:element name="ownerGeneralCustomer" minOccurs="1" maxOccurs="1" type="tns:generalCustomerToDisplay" />
            </xsd:sequence>
        </xsd:complexType>

        <xsd:complexType name="generalCustomerToDisplay">
            <xsd:sequence>
                <xsd:element name="nameToDisplay" type="xsd:string" minOccurs="1" maxOccurs="1" />
                <xsd:element name="cuid" type="xsd:string" minOccurs="1" maxOccurs="1" />
                <xsd:element name="legalSegment" type="xsd:string" minOccurs="1" maxOccurs="1" />
            </xsd:sequence>
        </xsd:complexType>


        <xsd:complexType name="customerSimple">
            <xsd:sequence>
                <xsd:element name="cuid" type="xsd:long" />
                <xsd:element name="firstName" type="xsd:string" />
                <xsd:element name="lastName" type="xsd:string" />
                <xsd:element name="birthDate" type="xsd:date" />
                <xsd:element name="birthNumber" type="xsd:string" minOccurs="0" />
                <xsd:element name="toBankRelation" minOccurs="1" maxOccurs="unbounded" type="tns:relationType" />
            </xsd:sequence>
        </xsd:complexType>
        <xsd:complexType name="customer">
            <xsd:sequence>
                <xsd:element name="cuid" type="xsd:long" />
                <xsd:element name="firstName" type="xsd:string" />
                <xsd:element name="lastName" type="xsd:string" />
                <xsd:element name="lastNameVocative" type="xsd:string" minOccurs="0" />
                <xsd:element name="salutation" type="xsd:string" />
                <xsd:element name="birthDate" type="xsd:date" />
                <xsd:element name="birthNumber" type="xsd:string" minOccurs="0" />
                <xsd:element name="birthPlace" type="tns:BirthPlaceCriterion" minOccurs="0" />
                <xsd:element name="citizenship" type="xsd:string" maxOccurs="unbounded" />
                <xsd:element name="gender" type="xsd:string" />
                <xsd:element name="iccNote" type="xsd:string" minOccurs="0" />
                <xsd:element name="customerContact" type="tns:CustomerContact" maxOccurs="unbounded" />
                <xsd:element name="anonymizationStatus" type="xsd:string" />
                <xsd:element name="toBankRelation" minOccurs="1" maxOccurs="unbounded" type="tns:relationType" />
            </xsd:sequence>
        </xsd:complexType>
        <xsd:complexType name="CustomerContact">
            <xsd:sequence>
                <xsd:element name="role" type="xsd:string" />
                <xsd:element name="contactValue" type="xsd:string" />
                <xsd:element name="callingCode" type="xsd:string" minOccurs="0" maxOccurs="1" />
            </xsd:sequence>
        </xsd:complexType>
        <xsd:complexType name="customerService">
            <xsd:sequence>
                <xsd:element name="service" type="xsd:string" minOccurs="1" maxOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>
                            Possible values: --------------- CURRENT_ACCOUNT SAVINGS_ACCOUNT CASH_LOAN OVERDRAFT CONSOLIDATION MORTGAGE_REF MORTGAGE INSURANCE_EXPENSES MOBILITY DOCUMENT_ORGANIZER STICKER CARD DISPONENT CARD_HOLDER ATOM MOBILE_APP SWT TRAVEL_INSURANCE INVESTMENT_INSTRUMENT ----------------
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="status" type="tns:status" minOccurs="0" maxOccurs="1" />
            </xsd:sequence>
        </xsd:complexType>
        <xsd:simpleType name="relationType">
            <xsd:restriction base="xsd:string">
                <xsd:enumeration value="CLIENT" />
                <xsd:enumeration value="DISPONENT" />
                <xsd:enumeration value="CARD_HOLDER" />
                <xsd:enumeration value="CODEBTOR" />
                <xsd:enumeration value="NO_RELATION" />
                <xsd:enumeration value="APPLICANT" />
                <xsd:enumeration value="PROSPECT" />
                <xsd:enumeration value="CONTRACT_PROPOSAL" />
                <xsd:enumeration value="ACTIVE_ENTITLED" />
                <xsd:enumeration value="ENTITLED_PROPOSAL" />
                <xsd:enumeration value="APPLICANT_ENTREPRENEUR" />
            </xsd:restriction>
        </xsd:simpleType>
        <xsd:simpleType name="status">
            <xsd:restriction base="xsd:string">
                <xsd:enumeration value="ACTIVE" />
                <xsd:enumeration value="INACTIVE" />
                <xsd:enumeration value="UNDETECTED" />
            </xsd:restriction>
        </xsd:simpleType>

        <xsd:simpleType name="OnlineContactKind">
            <xsd:restriction base="xsd:string">
                <xsd:enumeration value="CALL" />
                <xsd:enumeration value="VISIT" />
            </xsd:restriction>
        </xsd:simpleType>

        <xsd:complexType name="customerBasicProfile">
            <xsd:sequence>
                <xsd:element name="cuid" type="xsd:long" minOccurs="0" />
                <xsd:element name="firstName" type="xsd:string" />
                <xsd:element name="lastName" type="xsd:string" />
                <xsd:element name="lastNameVocative" type="xsd:string" minOccurs="0" />
                <xsd:element name="salutation" type="xsd:string" minOccurs="0" />
                <xsd:element name="gender" type="xsd:string" minOccurs="0" />
            </xsd:sequence>
        </xsd:complexType>
        <xsd:complexType name="name">
            <xsd:sequence>
                <xsd:element name="firstName" type="xsd:string" />
                <xsd:element name="surName" type="xsd:string" />
            </xsd:sequence>
        </xsd:complexType>
        <xsd:complexType name="phoneNumber">
            <xsd:sequence>
                <xsd:element name="prefix">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:pattern value="\+\d{1,4}" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="number" type="xsd:string" />
            </xsd:sequence>
        </xsd:complexType>

        <xsd:complexType name="IdentificationDocument">
            <xsd:sequence>
                <xsd:element name="country" type="xsd:string" minOccurs="0" maxOccurs="1" />
                <xsd:element name="number" type="xsd:string" minOccurs="1" maxOccurs="1" />
                <xsd:element name="type" type="xsd:string" minOccurs="0" maxOccurs="1" />
            </xsd:sequence>
        </xsd:complexType>

        <xsd:complexType name="BirthPlaceCriterion">
            <xsd:sequence>
                <xsd:element name="discriminator" type="xsd:string" />
                <xsd:element name="stateToDisplay" type="xsd:string" minOccurs="0" />
                <xsd:element name="placeToDisplay" type="xsd:string" minOccurs="0" />
                <xsd:choice minOccurs="0">
                    <xsd:element name="ruianBirthPlace" type="tns:RuianBirthPlaceCriterion" />
                    <xsd:element name="countryBirthPlace" type="tns:CountryBirthPlaceCriterion" />
                    <xsd:element name="outsideCountryBirthPlace" type="tns:OutsideCountryBirthPlaceCriterion" />
                </xsd:choice>
            </xsd:sequence>
        </xsd:complexType>

        <xsd:complexType name="CountryBirthPlaceCriterion">
            <xsd:sequence>
                <xsd:element name="alpha2Code" type="xsd:string" />
                <xsd:element name="location" type="xsd:string" minOccurs="0" />
            </xsd:sequence>
        </xsd:complexType>

        <xsd:complexType name="RuianBirthPlaceCriterion">
            <xsd:sequence>
                <xsd:sequence>
                    <xsd:element name="townCode" type="xsd:long" minOccurs="0" />
                    <xsd:element name="pragueMunicipalDistrictCode" type="xsd:long" minOccurs="0" />
                </xsd:sequence>
            </xsd:sequence>
        </xsd:complexType>

        <xsd:complexType name="OutsideCountryBirthPlaceCriterion">
            <xsd:sequence>
                <xsd:sequence>
                    <xsd:element name="place" type="xsd:string" />
                </xsd:sequence>
            </xsd:sequence>
        </xsd:complexType>

    </xsd:schema>
