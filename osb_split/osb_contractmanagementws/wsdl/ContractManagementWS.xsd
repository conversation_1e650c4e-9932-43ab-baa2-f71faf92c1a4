<xs:schema targetNamespace="http://osb.banka.hci/OSB/ContractManagementWS" attributeFormDefault="unqualified" elementFormDefault="qualified" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:jxb="http://java.sun.com/xml/ns/jaxb" jxb:version="2.1" xmlns="http://osb.banka.hci/OSB/ContractManagementWS">
    <xs:element name="newContractActivatedRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="contractId" type="xs:long" maxOccurs="1" minOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Client general contract ID.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="applicationCreatedDate" type="xs:date" maxOccurs="1" minOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Date of application creation.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="contractActivationDate" type="xs:date" maxOccurs="1" minOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Date of application activation.</xs:documentation>
                    </xs:annotation>

                </xs:element>
                <xs:element name="ownerCuid" type="xs:long" maxOccurs="1" minOccurs="1">
                    <xs:annotation>
                        <xs:documentation>ownerCuid - cuid of owner</xs:documentation>
                    </xs:annotation>
                </xs:element>

                <xs:element name="entitledCuid" type="xs:long" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>entitledCuid - cuid of entitled person</xs:documentation>
                    </xs:annotation>
                </xs:element>

                <xs:element name="envelopeId" type="xs:long" maxOccurs="1" minOccurs="1">
                    <xs:annotation>
                        <xs:documentation>envelope ID</xs:documentation>
                    </xs:annotation>
                </xs:element>

                <xs:element name="activationReason" type="activityReason" maxOccurs="1" minOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Reason of activation. Podpis přes Kurýra, Podpis RS na Pobočce, Přihlášení na Pobočce, Přenesená identifikace</xs:documentation>
                    </xs:annotation>
                </xs:element>

                <xs:element name="identificationPayment" type="IdentificationPaymentTO" minOccurs="0">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Identification Payment.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>

                <xs:element name="generalContractType" type="generalContractType" minOccurs="1">
                    <xs:annotation>
                        <xs:documentation>generalContractType: enum (RETAIL / ENTREPRENEUR / LEGAL_ENTITY)</xs:documentation>
                    </xs:annotation>
                </xs:element>

                <xs:element name="contractNumber" type="xs:string" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>contractNumber</xs:documentation>
                    </xs:annotation>
                </xs:element>

            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="IdentificationPaymentTO">
        <xs:annotation>
            <xs:documentation>Identification Payment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="id" type="xs:long">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Identification Payment Id.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="debtorAccountName" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Debtor Account Name.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="messageForReceiver" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Message For Receiver.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="verifiedBy" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Identification of operator who accepted Identification Payment.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="activityReason">
        <xs:restriction base="xs:string">
            <xs:enumeration value="SIGNED_AT_MESSENGER" />
            <xs:enumeration value="SIGNED_AT_BRANCH" />
            <xs:enumeration value="LOGIN_AT_BRANCH" />
            <xs:enumeration value="REMOTE_IDENTIFICATION" />
            <xs:enumeration value="O2" />
            <xs:enumeration value="EXISTING_CONTRACT_IDENTIFICATION" />
            <xs:enumeration value="BANKID_IDENTIFICATION" />
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="generalContractType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="RETAIL" />
            <xs:enumeration value="ENTREPRENEUR" />
            <xs:enumeration value="LEGAL_ENTITY" />
        </xs:restriction>
    </xs:simpleType>

    <xs:element name="newContractActivatedResponse">
        <xs:complexType>
            <xs:sequence>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="ContractBeingClosedRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Contract being closed request</xs:documentation>
            </xs:annotation>
            <xs:sequence>
                <xs:element name="contractId" type="xs:long" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Id of closed contract</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="closeReason" type="xs:string" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Close/cancel reason for AMS.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="ownerCuid" type="xs:long" maxOccurs="1" minOccurs="1">
                    <xs:annotation>
                        <xs:documentation>cuid - id of client</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="generalContractType" type="generalContractType" maxOccurs="1" minOccurs="1">
                    <xs:annotation>
                        <xs:documentation>generalContractType: enum (RETAIL / ENTREPRENEUR / LEGAL_ENTITY)</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="contractNumber" type="xs:string" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>contractNumber</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="ContractBeingClosedResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Contract being closed response</xs:documentation>
            </xs:annotation>
            <xs:sequence />
        </xs:complexType>
    </xs:element>

    <xs:element name="customerIdentifiedRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>customerIdentified request</xs:documentation>
            </xs:annotation>
            <xs:sequence>
                <xs:element name="cuid" type="xs:long" maxOccurs="1" minOccurs="1">
                    <xs:annotation>
                        <xs:documentation>cuid - id of client</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="customerIdentifiedResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Contract being closed response</xs:documentation>
            </xs:annotation>
            <xs:sequence />
        </xs:complexType>
    </xs:element>

    <xs:element name="contractDeactivatedRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>customerIdentified request</xs:documentation>
            </xs:annotation>
            <xs:sequence>
                <xs:element name="contractId" type="xs:long" maxOccurs="1" minOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Client general contract ID.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="ownerCuid" type="xs:long" maxOccurs="1" minOccurs="1">
                    <xs:annotation>
                        <xs:documentation>cuid - id of client</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="contractDeactivatedResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Contract being closed response</xs:documentation>
            </xs:annotation>
            <xs:sequence />
        </xs:complexType>
    </xs:element>

    <xs:element name="InitBankIdRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>customerIdentified request</xs:documentation>
            </xs:annotation>
            <xs:sequence>
                <xs:element name="cuid" type="xs:long" minOccurs="1" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>cuid - id of client</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="InitBankIdResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>InitBankId response</xs:documentation>
            </xs:annotation>
            <xs:sequence />
        </xs:complexType>
    </xs:element>

    <xs:element name="CustomerIdentifiedAndUpdatedRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>customerIdentified request</xs:documentation>
            </xs:annotation>
            <xs:sequence>
                <xs:element name="cuid" type="xs:long" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>cuid - id of client</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="CustomerIdentifiedAndUpdatedResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>InitBankId response</xs:documentation>
            </xs:annotation>
            <xs:sequence />
        </xs:complexType>
    </xs:element>
    <xs:element name="ProcessContractCustomerRelationsRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="ContractCustomerRelation" type="ContractCustomerRelationTO" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Contract Customer Relation</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="ContractCustomerRelationTO">
        <xs:annotation>
            <xs:documentation>Identification Payment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="id" type="xs:long">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Id of client</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="event" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>CREATED/DELETED</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="generalContractId" type="xs:long">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>generalContractId of client</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="legalSegment" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>CUSTOMER</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="cuid" type="xs:long">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>cuid - id of client</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="relation" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>client's relation</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ProcessContractCustomerRelationsResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>ProcessContractCustomerRelations response</xs:documentation>
            </xs:annotation>
            <xs:sequence />
        </xs:complexType>
    </xs:element>
</xs:schema>
