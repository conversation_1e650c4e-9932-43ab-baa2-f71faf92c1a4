<wsdl:definitions targetNamespace="http://osb.banka.hci/OSB/ContractManagementWS" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:sch="http://osb.banka.hci/OSB/ContractManagementWS" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tns="http://osb.banka.hci/OSB/ContractManagementWS">
    <wsdl:types>
        <xs:schema targetNamespace="http://osb.banka.hci/OSB/ContractManagementWS" xmlns:xs="http://www.w3.org/2001/XMLSchema">
            <xs:include schemaLocation="ContractManagementWS.xsd" />
        </xs:schema>
    </wsdl:types>

    <wsdl:message name="newContractActivatedRequest">
        <wsdl:part element="tns:newContractActivatedRequest" name="newContractActivatedRequest">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="newContractActivatedResponse">
        <wsdl:part element="tns:newContractActivatedResponse" name="newContractActivatedResponse">
        </wsdl:part>
    </wsdl:message>

    <wsdl:message name="ContractBeingClosedRequestMessage">
        <wsdl:part element="tns:ContractBeingClosedRequest" name="ContractBeingClosedRequest" />
    </wsdl:message>
    <wsdl:message name="ContractBeingClosedResponseMessage">
        <wsdl:part element="tns:ContractBeingClosedResponse" name="ContractBeingClosedResponse" />
    </wsdl:message>

    <wsdl:message name="customerIdentifiedRequest">
        <wsdl:part element="tns:customerIdentifiedRequest" name="customerIdentifiedRequest">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="customerIdentifiedResponse">
        <wsdl:part element="tns:customerIdentifiedResponse" name="customerIdentifiedResponse">
        </wsdl:part>
    </wsdl:message>

    <wsdl:message name="contractDeactivatedRequest">
        <wsdl:part element="tns:contractDeactivatedRequest" name="contractDeactivatedRequest">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="contractDeactivatedResponse">
        <wsdl:part element="tns:contractDeactivatedResponse" name="contractDeactivatedResponse">
        </wsdl:part>
    </wsdl:message>

    <wsdl:message name="InitBankIdRequest">
        <wsdl:part element="tns:InitBankIdRequest" name="InitBankIdRequest">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="InitBankIdResponse">
        <wsdl:part element="tns:InitBankIdResponse" name="InitBankIdResponse">
        </wsdl:part>
    </wsdl:message>
    
    <wsdl:message name="CustomerIdentifiedAndUpdatedRequest">
        <wsdl:part element="tns:CustomerIdentifiedAndUpdatedRequest" name="CustomerIdentifiedAndUpdatedRequest">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="CustomerIdentifiedAndUpdatedResponse">
        <wsdl:part element="tns:CustomerIdentifiedAndUpdatedResponse" name="CustomerIdentifiedAndUpdatedResponse">
        </wsdl:part>
    </wsdl:message>
    
    <wsdl:message name="ProcessContractCustomerRelationsRequest">
        <wsdl:part element="tns:ProcessContractCustomerRelationsRequest" name="ProcessContractCustomerRelationsRequest">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="ProcessContractCustomerRelationsResponse">
        <wsdl:part element="tns:ProcessContractCustomerRelationsResponse" name="ProcessContractCustomerRelationsResponse">
        </wsdl:part>
    </wsdl:message>
    
    <wsdl:portType name="ContractManagementWS">
        <wsdl:operation name="newContractActivated">
            <wsdl:input message="tns:newContractActivatedRequest" name="newContractActivatedRequest">
            </wsdl:input>
            <wsdl:output message="tns:newContractActivatedResponse" name="newContractActivatedResponse">
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="contractBeingClosed">
            <wsdl:documentation>
                notifies interested systems (AMS, Sanon) that conntract is being closed
            </wsdl:documentation>
            <wsdl:input message="tns:ContractBeingClosedRequestMessage" />
            <wsdl:output message="tns:ContractBeingClosedResponseMessage" />
        </wsdl:operation>
        <wsdl:operation name="customerIdentified">
            <wsdl:input message="tns:customerIdentifiedRequest" name="customerIdentifiedRequest">
            </wsdl:input>
            <wsdl:output message="tns:customerIdentifiedResponse" name="customerIdentifiedResponse">
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="contractDeactivated">
            <wsdl:input message="tns:contractDeactivatedRequest" name="contractDeactivatedRequest">
            </wsdl:input>
            <wsdl:output message="tns:contractDeactivatedResponse" name="contractDeactivatedResponse">
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="CustomerIdentifiedAndUpdated">
            <wsdl:input message="tns:CustomerIdentifiedAndUpdatedRequest" name="CustomerIdentifiedAndUpdatedRequest">
            </wsdl:input>
            <wsdl:output message="tns:CustomerIdentifiedAndUpdatedResponse" name="CustomerIdentifiedAndUpdatedResponse">
			</wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="InitBankId">
            <wsdl:input message="tns:InitBankIdRequest" name="InitBankIdRequest">
            </wsdl:input>
            <wsdl:output message="tns:InitBankIdResponse" name="InitBankIdResponse">
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="processContractCustomerRelations">
            <wsdl:input message="tns:ProcessContractCustomerRelationsRequest" name="ProcessContractCustomerRelationsRequest">
            </wsdl:input>
            <wsdl:output message="tns:ProcessContractCustomerRelationsResponse" name="ProcessContractCustomerRelationsResponse">
            </wsdl:output>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="ContractManagementWSSoap11" type="tns:ContractManagementWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />
        <wsdl:operation name="newContractActivated">
            <soap:operation soapAction="" />
            <wsdl:input name="newContractActivatedRequest">
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output name="newContractActivatedResponse">
                <soap:body use="literal" />
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="contractBeingClosed">
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="customerIdentified">
            <soap:operation soapAction="" />
            <wsdl:input name="customerIdentifiedRequest">
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output name="customerIdentifiedResponse">
                <soap:body use="literal" />
            </wsdl:output>
        </wsdl:operation>

        <wsdl:operation name="contractDeactivated">
            <soap:operation soapAction="" />
            <wsdl:input name="contractDeactivatedRequest">
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output name="contractDeactivatedResponse">
                <soap:body use="literal" />
            </wsdl:output>
        </wsdl:operation>

        <wsdl:operation name="InitBankId">
            <soap:operation soapAction="" />
            <wsdl:input name="InitBankIdRequest">
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output name="InitBankIdResponse">
                <soap:body use="literal" />
            </wsdl:output>
        </wsdl:operation>

        <wsdl:operation name="CustomerIdentifiedAndUpdated">
            <soap:operation soapAction="" />
            <wsdl:input name="CustomerIdentifiedAndUpdatedRequest">
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output name="CustomerIdentifiedAndUpdatedResponse">
                <soap:body use="literal" />
            </wsdl:output>
        </wsdl:operation>

        <wsdl:operation name="processContractCustomerRelations">
            <soap:operation soapAction="" />
            <wsdl:input name="ProcessContractCustomerRelationsRequest">
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output name="ProcessContractCustomerRelationsResponse">
                <soap:body use="literal" />
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="ContractManagementService">
        <wsdl:port binding="tns:ContractManagementWSSoap11" name="ContractManagementWSSoap11">
            <soap:address location="LATER" />
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>
