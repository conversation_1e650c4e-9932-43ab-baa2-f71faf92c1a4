<wsdl:definitions name="AccessControl" targetNamespace="http://osb.banka.hci/AccessControl/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tns="http://osb.banka.hci/AccessControl/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">

	<wsdl:types>
		<xsd:schema targetNamespace="http://osb.banka.hci/AccessControl/">
			<xsd:include schemaLocation="AccessControlService.xsd"/>
		</xsd:schema>
	</wsdl:types>

	<wsdl:message name="authenticateRequest">
		<wsdl:part element="tns:authenticateRequest" name="authenticateRequest"/>
	</wsdl:message>
	<wsdl:message name="authenticateResponse">
		<wsdl:part element="tns:authenticateResponse" name="authenticateResponse"/>
	</wsdl:message>
	<wsdl:message name="isEmployeeCustomerRequest">
		<wsdl:part element="tns:isEmployeeCustomerRequest" name="isEmployeeCustomerRequest"/>
	</wsdl:message>
	<wsdl:message name="isEmployeeCustomerResponse">
		<wsdl:part element="tns:isEmployeeCustomerResponse" name="isEmployeeCustomerResponse"/>
	</wsdl:message>	
        <wsdl:portType name="AccessControlWS">
		<wsdl:operation name="authenticate">
			<wsdl:input message="tns:authenticateRequest" name="authenticateRequest"/>
			<wsdl:output message="tns:authenticateResponse" name="authenticateResponse"/>
		</wsdl:operation>
		<wsdl:operation name="isEmployeeCustomer">
			<wsdl:input message="tns:isEmployeeCustomerRequest" name="isEmployeeCustomerRequest"/>
			<wsdl:output message="tns:isEmployeeCustomerResponse" name="isEmployeeCustomerResponse"/>
		</wsdl:operation>		
	</wsdl:portType>
	<wsdl:binding name="AccessControlSOAP" type="tns:AccessControlWS">
		<soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
		<wsdl:operation name="authenticate">
			<soap:operation soapAction=""/>
			<wsdl:input>
				<soap:body use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal"/>
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="isEmployeeCustomer">
			<soap:operation soapAction=""/>
			<wsdl:input>
				<soap:body use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal"/>
			</wsdl:output>
		</wsdl:operation>		
	</wsdl:binding>
	<wsdl:service name="AccessControlServiceWS">
		<wsdl:port binding="tns:AccessControlSOAP" name="AccessControlSOAP">
			<soap:address location="https://localhost:7002/OSB/OpenAM/AccessControlWSProxy"/>
		</wsdl:port>
	</wsdl:service>
</wsdl:definitions>