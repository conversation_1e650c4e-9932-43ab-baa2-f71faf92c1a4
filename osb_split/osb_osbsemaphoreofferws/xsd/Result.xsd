<?xml version="1.0"?>
<!-- $Id: ee3d1c0fcd2404a15c76263dd900cc4c85722e90 $ -->
<xs:schema targetNamespace="http://osb.airbank.cz/contact/result/dto" xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:tns="http://osb.airbank.cz/contact/result/dto" elementFormDefault="qualified"
           attributeFormDefault="unqualified">
  <xs:complexType name="ResultTO">
    <xs:annotation>
      <xs:documentation>
				Complex type which carries information about result.
			</xs:documentation>
    </xs:annotation>
    <xs:sequence>
      <xs:choice>
        <xs:element name="commonResult" type="tns:CommonResultTO"/>
        <xs:element name="surveyResult" type="tns:SurveyResultTO"/>
      </xs:choice>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="CommonResultTO">
    <xs:annotation>
      <xs:documentation>
				Result with text answer only.
			</xs:documentation>
    </xs:annotation>
    <xs:sequence>
      <xs:element name="text" type="xs:string">
        <xs:annotation>
          <xs:documentation>
						Text answer
					</xs:documentation>
        </xs:annotation>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="SurveyResultTO">
    <xs:annotation>
      <xs:documentation>
				Survey result
			</xs:documentation>
    </xs:annotation>
    <xs:sequence>
      <xs:element name="text" type="xs:string" minOccurs="0"/>
      <xs:choice>
        <xs:element name="starsSurveyResult" type="tns:StarsSurveyResultTO"/>
        <xs:element name="choiceSurveyResult" type="tns:ChoiceSurveyResultTO"/>
      </xs:choice>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="IbSurveyResultTO">
    <xs:annotation>
      <xs:documentation>
				Survey result
			</xs:documentation>
    </xs:annotation>
    <xs:sequence>
      <xs:choice>
        <xs:element name="starsSurveyResult" type="tns:StarsSurveyResultTO"/>
        <xs:element name="choiceSurveyResult" type="tns:ChoiceSurveyResultTO"/>
      </xs:choice>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="StarsSurveyResultTO">
    <xs:annotation>
      <xs:documentation>
				Result of survey rated by stars.
			</xs:documentation>
    </xs:annotation>
    <xs:sequence>
      <xs:element name="number" type="xs:int">
        <xs:annotation>
          <xs:documentation>
						The count of selected stars (Number between 1-5)
					</xs:documentation>
        </xs:annotation>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="ChoiceSurveyResultTO">
    <xs:annotation>
      <xs:documentation>
				List of choice answers.
			</xs:documentation>
    </xs:annotation>
    <xs:sequence>
      <xs:element name="answerOrderNumber" type="xs:int" maxOccurs="unbounded">
        <xs:annotation>
          <xs:documentation>
						Order number of the answer
					</xs:documentation>
        </xs:annotation>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
</xs:schema>
