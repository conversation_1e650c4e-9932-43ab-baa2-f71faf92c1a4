<?xml version="1.0" encoding="UTF-8" ?>
    <xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://osb.abank.cz/semaphoreOffer/" targetNamespace="http://osb.abank.cz/semaphoreOffer/" xmlns:concmn="http://osb.airbank.cz/contact/common/dto" xmlns:res="http://osb.airbank.cz/contact/result/dto">
        <xsd:import namespace="http://osb.airbank.cz/contact/result/dto" schemaLocation="../xsd/Result.xsd" />
        <xsd:import schemaLocation="../xsd/Common.xsd" namespace="http://osb.airbank.cz/contact/common/dto" />
        <xsd:element name="SetSemaphoreOfferReactionRequest">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="semaphoreOfferId" type="xsd:string" />
                    <xsd:element name="employeeNumber" type="xsd:string" />
                    <xsd:element name="employeeDepartment" type="xsd:string" minOccurs="0" />
                    <xsd:element name="contactId" type="concmn:ContactIdTO" />
                    <xsd:element name="reactionChange" type="ReactionChangeTO" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>
        <xsd:element name="SetSemaphoreOfferReactionResponse">
            <xsd:complexType>
                <xsd:sequence/>
            </xsd:complexType>
        </xsd:element>
        <xsd:complexType name="ReactionChangeTO">
            <xsd:sequence>
                <xsd:choice>
                    <xsd:element name="commonReaction" type="CommonReactionTO" />
                    <xsd:element name="resultReaction" type="res:ResultTO" />
                </xsd:choice>
            </xsd:sequence>
        </xsd:complexType>

        <xsd:complexType name="CommonReactionTO">
            <xsd:sequence>
                <xsd:choice>
                    <xsd:element name="meaning" type="CommonReactionMeaningType" />
                    <xsd:element name="comment" type="xsd:string" minOccurs="0" />
                </xsd:choice>
            </xsd:sequence>
        </xsd:complexType>

        <xsd:simpleType name="CommonReactionMeaningType">
            <xsd:annotation>
                <xsd:documentation>Meaning type for common reaction to semaphore offer</xsd:documentation>
            </xsd:annotation>
            <xsd:restriction base="xsd:string">
                <xsd:enumeration value="READING_TO_CLIENT" />
                <xsd:enumeration value="OUTDATING" />
                <xsd:enumeration value="INAPPROPRIATE_OFFERING" />
                <xsd:enumeration value="SAVING_COMMENT" />
                <xsd:enumeration value="FINISHING_WITHOUT_RESULT" />
            </xsd:restriction>
        </xsd:simpleType>
    </xsd:schema>
