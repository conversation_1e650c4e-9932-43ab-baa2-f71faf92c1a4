<?xml version = '1.0' encoding = 'UTF-8'?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://osb.airbank.cz/mdes/card/nfc/wallet"
            targetNamespace="http://osb.airbank.cz/mdes/card/nfc/wallet" elementFormDefault="qualified"
            xmlns:nxsd="http://xmlns.oracle.com/pcbpel/nxsd" 
            xmlns:dig="http://osb.abank.cz/digitization/rest"
            xmlns:com="http://osb.abank.cz/mdes/rest/common"
            nxsd:encoding="UTF-8">
  <xsd:import schemaLocation="digitization.xsd" namespace="http://osb.abank.cz/digitization/rest"/>
  <xsd:import schemaLocation="mdes_common.xsd" namespace="http://osb.abank.cz/mdes/rest/common"/>

  <xsd:element name="notifyTokenUpdatedRequest">
      <xsd:complexType>
          <xsd:sequence>
              <xsd:element name="responseHost" type="xsd:string"/>
              <xsd:element name="requestId" type="xsd:string"/>
              <xsd:element name="encryptedPayload" type="com:EncryptedPayload"/>
          </xsd:sequence>
      </xsd:complexType>
  </xsd:element>
  <xsd:element name="notifyTokenUpdatedResponse">
      <xsd:complexType>
          <xsd:sequence>
              <xsd:element name="responseHost" type="xsd:string"/>
              <xsd:element name="responseId" type="xsd:string"/>
              <xsd:element name="errors" type="com:Error" minOccurs="0" maxOccurs="unbounded"/>
          </xsd:sequence>
      </xsd:complexType>
  </xsd:element>

  <xsd:annotation xmlns="">
    <xsd:appinfo>NXSDSAMPLE=</xsd:appinfo>
    <xsd:appinfo>USEHEADER=false</xsd:appinfo>
  </xsd:annotation>
</xsd:schema>