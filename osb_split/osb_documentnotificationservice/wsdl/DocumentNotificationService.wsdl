<?xml version="1.0" encoding="UTF-8" ?>
<definitions targetNamespace="http://osb.airbank.cz/document/notification"
             xmlns="http://schemas.xmlsoap.org/wsdl/"
             xmlns:tns="http://osb.airbank.cz/document/notification"
             xmlns:xsd="http://www.w3.org/2001/XMLSchema"
             xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/">
  <types>
    <xsd:schema targetNamespace="http://osb.airbank.cz/document/notification" elementFormDefault="qualified">
      <xsd:include schemaLocation="DocumentNotificationService.xsd"/>
    </xsd:schema>
  </types>
  <message name="documentCreatedRequest">
    <part name="documentCreatedRequest" element="tns:documentCreatedRequest"/>
  </message>
  <message name="documentCreatedResponse">
    <part name="documentCreatedResponse" element="tns:documentCreatedResponse"/>
  </message>
  <portType name="DocumentNotificationPort">
    <operation name="documentCreated">
      <input message="tns:documentCreatedRequest"/>
      <output message="tns:documentCreatedResponse"/>
    </operation>
  </portType>
  <binding name="DocumentNotificationSOAP" type="tns:DocumentNotificationPort">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http"/>
    <operation name="documentCreated">
      <soap:operation style="document" soapAction=""/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
  </binding>
  <service name="DocumentNotificationService">
    <port binding="tns:DocumentNotificationSOAP" name="DocumentNotificationSOAP">
      <soap:address location="http://localhost/document/notification"/>
    </port>
  </service>
</definitions>