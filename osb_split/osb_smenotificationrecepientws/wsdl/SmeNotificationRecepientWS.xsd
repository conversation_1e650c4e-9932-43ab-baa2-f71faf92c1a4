<?xml version="1.0" encoding="UTF-8"?>
    <xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://airbank.cz/osb/NotificationRecepient" targetNamespace="http://airbank.cz/osb/NotificationRecepient">

        <xs:element name="SmeNotificationRecepientRequest">
            <xs:complexType>
                <xs:sequence>
                    <xs:element name="cuid" type="xs:long" minOccurs="1" maxOccurs="1" />
                    <xs:element name="deepLink" type="xs:string" minOccurs="0" maxOccurs="1" />
                </xs:sequence>
            </xs:complexType>
        </xs:element>

        <xs:element name="SmeNotificationRecepientResponse">
            <xs:complexType>
                <xs:sequence>
                    <xs:element name="recepientCuid" type="xs:long" minOccurs="0" maxOccurs="1" />
                    <xs:element name="recipientDeepLink" type="xs:long" minOccurs="0" maxOccurs="1" />
                    <xs:element name="contactExternalRelationList" type="ContactExternalRelationListTO" minOccurs="0" maxOccurs="1" />
                </xs:sequence>
            </xs:complexType>
        </xs:element>
        <xs:complexType name="ContactExternalRelationListTO">
            <xs:sequence>
                <xs:element name="entityCode" type="xs:string" minOccurs="1" maxOccurs="1" />
                <xs:element name="instanceId " type="xs:string" minOccurs="1" maxOccurs="1" />
            </xs:sequence>
        </xs:complexType>
    </xs:schema>
