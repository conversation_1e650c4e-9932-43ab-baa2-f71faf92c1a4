<?xml version="1.0" encoding="UTF-8"?>
    <wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:commonFault="http://airbank.cz/common/ws/fault" xmlns="http://airbank.cz/osb/ws/ProcessCustomerAmlRiskDataSet" targetNamespace="http://airbank.cz/osb/ws/ProcessCustomerAmlRiskDataSet">
        <wsdl:types>
            <xs:schema targetNamespace="http://airbank.cz/osb/ws/ProcessCustomerAmlRiskDataSet">
                <xs:include schemaLocation="../xsd/OsbFsmAmlProfileNotificationWS.xsd" />
            </xs:schema>
        </wsdl:types>

        <wsdl:message name="processCustomerAmlRiskDataSetRequest">
            <wsdl:part element="processCustomerAmlRiskDataSetRequest" name="processCustomerAmlRiskDataSetRequest" />
        </wsdl:message>
        <wsdl:message name="processCustomerAmlRiskDataSetResponse">
            <wsdl:part element="processCustomerAmlRiskDataSetResponse" name="processCustomerAmlRiskDataSetResponse" />
        </wsdl:message>

        <wsdl:portType name="processCustomerAmlRiskDataSetPort">
            <wsdl:operation name="processCustomerAmlRiskDataSet">
                <wsdl:input message="processCustomerAmlRiskDataSetRequest" />
                <wsdl:output message="processCustomerAmlRiskDataSetResponse" />
            </wsdl:operation>
        </wsdl:portType>

        <wsdl:binding name="processCustomerAmlRiskDataSetBinding" type="processCustomerAmlRiskDataSetPort">
            <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />
            <wsdl:operation name="processCustomerAmlRiskDataSet">
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
            </wsdl:operation>
        </wsdl:binding>

        <wsdl:service name="processCustomerAmlRiskDataSetService">
            <wsdl:documentation>Private web service</wsdl:documentation>
            <wsdl:port name="processCustomerAmlRiskDataSetPort" binding="processCustomerAmlRiskDataSetBinding">
                <soap:address location="http://localhost:8000/ws/" />
            </wsdl:port>
        </wsdl:service>

    </wsdl:definitions>
