<?xml version="1.0" encoding="UTF-8"?>
    <wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:commonFault="http://airbank.cz/common/ws/fault" xmlns="http://airbank.cz/osb/ws/ProcessBwlCustomerChangeNotification" targetNamespace="http://airbank.cz/osb/ws/ProcessBwlCustomerChangeNotification">
        <wsdl:types>
            <xs:schema targetNamespace="http://airbank.cz/osb/ws/ProcessBwlCustomerChangeNotification">
                <xs:include schemaLocation="../xsd/OsbBwlNotificationWS.xsd" />
            </xs:schema>
            <xs:schema targetNamespace="http://airbank.cz/common/ws/fault">
                <xs:include schemaLocation="../xsd/commonSoapFault.xsd" />
            </xs:schema>
        </wsdl:types>

        <wsdl:message name="ProcessBwlCustomerChangeNotificationRequest">
            <wsdl:part element="ProcessBwlCustomerChangeNotificationRequest" name="ProcessBwlCustomerChangeNotificationRequest" />
        </wsdl:message>
        <wsdl:message name="ProcessBwlCustomerChangeNotificationResponse">
            <wsdl:part element="ProcessBwlCustomerChangeNotificationResponse" name="ProcessBwlCustomerChangeNotificationResponse" />
        </wsdl:message>
        <wsdl:message name="ProcessBwlCustomerChangeNotificationFault">
            <wsdl:part element="commonFault:CoreFaultElement" name="ProcessBwlCustomerChangeNotificationFault" />
        </wsdl:message>


        <wsdl:portType name="ProcessBwlCustomerChangeNotificationPort">
            <wsdl:operation name="ProcessBwlCustomerChangeNotification">
                <wsdl:input message="ProcessBwlCustomerChangeNotificationRequest" />
                <wsdl:output message="ProcessBwlCustomerChangeNotificationResponse" />
                <wsdl:fault name="ProcessBwlCustomerChangeNotificationFault" message="ProcessBwlCustomerChangeNotificationFault" />
            </wsdl:operation>
        </wsdl:portType>

        <wsdl:binding name="ProcessBwlCustomerChangeNotificationBinding" type="ProcessBwlCustomerChangeNotificationPort">
            <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />
            <wsdl:operation name="ProcessBwlCustomerChangeNotification">
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
                <wsdl:fault name="ProcessBwlCustomerChangeNotificationFault">
                    <soap:fault name="ProcessBwlCustomerChangeNotificationFault" use="literal" />
                </wsdl:fault>
            </wsdl:operation>
        </wsdl:binding>

        <wsdl:service name="ProcessBwlCustomerChangeNotificationService">
            <wsdl:documentation>Private web service</wsdl:documentation>
            <wsdl:port name="ProcessBwlCustomerChangeNotificationPort" binding="ProcessBwlCustomerChangeNotificationBinding">
                <soap:address location="http://localhost:8000/ws/" />
            </wsdl:port>
        </wsdl:service>

    </wsdl:definitions>
