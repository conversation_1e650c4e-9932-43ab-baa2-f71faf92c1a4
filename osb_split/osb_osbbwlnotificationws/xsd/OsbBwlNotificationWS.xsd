<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://airbank.cz/osb/ws/ProcessBwlCustomerChangeNotification" elementFormDefault="qualified" targetNamespace="http://airbank.cz/osb/ws/ProcessBwlCustomerChangeNotification">


    <xs:element name="ProcessBwlCustomerChangeNotificationRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Přeposílá notifikace o verifikaci dodaného dokladu klienta z AMS</xs:documentation>
            </xs:annotation>
            <xs:sequence>
                <xs:element name="bwlCustomer" type="BwlCustomer" minOccurs="1" maxOccurs="unbounded" />
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="ProcessBwlCustomerChangeNotificationResponse">
        <xs:complexType>
        </xs:complexType>
    </xs:element>



    <xs:complexType name="BwlCustomer">
        <xs:sequence>
            <xs:element name="change" type="BwListChange" minOccurs="1" maxOccurs="1" />
            <xs:element name="bwListName" type="xs:string" minOccurs="1" maxOccurs="1" />
            <xs:element name="cuid" type="xs:long" minOccurs="1" maxOccurs="1" />
        </xs:sequence>
    </xs:complexType>


    <xs:simpleType name="BwListChange">
        <xs:restriction base="xs:string">
            <xs:enumeration value="ADD" />
            <xs:enumeration value="DELETE" />
        </xs:restriction>
    </xs:simpleType>


</xs:schema>
