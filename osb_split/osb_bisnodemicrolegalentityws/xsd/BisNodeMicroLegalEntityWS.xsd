<?xml version="1.0" encoding="UTF-8"?>
    <xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:jxb="http://java.sun.com/xml/ns/jaxb" targetNamespace="http://airbank.cz/osb/bisnode/microlegalentity" xmlns="http://airbank.cz/osb/bisnode/microlegalentity" jxb:version="2.1" elementFormDefault="qualified">

        <xsd:element name="ReportRequest">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="identificationNumber" type="xsd:string" maxOccurs="unbounded">
                        <xsd:annotation>
                            <xsd:documentation>Client's id</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>

        <xsd:element name="ReportResponse">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="report" type="reportType" minOccurs="1" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:documentation>Client's BisNode complex info</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>



        <xsd:complexType name="office_informationType">
            <xsd:sequence>
                <xsd:element name="ent_id" maxOccurs="1" minOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Unikátní identifikátor entity</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:long">
                            <xsd:pattern value="\d{1,20}" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="reg_nbr" maxOccurs="1" minOccurs="0" type="xsd:string">
                    <xsd:annotation>
                        <xsd:documentation>CZ IČO</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="vat_nbr" maxOccurs="1" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>CZ DIČ</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:pattern value="[*]{0}|(CZ)?[0-9]{8,10}" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="group_vat_nbr" maxOccurs="1" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>CZ skupinové DIČ</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:pattern value="[*]{0}|(CZ)?[0-9]{8,10}" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="duns" maxOccurs="1" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>DUNS číslo</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:pattern value="[*]{0}|\d{9}" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="name" maxOccurs="1" minOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Název subjektu</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:maxLength value="500" />
                            <xsd:minLength value="1" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="date_of_establishment" maxOccurs="1" minOccurs="0" type="xsd:string">
                    <xsd:annotation>
                        <xsd:documentation>Datum založení</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="date_of_termination" maxOccurs="1" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Datum zániku</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:maxLength value="8" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="court_txt" maxOccurs="1" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Rejstříkový soud</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:maxLength value="50" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="file_nbr" maxOccurs="1" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Spisová značka</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:maxLength value="15" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="lei_txt" maxOccurs="1" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>LEI code</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:maxLength value="20" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="data_box_txt" maxOccurs="1" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Datová schránka</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:maxLength value="10" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="legal_form" maxOccurs="1" minOccurs="0" type="xsd:string">
                    <xsd:annotation>
                        <xsd:documentation>Právní forma</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="esa2010" maxOccurs="1" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>ESA 2010</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:maxLength value="5" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="reg_capl" type="empty_or_decimal" maxOccurs="1" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Registrovaný kapitál</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="reg_capl_crcy" maxOccurs="1" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Měna registrovaného kapitálu</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:pattern value="[*]{0}|[A-Z]{3}" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="empl_cnt_category" maxOccurs="1" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Kategorie dle počtu zaměstnanců</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:maxLength value="2" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="turnover_category" maxOccurs="1" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Kategorie dle obratu</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:maxLength value="2" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>

        <xsd:simpleType name="empty_or_decimal">
            <xsd:union memberTypes="empty xsd:decimal" />
        </xsd:simpleType>
        <xsd:simpleType name="empty">
            <xsd:restriction base="xsd:string">
                <xsd:enumeration value="" />
            </xsd:restriction>
        </xsd:simpleType>

        <xsd:complexType name="addressType">
            <xsd:sequence>
                <xsd:element name="ent_id" maxOccurs="1" minOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Unikátní identifikátor entity</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:long">
                            <xsd:pattern value="\d{1,20}" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="ruian_id" maxOccurs="1" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>RUIAN ID</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:pattern value="[*]{0}|[0-9]{2,12}" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="full_txt" maxOccurs="1" minOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Plná adresa subjektu</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:maxLength value="150" />
                            <xsd:minLength value="1" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="gps_x" maxOccurs="1" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>GPS X</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:maxLength value="20" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="gps_y" maxOccurs="1" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>GPS Y</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:maxLength value="20" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="district" maxOccurs="1" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Okres</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:maxLength value="25" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="region" maxOccurs="1" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Kraj</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:maxLength value="25" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="city" maxOccurs="1" minOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Město</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:maxLength value="65" />
                            <xsd:minLength value="1" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="city_part" maxOccurs="1" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Městská část</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:maxLength value="40" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="street" maxOccurs="1" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Ulice</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:maxLength value="50" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="house_nbr" maxOccurs="1" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Číslo popisné</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:maxLength value="5" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="ori_nbr" maxOccurs="1" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Číslo orientační</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:maxLength value="5" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="zip" maxOccurs="1" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>ZIP/PSČ</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:maxLength value="25" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="country" maxOccurs="1" minOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Země</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:maxLength value="55" />
                            <xsd:minLength value="1" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="state_iso_2" maxOccurs="1" minOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Country Codes List: ISO Alpha-2</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:pattern value="[A-Z]{2}" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="state_iso_3" maxOccurs="1" minOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Country Codes List: ISO Alpha-3</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:pattern value="[A-Z]{3}" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="strt_dt" maxOccurs="1" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Platnost adresy OD</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:maxLength value="8" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
        <xsd:complexType name="main_naceType">
            <xsd:sequence>
                <xsd:element name="code" maxOccurs="1" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Hlavní NACE</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:maxLength value="5" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
        <xsd:complexType name="secondary_naceType">
            <xsd:sequence>
                <xsd:element name="code" maxOccurs="1" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Vedlejší NACE</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:maxLength value="5" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
        <xsd:complexType name="nacesType">
            <xsd:sequence>
                <xsd:element type="main_naceType" name="main_nace" maxOccurs="1" minOccurs="0" />
                <xsd:element type="secondary_naceType" name="secondary_nace" maxOccurs="unbounded" minOccurs="0" />
            </xsd:sequence>
        </xsd:complexType>
        <xsd:complexType name="business_subjectType">
            <xsd:sequence>
                <xsd:element name="trd_nbr" maxOccurs="1" minOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Unikátní identifikátor živnosti</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:long">
                            <xsd:pattern value="\d{1,20}" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="trd_type_cd" maxOccurs="1" minOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Kód typu živnosti</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:maxLength value="20" />
                            <xsd:minLength value="1" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="value_txt" maxOccurs="1" minOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Předmět podnikání</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:maxLength value="1000" />
                            <xsd:minLength value="1" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="strt_dt" maxOccurs="1" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Datum vzniku oprávnění</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:pattern value="[*]{0}|\d{8}" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="end_dt" maxOccurs="1" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Datum zániku oprávnění</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:pattern value="[*]{0}|\d{8}" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
        <xsd:complexType name="field_of_activityType">
            <xsd:sequence>
                <xsd:element name="trd_nbr" maxOccurs="1" minOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Unikátní identifikátor živnosti</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:long">
                            <xsd:pattern value="\d{1,20}" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="value_txt" maxOccurs="1" minOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Obor činnosti</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:maxLength value="500" />
                            <xsd:minLength value="1" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="strt_dt" maxOccurs="1" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Datum vzniku oboru činnosti</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:pattern value="[*]{0}|\d{8}" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
        <xsd:complexType name="trade_licensesType">
            <xsd:sequence>
                <xsd:element type="business_subjectType" name="business_subject" maxOccurs="unbounded" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Předmět podnikání</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element type="field_of_activityType" name="field_of_activity" maxOccurs="unbounded" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Obor činnosti</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element type="business_interruptionType" name="business_interruption" maxOccurs="unbounded" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Přerušení živnosti</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
        <xsd:complexType name="business_interruptionType">
            <xsd:sequence>
                <xsd:element name="trd_nbr" maxOccurs="1" minOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Unikátní identifikátor živnosti</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:long">
                            <xsd:pattern value="\d{1,20}" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="strt_dt" maxOccurs="1" minOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Přerušení živnosti OD</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:pattern value="\d{8}" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="end_dt" maxOccurs="1" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Přerušení živnosti DO</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:pattern value="[*]{0}|\d{8}" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
        <xsd:complexType name="foType">
            <xsd:sequence>
                <xsd:element name="ent_id" maxOccurs="1" minOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Unikátní identifikátor entity</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:long">
                            <xsd:pattern value="\d{1,20}" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="full_name" maxOccurs="1" minOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Celé jméno FO</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:maxLength value="250" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="title_pfx" maxOccurs="1" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Titul před jménem</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:maxLength value="30" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="first_name" maxOccurs="1" minOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Křestní jméno</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:maxLength value="80" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="last_name" maxOccurs="1" minOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Příjmení</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:maxLength value="60" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="title_sfx" maxOccurs="1" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Titul za jménem</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:maxLength value="30" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="birth_date" maxOccurs="1" minOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Datum narození</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:pattern value="\d{8}" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="pers_nationality" maxOccurs="1" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Country Codes: ISO Alpha-2</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:pattern value="[*]{0}|[A-Z]{2}" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
        <xsd:complexType name="entitiesType">
            <xsd:sequence>
                <xsd:element type="foType" name="fo" maxOccurs="unbounded" minOccurs="0" />
                <xsd:element type="office_informationType" name="po" maxOccurs="unbounded" minOccurs="0" />
            </xsd:sequence>
        </xsd:complexType>
        <xsd:complexType name="actType">
            <xsd:sequence>
                <xsd:element name="type_cd" maxOccurs="1" minOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Kód typu</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:minLength value="1" />
                            <xsd:maxLength value="10" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="value_txt" maxOccurs="1" minOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Hodnoty typu</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:minLength value="1" />
                            <xsd:maxLength value="3500" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="acts_independently_ind" maxOccurs="1" minOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Indikace - jedná samostatně</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:boolean" />
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="strt_dt" maxOccurs="1" minOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Platnost OD</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:pattern value="\d{8}" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="end_dt" maxOccurs="1" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Platnost DO</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:pattern value="[*]{0}|\d{8}" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
        <xsd:complexType name="board_memberType">
            <xsd:sequence>
                <xsd:element name="ent_id" maxOccurs="1" minOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Unikátní identifikátor entity</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:long">
                            <xsd:pattern value="\d{1,20}" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="type_cd" maxOccurs="1" minOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Kód typu</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:minLength value="1" />
                            <xsd:maxLength value="2" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="strt_dt" maxOccurs="1" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Platnost OD</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:pattern value="[*]{0}|\d{8}" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="end_dt" maxOccurs="1" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Platnost DO</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:pattern value="[*]{0}|\d{8}" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
        <xsd:complexType name="firstLevelOwnerType">
            <xsd:sequence>
                <xsd:element name="ent_id" maxOccurs="1" minOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Unikátní identifikátor entity</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:long">
                            <xsd:pattern value="\d{1,20}" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="type_cd" maxOccurs="1" minOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Kód typu</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:minLength value="1" />
                            <xsd:maxLength value="2" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element type="xsd:float" name="value_pct" maxOccurs="1" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>% vlastnictví</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="strt_dt" maxOccurs="1" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Platnost OD</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:pattern value="[*]{0}|\d{8}" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="end_dt" maxOccurs="1" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Platnost DO</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:pattern value="[*]{0}|\d{8}" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
        <xsd:complexType name="beneficialOwnerType">
            <xsd:sequence>
                <xsd:element name="ent_id" maxOccurs="1" minOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Unikátní identifikátor entity</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:long">
                            <xsd:pattern value="\d{1,20}" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="owner_type" maxOccurs="1" minOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Typ</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:minLength value="1" />
                            <xsd:maxLength value="100" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="direct_share_pct" maxOccurs="1" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>% přímého vlastnictví</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:maxLength value="10" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="indirect_share_pct" maxOccurs="1" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>% nepřímého vlastnictví</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:maxLength value="10" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="voting_direct_share_pct" maxOccurs="1" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>% přímého hlasovacího práva</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:maxLength value="15" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="voting_indirect_share_pct" maxOccurs="1" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>% nepřímého hlasovacího práva</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:maxLength value="15" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="note_txt" maxOccurs="1" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Poznámka</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:maxLength value="2000" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="entry_dt" maxOccurs="1" minOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Datum zápis</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:pattern value="\d{8}" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="strt_dt" maxOccurs="1" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Platnost OD</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:pattern value="[*]{0}|\d{8}" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="end_dt" maxOccurs="1" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Platnost DO</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:pattern value="[*]{0}|\d{8}" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
        <xsd:complexType name="subjectType">
            <xsd:sequence>
                <xsd:element type="office_informationType" name="office_information" />
                <xsd:element type="addressType" name="address" maxOccurs="unbounded" minOccurs="1" />
                <xsd:element type="nacesType" name="naces" maxOccurs="1" minOccurs="1" />
                <xsd:element name="entities" type="entitiesType" />
                <xsd:element type="actType" name="act" maxOccurs="1" minOccurs="0" />
                <xsd:element type="board_memberType" name="board_member" maxOccurs="unbounded" minOccurs="0" />
                <xsd:element type="firstLevelOwnerType" name="firstLevelOwner" maxOccurs="unbounded" minOccurs="0" />
                <xsd:element type="beneficialOwnerType" name="beneficialOwner" maxOccurs="unbounded" minOccurs="0" />
                <xsd:element name="trade_licenses" type="trade_licensesType" />
            </xsd:sequence>
        </xsd:complexType>
        <xsd:complexType name="reportType">
            <xsd:choice maxOccurs="unbounded" minOccurs="0">
                <xsd:element type="subjectType" name="subject" />
            </xsd:choice>
        </xsd:complexType>
    </xsd:schema>
