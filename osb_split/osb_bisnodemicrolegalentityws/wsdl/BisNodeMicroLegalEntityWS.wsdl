<?xml version="1.0" encoding="UTF-8"?>
    <wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:commonFault="http://airbank.cz/common/ws/fault" xmlns="http://airbank.cz/osb/bisnode/microlegalentity" targetNamespace="http://airbank.cz/osb/bisnode/microlegalentity">

        <wsdl:types>
            <xsd:schema targetNamespace="http://airbank.cz/osb/bisnode/microlegalentity">
                <xsd:include schemaLocation="../xsd/BisNodeMicroLegalEntityWS.xsd" />
            </xsd:schema>
            <xsd:schema targetNamespace="http://airbank.cz/common/ws/fault">
                <xsd:include schemaLocation="../xsd/commonSoapFault.xsd" />
            </xsd:schema>
        </wsdl:types>


        <wsdl:message name="ReportRequest">
            <wsdl:part element="ReportRequest" name="ReportRequest" />
        </wsdl:message>
        <wsdl:message name="ReportResponse">
            <wsdl:part element="ReportResponse" name="ReportResponse" />
        </wsdl:message>
        <wsdl:message name="ReportFault">
            <wsdl:part element="commonFault:CoreFaultElement" name="ReportFault" />
        </wsdl:message>

        <wsdl:portType name="ReportWS">

            <wsdl:operation name="Report">
                <wsdl:input message="ReportRequest" />
                <wsdl:output message="ReportResponse" />
                <wsdl:fault name="ReportFault" message="ReportFault" />
            </wsdl:operation>

        </wsdl:portType>

        <wsdl:binding name="ReportBinding" type="ReportWS">
            <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />

            <wsdl:operation name="Report">
                <soap:operation soapAction="" />
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
                <wsdl:fault name="ReportFault">
                    <soap:fault name="ReportFault" use="literal" />
                </wsdl:fault>
            </wsdl:operation>

        </wsdl:binding>

        <wsdl:service name="ReportWSBinding">
            <wsdl:port binding="ReportBinding" name="ReportBinding">
                <soap:address location="/ws/ReportWS" />
            </wsdl:port>
        </wsdl:service>
    </wsdl:definitions>
