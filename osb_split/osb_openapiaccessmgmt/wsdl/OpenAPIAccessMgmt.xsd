<?xml version="1.0" encoding="UTF-8"?>
<xs:schema elementFormDefault="qualified" xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns="http://homecredit.eu/openapi/accessManagement"
           targetNamespace="http://homecredit.eu/openapi/accessManagement">
  <xs:annotation>
    <xs:documentation>Calendar manager</xs:documentation>
  </xs:annotation>
  <xs:element name="getApiClientCertAuthDetailsResponse">
    <xs:complexType>
      <xs:annotation>
        <xs:documentation>getApiClientDetails response
                    <!--"api_key": <string>, required-->
                    <!--"consumer_id": <uuid>, required-->
                    <!--"created_at": <number>, required-->
                    <!--"id": <uuid>, required-->
                    <!--"name": <string>, required-->
                </xs:documentation>
      </xs:annotation>
      <xs:sequence>
        <xs:element name="api_key" type="xs:string" minOccurs="1" maxOccurs="1">
          <xs:annotation>
            <xs:documentation>client id</xs:documentation>
          </xs:annotation>
        </xs:element>
        <xs:element name="consumer_id" type="xs:string" minOccurs="1" maxOccurs="1">
          <xs:annotation>
            <xs:documentation>consumer uuid</xs:documentation>
          </xs:annotation>
        </xs:element>
        <xs:element name="created_at" type="xs:dateTime" minOccurs="1" maxOccurs="1">
          <xs:annotation>
            <xs:documentation>create at</xs:documentation>
          </xs:annotation>
        </xs:element>
        <xs:element name="id" type="xs:string" minOccurs="1" maxOccurs="1">
          <xs:annotation>
            <xs:documentation>UUID identifier</xs:documentation>
          </xs:annotation>
        </xs:element>
        <xs:element name="name" type="xs:string" minOccurs="1" maxOccurs="1">
          <xs:annotation>
            <xs:documentation>name</xs:documentation>
          </xs:annotation>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="getApiClientCertAuthDetailsRequest">
    <xs:complexType>
      <xs:annotation>
        <xs:documentation>getApiClientDetails request</xs:documentation>
      </xs:annotation>
      <xs:sequence>
        <xs:element name="api_key" type="xs:string" minOccurs="1" maxOccurs="1">
          <xs:annotation>
            <xs:documentation>client id</xs:documentation>
          </xs:annotation>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="getApiClientDetailsResponse">
    <xs:complexType>
      <xs:annotation>
        <xs:documentation>getApiClientDetails response
                    <!--"client_id": <string>, required-->
                    <!--"client_secret": <string>, optional-->
                    <!--"consumer_id": <uuid>, required-->
                    <!--"created_at": <number>, required-->
                    <!--"id": <uuid>, required-->
                    <!--"name": <string>, required-->
                    <!--"redirect_uri": <array of string>, required-->
                </xs:documentation>
      </xs:annotation>
      <xs:sequence>
        <xs:element name="client_id" type="xs:string" minOccurs="1" maxOccurs="1">
          <xs:annotation>
            <xs:documentation>client id</xs:documentation>
          </xs:annotation>
        </xs:element>
        <xs:element name="client_secret" type="xs:string" minOccurs="0" maxOccurs="1">
          <xs:annotation>
            <xs:documentation>client secret</xs:documentation>
          </xs:annotation>
        </xs:element>
        <xs:element name="consumer_id" type="xs:string" minOccurs="1" maxOccurs="1">
          <xs:annotation>
            <xs:documentation>consumer uuid</xs:documentation>
          </xs:annotation>
        </xs:element>
        <xs:element name="created_at" type="xs:dateTime" minOccurs="1" maxOccurs="1">
          <xs:annotation>
            <xs:documentation>create at</xs:documentation>
          </xs:annotation>
        </xs:element>
        <xs:element name="id" type="xs:string" minOccurs="1" maxOccurs="1">
          <xs:annotation>
            <xs:documentation>UUID identifier</xs:documentation>
          </xs:annotation>
        </xs:element>
        <xs:element name="name" type="xs:string" minOccurs="1" maxOccurs="1">
          <xs:annotation>
            <xs:documentation>name</xs:documentation>
          </xs:annotation>
        </xs:element>
        <xs:element name="redirect_uri" type="xs:string" minOccurs="1" maxOccurs="unbounded">
          <xs:annotation>
            <xs:documentation>redirect uris</xs:documentation>
          </xs:annotation>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="getApiClientDetailsRequest">
    <xs:complexType>
      <xs:annotation>
        <xs:documentation>getApiClientDetails request</xs:documentation>
      </xs:annotation>
      <xs:sequence>
        <xs:element name="client_id" type="xs:string" minOccurs="1" maxOccurs="1">
          <xs:annotation>
            <xs:documentation>client id</xs:documentation>
          </xs:annotation>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="authorizeUserRequest">
    <xs:complexType>
      <xs:annotation>
        <xs:documentation>AuthorizeUser request
                    <!--client_id": <string>, required-->
                    <!--"response_type": <enum of strings>, required-->
                    <!--"code"-->
                    <!--"token"-->
                    <!--"provision_key": <string>, required-->
                    <!--"authenticated_userid": <string>, required-->
                    <!--"scope": <string>, optional-->
                    <!--"state": <string>, optional-->
                    <!--"redirect_uri": <string>, optional-->
                </xs:documentation>
      </xs:annotation>
      <xs:sequence>
        <xs:element name="client_id" type="xs:string" minOccurs="1" maxOccurs="1">
          <xs:annotation>
            <xs:documentation>client id</xs:documentation>
          </xs:annotation>
        </xs:element>
        <xs:element name="response_type" type="xs:string" minOccurs="1" maxOccurs="1">
          <xs:annotation>
            <xs:documentation>code or token</xs:documentation>
          </xs:annotation>
        </xs:element>
        <xs:element name="provision_key" type="xs:string" minOccurs="1" maxOccurs="1">
          <xs:annotation>
            <xs:documentation>code or token</xs:documentation>
          </xs:annotation>
        </xs:element>
        <xs:element name="authenticated_userid" type="xs:string" minOccurs="1" maxOccurs="1">
          <xs:annotation>
            <xs:documentation>authenticated user id</xs:documentation>
          </xs:annotation>
        </xs:element>
        <xs:element name="scope" type="xs:string" minOccurs="0" maxOccurs="1">
          <xs:annotation>
            <xs:documentation>scope</xs:documentation>
          </xs:annotation>
        </xs:element>
        <xs:element name="state" type="xs:string" minOccurs="0" maxOccurs="1">
          <xs:annotation>
            <xs:documentation>state</xs:documentation>
          </xs:annotation>
        </xs:element>
        <xs:element name="redirect_uri" type="xs:string" minOccurs="0" maxOccurs="1">
          <xs:annotation>
            <xs:documentation>redirect uri</xs:documentation>
          </xs:annotation>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="authorizeUserResponse">
    <xs:complexType>
      <xs:annotation>
        <xs:documentation>AuthorizeUser response
                    <!--"redirect_uri": <string>, required-->
                </xs:documentation>
      </xs:annotation>
      <xs:sequence>
        <xs:element name="redirect_uri" type="xs:string" minOccurs="1" maxOccurs="1">
          <xs:annotation>
            <xs:documentation>redirect uri</xs:documentation>
          </xs:annotation>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="getPairedAppsRequest">
    <xs:complexType>
      <xs:annotation>
        <xs:documentation>GetPairedApps request
                    <!--"cuid": <string>, required-->
                </xs:documentation>
      </xs:annotation>
      <xs:sequence>
        <xs:element name="cuid" type="xs:string" minOccurs="1" maxOccurs="1">
          <xs:annotation>
            <xs:documentation>customer id</xs:documentation>
          </xs:annotation>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="getPairedAppsResponse">
    <xs:complexType>
      <xs:annotation>
        <xs:documentation>GetPairedApps response</xs:documentation>
      </xs:annotation>
      <xs:sequence>
        <xs:element name="pairedApp" type="PairedApp" minOccurs="0" maxOccurs="unbounded">
          <xs:annotation>
            <xs:documentation>redirect uri</xs:documentation>
          </xs:annotation>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:complexType name="PairedApp">
    <xs:annotation>
      <xs:documentation>Paired App
                <!--"app_name": <string>, required-->
                <!--"created_at": <number>, required-->
                <!--"redirect_uri": <array of strings>, required-->
                <!--"scope": <string>, optional-->
                <!--"token_id": <uuid>, required-->
            </xs:documentation>
    </xs:annotation>
    <xs:sequence>
      <xs:element name="app_name" type="xs:string" minOccurs="1" maxOccurs="1">
        <xs:annotation>
          <xs:documentation>application name</xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element name="created_at" type="xs:dateTime" minOccurs="1" maxOccurs="1">
        <xs:annotation>
          <xs:documentation>create at</xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element name="redirect_uri" type="xs:string" minOccurs="1" maxOccurs="unbounded">
        <xs:annotation>
          <xs:documentation>registered redirect uris</xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element name="scope" type="xs:string" minOccurs="0" maxOccurs="1">
        <xs:annotation>
          <xs:documentation>scope</xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element name="token_id" type="xs:string" minOccurs="1" maxOccurs="1">
        <xs:annotation>
          <xs:documentation>token id</xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element name="consumer_id" type="xs:string" minOccurs="1" maxOccurs="1">
        <xs:annotation>
          <xs:documentation>customer id</xs:documentation>
        </xs:annotation>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="revokeApplicationAccessRequest">
    <xs:complexType>
      <xs:annotation>
        <xs:documentation>revokeApplicationAccess request</xs:documentation>
      </xs:annotation>
      <xs:sequence>
        <xs:element name="access_id" type="xs:string" minOccurs="1" maxOccurs="1">
          <xs:annotation>
            <xs:documentation>access token id</xs:documentation>
          </xs:annotation>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="revokeApplicationAccessResponse">
    <xs:complexType>
      <xs:annotation>
        <xs:documentation>revokeApplicationAccess response
                    <!--"status_code": <string> (from returned HTTP status code)-->
                </xs:documentation>
      </xs:annotation>
      <xs:sequence>
        <xs:element name="status_code" type="xs:unsignedShort" minOccurs="1" maxOccurs="1">
          <xs:annotation>
            <xs:documentation>http status code</xs:documentation>
          </xs:annotation>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="getConsumerRequest">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="consumer_id" type="xs:string"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="getConsumerResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="created_at" type="xs:dateTime"/>
        <xs:element name="custom_id" type="xs:string"/>
        <xs:element name="id" type="xs:string"/>
        <xs:element name="username" type="xs:string"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="getConsumersACLSRequest">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="consumer_id" type="xs:string"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="getConsumersACLSResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="data" maxOccurs="unbounded">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="group" type="xs:string"/>
              <xs:element name="consumer_id" type="xs:string"/>
              <xs:element name="created_at" type="xs:dateTime"/>
              <xs:element name="id" type="xs:string"/>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="total" type="xs:integer"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="simpleFault">
    <xs:complexType>
      <xs:annotation>
        <xs:documentation>common http fault</xs:documentation>
      </xs:annotation>
      <xs:sequence>
        <xs:element name="status_code" type="xs:unsignedShort" minOccurs="1">
          <xs:annotation>
            <xs:documentation>http status code</xs:documentation>
          </xs:annotation>
        </xs:element>
        <xs:element name="body" type="xs:string" minOccurs="0">
          <xs:annotation>
            <xs:documentation>http response body</xs:documentation>
          </xs:annotation>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
</xs:schema>
