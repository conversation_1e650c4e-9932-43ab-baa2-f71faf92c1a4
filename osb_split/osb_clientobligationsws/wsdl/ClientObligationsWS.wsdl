<?xml version="1.0" encoding="UTF-8" ?>
    <wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns="http://airbank.cz/osb/ClientObligations" targetNamespace="http://airbank.cz/osb/ClientObligations">

        <wsdl:types>
            <xs:schema targetNamespace="http://airbank.cz/osb/ClientObligations">
                <xs:include schemaLocation="ClientObligationsWS.xsd" />
            </xs:schema>
        </wsdl:types>

        <wsdl:message name="GetCreditExposureRequest">
            <wsdl:part element="GetCreditExposureRequest" name="GetCreditExposureRequest" />
        </wsdl:message>
        <wsdl:message name="GetCreditExposureResponse">
            <wsdl:part element="GetCreditExposureResponse" name="GetCreditExposureResponse" />
        </wsdl:message>

        <wsdl:portType name="ClientObligations">
            <wsdl:operation name="GetCreditExposure">
                <xs:documentation>Rozhraní webove operace pro zasílání informací o úvěrové angažovanosti klienta</xs:documentation>
                <wsdl:input message="GetCreditExposureRequest" />
                <wsdl:output message="GetCreditExposureResponse" />
            </wsdl:operation>
        </wsdl:portType>

        <wsdl:binding name="ClientObligationsBinding" type="ClientObligations">
            <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />
            <wsdl:operation name="GetCreditExposure">
                <soap:operation soapAction="" />
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
            </wsdl:operation>
        </wsdl:binding>

        <wsdl:service name="ClientObligationsWS">
            <wsdl:port binding="ClientObligationsBinding" name="ClientObligationsPort">
                <soap:address location="http://TO-BE-SPECIFIED/osb/ClientObligations" />
            </wsdl:port>
        </wsdl:service>

    </wsdl:definitions>
