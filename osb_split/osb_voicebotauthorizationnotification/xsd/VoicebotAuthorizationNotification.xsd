<?xml version="1.0" encoding="UTF-8" standalone="no"?>
    <xsd:schema targetNamespace="http://osb.airbank.cz/addAI/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tns="http://osb.airbank.cz/addAI/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified">
        <xsd:element name="VoicebotAuthorizationNotificationRequest">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="sessionId" type="xsd:string">
                        <xsd:annotation>
                            <xsd:documentation>client's sessionId</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="authId" type="xsd:string">
                        <xsd:annotation>
                            <xsd:documentation>client's authId</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>

        <xsd:element name="VoicebotAuthorizationNotificationResponse">
            <xsd:complexType/>
        </xsd:element>

    </xsd:schema>
