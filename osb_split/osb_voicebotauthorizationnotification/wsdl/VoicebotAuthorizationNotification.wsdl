<wsdl:definitions name="VoicebotAuthorizationNotification" targetNamespace="http://osb.airbank.cz/addAI/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tns="http://osb.airbank.cz/addAI/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">

    <wsdl:types>
        <xsd:schema targetNamespace="http://osb.airbank.cz/addAI/">
            <xsd:include schemaLocation="../xsd/VoicebotAuthorizationNotification.xsd" />
        </xsd:schema>
    </wsdl:types>

    <wsdl:message name="VoicebotAuthorizationNotificationRequest">
        <wsdl:part element="tns:VoicebotAuthorizationNotificationRequest" name="VoicebotAuthorizationNotificationRequest" />
    </wsdl:message>
    <wsdl:message name="VoicebotAuthorizationNotificationResponse">
        <wsdl:part element="tns:VoicebotAuthorizationNotificationResponse" name="VoicebotAuthorizationNotificationResponse" />
    </wsdl:message>

    <wsdl:portType name="VoicebotAuthorizationNotificationWS">
        <wsdl:operation name="VoicebotAuthorizationNotification">
            <wsdl:input message="tns:VoicebotAuthorizationNotificationRequest" name="VoicebotAuthorizationNotificationRequest" />
            <wsdl:output message="tns:VoicebotAuthorizationNotificationResponse" name="VoicebotAuthorizationNotificationResponse" />
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="VoicebotAuthorizationNotificationSOAP" type="tns:VoicebotAuthorizationNotificationWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />
        <wsdl:operation name="VoicebotAuthorizationNotification">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="VoicebotAuthorizationNotificationServiceWS">
        <wsdl:port binding="tns:VoicebotAuthorizationNotificationSOAP" name="VoicebotAuthorizationNotificationSOAP">
            <soap:address location="https://localhost:7002/AuthorizingProxy/AddAIAssistant/VoicebotAuthorizationNotificationWSProxy" />
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>
