<?xml version="1.0"?>
    <!-- $Id: e4d5dc3cca7f9a0b8b405180198626323ddf352e $ -->
    <xsd:schema targetNamespace="http://osb.abank.cz/contact/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" elementFormDefault="unqualified" attributeFormDefault="unqualified">
        <xsd:simpleType name="Channel">
            <xsd:annotation>
                <xsd:documentation>
                    Available Contact channels.
                </xsd:documentation>
            </xsd:annotation>
            <xsd:restriction base="xsd:string">
                <xsd:enumeration value="EMAIL" />
                <xsd:enumeration value="IB" />
                <xsd:enumeration value="SMS" />
                <xsd:enumeration value="CALL" />
                <xsd:enumeration value="PRINT" />
                <xsd:enumeration value="VISIT" />
                <xsd:enumeration value="WEB_CHAT" />
                <xsd:enumeration value="MA_CHAT" />
            </xsd:restriction>
        </xsd:simpleType>

        <xsd:simpleType name="AlertChannelTO">
            <xsd:annotation>
                <xsd:documentation>
                    Available alert contact channels.
                </xsd:documentation>
            </xsd:annotation>
            <xsd:restriction base="xsd:string">
                <xsd:enumeration value="EMAIL" />
                <xsd:enumeration value="IB" />
                <xsd:enumeration value="CALL" />
            </xsd:restriction>
        </xsd:simpleType>

        <xsd:complexType name="OriginContext">
            <xsd:annotation>
                <xsd:documentation>
                    Context of contact origin
                </xsd:documentation>
            </xsd:annotation>
            <xsd:sequence>
                <xsd:element name="businessProcess" type="xsd:string">
                    <xsd:annotation>
                        <xsd:documentation>
                            Business process context
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="businessIdentification" type="xsd:string" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>
                            Business process identification, e.g. application ID
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="businessProcessEvent" type="xsd:string" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>
                            A business process event that triggered planned call
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:schema>
