<xsd:schema targetNamespace="http://osb.abank.cz/click2interaction/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:tns="http://osb.abank.cz/click2interaction/" xmlns:con="http://osb.abank.cz/contact/" xmlns:comm="http://osb.airbank.cz/contact/common/dto">
    <xsd:import schemaLocation="osbContactWS.xsd" namespace="http://osb.abank.cz/contact/" />
    <xsd:import schemaLocation="../xsd/Common.xsd" namespace="http://osb.airbank.cz/contact/common/dto" />
    <xsd:element name="Click2CallRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="cuid" type="xsd:long" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Unique identification of the customer</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="employeeNumber" type="xsd:string" />
                <xsd:element name="personPhone" type="comm:PhoneNumberTO" />
                <xsd:element name="originContext" type="con:OriginContextExtendedTO" />
                <xsd:element name="externalRelation" type="comm:ExternalRelationTO" minOccurs="0" maxOccurs="unbounded" />
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="Click2CallResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="conceptContactId" type="comm:ContactIdTO">
                    <xsd:annotation>
                        <xsd:documentation>Message identification in CML.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="resultCode" type="xsd:string" />
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="Click2OfflineContactRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="employeeNumber" type="xsd:string" />
                <xsd:element name="person" type="con:PersonTO" />
                <xsd:element name="offlineFromBankContact" type="con:OfflineFromBankContactTO" />
                <xsd:element name="originContext" type="con:OriginContextExtendedTO" />
                <xsd:element name="externalRelation" type="comm:ExternalRelationTO" minOccurs="0" maxOccurs="unbounded" />
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="Click2OfflineContactResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="conceptContactId" type="comm:ContactIdTO" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Message identification in CML.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="resultCode" type="xsd:string" minOccurs="0" />
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="Click2CustomerDetailRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="employeeNumber" type="xsd:string" minOccurs="1" maxOccurs="1" />
                <xsd:element name="cuid" type="xsd:long" minOccurs="1" maxOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Unique identification of the customer</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="originContext" type="con:OriginContextExtendedTO" />
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="Click2CustomerDetailResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="resultCode" type="xsd:string" minOccurs="1" maxOccurs="1" />
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
</xsd:schema>
