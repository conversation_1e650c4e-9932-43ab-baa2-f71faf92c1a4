<?xml version="1.0" encoding="UTF-8"?>
    <xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://airbank.cz/osb/nia/InputsService" targetNamespace="http://airbank.cz/osb/nia/InputsService">

        <xs:element name="IsPhysicallyIdentifiedRequest">
            <xs:complexType>
                <xs:sequence>
                    <xs:element name="cuid" type="xs:long" minOccurs="1" maxOccurs="1" />
                </xs:sequence>
            </xs:complexType>
        </xs:element>

        <xs:element name="IsPhysicallyIdentifiedResponse">
            <xs:complexType>
                <xs:sequence>
                    <xs:element name="physicallyIdentified" type="xs:boolean" minOccurs="1" maxOccurs="1" />
                    <xs:element name="identifiedBy" type="IdentifiedBy" minOccurs="0" maxOccurs="2" />
                </xs:sequence>
            </xs:complexType>
        </xs:element>

        <xs:element name="IsEligibleRequest">
            <xs:complexType>
                <xs:sequence>
                    <xs:element name="cuid" type="xs:long" minOccurs="1" maxOccurs="1" />
                </xs:sequence>
            </xs:complexType>
        </xs:element>

        <xs:element name="IsEligibleResponse">
            <xs:complexType>
                <xs:sequence>
                    <xs:element name="eligibilityCheckResult" type="EligibilityCheckResult" minOccurs="1" maxOccurs="1" />
                </xs:sequence>
            </xs:complexType>
        </xs:element>

        <xs:element name="GetIdCardNumberRequest">
            <xs:complexType>
                <xs:sequence>
                    <xs:element name="cuid" type="xs:long" minOccurs="1" maxOccurs="1" />
                </xs:sequence>
            </xs:complexType>
        </xs:element>

        <xs:element name="GetIdCardNumberResponse">
            <xs:complexType>
                <xs:sequence>
                    <xs:element name="idCardNumber" type="xs:string" minOccurs="1" maxOccurs="1" />
                </xs:sequence>
            </xs:complexType>
        </xs:element>

        <xs:element name="GetIdentificationInputsRequest">
            <xs:complexType>
                <xs:sequence>
                    <xs:element name="cuid" type="xs:long" minOccurs="1" maxOccurs="1" />
                </xs:sequence>
            </xs:complexType>
        </xs:element>

        <xs:element name="GetIdentificationInputsResponse">
            <xs:complexType>
                <xs:sequence>
                    <xs:element name="firstName" type="xs:string" minOccurs="1" maxOccurs="1" />
                    <xs:element name="lastName" type="xs:string" minOccurs="1" maxOccurs="1" />
                    <xs:element name="birthDate" type="xs:date" minOccurs="1" maxOccurs="1" />
                    <xs:element name="document" type="CustomerDocument" minOccurs="0" />
                    <xs:element name="externalIdentityVerification" type="ExternalIdentityVerification" minOccurs="0" />
                </xs:sequence>
            </xs:complexType>
        </xs:element>


        <xs:complexType name="CustomerDocument">
            <xs:sequence>
                <xs:element name="documentNumber" type="xs:string" minOccurs="0" />
                <xs:element name="documentType" type="xs:string" minOccurs="0" />
            </xs:sequence>
        </xs:complexType>

        <xs:complexType name="ExternalIdentityVerification">
            <xs:sequence>
                <xs:element name="verifierCompRegNumber" type="xs:string" minOccurs="0" />
                <xs:element name="providerBankCode" type="xs:string" minOccurs="0" />
            </xs:sequence>
        </xs:complexType>


        <xs:simpleType name="EligibilityCheckResult">
            <xs:restriction base="xs:string">
                <xs:enumeration value="OK" />
                <xs:enumeration value="NOK_NOT_IN_PILOT" />
                <xs:enumeration value="NOK_NO_ELIGIBLE_RELATION" />
                <xs:enumeration value="NOK_FOREIGNER" />
                <xs:enumeration value="NOK_BELOW_AGE_OF_MAJORITY" />
                <xs:enumeration value="NOK_NOT_PHYSICALLY_IDENTIFIED" />
                <xs:enumeration value="NOK_DEAD" />
            </xs:restriction>
        </xs:simpleType>

        <xs:simpleType name="IdentifiedBy">
            <xs:restriction base="xs:string">
                <xs:enumeration value="BRANCH_VISIT" />
                <xs:enumeration value="MESSENGER_SIGNED_CONTRACT" />
                <xs:enumeration value="BRANCH_SIGNED_CONTRACT" />
            </xs:restriction>
        </xs:simpleType>

        <xs:simpleType name="PersonalDocumentType">
            <xs:restriction base="xs:string">
                <xs:enumeration value="ID_CARD" />
                <xs:enumeration value="PASSPORT" />
                <xs:enumeration value="STAY_PERMIT" />
                <xs:enumeration value="STAY_PERMIT_BOOKLET" />
                <xs:enumeration value="STAY_PERMIT_FORM" />
                <xs:enumeration value="STAY_PERMIT_STICKER" />
                <xs:enumeration value="VISA_STICKER" />
            </xs:restriction>
        </xs:simpleType>

    </xs:schema>
