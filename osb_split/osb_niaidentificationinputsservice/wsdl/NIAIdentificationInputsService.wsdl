<wsdl:definitions targetNamespace="http://airbank.cz/osb/nia/InputsService" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns="http://airbank.cz/osb/nia/InputsService">

        <wsdl:types>
            <xs:schema targetNamespace="http://airbank.cz/osb/nia/InputsService">
                <xs:include schemaLocation="NIAIdentificationInputsService.xsd"/>
            </xs:schema>
        </wsdl:types>

        <wsdl:message name="IsPhysicallyIdentifiedRequest">
            <wsdl:part element="IsPhysicallyIdentifiedRequest" name="IsPhysicallyIdentifiedRequest"/>
        </wsdl:message>
        <wsdl:message name="IsPhysicallyIdentifiedResponse">
            <wsdl:part element="IsPhysicallyIdentifiedResponse" name="IsPhysicallyIdentifiedResponse"/>
        </wsdl:message>

        <wsdl:message name="IsEligibleRequest">
            <wsdl:part element="IsEligibleRequest" name="IsEligibleRequest"/>
        </wsdl:message>
        <wsdl:message name="IsEligibleResponse">
            <wsdl:part element="IsEligibleResponse" name="IsEligibleResponse"/>
        </wsdl:message>

        <wsdl:message name="GetIdCardNumberRequest">
            <wsdl:part element="GetIdCardNumberRequest" name="GetIdCardNumberRequest"/>
        </wsdl:message>
        <wsdl:message name="GetIdCardNumberResponse">
            <wsdl:part element="GetIdCardNumberResponse" name="GetIdCardNumberResponse"/>
        </wsdl:message>

        <wsdl:message name="GetIdentificationInputsRequest">
            <wsdl:part element="GetIdentificationInputsRequest" name="GetIdentificationInputsRequest"/>
        </wsdl:message>
        <wsdl:message name="GetIdentificationInputsResponse">
            <wsdl:part element="GetIdentificationInputsResponse" name="GetIdentificationInputsResponse"/>
        </wsdl:message>

        <wsdl:portType name="InputsServicePort">
            <wsdl:operation name="IsPhysicallyIdentified">
                <wsdl:input message="IsPhysicallyIdentifiedRequest"/>
                <wsdl:output message="IsPhysicallyIdentifiedResponse"/>
            </wsdl:operation>
            <wsdl:operation name="IsEligible">
                <wsdl:input message="IsEligibleRequest"/>
                <wsdl:output message="IsEligibleResponse"/>
            </wsdl:operation>
            <wsdl:operation name="GetIdCardNumber">
                <wsdl:input message="GetIdCardNumberRequest"/>
                <wsdl:output message="GetIdCardNumberResponse"/>
            </wsdl:operation>
            <wsdl:operation name="GetIdentificationInputs">
                <wsdl:input message="GetIdentificationInputsRequest"/>
                <wsdl:output message="GetIdentificationInputsResponse"/>
            </wsdl:operation>
        </wsdl:portType>

        <wsdl:binding name="InputsServiceBinding" type="InputsServicePort">
            <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
            <wsdl:operation name="IsPhysicallyIdentified">
                <soap:operation soapAction=""/>
                <wsdl:input>
                    <soap:body use="literal"/>
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal"/>
                </wsdl:output>
            </wsdl:operation>
            <wsdl:operation name="IsEligible">
                <soap:operation soapAction=""/>
                <wsdl:input>
                    <soap:body use="literal"/>
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal"/>
                </wsdl:output>
            </wsdl:operation>
            <wsdl:operation name="GetIdCardNumber">
                <soap:operation soapAction=""/>
                <wsdl:input>
                    <soap:body use="literal"/>
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal"/>
                </wsdl:output>
            </wsdl:operation>
            <wsdl:operation name="GetIdentificationInputs">
                <soap:operation soapAction=""/>
                <wsdl:input>
                    <soap:body use="literal"/>
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal"/>
                </wsdl:output>
            </wsdl:operation>
        </wsdl:binding>

        <wsdl:service name="InputsServiceWS">
            <wsdl:port binding="InputsServiceBinding" name="InputsServicePort">
                <soap:address location="http://TO-BE-SPECIFIED/osb/InputsServiceWS"/>
            </wsdl:port>
        </wsdl:service>

    </wsdl:definitions>