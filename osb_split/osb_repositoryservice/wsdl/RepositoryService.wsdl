<?xml version="1.0" encoding="UTF-8" ?>
<!-- $Id: d04ba004d95bc34127292d8d4c3af5f75cceba64 $ -->
<!-- Published by JAX-WS RI at http://jax-ws.dev.java.net. RI's version is JAX-WS RI 2.1.7-b01-. -->
<!--
		Licensed under the Apache License, Version 2.0 (the "License"); you
		may not use this file except in compliance with the License. You may
		obtain a copy of the License at

		http://www.apache.org/licenses/LICENSE-2.0 Unless required by
		applicable law or agreed to in writing, software distributed under the
		License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
		CONDITIONS OF ANY KIND, either express or implied. See the License for
		the specific language governing permissions and limitations under the
		License.
-->
	<definitions targetNamespace="http://docs.oasis-open.org/ns/cmis/ws/200908/" name="CMISWebServices" xmlns:cmis="http://docs.oasis-open.org/ns/cmis/core/200908/" xmlns:cmism="http://docs.oasis-open.org/ns/cmis/messaging/200908/" xmlns:cmisw="http://docs.oasis-open.org/ns/cmis/ws/200908/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns="http://schemas.xmlsoap.org/wsdl/" xmlns:ns="http://schemas.xmlsoap.org/soap/encoding/" xmlns:jaxws="http://java.sun.com/xml/ns/jaxws">
	<types>
		<xsd:schema elementFormDefault="qualified" targetNamespace="http://docs.oasis-open.org/ns/cmis/ws/200908/">
			<xsd:import schemaLocation="CmisCore.xsd" namespace="http://docs.oasis-open.org/ns/cmis/core/200908/"/>
			<xsd:import schemaLocation="CmisMessaging.xsd" namespace="http://docs.oasis-open.org/ns/cmis/messaging/200908/"/>
		</xsd:schema>
	</types>

	<message name="cmisException">
		<part name="fault" element="cmism:cmisFault"/>
	</message>


	<message name="createDocumentRequest">
		<part name="parameters" element="cmism:createDocument"/>
	</message>
	<message name="createDocumentResponse">
		<part name="parameters" element="cmism:createDocumentResponse"/>
	</message>
	<message name="getObjectRequest">
		<part name="parameters" element="cmism:getObject"/>
	</message>
	<message name="getObjectResponse">
		<part name="parameters" element="cmism:getObjectResponse"/>
	</message>
	<message name="getPropertiesRequest">
		<part name="parameters" element="cmism:getProperties"/>
	</message>
	<message name="getPropertiesResponse">
		<part name="parameters" element="cmism:getPropertiesResponse"/>
	</message>

	<message name="getContentStreamRequest">
		<part name="parameters" element="cmism:getContentStream"/>
	</message>
	<message name="getContentStreamResponse">
		<part name="parameters" element="cmism:getContentStreamResponse"/>
	</message>
	<message name="updatePropertiesRequest">
		<part name="parameters" element="cmism:updateProperties"/>
	</message>
	<message name="updatePropertiesResponse">
		<part name="parameters" element="cmism:updatePropertiesResponse"/>
	</message>
	<message name="moveObjectRequest">
		<part name="parameters" element="cmism:moveObject"/>
	</message>
	<message name="moveObjectResponse">
		<part name="parameters" element="cmism:moveObjectResponse"/>
	</message>
	<message name="deleteObjectRequest">
		<part name="parameters" element="cmism:deleteObject"/>
	</message>
	<message name="deleteObjectResponse">
		<part name="parameters" element="cmism:deleteObjectResponse"/>
	</message>
	<message name="setContentStreamRequest">
		<part name="parameters" element="cmism:setContentStream"/>
	</message>
	<message name="setContentStreamResponse">
		<part name="parameters" element="cmism:setContentStreamResponse"/>
	</message>
	<message name="listDocumentsRequest">
		<part name="parameters" element="cmism:listDocuments"/>
	</message>
	<message name="listDocumentsResponse">
		<part name="parameters" element="cmism:listDocumentsResponse"/>
	</message>

	<portType name="ObjectServicePort">
		<operation name="createDocument">
			<input message="cmisw:createDocumentRequest"/>
			<output message="cmisw:createDocumentResponse"/>
			<fault message="cmisw:cmisException" name="cmisException"/>
		</operation>
		<operation name="getObject">
			<input message="cmisw:getObjectRequest"/>
			<output message="cmisw:getObjectResponse"/>
			<fault message="cmisw:cmisException" name="cmisException"/>
		</operation>
		<operation name="getProperties">
			<input message="cmisw:getPropertiesRequest"/>
			<output message="cmisw:getPropertiesResponse"/>
			<fault message="cmisw:cmisException" name="cmisException"/>
		</operation>
		<operation name="getContentStream">
			<input message="cmisw:getContentStreamRequest"/>
			<output message="cmisw:getContentStreamResponse"/>
			<fault message="cmisw:cmisException" name="cmisException"/>
		</operation>
		<operation name="updateProperties">
			<input message="cmisw:updatePropertiesRequest"/>
			<output message="cmisw:updatePropertiesResponse"/>
			<fault message="cmisw:cmisException" name="cmisException"/>
		</operation>
		<operation name="moveObject">
			<input message="cmisw:moveObjectRequest"/>
			<output message="cmisw:moveObjectResponse"/>
			<fault message="cmisw:cmisException" name="cmisException"/>
		</operation>
		<operation name="deleteObject">
			<input message="cmisw:deleteObjectRequest"/>
			<output message="cmisw:deleteObjectResponse"/>
			<fault message="cmisw:cmisException" name="cmisException"/>
		</operation>
		<operation name="setContentStream">
			<input message="cmisw:setContentStreamRequest"/>
			<output message="cmisw:setContentStreamResponse"/>
			<fault message="cmisw:cmisException" name="cmisException"/>
		</operation>
		<operation name="listDocuments">
			<input message="cmisw:listDocumentsRequest"/>
			<output message="cmisw:listDocumentsResponse"/>
			<fault message="cmisw:cmisException" name="cmisException"/>
		</operation>
	</portType>



	<binding name="ObjectServicePortBinding" type="cmisw:ObjectServicePort">
		<soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
		<operation name="createDocument">
			<soap:operation soapAction=""/>
			<input>
				<soap:body use="literal"/>
			</input>
			<output>
				<soap:body use="literal"/>
			</output>
			<fault name="cmisException">
				<soap:fault name="cmisException" use="literal"/>
			</fault>
		</operation>
		<operation name="getObject">
			<soap:operation soapAction=""/>
			<input>
				<soap:body use="literal"/>
			</input>
			<output>
				<soap:body use="literal"/>
			</output>
			<fault name="cmisException">
				<soap:fault name="cmisException" use="literal"/>
			</fault>
		</operation>
		<operation name="getProperties">
			<soap:operation soapAction=""/>
			<input>
				<soap:body use="literal"/>
			</input>
			<output>
				<soap:body use="literal"/>
			</output>
			<fault name="cmisException">
				<soap:fault name="cmisException" use="literal"/>
			</fault>
		</operation>
		<operation name="getContentStream">
			<soap:operation soapAction=""/>
			<input>
				<soap:body use="literal"/>
			</input>
			<output>
				<soap:body use="literal"/>
			</output>
			<fault name="cmisException">
				<soap:fault name="cmisException" use="literal"/>
			</fault>
		</operation>
		<operation name="updateProperties">
			<soap:operation soapAction=""/>
			<input>
				<soap:body use="literal"/>
			</input>
			<output>
				<soap:body use="literal"/>
			</output>
			<fault name="cmisException">
				<soap:fault name="cmisException" use="literal"/>
			</fault>
		</operation>
		<operation name="moveObject">
			<soap:operation soapAction=""/>
			<input>
				<soap:body use="literal"/>
			</input>
			<output>
				<soap:body use="literal"/>
			</output>
			<fault name="cmisException">
				<soap:fault name="cmisException" use="literal"/>
			</fault>
		</operation>
		<operation name="deleteObject">
			<soap:operation soapAction=""/>
			<input>
				<soap:body use="literal"/>
			</input>
			<output>
				<soap:body use="literal"/>
			</output>
			<fault name="cmisException">
				<soap:fault name="cmisException" use="literal"/>
			</fault>
		</operation>
		<operation name="setContentStream">
			<soap:operation soapAction=""/>
			<input>
				<soap:body use="literal"/>
			</input>
			<output>
				<soap:body use="literal"/>
			</output>
			<fault name="cmisException">
				<soap:fault name="cmisException" use="literal"/>
			</fault>
		</operation>
		<operation name="listDocuments">
			<soap:operation soapAction=""/>
			<input>
				<soap:body use="literal"/>
			</input>
			<output>
				<soap:body use="literal"/>
			</output>
			<fault name="cmisException">
				<soap:fault name="cmisException" use="literal"/>
			</fault>
		</operation>
	</binding>


	<service name="ObjectService">
		<port name="ObjectServicePort" binding="cmisw:ObjectServicePortBinding">
			<soap:address location="http://localhost/cmisws/ObjectService"/>
		</port>
	</service>

</definitions>
