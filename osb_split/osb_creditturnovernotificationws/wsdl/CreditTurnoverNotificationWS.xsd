<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://airbank.cz/ws/o2Proxy" elementFormDefault="qualified"
           targetNamespace="http://airbank.cz/ws/o2Proxy">

    <xs:element name="MonthCreditTurnoverChangedRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="unbounded" minOccurs="1" name="creditTurnover" type="CreditTurnover" />
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="MonthCreditTurnoverChangedResponse">
        <xs:complexType>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="CreditTurnover">
        <xs:sequence>
            <xs:element name="generalContractOwnerCuid" type="xs:long" />
			<xs:element name="calendarMonth" type="xs:gYearMonth"/>
            <xs:element name="amount" type="xs:decimal"/>
            <xs:element name="currencyCode" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
</xs:schema>