<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xs="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns="http://airbank.cz/ws/o2Proxy"
                  targetNamespace="http://airbank.cz/ws/o2Proxy">
    <wsdl:types>
        <xs:schema targetNamespace="http://airbank.cz/ws/o2Proxy">
            <xs:include schemaLocation="CreditTurnoverNotificationWS.xsd"/>
        </xs:schema>
    </wsdl:types>

     <wsdl:message name="MonthCreditTurnoverChangedRequest">
        <wsdl:part element="MonthCreditTurnoverChangedRequest" name="MonthCreditTurnoverChangedRequest"/>
    </wsdl:message>
    <wsdl:message name="MonthCreditTurnoverChangedResponse">
        <wsdl:part element="MonthCreditTurnoverChangedResponse" name="MonthCreditTurnoverChangedResponse"/>
    </wsdl:message>

    <wsdl:portType name="O2ProxyPort">
        <wsdl:operation name="MonthCreditTurnoverChangedOperation">
            <wsdl:documentation>
                Get details of client's cooperation with O2.
            </wsdl:documentation>
            <wsdl:input message="MonthCreditTurnoverChangedRequest"/>
            <wsdl:output message="MonthCreditTurnoverChangedResponse"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="O2ProxyBinding" type="O2ProxyPort">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

        <wsdl:operation name="MonthCreditTurnoverChangedOperation">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="O2ProxyService">
        <wsdl:documentation>O2 proxy web service.</wsdl:documentation>
        <wsdl:port name="O2ProxyService" binding="O2ProxyBinding">
            <soap:address location="http://localhost:8000/ws/"/>
        </wsdl:port>
    </wsdl:service>

</wsdl:definitions>