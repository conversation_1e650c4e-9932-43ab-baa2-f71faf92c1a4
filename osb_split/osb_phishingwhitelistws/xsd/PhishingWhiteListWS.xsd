<?xml version="1.0" encoding="UTF-8"?>
    <xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:jxb="http://java.sun.com/xml/ns/jaxb" targetNamespace="http://airbank.cz/lap/ws/phishingWhiteList" xmlns="http://airbank.cz/lap/ws/phishingWhiteList" jxb:version="2.1" xmlns:data="http://osb.abank.cz/digitization/rest" elementFormDefault="qualified">

        <xsd:element name="GetClientsVPNIPsRequest">
            <xsd:annotation>
                <xsd:documentation>Načtení seznamu položek a jejich naplnění do seznamu výstupních elementů ip:string.</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType/>
        </xsd:element>

        <xsd:element name="GetReferersRequest">
            <xsd:annotation>
                <xsd:documentation>Načtení seznamu z výstupu REST služby a jeho naplnění do výstupních položek SOAP služby</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType/>
        </xsd:element>

        <xsd:element name="GetClientsVPNIPsResponse" type="response">
            <xsd:annotation>
                <xsd:documentation>Načtení seznamu z výstupu REST služby a jeho naplnění do výstupních položek SOAP služby</xsd:documentation>
            </xsd:annotation>
        </xsd:element>

        <xsd:element name="GetReferersResponse" type="responseRef">
            <xsd:annotation>
                <xsd:documentation>Načtení seznamu z výstupu REST služby a jeho naplnění do výstupních položek SOAP služby</xsd:documentation>
            </xsd:annotation>
        </xsd:element>

        <xsd:element name="GetBrowserLanguagesRequest">
            <xsd:annotation>
                <xsd:documentation>Načtení seznamu z výstupu REST služby a jeho naplnění do výstupních položek SOAP služby</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType/>
        </xsd:element>

        <xsd:element name="GetBrowserLanguagesResponse" type="responseBrowserLanguages">
            <xsd:annotation>
                <xsd:documentation>Načtení seznamu z výstupu REST služby a jeho naplnění do výstupních položek SOAP služby</xsd:documentation>
            </xsd:annotation>
        </xsd:element>

        <xsd:element name="GetCountriesRequest">
            <xsd:annotation>
                <xsd:documentation>Načtení seznamu z výstupu REST služby a jeho naplnění do výstupních položek SOAP služby</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType/>
        </xsd:element>

        <xsd:element name="GetCountriesResponse" type="responseCountries">
            <xsd:annotation>
                <xsd:documentation>Načtení seznamu z výstupu REST služby a jeho naplnění do výstupních položek SOAP služby</xsd:documentation>
            </xsd:annotation>
        </xsd:element>

        <xsd:element name="GetIPsRequest">
            <xsd:annotation>
                <xsd:documentation>Načtení seznamu z výstupu REST služby a jeho naplnění do výstupních položek SOAP služby</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType/>
        </xsd:element>

        <xsd:element name="GetIPsResponse" type="responseIPs">
            <xsd:annotation>
                <xsd:documentation>Načtení seznamu z výstupu REST služby a jeho naplnění do výstupních položek SOAP služby</xsd:documentation>
            </xsd:annotation>
        </xsd:element>

        <xsd:element name="GetCuidsRequest">
            <xsd:annotation>
                <xsd:documentation>Načtení seznamu z výstupu REST služby a jeho naplnění do výstupních položek SOAP služby</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType/>
        </xsd:element>

        <xsd:element name="GetCuidsResponse" type="responseCuids">
            <xsd:annotation>
                <xsd:documentation>Načtení seznamu z výstupu REST služby a jeho naplnění do výstupních položek SOAP služby</xsd:documentation>
            </xsd:annotation>
        </xsd:element>

        <xsd:element name="GetCookiesRequest">
            <xsd:annotation>
                <xsd:documentation>Načtení seznamu z výstupu REST služby a jeho naplnění do výstupních položek SOAP služby</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType/>
        </xsd:element>

        <xsd:element name="GetCookiesResponse" type="responseCookies">
            <xsd:annotation>
                <xsd:documentation>Načtení seznamu z výstupu REST služby a jeho naplnění do výstupních položek SOAP služby</xsd:documentation>
            </xsd:annotation>
        </xsd:element>



        <xsd:complexType name="ClientsVPNs">
            <xsd:sequence>
                <xsd:element minOccurs="1" maxOccurs="1" name="cuid" type="xsd:long" />
                <xsd:element minOccurs="1" maxOccurs="unbounded" name="ip" type="xsd:string" />
            </xsd:sequence>
        </xsd:complexType>


        <xsd:complexType name="response">
            <xsd:sequence>
                <xsd:element name="clientsVPNs" type="ClientsVPNs" minOccurs="0" maxOccurs="unbounded" />
            </xsd:sequence>
        </xsd:complexType>

        <xsd:complexType name="responseRef">
            <xsd:sequence>
                <xsd:element minOccurs="0" maxOccurs="unbounded" name="referer" type="xsd:string" />
            </xsd:sequence>
        </xsd:complexType>

        <xsd:complexType name="responseBrowserLanguages">
            <xsd:sequence>
                <xsd:element minOccurs="0" maxOccurs="unbounded" name="browserLanguage" type="xsd:string" />
            </xsd:sequence>
        </xsd:complexType>

        <xsd:complexType name="responseCountries">
            <xsd:sequence>
                <xsd:element minOccurs="0" maxOccurs="unbounded" name="country" type="xsd:string" />
            </xsd:sequence>
        </xsd:complexType>

        <xsd:complexType name="responseIPs">
            <xsd:sequence>
                <xsd:element minOccurs="0" maxOccurs="unbounded" name="ip" type="xsd:string" />
            </xsd:sequence>
        </xsd:complexType>

        <xsd:complexType name="responseCuids">
            <xsd:sequence>
                <xsd:element minOccurs="0" maxOccurs="unbounded" name="cuid" type="xsd:long" />
            </xsd:sequence>
        </xsd:complexType>

        <xsd:complexType name="responseCookies">
            <xsd:sequence>
                <xsd:element minOccurs="0" maxOccurs="unbounded" name="cookie" type="xsd:string" />
            </xsd:sequence>
        </xsd:complexType>


    </xsd:schema>
