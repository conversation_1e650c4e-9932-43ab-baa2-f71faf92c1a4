<?xml version="1.0" encoding="UTF-8" ?>
<wsdl:definitions targetNamespace="http://mdes.abank.cz/customer/service/" xmlns="http://mdes.abank.cz/customer/service/"
                  xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/">
    <wsdl:types>
        <xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema">
            <xsd:import namespace="http://mdes.abank.cz/customer/service/" schemaLocation="../xsd/MDESCustomerServiceWS.xsd"/>
        </xsd:schema>
    </wsdl:types>
    <wsdl:message name="tokenActivateRequest">
        <wsdl:part name="tokenActivateRequest" element="TokenActivateRequest"/>
    </wsdl:message>
    <wsdl:message name="tokenActivateResponse">
        <wsdl:part name="tokenActivateResponse" element="TokenActivateResponse"/>
    </wsdl:message>
    <wsdl:message name="tokenSuspendRequest">
        <wsdl:part name="tokenSuspendRequest" element="TokenSuspendRequest"/>
    </wsdl:message>
    <wsdl:message name="tokenSuspendResponse">
        <wsdl:part name="tokenSuspendResponse" element="TokenSuspendResponse"/>
    </wsdl:message>
    <wsdl:message name="tokenUnsuspendRequest">
        <wsdl:part name="tokenUnsuspendRequest" element="TokenUnsuspendRequest"/>
    </wsdl:message>
    <wsdl:message name="tokenUnsuspendResponse">
        <wsdl:part name="tokenUnsuspendResponse" element="TokenUnsuspendResponse"/>
    </wsdl:message>
    <wsdl:message name="tokenDeleteRequest">
        <wsdl:part name="tokenDeleteRequest" element="TokenDeleteRequest"/>
    </wsdl:message>
    <wsdl:message name="tokenDeleteResponse">
        <wsdl:part name="tokenDeleteResponse" element="TokenDeleteResponse"/>
    </wsdl:message>
    <wsdl:message name="tokenUpdateRequest">
        <wsdl:part name="tokenUpdateRequest" element="TokenUpdateRequest"/>
    </wsdl:message>
    <wsdl:message name="tokenUpdateResponse">
        <wsdl:part name="tokenUpdateResponse" element="TokenUpdateResponse"/>
    </wsdl:message>
    <wsdl:portType name="MDESCustomerServiceWS">
        <wsdl:operation name="tokenActivate">
            <wsdl:input message="tokenActivateRequest"/>
            <wsdl:output message="tokenActivateResponse"/>
        </wsdl:operation>
        <wsdl:operation name="tokenSuspend">
            <wsdl:input message="tokenSuspendRequest"/>
            <wsdl:output message="tokenSuspendResponse"/>
        </wsdl:operation>
        <wsdl:operation name="tokenUnsuspend">
            <wsdl:input message="tokenUnsuspendRequest"/>
            <wsdl:output message="tokenUnsuspendResponse"/>
        </wsdl:operation>
        <wsdl:operation name="tokenDelete">
            <wsdl:input message="tokenDeleteRequest"/>
            <wsdl:output message="tokenDeleteResponse"/>
        </wsdl:operation>
        <wsdl:operation name="tokenUpdate">
            <wsdl:input message="tokenUpdateRequest"/>
            <wsdl:output message="tokenUpdateResponse"/>
        </wsdl:operation>

    </wsdl:portType>
    <wsdl:binding name="MDESCustomerServiceWS-binding" type="MDESCustomerServiceWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="tokenActivate">
            <soap:operation soapAction="tokenActivate"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="tokenSuspend">
            <soap:operation soapAction="tokenSuspend"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="tokenUnsuspend">
            <soap:operation soapAction="tokenUnsuspend"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="tokenDelete">
            <soap:operation soapAction="tokenDelete"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="tokenUpdate">
            <soap:operation soapAction="tokenUpdate"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>
    <wsdl:service name="MDESCustomerServiceWS">
        <wsdl:port binding="MDESCustomerServiceWS-binding" name="MDESCustomerServiceWSSoap11">
            <soap:address location="http://TO-BE-SPECIFIED/MDESCustomerServiceWS/"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>
