<?xml version="1.0" encoding="UTF-8" ?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://mdes.abank.cz/customer/service/" xmlns:xs="http://www.w3.org/2001/XMLSchema"
            targetNamespace="http://mdes.abank.cz/customer/service/" elementFormDefault="qualified">
    <xsd:element name="TokenDeleteRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="bodyJSON" type="xsd:string" minOccurs="1"/>
                <xsd:element name="oAuthAuthorization" type="xsd:string" minOccurs="1"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="TokenDeleteResponse">
        <xsd:complexType>
            <xsd:all>
                <xsd:element name="error" type="Error" minOccurs="0"/>
                <xs:element name="responseJSON" type="xsd:string" minOccurs="0"/>
            </xsd:all>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="TokenActivateRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="bodyJSON" type="xsd:string" minOccurs="1"/>
                <xsd:element name="oAuthAuthorization" type="xsd:string" minOccurs="1"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="TokenActivateResponse">
        <xsd:complexType>
            <xsd:all>
                <xsd:element name="error" type="Error" minOccurs="0"/>
                <xs:element name="responseJSON" type="xsd:string" minOccurs="0"/>
            </xsd:all>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="TokenSuspendRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="bodyJSON" type="xsd:string" minOccurs="1"/>
                <xsd:element name="oAuthAuthorization" type="xsd:string" minOccurs="1"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="TokenSuspendResponse">
        <xsd:complexType>
            <xsd:all>
                <xsd:element name="error" type="Error" minOccurs="0"/>
                <xs:element name="responseJSON" type="xsd:string" minOccurs="0"/>
            </xsd:all>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="TokenUnsuspendRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="bodyJSON" type="xsd:string" minOccurs="1"/>
                <xsd:element name="oAuthAuthorization" type="xsd:string" minOccurs="1"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="TokenUnsuspendResponse">
        <xsd:complexType>
            <xsd:all>
                <xsd:element name="error" type="Error" minOccurs="0"/>
                <xs:element name="responseJSON" type="xsd:string" minOccurs="0"/>
            </xsd:all>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="TokenUpdateRequest">
        <xsd:complexType>
                <xsd:sequence>
                        <xsd:element name="bodyJSON" type="xsd:string" minOccurs="1"/>
                        <xsd:element name="oAuthAuthorization" type="xsd:string" minOccurs="1"/>
                    </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="TokenUpdateResponse">
        <xsd:complexType>
            <xsd:all>
                <xsd:element name="error" type="Error" minOccurs="0"/>
                <xs:element name="responseJSON" type="xsd:string" minOccurs="0"/>
            </xsd:all>
        </xsd:complexType>
    </xsd:element>


    <xsd:complexType name="Error">
        <xsd:all>
            <xsd:element name="HttpStatus" type="xsd:string"/>
            <xsd:element name="Message" type="xsd:string" minOccurs="0"/>
            <xsd:element name="ReasonCode" type="xsd:string" minOccurs="0"/>
            <xsd:element name="Source" type="xsd:string" minOccurs="0"/>
        </xsd:all>
    </xsd:complexType>



</xsd:schema>
