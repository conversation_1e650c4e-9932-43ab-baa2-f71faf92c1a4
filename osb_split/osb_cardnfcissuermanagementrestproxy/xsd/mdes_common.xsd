<?xml version="1.0" encoding="UTF-8" ?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" targetNamespace="http://osb.abank.cz/mdes/rest/common"
            elementFormDefault="qualified" xmlns="http://osb.abank.cz/mdes/rest/common">
  <xsd:complexType name="BasicEntity" abstract="true">
    <xsd:sequence>
      <xsd:element name="responseHost" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="ResponseEntity" abstract="true">
    <xsd:complexContent>
    <xsd:extension base="BasicEntity">
     <xsd:sequence>
      <xsd:element name="responseId" type="xsd:string"/>
      <xsd:element name="errors" type="Error" minOccurs="0" maxOccurs="unbounded"/>
     </xsd:sequence>
    </xsd:extension>
    </xsd:complexContent>
  </xsd:complexType>
  <xsd:complexType name="RequestEntity" abstract="true">
    <xsd:complexContent>
    <xsd:extension base="BasicEntity">
     <xsd:sequence>
      <xsd:element name="requestId" type="xsd:string"/>
     </xsd:sequence>
    </xsd:extension>
    </xsd:complexContent>
  </xsd:complexType>
  <xsd:complexType name="Response" abstract="true">
    <xsd:complexContent>
      <xsd:extension base="ResponseEntity">
        <xsd:sequence>
          <xsd:element name="tokens" maxOccurs="unbounded" type="Token"/>
        </xsd:sequence>
      </xsd:extension>
    </xsd:complexContent>
  </xsd:complexType>
  <xsd:complexType name="Request" abstract="true">
    <xsd:complexContent>
      <xsd:extension base="RequestEntity">
        <xsd:sequence>
          <xsd:element name="tokenUniqueReferences" maxOccurs="unbounded" type="xsd:string"/>
          <xsd:element name="causedBy" type="CausedBy"/>
          <xsd:element name="reasonCode" type="ReasonCodeClient"/>
          <xsd:element name="reason" type="xsd:string"/>
        </xsd:sequence>
      </xsd:extension>
    </xsd:complexContent>
  </xsd:complexType>
  <xsd:complexType name="Token">
    <xsd:sequence>
      <xsd:element name="tokenUniqueReference" type="xsd:string"/>
      <xsd:element name="status" minOccurs="0" type="xsd:string"/>
      <xsd:element name="suspendedBy" minOccurs="0" type="xsd:string"/>
      <xsd:element name="errorCode" type="xsd:string"/>
      <xsd:element name="errorDescription" type="xsd:string"/>
      <xsd:element name="errors" type="Error" minOccurs="0" maxOccurs="unbounded"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="Error">
    <xsd:sequence>
      <xsd:element name="source" type="xsd:string"/>
      <xsd:element name="errorCode" type="xsd:string" minOccurs="0"/>
      <xsd:element name="reasonCode" type="ReasonCodeServer"/>
      <xsd:element name="description" type="xsd:string"/>
      <xsd:element name="recoverable" type="xsd:boolean" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="EncryptedPayload">
    <xsd:sequence>
      <xsd:element name="encryptedData" type="xsd:base64Binary" minOccurs="0"/>
      <xsd:element name="publicKeyFingerprint" type="xsd:base64Binary" minOccurs="0"/>
      <xsd:element name="encryptedKey" type="xsd:base64Binary" minOccurs="0"/>
      <xsd:element name="oaepHashingAlgorithm" type="xsd:string" minOccurs="0"/>
      <xsd:element name="iv" type="xsd:base64Binary" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="CardInfo">
    <xsd:complexContent>
      <xsd:extension base="EncryptedPayload">
        <xsd:sequence>
          <xsd:element name="panUniqueReference" type="xsd:string" minOccurs="0"/>
          <xsd:element name="tokenUniqueReference" type="xsd:string" minOccurs="0"/>
        </xsd:sequence>
      </xsd:extension>
    </xsd:complexContent>
  </xsd:complexType>
  <xsd:simpleType name="TokenType">
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="EMBEDDED_SE"/>
      <xsd:enumeration value="CLOUD"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="NotEligibleReasons">
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="OS_NOT_SUPPORTED"/>
      <xsd:enumeration value="OS_VERSION_NOT_SUPPORTED"/>
      <xsd:enumeration value="DEVICE_TYPE_NOT_SUPPORTED"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="ReasonCodeClient">
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="DEVICE_LOST"/>
      <xsd:enumeration value="DEVICE_STOLEN"/>
      <xsd:enumeration value="ACCOUNT_CLOSED"/>
      <xsd:enumeration value="SUSPECTED_FRAUD"/>
      <xsd:enumeration value="OTHER"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="CausedBy">
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="CARDHOLDER"/>
      <xsd:enumeration value="TOKEN_REQUESTOR"/>
      <xsd:enumeration value="PAYMENT_APP_PROVIDER"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="DecisionReason">
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="APPROVED"/>
      <xsd:enumeration value="DECLINED"/>
      <xsd:enumeration value="REQUIRE_ADDITIONAL_AUTHENTICATION"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="ReasonCodeServer">
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="INVALID_JSON">
        <xsd:annotation>
          <xsd:documentation>The JSON could not be parsed</xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="UNRECOGNIZED_FIELD">
        <xsd:annotation>
          <xsd:documentation>The field name is not valid for the object</xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="AUTHORIZATION_FAILED">
        <xsd:annotation>
          <xsd:documentation>The request failed to present a valid cert to access the API</xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="INVALID_FIELD_FORMAT">
        <xsd:annotation>
          <xsd:documentation>The field is not in the correct format. For instance, it should be a number but is a string</xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="INVALID_FIELD_LENGTH">
        <xsd:annotation>
          <xsd:documentation>The value does not fall between the minimum and maximum length for the field</xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="INVALID_FIELD_VALUE">
        <xsd:annotation>
          <xsd:documentation>The value is not allowed for the field</xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="INVALID_RESPONSE_HOST">
        <xsd:annotation>
          <xsd:documentation>The requested response host is invalid</xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="NO_RESPONSE_FROM_ISSUER">
        <xsd:annotation>
          <xsd:documentation>The issuer did not respond to the network message in the allotted time. An asynchronous message will be sent. The Token Requestor should retry after the time specified in the retry-after header</xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="MISSING_REQUIRED_FIELD">
        <xsd:annotation>
          <xsd:documentation>A required field is missing</xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="CRYPTOGRAPHY_ERROR">
        <xsd:annotation>
          <xsd:documentation>There was an error decrypting the encrypted payload</xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="INTERNAL_SERVICE_FAILURE">
        <xsd:annotation>
          <xsd:documentation>MDES had an internal exception</xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="INVALID_PAN">
        <xsd:annotation>
          <xsd:documentation>The PAN format is not valid, or other data associated with the PAN was incorrect or entered incorrectly. The request may be retried if the data is re-entered correctly</xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="MISSING_EXPIRY_DATE">
        <xsd:annotation>
          <xsd:documentation>The expiry date is required for this product but was missing. Retry the request supplying the expiry date for this card</xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="DUPLICATE_REQUEST">
        <xsd:annotation>
          <xsd:documentation>The PAN has already been provisioned to the device or the same request is currently being processed</xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="PROVISION_FAILED">
        <xsd:annotation>
          <xsd:documentation>The provisioning of PAN to the device has failed</xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="PAN_INELIGIBLE">
        <xsd:annotation>
          <xsd:documentation>The PAN is not in an approved account range for MDES</xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="DEVICE_INELIGIBLE">
        <xsd:annotation>
          <xsd:documentation>The device is not supported for use with MDES</xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="PAN_INELIGIBLE_FOR_DEVICE">
        <xsd:annotation>
          <xsd:documentation>The PAN is not allowed to be provisioned to the device because of Issuer rules</xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="PAN_PROVISIONING_COUNT_EXCEEDED">
        <xsd:annotation>
          <xsd:documentation>The PAN has already been provisioned to the maximum number of devices</xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="INVALID_ELIGIBILITY_RECEIPT">
        <xsd:annotation>
          <xsd:documentation>The eligibility receipt is expired or the value cannot be found</xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="INVALID_TASK_ID">
        <xsd:annotation>
          <xsd:documentation>The taskId could not be found or, for inbound calls, the taskId was not unique</xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="INVALID_TERMS_AND_CONDITIONS">
        <xsd:annotation>
          <xsd:documentation>The terms and conditions id accepted by the cardholder does not match what was sent on the CheckEligibility response</xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="INVALID_ACTIVATION_METHOD">
        <xsd:annotation>
          <xsd:documentation>The activation method could not be found. DEPRECATED</xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="INVALID_AUTHENTICATION_METHOD">
        <xsd:annotation>
          <xsd:documentation>The authentication method could not be found</xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="INVALID_TOKEN_UNIQUE_REFERENCE">
        <xsd:annotation>
          <xsd:documentation>The token unique reference could not be found or does not match the paymentAppInstanceId provided</xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="INVALID_PAN_UNIQUE_REFERENCE">
        <xsd:annotation>
          <xsd:documentation>The PAN unique reference could not be found</xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="INVALID_TOKEN_STATUS">
        <xsd:annotation>
          <xsd:documentation>The token is in an invalid status for the requested operation. For instance, trying to unsuspend a deleted token</xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="INVALID_WORKFLOW">
        <xsd:annotation>
          <xsd:documentation>The operation requested is invalid for the token. For instance, calling Activate for an Approved mapping, or supplying rnsInfo when it is not the first card being digitized</xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="INVALID_AID_CARD_TYPE">
        <xsd:annotation>
          <xsd:documentation>The PIX value in the aid was not correct for the account range brand product</xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="INVALID_AID_RID">
        <xsd:annotation>
          <xsd:documentation>The RID value in the aid was not correct for MasterCard</xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="INVALID_CARDLET_ID">
        <xsd:annotation>
          <xsd:documentation>The cardlet ID could not be mapped to a cardlet</xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="INVALID_METHOD">
        <xsd:annotation>
          <xsd:documentation>The URI for the call was not able to be mapped to an API endpoint</xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="INCORRECT_TAV">
        <xsd:annotation>
          <xsd:documentation>The Tokenization Authentication Value provided in the call was incorrect and rejected</xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="EXPIRED_CODE">
        <xsd:annotation>
          <xsd:documentation>Authentication Code has expired or was invalidated</xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="INVALID_DATA">
        <xsd:annotation>
          <xsd:documentation>The data supplied for the request was invalid. No further detail is provided</xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="NO_ACTIVE_TOKENS">
        <xsd:annotation>
          <xsd:documentation>There are no active (not suspended) Tokens for the given Account PAN and consumer account</xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
    </xsd:restriction>
  </xsd:simpleType>
</xsd:schema>
