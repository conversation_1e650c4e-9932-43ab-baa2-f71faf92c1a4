<?xml version = '1.0' encoding = 'UTF-8'?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://osb.abank.cz/digitization/rest"
            targetNamespace="http://osb.abank.cz/digitization/rest" elementFormDefault="qualified"
            xmlns:nxsd="http://xmlns.oracle.com/pcbpel/nxsd" nxsd:version="JSON" nxsd:encoding="UTF-8"
            xmlns:comm="http://osb.abank.cz/mdes/rest/common">
  <xsd:import schemaLocation="mdes_common.xsd" namespace="http://osb.abank.cz/mdes/rest/common"/>
  <xsd:element name="DeleteRequest">
    <xsd:complexType>
      <xsd:complexContent>
        <xsd:extension base="comm:Request">
          <xsd:sequence>
            <xsd:element name="paymentAppInstanceId" type="xsd:string" minOccurs="0"/>
          </xsd:sequence>
        </xsd:extension>
      </xsd:complexContent>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="DeleteResponse">
    <xsd:complexType>
      <xsd:complexContent>
        <xsd:extension base="comm:Response">
          <xsd:sequence>
            <!-- -->
          </xsd:sequence>
        </xsd:extension>
      </xsd:complexContent>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="SuspendRequest">
    <xsd:complexType>
      <xsd:complexContent>
        <xsd:extension base="comm:Request">
          <xsd:sequence>
            <xsd:element name="paymentAppInstanceId" type="xsd:string" minOccurs="0"/>
          </xsd:sequence>
        </xsd:extension>
      </xsd:complexContent>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="SuspendResponse">
    <xsd:complexType>
      <xsd:complexContent>
        <xsd:extension base="comm:Response">
          <xsd:sequence>
            <!-- -->
          </xsd:sequence>
        </xsd:extension>
      </xsd:complexContent>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="UnsuspendRequest">
    <xsd:complexType>
      <xsd:complexContent>
        <xsd:extension base="comm:Request">
          <xsd:sequence>
            <xsd:element name="paymentAppInstanceId" type="xsd:string" minOccurs="0"/>
          </xsd:sequence>
        </xsd:extension>
      </xsd:complexContent>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="UnsuspendResponse">
    <xsd:complexType>
      <xsd:complexContent>
        <xsd:extension base="comm:Response">
          <xsd:sequence>
            <xsd:element name="paymentAppInstanceId" type="xsd:string" minOccurs="0"/>
          </xsd:sequence>
        </xsd:extension>
      </xsd:complexContent>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="CheckEligibilityRequest">
    <xsd:complexType>
      <xsd:complexContent>
        <xsd:extension base="comm:RequestEntity">
          <xsd:sequence>
            <xsd:element name="paymentAppInstanceId" type="xsd:string" minOccurs="0"/>
            <xsd:element name="tokenType" type="comm:TokenType"/>
            <xsd:element name="paymentAppId" type="xsd:string"/>
            <xsd:element name="deviceInfo" type="DeviceInfo" minOccurs="0"/>
            <xsd:element name="seInfo" type="SeInfo" minOccurs="0"/>
            <xsd:element name="cardInfo" type="CardInfo" minOccurs="0"/>
            <xsd:element name="cardletId" type="xsd:string"/>
            <xsd:element name="spsdInfo" type="SpsdInfo" minOccurs="0"/>
            <xsd:element name="consumerLanguage" type="xsd:string"/>
          </xsd:sequence>
        </xsd:extension>
      </xsd:complexContent>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="CheckEligibilityResponse">
    <xsd:complexType>
      <xsd:complexContent>
        <xsd:extension base="comm:ResponseEntity">
          <xsd:sequence>
            <xsd:element name="eligibilityReceipt" type="EligibilityReceipt" minOccurs="0"/>
            <xsd:element name="termsAndConditionsAssetId" type="xsd:string" minOccurs="0"/>
            <xsd:element name="applicableCardInfo" type="ApplicableCardInfo" minOccurs="0"/>
            <xsd:element name="deviceNotEligibleReasons" type="comm:NotEligibleReasons" minOccurs="0"/>
          </xsd:sequence>
        </xsd:extension>
      </xsd:complexContent>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="DigitizeRequest">
    <xsd:complexType>
      <xsd:complexContent>
        <xsd:extension base="comm:RequestEntity">
          <xsd:sequence>
            <xsd:element name="paymentAppInstanceId" type="xsd:string" minOccurs="0"/>
            <xsd:element name="eligibilityReceipt" type="EligibilityReceipt"/>
            <xsd:element name="taskId" type="xsd:string"/>
            <xsd:element name="termsAndConditionsAssetId" type="xsd:string"/>
            <xsd:element name="termsAndConditionsAcceptedTimestamp" type="xsd:string"/>
            <xsd:element name="cardInfo" type="EncryptedPayload" minOccurs="0"/>
            <xsd:element name="tokenizationAuthenticationValue" type="xsd:string" minOccurs="0"/>
            <xsd:element name="decisioningData" type="DecisioningData" minOccurs="0"/>
          </xsd:sequence>
        </xsd:extension>
      </xsd:complexContent>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="DigitizeResponse">
    <xsd:complexType>
      <xsd:complexContent>
        <xsd:extension base="comm:ResponseEntity">
          <xsd:sequence>
            <xsd:element name="decision" type="comm:DecisionReason"/>
            <xsd:element name="authenticationMethods" minOccurs="0" maxOccurs="unbounded" type="AuthenticationMethods"/>
            <xsd:element name="tokenUniqueReference" type="xsd:string" minOccurs="0"/>
            <xsd:element name="panUniqueReference" type="xsd:string" minOccurs="0"/>
            <xsd:element name="productConfig" type="ProductConfig" minOccurs="0"/>
            <xsd:element name="tokenInfo" type="TokenInfo" minOccurs="0"/>
            <xsd:element name="tdsRegistrationUrl" type="xsd:string" minOccurs="0"/>
          </xsd:sequence>
        </xsd:extension>
      </xsd:complexContent>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="NotifyTokenUpdatedRequest">
    <xsd:complexType>
      <xsd:complexContent>
        <xsd:extension base="comm:RequestEntity">
          <xsd:sequence>
            <xsd:element name="encryptedPayload" type="EncryptedPayload"/>
          </xsd:sequence>
        </xsd:extension>
      </xsd:complexContent>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="NotifyTokenUpdatedResponse">
    <xsd:complexType>
      <xsd:complexContent>
        <xsd:extension base="comm:ResponseEntity">
          <xsd:sequence>
            <!-- -->
          </xsd:sequence>
        </xsd:extension>
      </xsd:complexContent>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="EligibilityReceipt">
    <xsd:sequence>
      <xsd:element name="value" type="xsd:string"/>
      <xsd:element name="validForMinutes" type="xsd:integer" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="ApplicableCardInfo">
    <xsd:sequence>
      <xsd:element name="isSecurityCodeApplicable" type="xsd:boolean"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="DeviceInfo">
    <xsd:sequence>
      <xsd:element name="deviceName" type="xsd:string" minOccurs="0"/>
      <xsd:element name="serialNumber" type="xsd:string" minOccurs="0"/>
      <xsd:element name="formFactor" type="xsd:string"  minOccurs="0"/>
      <xsd:element name="storageTechnology" type="xsd:string"/>
      <xsd:element name="osName" type="xsd:string" minOccurs="0"/>
      <xsd:element name="osVersion" type="xsd:string" minOccurs="0"/>
      <xsd:element name="nfcCapable" type="xsd:boolean" minOccurs="0"/>
      <xsd:element name="imei" type="xsd:string" minOccurs="0"/>
      <xsd:element name="msisdn" type="xsd:string" minOccurs="0"/>
      <xsd:element name="paymentTypes" type="xsd:string" maxOccurs="unbounded" minOccurs="0"/>
      <xsd:element name="isoDeviceType" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="SeInfo">
    <xsd:sequence>
      <xsd:element name="seId" type="xsd:string"/>
      <xsd:element name="seCapabilities" minOccurs="0" maxOccurs="unbounded">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="key" type="xsd:string" minOccurs="0"/>
            <xsd:element name="value" type="xsd:string" minOccurs="0"/>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="EncryptedPayload">
    <xsd:sequence>
      <xsd:element name="encryptedData" type="xsd:base64Binary" minOccurs="0"/>
      <xsd:element name="publicKeyFingerprint" type="xsd:base64Binary" minOccurs="0"/>
      <xsd:element name="encryptedKey" type="xsd:base64Binary" minOccurs="0"/>
      <xsd:element name="oaepHashingAlgorithm" type="xsd:string" minOccurs="0"/>
      <xsd:element name="iv" type="xsd:base64Binary" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="CardInfo">
    <xsd:complexContent>
      <xsd:extension base="EncryptedPayload">
        <xsd:sequence>
          <xsd:element name="panUniqueReference" type="xsd:string" minOccurs="0"/>
          <xsd:element name="tokenUniqueReferenceForPanInfo" type="xsd:string" minOccurs="0"/>
        </xsd:sequence>
      </xsd:extension>
    </xsd:complexContent>
  </xsd:complexType>
  <xsd:complexType name="SpsdInfo">
    <xsd:sequence>
      <xsd:element name="aid" type="xsd:base64Binary" minOccurs="0"/>
      <xsd:element name="appletInstanceAid" type="xsd:base64Binary"/>
      <xsd:element name="spsdSequenceCounter" type="xsd:base64Binary" minOccurs="0"/>
      <xsd:element name="rgk" type="xsd:base64Binary" minOccurs="0"/>
      <xsd:element name="casdPkCertificate" type="xsd:base64Binary" minOccurs="0"/>
      <xsd:element name="casdPkJwk" type="JsonWebKey" minOccurs="0"/>
      <xsd:element name="semsPkCertificate" type="xsd:base64Binary" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="JsonWebKey">
    <xsd:sequence>
      <xsd:element name="kty" type="xsd:string"/>
      <xsd:element name="x5c" type="xsd:base64Binary"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="DecisioningData">
    <xsd:sequence>
      <xsd:element name="recommendation" type="xsd:string"/>
      <xsd:element name="recommendationAlgorithmVersion" type="xsd:integer"/>
      <xsd:element name="deviceScore" type="xsd:integer"/>
      <xsd:element name="accountScore" type="xsd:integer"/>
      <xsd:element name="recommendationReasons" maxOccurs="unbounded" type="xsd:string"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="AuthenticationMethods">
    <xsd:sequence>
      <xsd:element name="id" type="xsd:integer"/>
      <xsd:element name="type" type="xsd:string"/>
      <xsd:element name="value" type="xsd:string"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="ProductConfig">
    <xsd:sequence>
      <xsd:element name="brandLogoAssetId" type="xsd:string"/>
      <xsd:element name="isCoBranded" type="xsd:string"/>
      <xsd:element name="coBrandName" type="xsd:string" minOccurs="0"/>
      <xsd:element name="coBrandLogoAssetId" type="xsd:string" minOccurs="0"/>
      <xsd:element name="cardBackgroundCombinedAssetId" type="xsd:string" minOccurs="0"/>
      <xsd:element name="iconAssetId" type="xsd:string"/>
      <xsd:element name="foregroundColor" type="xsd:base64Binary"/>
      <xsd:element name="issuerName" type="xsd:string"/>
      <xsd:element name="shortDescription" type="xsd:string"/>
      <xsd:element name="longDescription" type="xsd:string" minOccurs="0"/>
      <xsd:element name="customerServiceUrl" type="xsd:string" minOccurs="0"/>
      <xsd:element name="customerServiceEmail" type="xsd:string" minOccurs="0"/>
      <xsd:element name="customerServicePhoneNumber" type="xsd:string" minOccurs="0"/>
      <xsd:element name="issuerMobileApp" type="IssuerMobileApp"/>
      <xsd:element name="termsAndConditionsUrl" type="xsd:string"/>
      <xsd:element name="privacyPolicyUrl" type="xsd:string"/>
      <xsd:element name="issuerProductConfigCode" type="xsd:integer"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="IssuerMobileApp">
    <xsd:sequence>
      <xsd:element name="openIssuerMobileAppAndroidIntent" type="AndroidIntent" minOccurs="0"/>
      <xsd:element name="activateWithIssuerMobileAppAndroidIntent" type="AndroidIntent" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="AndroidIntent">
    <xsd:sequence>
      <xsd:element name="action" type="xsd:string"/>
      <xsd:element name="packageName" type="xsd:string"/>
      <xsd:element name="extraTextValue" type="xsd:base64Binary"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="TokenInfo">
    <xsd:sequence>
      <xsd:element name="tokenPanSuffix" type="xsd:string"/>
      <xsd:element name="accountPanSuffix" type="xsd:string"/>
      <xsd:element name="tokenExpiry" type="xsd:string"/>
      <xsd:element name="dsrpCapable" type="xsd:boolean"/>
      <xsd:element name="tokenAssuranceLevel" type="xsd:integer" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:annotation xmlns="">
    <xsd:appinfo>NXSDSAMPLE=</xsd:appinfo>
    <xsd:appinfo>USEHEADER=false</xsd:appinfo>
  </xsd:annotation>
</xsd:schema>