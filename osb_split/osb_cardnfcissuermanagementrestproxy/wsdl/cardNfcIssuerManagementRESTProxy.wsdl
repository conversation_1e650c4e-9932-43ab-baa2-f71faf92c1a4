<wsdl:definitions name="CardNfcIssuerManagementRESTProxy" targetNamespace="http://xmlns.oracle.com/OSB-rewrite/MDES/CardNfcIssuerManagementRESTProxy" xmlns:tns="http://xmlns.oracle.com/OSB-rewrite/MDES/CardNfcIssuerManagementRESTProxy" xmlns:inp1="http://osb.airbank.cz/mdes/card/nfc/issuer" xmlns:plnk="http://docs.oasis-open.org/wsbpel/2.0/plnktype" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/">
    <plnk:partnerLinkType name="CardNfcIssuerManagementRESTProxy">
        <plnk:role name="CardNfcIssuerManagementRESTProxyProvider" portType="tns:CardNfcIssuerManagementRESTProxy_ptt"/>
    </plnk:partnerLinkType>
    <wsdl:types>
        <xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema">
            <xsd:import namespace="http://osb.airbank.cz/mdes/card/nfc/issuer" schemaLocation="../xsd/cardNfcIssuerManagementRESTProxy.xsd"/>
        </xsd:schema>
    </wsdl:types>
    <wsdl:message name="Rest_EmptyMessage"/>
    
    <wsdl:message name="authorizeService_inputMessage">
      <wsdl:part name="request" element="inp1:authorizeServiceRequest"/>
    </wsdl:message>
    <wsdl:message name="authorizeService_outputMessage">
      <wsdl:part name="reply" element="inp1:authorizeServiceResponse"/>
    </wsdl:message>
    <wsdl:message name="notifyTokenUpdated_inputMessage">
      <wsdl:part name="request" element="inp1:notifyTokenUpdatedRequest"/>
    </wsdl:message>
    <wsdl:message name="notifyTokenUpdated_outputMessage">
      <wsdl:part name="reply" element="inp1:notifyTokenUpdatedResponse"/>
    </wsdl:message>
    <wsdl:message name="deliverActivationCode_inputMessage">
      <wsdl:part name="request" element="inp1:deliverActivationCodeRequest"/>
    </wsdl:message>
    <wsdl:message name="deliverActivationCode_outputMessage">
      <wsdl:part name="reply" element="inp1:deliverActivationCodeResponse"/>
    </wsdl:message>
    <wsdl:message name="notifyServiceActivated_inputMessage">
      <wsdl:part name="request" element="inp1:notifyServiceActivatedRequest"/>
    </wsdl:message>
    <wsdl:message name="notifyServiceActivated_outputMessage">
      <wsdl:part name="reply" element="inp1:notifyServiceActivatedResponse"/>
    </wsdl:message>
    <wsdl:portType name="CardNfcIssuerManagementRESTProxy_ptt">
        <wsdl:operation name="authorizeService">
            <wsdl:input message="tns:authorizeService_inputMessage"/>
            <wsdl:output message="tns:authorizeService_outputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="notifyTokenUpdated">
            <wsdl:input message="tns:notifyTokenUpdated_inputMessage"/>
            <wsdl:output message="tns:notifyTokenUpdated_outputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="notifyServiceActivated">
            <wsdl:input message="tns:notifyServiceActivated_inputMessage"/>
            <wsdl:output message="tns:notifyServiceActivated_outputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="deliverActivationCode">
            <wsdl:input message="tns:deliverActivationCode_inputMessage"/>
            <wsdl:output message="tns:deliverActivationCode_outputMessage"/>
        </wsdl:operation>
    </wsdl:portType>
    <wsdl:binding name="CardNfcIssuerManagementRESTProxy_ptt-binding" type="tns:CardNfcIssuerManagementRESTProxy_ptt">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="authorizeService">
            <soap:operation soapAction="authorizeService"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="notifyTokenUpdated">
            <soap:operation soapAction="notifyTokenUpdated"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="notifyServiceActivated">
            <soap:operation soapAction="notifyServiceActivated"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="deliverActivationCode">
            <soap:operation soapAction="deliverActivationCode"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>
</wsdl:definitions>