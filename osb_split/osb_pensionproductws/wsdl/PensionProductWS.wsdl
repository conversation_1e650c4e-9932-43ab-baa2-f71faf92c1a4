<?xml version="1.0" encoding="UTF-8" ?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns="http://osb.airbank.cz/dps/pensionProductWS" targetNamespace="http://osb.airbank.cz/dps/pensionProductWS">

  <wsdl:types>
    <xsd:schema targetNamespace="http://osb.airbank.cz/dps/pensionProductWS">
      <xsd:include schemaLocation="PensionProductWS.xsd"/>
    </xsd:schema>
  </wsdl:types>

  <wsdl:message name="validateBeneficiariesRequest">
    <wsdl:part element="validateBeneficiariesRequest" name="validateBeneficiariesRequest" />
  </wsdl:message>
  <wsdl:message name="validateBeneficiariesResponse">
    <wsdl:part element="validateBeneficiariesResponse" name="validateBeneficiariesResponse" />
  </wsdl:message>
  <wsdl:message name="validateBeneficiariesFault">
    <wsdl:part element="errorList" name="validateBeneficiariesFault"/>
  </wsdl:message>
  
  <wsdl:message name="validateClientRequest">
    <wsdl:part element="validateClientRequest" name="validateClientRequest" />
  </wsdl:message>
  <wsdl:message name="validateClientResponse">
    <wsdl:part element="validateClientResponse" name="validateClientResponse" />
  </wsdl:message>
  <wsdl:message name="validateClientFault">
    <wsdl:part element="errorList" name="validateClientFault"/>
  </wsdl:message>
  
  <wsdl:message name="validateInvestmentStrategiesRequest">
    <wsdl:part element="validateInvestmentStrategiesRequest" name="validateInvestmentStrategiesRequest" />
  </wsdl:message>
  <wsdl:message name="validateInvestmentStrategiesResponse">
    <wsdl:part element="validateInvestmentStrategiesResponse" name="validateInvestmentStrategiesResponse" />
  </wsdl:message>
  <wsdl:message name="validateInvestmentStrategiesFault">
    <wsdl:part element="errorList" name="validateInvestmentStrategiesFault"/>
  </wsdl:message>
  
  <wsdl:message name="validateRequest">
    <wsdl:part element="validateRequest" name="validateRequest" />
  </wsdl:message>
  <wsdl:message name="validateResponse">
    <wsdl:part element="validateResponse" name="validateResponse" />
  </wsdl:message>
  <wsdl:message name="validateFault">
    <wsdl:part element="errorList" name="validateFault"/>
  </wsdl:message>
  
  <wsdl:message name="getCodeTableRequest">
    <wsdl:part element="getCodeTableRequest" name="getCodeTableRequest" />
  </wsdl:message>
  <wsdl:message name="getCodeTableResponse">
    <wsdl:part element="getCodeTableResponse" name="getCodeTableResponse" />
  </wsdl:message>
  <wsdl:message name="getCodeTableFault">
    <wsdl:part element="errorList" name="getCodeTableFault"/>
  </wsdl:message>
  
  <wsdl:message name="getContractNumberRequest">
    <wsdl:part element="getContractNumberRequest" name="getContractNumberRequest" />
  </wsdl:message>
  <wsdl:message name="getContractNumberResponse">
    <wsdl:part element="getContractNumberResponse" name="getContractNumberResponse" />
  </wsdl:message>
  <wsdl:message name="getContractNumberFault">
    <wsdl:part element="errorList" name="getContractNumberFault"/>
  </wsdl:message>
  
  <wsdl:message name="submitCaseRequest">
    <wsdl:part element="submitCaseRequest" name="submitCaseRequest" />
  </wsdl:message>
  <wsdl:message name="submitCaseResponse">
    <wsdl:part element="submitCaseResponse" name="submitCaseResponse" />
  </wsdl:message>
  <wsdl:message name="submitCaseFault">
    <wsdl:part element="errorList" name="submitCaseFault"/>
  </wsdl:message>
  
  <wsdl:message name="getQuestionRequest">
    <wsdl:part element="getQuestionRequest" name="getQuestionRequest" />
  </wsdl:message>
  <wsdl:message name="getQuestionResponse">
    <wsdl:part element="getQuestionResponse" name="getQuestionResponse" />
  </wsdl:message>
  <wsdl:message name="getQuestionFault">
    <wsdl:part element="errorList" name="getQuestionFault"/>
  </wsdl:message>
  
  <wsdl:message name="getQuestionnaireRequest">
    <wsdl:part element="getQuestionnaireRequest" name="getQuestionnaireRequest" />
  </wsdl:message>
  <wsdl:message name="getQuestionnaireResponse">
    <wsdl:part element="getQuestionnaireResponse" name="getQuestionnaireResponse" />
  </wsdl:message>
  <wsdl:message name="getQuestionnaireFault">
    <wsdl:part element="errorList" name="getQuestionnaireFault"/>
  </wsdl:message>
  
  <wsdl:message name="evaluateQuestionnaireRequest">
    <wsdl:part element="evaluateQuestionnaireRequest" name="evaluateQuestionnaireRequest" />
  </wsdl:message>
  <wsdl:message name="evaluateQuestionnaireResponse">
    <wsdl:part element="evaluateQuestionnaireResponse" name="evaluateQuestionnaireResponse" />
  </wsdl:message>
  <wsdl:message name="evaluateQuestionnaireFault">
    <wsdl:part element="errorList" name="evaluateQuestionnaireFault"/>
  </wsdl:message>
  
  <wsdl:message name="saveMeetingRecordRequest">
    <wsdl:part element="saveMeetingRecordRequest" name="saveMeetingRecordRequest" />
  </wsdl:message>
  <wsdl:message name="saveMeetingRecordResponse">
    <wsdl:part element="saveMeetingRecordResponse" name="saveMeetingRecordResponse" />
  </wsdl:message>
  <wsdl:message name="saveMeetingRecordFault">
    <wsdl:part element="errorList" name="saveMeetingRecordFault"/>
  </wsdl:message>
  
  <wsdl:message name="getContractInfoRequest">
    <wsdl:part element="getContractInfoRequest" name="getContractInfoRequest" />
  </wsdl:message>
  <wsdl:message name="getContractInfoResponse">
    <wsdl:part element="getContractInfoResponse" name="getContractInfoResponse" />
  </wsdl:message>
  <wsdl:message name="getContractInfoFault">
    <wsdl:part element="errorList" name="getContractInfoFault"/>
  </wsdl:message>
  
  <wsdl:message name="getAccountRequest">
    <wsdl:part element="getAccountRequest" name="getAccountRequest" />
  </wsdl:message>
  <wsdl:message name="getAccountResponse">
    <wsdl:part element="getAccountResponse" name="getAccountResponse" />
  </wsdl:message>
  <wsdl:message name="getAccountFault">
    <wsdl:part element="errorList" name="getAccountFault"/>
  </wsdl:message>
  
  <wsdl:message name="updateContractRequest">
    <wsdl:part element="updateContractRequest" name="updateContractRequest" />
  </wsdl:message>
  <wsdl:message name="updateContractResponse">
    <wsdl:part element="updateContractResponse" name="updateContractResponse" />
  </wsdl:message>
  <wsdl:message name="updateContractFault">
    <wsdl:part element="errorList" name="updateContractFault"/>
  </wsdl:message>
  
  <wsdl:message name="getDwhTransactionsRequest">
    <wsdl:part element="getDwhTransactionsRequest" name="getDwhTransactionsRequest" />
  </wsdl:message>
  <wsdl:message name="getDwhTransactionsResponse">
    <wsdl:part element="getDwhTransactionsResponse" name="getDwhTransactionsResponse" />
  </wsdl:message>
  <wsdl:message name="getDwhTransactionsFault">
    <wsdl:part element="errorList" name="getDwhTransactionsFault"/>
  </wsdl:message>

  <wsdl:message name="getLoginUrlRequest">
    <wsdl:part element="getLoginUrlRequest" name="getLoginUrlRequest" />
  </wsdl:message>
  <wsdl:message name="getLoginUrlResponse">
    <wsdl:part element="getLoginUrlResponse" name="getLoginUrlResponse" />
  </wsdl:message>
  <wsdl:message name="getLoginUrlFault">
    <wsdl:part element="errorList" name="getLoginUrlFault"/>
  </wsdl:message>
  
  <wsdl:portType name="PensionProductWSPort">
  
    <wsdl:operation name="validateBeneficiaries">
      <wsdl:documentation>>Method for validating a part of a contract.

Request contains Contract model for validation.
Method returns validation results.</wsdl:documentation>
      <wsdl:input message="validateBeneficiariesRequest" />
      <wsdl:output message="validateBeneficiariesResponse" />
      <wsdl:fault message="validateBeneficiariesFault" name="validateBeneficiariesFault"/>
    </wsdl:operation>
    
    <wsdl:operation name="validateClient">
      <wsdl:documentation>>Method for validating a part of a contract.

Request contains Contract model for validation.
Method returns validation results.</wsdl:documentation>
      <wsdl:input message="validateClientRequest" />
      <wsdl:output message="validateClientResponse" />
      <wsdl:fault message="validateClientFault" name="validateClientFault"/>
    </wsdl:operation>
    
    <wsdl:operation name="validateInvestmentStrategies">
      <wsdl:documentation>>Method for validating a part of a contract.

Request contains Contract model for validation.
Method returns validation results.</wsdl:documentation>
      <wsdl:input message="validateInvestmentStrategiesRequest" />
      <wsdl:output message="validateInvestmentStrategiesResponse" />
      <wsdl:fault message="validateInvestmentStrategiesFault" name="validateInvestmentStrategiesFault"/>
    </wsdl:operation>
    
    <wsdl:operation name="validate">
      <wsdl:documentation>>Method for validating a part of a contract.

Request contains Contract model for validation.
Method returns validation results.</wsdl:documentation>
      <wsdl:input message="validateRequest" />
      <wsdl:output message="validateResponse" />
      <wsdl:fault message="validateFault" name="validateFault"/>
    </wsdl:operation>
    
    <wsdl:operation name="getCodeTable">
      <wsdl:documentation>Method for getting code tables.

Returns all codes with names for specific code table.</wsdl:documentation>
      <wsdl:input message="getCodeTableRequest" />
      <wsdl:output message="getCodeTableResponse" />
      <wsdl:fault message="getCodeTableFault" name="getCodeTableFault"/>
    </wsdl:operation>
    
    <wsdl:operation name="submitCase">
      <wsdl:documentation>Create contract in pension system.
      
Request contains necessary data to conclude a contract and generated pdf file contract.
Returns the effective date of the contract.</wsdl:documentation>
      <wsdl:input message="submitCaseRequest" />
      <wsdl:output message="submitCaseResponse" />
      <wsdl:fault message="submitCaseFault" name="submitCaseFault"/>
    </wsdl:operation>
    
    <wsdl:operation name="getContractNumber">
      <wsdl:documentation>Contract numbers cannot be chosen randomly, they need to be from the specific list. Method serves to get such a number. It also validates if client with specified personal identification number has already contract in NN. The issued contract number can only be used for the personal identification number sent in request.

Request contains model with personal identification number.
The issued contract number can be used only for the personal identification number sent in request.</wsdl:documentation>
      <wsdl:input message="getContractNumberRequest" />
      <wsdl:output message="getContractNumberResponse" />
      <wsdl:fault message="getContractNumberFault" name="getContractNumberFault"/>
    </wsdl:operation>
    
    <wsdl:operation name="getQuestion">
      <wsdl:documentation>>Method for getting questions with answers for single question.

Returns question with all possible answers.</wsdl:documentation>
      <wsdl:input message="getQuestionRequest" />
      <wsdl:output message="getQuestionResponse" />
      <wsdl:fault message="getQuestionFault" name="getQuestionFault"/>
    </wsdl:operation>
    
    <wsdl:operation name="getQuestionnaire">
      <wsdl:documentation>>Method for getting questionnaire.

Method returns full questionnaire.</wsdl:documentation>
      <wsdl:input message="getQuestionnaireRequest" />
      <wsdl:output message="getQuestionnaireResponse" />
      <wsdl:fault message="getQuestionnaireFault" name="getQuestionnaireFault"/>
    </wsdl:operation>
    
    <wsdl:operation name="evaluateQuestionnaire">
      <wsdl:documentation>>Method for evaluating a filled questionnnare.

Request body contains array of filled questions.</wsdl:documentation>
      <wsdl:input message="evaluateQuestionnaireRequest" />
      <wsdl:output message="evaluateQuestionnaireResponse" />
      <wsdl:fault message="evaluateQuestionnaireFault" name="evaluateQuestionnaireFault"/>
    </wsdl:operation>
    
    <wsdl:operation name="saveMeetingRecord">
      <wsdl:documentation>>Save document.

Request contains Document model.</wsdl:documentation>
      <wsdl:input message="saveMeetingRecordRequest" />
      <wsdl:output message="saveMeetingRecordResponse" />
      <wsdl:fault message="saveMeetingRecordFault" name="saveMeetingRecordFault"/>
    </wsdl:operation>
    
    <wsdl:operation name="getContractInfo">
      <wsdl:documentation>>Get account.</wsdl:documentation>
      <wsdl:input message="getContractInfoRequest" />
      <wsdl:output message="getContractInfoResponse" />
      <wsdl:fault message="getContractInfoFault" name="getContractInfoFault"/>
    </wsdl:operation>
    
    <wsdl:operation name="getAccount">
      <wsdl:documentation>>Get account.</wsdl:documentation>
      <wsdl:input message="getAccountRequest" />
      <wsdl:output message="getAccountResponse" />
      <wsdl:fault message="getAccountFault" name="getAccountFault"/>
    </wsdl:operation>
    
    <wsdl:operation name="updateContract">
      <wsdl:documentation>>Update contract.</wsdl:documentation>
      <wsdl:input message="updateContractRequest" />
      <wsdl:output message="updateContractResponse" />
      <wsdl:fault message="updateContractFault" name="updateContractFault"/>
    </wsdl:operation>
    
    <wsdl:operation name="getDwhTransactions">
      <wsdl:documentation>>Get DWH transactions.</wsdl:documentation>
      <wsdl:input message="getDwhTransactionsRequest" />
      <wsdl:output message="getDwhTransactionsResponse" />
      <wsdl:fault message="getDwhTransactionsFault" name="getDwhTransactionsFault"/>
    </wsdl:operation>
    
    <wsdl:operation name="getLoginUrl">
      <wsdl:documentation>>Get DWH transactions.</wsdl:documentation>
      <wsdl:input message="getLoginUrlRequest" />
      <wsdl:output message="getLoginUrlResponse" />
      <wsdl:fault message="getLoginUrlFault" name="getLoginUrlFault"/>
    </wsdl:operation>
  </wsdl:portType>

  <wsdl:binding name="PensionProductWSBinding" type="PensionProductWSPort">
    <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />

    <wsdl:operation name="validateBeneficiaries">
      <soap:operation soapAction="" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
      <wsdl:fault name="validateBeneficiariesFault">
        <soap:fault use="literal" name="validateBeneficiariesFault"/>
      </wsdl:fault>
    </wsdl:operation>
    
    <wsdl:operation name="validateClient">
      <soap:operation soapAction="" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
      <wsdl:fault name="validateClientFault">
        <soap:fault use="literal" name="validateClientFault"/>
      </wsdl:fault>
    </wsdl:operation>
    
    <wsdl:operation name="validateInvestmentStrategies">
      <soap:operation soapAction="" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
      <wsdl:fault name="validateInvestmentStrategiesFault">
        <soap:fault use="literal" name="validateInvestmentStrategiesFault"/>
      </wsdl:fault>
    </wsdl:operation>
    
    <wsdl:operation name="validate">
      <soap:operation soapAction="" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
      <wsdl:fault name="validateFault">
        <soap:fault use="literal" name="validateFault"/>
      </wsdl:fault>
    </wsdl:operation>
    
    <wsdl:operation name="getCodeTable">
      <soap:operation soapAction="" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
      <wsdl:fault name="getCodeTableFault">
        <soap:fault use="literal" name="getCodeTableFault"/>
      </wsdl:fault>
    </wsdl:operation>
    
    <wsdl:operation name="submitCase">
      <soap:operation soapAction="" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
      <wsdl:fault name="submitCaseFault">
        <soap:fault use="literal" name="submitCaseFault"/>
      </wsdl:fault>
    </wsdl:operation>
    
    <wsdl:operation name="getContractNumber">
      <soap:operation soapAction="" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
      <wsdl:fault name="getContractNumberFault">
        <soap:fault use="literal" name="getContractNumberFault"/>
      </wsdl:fault>
    </wsdl:operation>
    
    <wsdl:operation name="getQuestion">
      <soap:operation soapAction="" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
      <wsdl:fault name="getQuestionFault">
        <soap:fault use="literal" name="getQuestionFault"/>
      </wsdl:fault>
    </wsdl:operation>
    
    <wsdl:operation name="getQuestionnaire">
      <soap:operation soapAction="" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
      <wsdl:fault name="getQuestionnaireFault">
        <soap:fault use="literal" name="getQuestionnaireFault"/>
      </wsdl:fault>
    </wsdl:operation>

    <wsdl:operation name="evaluateQuestionnaire">
      <soap:operation soapAction="" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
      <wsdl:fault name="evaluateQuestionnaireFault">
        <soap:fault use="literal" name="evaluateQuestionnaireFault"/>
      </wsdl:fault>
    </wsdl:operation>
    
    <wsdl:operation name="saveMeetingRecord">
      <soap:operation soapAction="" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
      <wsdl:fault name="saveMeetingRecordFault">
        <soap:fault use="literal" name="saveMeetingRecordFault"/>
      </wsdl:fault>
    </wsdl:operation>
    
    <wsdl:operation name="getContractInfo">
      <soap:operation soapAction="" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
      <wsdl:fault name="getContractInfoFault">
        <soap:fault use="literal" name="getContractInfoFault"/>
      </wsdl:fault>
    </wsdl:operation>
    
    <wsdl:operation name="getAccount">
      <soap:operation soapAction="" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
      <wsdl:fault name="getAccountFault">
        <soap:fault use="literal" name="getAccountFault"/>
      </wsdl:fault>
    </wsdl:operation>
    
    <wsdl:operation name="updateContract">
      <soap:operation soapAction="" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
      <wsdl:fault name="updateContractFault">
        <soap:fault use="literal" name="updateContractFault"/>
      </wsdl:fault>
    </wsdl:operation>
    
    <wsdl:operation name="getDwhTransactions">
      <soap:operation soapAction="" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
      <wsdl:fault name="getDwhTransactionsFault">
        <soap:fault use="literal" name="getDwhTransactionsFault"/>
      </wsdl:fault>
    </wsdl:operation>
    
    <wsdl:operation name="getLoginUrl">
      <soap:operation soapAction="" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
      <wsdl:fault name="getLoginUrlFault">
        <soap:fault use="literal" name="getLoginUrlFault"/>
      </wsdl:fault>
    </wsdl:operation>    
  </wsdl:binding>

  <wsdl:service name="PensionProductWS">
    <wsdl:port binding="PensionProductWSBinding" name="PensionProductWSBinding"/>
  </wsdl:service>
</wsdl:definitions>
