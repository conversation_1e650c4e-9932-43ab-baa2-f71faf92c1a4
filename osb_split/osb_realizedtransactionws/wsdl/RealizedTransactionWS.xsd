<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xsd:schema targetNamespace="http://airbank.cz/payments/realizedtransactions/" xmlns:tns="http://airbank.cz/payments/realizedtransactions/" xmlns:xsd="http://www.w3.org/2001/XMLSchema"
	elementFormDefault="qualified" xmlns:Q1="http://homecredit.net/manhattan-ib/ws/cz/FilterSchema">

	<xsd:import schemaLocation="../xsd/Filter.xsd" namespace="http://homecredit.net/manhattan-ib/ws/cz/FilterSchema" />
	
	<xsd:element name="getCompleteRealizedTransactionsRequest">
		<xsd:complexType>
			<xsd:sequence>				
				<xsd:element name="filter" type="Q1:SelectFilter" maxOccurs="1" minOccurs="1">
					<xsd:annotation>
						<xsd:documentation>mo<PERSON>n<PERSON> filtrační podmínky:

						IDBANKACCOUNT - long
						IDCATEGORY - long
						IDENVELOPE - long
						REALIZATIONDATE - date from,to (pro "to" de automaticky doplni cas 23:59:59)
						VALUTA - date from, to (pro "to" de automaticky doplni cas 23:59:59)
						TRANSACTIONTYPE - FilterRealizedTransactionType (TransactionTO.xsd)
						AMOUNT - decimal from, to - dle částky v měně účtu
						CURRENCY - string, operations equal
						DIRECTION - hodnoty I pro příchozí, O pro odchozí
						CONTRAACCOUNTNUMBER - string
						CONTRABANKCODE - string
						VS - string
						KS - string
						SS - string
						IDHOMERCARD - long id karty z homera
						BLOCKATIONTYPECODE - KBLOCK = karetni blokace / SBLOCK = Sazka blokace

						IDPRESCRIPTION - long -id předpisu (SIPO, povolení inkasa, trvalý příkaz, šablona)
						PRESCRIPTIONTYPE - string - typu PrescriptionEnumType (TransactionTO.xsd)
						(poznámka: IDPRESCRIPTION a PRESCRIPTIONTYPE musí být vždy ve filtru přítomny spolu)

						IDLOAN - long - nepovinné - identifikátor úvěru v OBS

						IDDOORDOCUMENT - identificator of document in DOOR system
						HASDOORDOCUMENT - transaction is paired with the document - value Y as yes or N as no

						IDCOUNTERPARTY - long, counterparty identifier

						Všechny atributy filtru mají mezi sebou
						vazbu AND

						Řazení podle (lze v daný okamžik pouze podle jednoho atributu):
						IDCATEGORY
						REALIZATIONDATE
						AMOUNT - dle částky v měně účtu
						TRANSACTIONTYPE
						VALUTA</xsd:documentation>
					</xsd:annotation>
				</xsd:element>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="getCompleteRealizedTransactionsResponse">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="realizedTransaction" maxOccurs="unbounded" minOccurs="0">
					<xsd:complexType>
						<xsd:sequence>
							<xsd:element name="realizationDate" type="xsd:date" maxOccurs="1" minOccurs="1">
								<xsd:annotation>
									<xsd:documentation>datum realizace</xsd:documentation>
								</xsd:annotation>
							</xsd:element>
							<xsd:element name="validFrom" type="xsd:dateTime" maxOccurs="1" minOccurs="1">
								<xsd:annotation>
									<xsd:documentation>počáteční datum platnosti předepsané transakce</xsd:documentation>
								</xsd:annotation>
							</xsd:element>
							<xsd:element name="amountInAccountCurrency" type="xsd:decimal" maxOccurs="1" minOccurs="1">
								<xsd:annotation>
									<xsd:documentation>částka v měně účtu</xsd:documentation>
								</xsd:annotation>
							</xsd:element>
							<xsd:element name="actionName" type="xsd:string" maxOccurs="1" minOccurs="1">
								<xsd:annotation>
									<xsd:documentation>popis typu realizované platby. Např
									platba, inkaso, poplatek za výpis,
									sankční úrok atd.</xsd:documentation>
								</xsd:annotation>
							</xsd:element>
							<xsd:element name="idRealizedTransaction" type="xsd:long" maxOccurs="1" minOccurs="1">
								<xsd:annotation>
									<xsd:documentation>primární klíč realizované transakce</xsd:documentation>
								</xsd:annotation>
							</xsd:element>
						</xsd:sequence>
					</xsd:complexType>
				</xsd:element>
          		</xsd:sequence>
		</xsd:complexType>
	</xsd:element>

</xsd:schema>
