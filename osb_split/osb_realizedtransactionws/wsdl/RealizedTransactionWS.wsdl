<wsdl:definitions name="RealizedTransactionWS" targetNamespace="http://airbank.cz/payments/realizedtransactions/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
	xmlns:tns="http://airbank.cz/payments/realizedtransactions/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
	<wsdl:types>
		<xsd:schema targetNamespace="http://airbank.cz/payments/realizedtransactions/" elementFormDefault="qualified">
			<xsd:include schemaLocation="RealizedTransactionWS.xsd" />
		</xsd:schema>
	</wsdl:types>
	<wsdl:message name="getCompleteRealizedTransactionsRequest">
		<wsdl:part element="tns:getCompleteRealizedTransactionsRequest" name="parameters" />
	</wsdl:message>
	<wsdl:message name="getCompleteRealizedTransactionsResponse">
		<wsdl:part element="tns:getCompleteRealizedTransactionsResponse" name="parameters" />
	</wsdl:message>
	<wsdl:portType name="RealizedTransactionWS">
		<wsdl:operation name="getCompleteRealizedTransactions">
			<wsdl:input message="tns:getCompleteRealizedTransactionsRequest" />
			<wsdl:output message="tns:getCompleteRealizedTransactionsResponse" />
		</wsdl:operation>
	</wsdl:portType>
	<wsdl:binding name="RealizedTransactionWSSOAP" type="tns:RealizedTransactionWS">
		<soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />
		<wsdl:operation name="getCompleteRealizedTransactions">
			<soap:operation soapAction="" />
			<wsdl:input>
				<soap:body use="literal" />
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal" />
			</wsdl:output>
		</wsdl:operation>
	</wsdl:binding>
	<wsdl:service name="RealizedTransactionWS">
		<wsdl:port binding="tns:RealizedTransactionWSSOAP" name="RealizedTransactionWSSOAP">
			<soap:address location="https://localhost:7002/" />
		</wsdl:port>
	</wsdl:service>
</wsdl:definitions>