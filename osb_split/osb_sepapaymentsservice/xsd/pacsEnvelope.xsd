<?xml version="1.0" encoding="UTF-8"?>
<schema xmlns="http://www.w3.org/2001/XMLSchema" targetNamespace="urn:S2SCTIcf:xsd:$SCTIcfBlkCredTrf" xmlns:S2SCTIcf="urn:S2SCTIcf:xsd:$SCTIcfBlkCredTrf"
	xmlns:pacs="urn:iso:std:iso:20022:tech:xsd:pacs.008.001.02" elementFormDefault="qualified">

	<import namespace="urn:iso:std:iso:20022:tech:xsd:pacs.008.001.02" schemaLocation="pacs.008.001.02.xsd" />

	<element name="SCTIcfBlkCredTrf" type="S2SCTIcf:SCTIcfBlkCredTrf" />

	<complexType name="SCTIcfBlkCredTrf">
		<sequence>
			<element name="SndgInst" type="string" />
			<element name="RcvgInst" type="string" />
			<element name="FileRef" type="string" />
			<element name="SrvcId" type="string" />
			<element name="TstCode" type="string" />
			<element name="FType" type="string" />

			<element name="FDtTm" type="string" />
			<element name="NumCTBlk" type="string" />
			<element name="NumPCRBlk" type="string" />
			<element name="NumRFRBlk" type="string" />
			<element name="NumROIBlk" type="string" />
			<element name="FIToFICstmrCdtTrf" type="pacs:FIToFICustomerCreditTransferV02" maxOccurs="unbounded" />
		</sequence>
	</complexType>

</schema>