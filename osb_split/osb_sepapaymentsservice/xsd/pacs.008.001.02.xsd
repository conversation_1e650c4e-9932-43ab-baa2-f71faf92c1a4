<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- STEP2 SCT Schema, pacs.008.001.02, January 22th 2019, Release November 2019 -->
<!-- STEP2 Removed choice between Ustrd and Strd of RmtInf for CR 3649, December 21th 2018 -->
<!-- STEP2 Set MaxOccurs of Ustrd to 1 for CR 3649, December 21th 2018 -->
<!-- STEP2 Add costraints to Amount for CR 3534, Dec 4th 2015 -->
<!-- STEP2 AOS2 999 occurrences for Structured Remittance Information for CR 3525, September 18th 2015 -->
<!-- STEP2 Spaces Allowed in Local Instrument for CR 3404, March 23th 2012 -->
<!-- STEP2 Add IntrBkSttlmDt for CR3344, March 29th 2010 -->
<!-- STEP2 Add PrvsInstgAgt for CR3344, March 29th 2010 -->
<!-- STEP2 ISODate with pattern restriction YYYY-MM-DD, January 26th 2010 -->
<!-- STEP2 ISODateTime with pattern restriction YYYY-MM-DDThh:mm:dd..., January 26th 2010 -->
<!-- STEP2 String with whiteSpace collapse and pattern restriction "\S+.*", January 26th 2010 -->
<xs:schema xmlns="urn:iso:std:iso:20022:tech:xsd:pacs.008.001.02" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="urn:iso:std:iso:20022:tech:xsd:pacs.008.001.02" elementFormDefault="qualified">
	<xs:element name="Document" type="Document"/>
	<xs:complexType name="S2SCTAccountIdentification4Choice">
		<xs:sequence>
			<xs:element name="IBAN" type="IBAN2007Identifier"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="S2SCTCurrencyAndAmount_SimpleType">
		<xs:restriction base="xs:decimal">
			<xs:minInclusive value="0"/>
			<xs:fractionDigits value="2"/>
			<xs:totalDigits value="18"/>
			<xs:pattern value="[0-9]{0,15}([\.]([0-9]{0,2})){0,1}"/>
			<!--xs:pattern added on 29-11-07-->
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="S2SCTCurrencyAndAmount">
		<xs:simpleContent>
			<xs:extension base="S2SCTCurrencyAndAmount_SimpleType">
				<xs:attribute name="Ccy" type="S2SCTCurrencyCode" use="required"/>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	<xs:complexType name="S2SCTCurrencyAndAmount1">
		<xs:simpleContent>
			<xs:extension base="S2SCTCurrencyAndAmount_SimpleType">
				<xs:attribute name="Ccy" type="S2SCTCurrencyCode1" use="required"/>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	<xs:simpleType name="S2SCTCurrencyCode">
		<xs:restriction base="xs:string">
			<xs:enumeration value="EUR"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="S2SCTCurrencyCode1">
		<xs:restriction base="xs:string">
			<xs:pattern value="[A-Z]{3,3}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="AnyBICIdentifier">
		<xs:restriction base="xs:string">
			<xs:pattern value="[A-Z]{6,6}[A-Z2-9][A-NP-Z0-9]([A-Z0-9]{3,3}){0,1}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="BICIdentifier">
		<xs:restriction base="xs:string">
			<xs:pattern value="[A-Z]{6,6}[A-Z2-9][A-NP-Z0-9]([A-Z0-9]{3,3}){0,1}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="BaseOneRate">
		<xs:restriction base="xs:decimal">
			<xs:fractionDigits value="10"/>
			<xs:totalDigits value="11"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="S2SCTBranchAndFinancialInstitutionIdentification4">
		<xs:sequence>
			<xs:element name="FinInstnId" type="S2SCTFinancialInstitutionIdentification7"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTCashAccount161">
		<xs:sequence>
			<xs:element name="Id" type="S2SCTAccountIdentification4Choice"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CategoryPurpose1Choice">
		<xs:sequence>
			<xs:choice>
				<xs:element name="Cd" type="ExternalCategoryPurpose1Code"/>
				<xs:element name="Prtry" type="Max35Text"/>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="S2SCTChargeBearerType1Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="SLEV"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="S2SCTChargesInformation5">
		<xs:sequence>
			<xs:element name="Amt" type="S2SCTCurrencyAndAmount"/>
			<xs:element name="Pty" type="S2SCTBranchAndFinancialInstitutionIdentification4"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTClearingSystemIdentification3Choice">
		<xs:sequence>
			<xs:element name="Prtry" type="S2SCTId7"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ContactDetails2">
		<xs:sequence>
			<xs:element name="NmPrfx" type="NamePrefix1Code" minOccurs="0"/>
			<xs:element name="Nm" type="Max140Text" minOccurs="0"/>
			<xs:element name="PhneNb" type="PhoneNumber" minOccurs="0"/>
			<xs:element name="MobNb" type="PhoneNumber" minOccurs="0"/>
			<xs:element name="FaxNb" type="PhoneNumber" minOccurs="0"/>
			<xs:element name="EmailAdr" type="Max2048Text" minOccurs="0"/>
			<xs:element name="Othr" type="Max35Text" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="CountryCode">
		<xs:restriction base="xs:string">
			<xs:pattern value="[A-Z]{2,2}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="CreditDebitCode">
		<xs:restriction base="xs:string">
			<xs:enumeration value="CRDT"/>
			<xs:enumeration value="DBIT"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="CreditTransferTransactionInformation11">
		<xs:sequence>
			<xs:element name="PmtId">
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="S2SCTPaymentIdentification3">
							<xs:sequence>
								<xs:element name="InstrId" type="S2SCTId7" minOccurs="0"/>
								<xs:element name="EndToEndId" type="Max35Text"/>
								<xs:element name="TxId" type="S2SCTId7"/>
							</xs:sequence>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="PmtTpInf" type="S2SCTPaymentTypeInformation21"/>
			<xs:element name="IntrBkSttlmAmt">
				<xs:complexType>
					<xs:simpleContent>
						<xs:restriction base="S2SCTCurrencyAndAmount">
							<xs:minInclusive value="0.01"/>
							<xs:maxInclusive value="999999999.99"/>
						</xs:restriction>
						<!-- STEP2 Add costraints to Amount for CR 3534, Dec 4th 2015 -->
					</xs:simpleContent>
				</xs:complexType>
			</xs:element>
			<!-- STEP2 Add IntrBkSttlmDt, March 29th 2010 -->
			<xs:element name="IntrBkSttlmDt" type="ISODate" minOccurs="0"/>
			<xs:element name="AccptncDtTm" type="ISODateTime" minOccurs="0"/>
			<xs:element name="InstdAmt" type="S2SCTCurrencyAndAmount1" minOccurs="0"/>
			<xs:element name="XchgRate" type="BaseOneRate" minOccurs="0"/>
			<xs:element name="ChrgBr" type="S2SCTChargeBearerType1Code"/>
			<xs:element name="ChrgsInf" type="S2SCTChargesInformation5" minOccurs="0"/>
			<!-- STEP2 Add PrvsInstgAgt, March 29th 2010 -->
			<xs:element name="PrvsInstgAgt" type="S2SCTBranchAndFinancialInstitutionIdentification4" minOccurs="0"/>
			<xs:element name="InstgAgt" type="S2SCTBranchAndFinancialInstitutionIdentification4" minOccurs="0"/>
			<xs:element name="UltmtDbtr" type="S2SCTPartyIdentification321" minOccurs="0"/>
			<xs:element name="Dbtr" type="S2SCTPartyIdentification322"/>
			<xs:element name="DbtrAcct" type="S2SCTCashAccount161"/>
			<xs:element name="DbtrAgt" type="S2SCTBranchAndFinancialInstitutionIdentification4"/>
			<xs:element name="CdtrAgt" type="S2SCTBranchAndFinancialInstitutionIdentification4"/>
			<xs:element name="Cdtr" type="S2SCTPartyIdentification322"/>
			<xs:element name="CdtrAcct" type="S2SCTCashAccount161"/>
			<xs:element name="UltmtCdtr" type="S2SCTPartyIdentification321" minOccurs="0"/>
			<xs:element name="InstrForCdtrAgt" type="InstructionForCreditorAgent1" minOccurs="0"/>
			<xs:element name="Purp" type="Purpose2Choice" minOccurs="0"/>
			<xs:element name="RgltryRptg" type="S2SCTRegulatoryReporting3" minOccurs="0"/>
			<xs:element name="RltdRmtInf" type="RemittanceLocation2" minOccurs="0"/>
			<xs:element name="RmtInf" type="S2SCTRemittanceInformation5" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTCreditorReferenceInformation2">
		<xs:sequence>
			<xs:element name="Tp" type="S2SCTCreditorReferenceType2"/>
			<xs:element name="Ref" type="Max35Text"/>
		</xs:sequence>
	</xs:complexType>
	<!-- solo per RmtInf.Strd.CdtrRefInf.Tp   -->
	<xs:complexType name="S2SCTCreditorReferenceType1Choice">
		<xs:sequence>
			<xs:choice>
				<xs:element name="Cd" type="S2SCTDocumentType3Code"/>
				<xs:element name="Prtry" type="Max35Text"/>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTCreditorReferenceType2">
		<xs:sequence>
			<xs:element name="CdOrPrtry" type="S2SCTCreditorReferenceType1Choice"/>
			<xs:element name="Issr" type="Max35Text" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="DateAndPlaceOfBirth">
		<xs:sequence>
			<xs:element name="BirthDt" type="ISODate"/>
			<xs:element name="PrvcOfBirth" type="Max35Text" minOccurs="0"/>
			<xs:element name="CityOfBirth" type="Max35Text"/>
			<xs:element name="CtryOfBirth" type="CountryCode"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Document">
		<xs:sequence>
			<xs:element name="FIToFICstmrCdtTrf" type="FIToFICustomerCreditTransferV02"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTDocumentAdjustment1">
		<xs:sequence>
			<xs:element name="Amt" type="S2SCTCurrencyAndAmount"/>
			<xs:element name="CdtDbtInd" type="CreditDebitCode" minOccurs="0"/>
			<xs:element name="Rsn" type="Max4Text" minOccurs="0"/>
			<xs:element name="AddtlInf" type="Max140Text" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="S2SCTDocumentType3Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="SCOR"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="DocumentType5Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="MSIN"/>
			<xs:enumeration value="CNFA"/>
			<xs:enumeration value="DNFA"/>
			<xs:enumeration value="CINV"/>
			<xs:enumeration value="CREN"/>
			<xs:enumeration value="DEBN"/>
			<xs:enumeration value="HIRI"/>
			<xs:enumeration value="SBIN"/>
			<xs:enumeration value="CMCN"/>
			<xs:enumeration value="SOAC"/>
			<xs:enumeration value="DISP"/>
			<xs:enumeration value="BOLD"/>
			<xs:enumeration value="VCHR"/>
			<xs:enumeration value="AROI"/>
			<xs:enumeration value="TSUT"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ExternalCategoryPurpose1Code">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="4"/>
			<xs:whiteSpace value="collapse"/>
			<xs:pattern value="\S+.*"/>
		</xs:restriction>
	</xs:simpleType>
	<!-- Spaces Allowed in Local Instrument, 2012-03-23 -->
	<xs:simpleType name="ExternalLocalInstrument1Code">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="35"/>
			<xs:whiteSpace value="collapse"/>
			<!--	<xs:pattern value="\S+.*"/>  -->
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ExternalOrganisationIdentification1Code">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="4"/>
			<xs:whiteSpace value="collapse"/>
			<xs:pattern value="\S+.*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ExternalPersonIdentification1Code">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="4"/>
			<xs:whiteSpace value="collapse"/>
			<xs:pattern value="\S+.*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ExternalPurpose1Code">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="4"/>
			<xs:whiteSpace value="collapse"/>
			<xs:pattern value="\S+.*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="S2SCTExternalServiceLevel1Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="SEPA"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="FIToFICustomerCreditTransferV02">
		<xs:sequence>
			<xs:element name="GrpHdr" type="S2SCTGroupHeader33"/>
			<xs:element name="CdtTrfTxInf" type="CreditTransferTransactionInformation11" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTFinancialInstitutionIdentification7">
		<xs:sequence>
			<xs:element name="BIC" type="BICIdentifier"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="GenericOrganisationIdentification1">
		<xs:sequence>
			<xs:element name="Id" type="Max35Text"/>
			<xs:element name="SchmeNm" type="OrganisationIdentificationSchemeName1Choice" minOccurs="0"/>
			<xs:element name="Issr" type="Max35Text" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="GenericPersonIdentification1">
		<xs:sequence>
			<xs:element name="Id" type="Max35Text"/>
			<xs:element name="SchmeNm" type="PersonIdentificationSchemeName1Choice" minOccurs="0"/>
			<xs:element name="Issr" type="Max35Text" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTGroupHeader33">
		<xs:sequence>
			<xs:element name="MsgId" type="S2SCTId7"/>
			<xs:element name="CreDtTm" type="ISODateTime"/>
			<xs:element name="NbOfTxs" type="Max15NumericText"/>
			<xs:element name="TtlIntrBkSttlmAmt" type="S2SCTCurrencyAndAmount"/>
			<xs:element name="IntrBkSttlmDt" type="ISODate"/>
			<xs:element name="SttlmInf" type="S2SCTSettlementInformation13"/>
			<xs:element name="InstgAgt" type="S2SCTBranchAndFinancialInstitutionIdentification4" minOccurs="0"/>
			<xs:element name="InstdAgt" type="S2SCTBranchAndFinancialInstitutionIdentification4" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="IBAN2007Identifier">
		<xs:restriction base="xs:string">
			<xs:pattern value="[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ISODate">
		<xs:restriction base="xs:date">
			<xs:pattern value="[0-9]{4,4}\-[0-9]{2,2}\-[0-9]{2,2}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ISODateTime">
		<xs:restriction base="xs:dateTime">
			<xs:pattern value="[0-9]{4,4}\-[0-9]{2,2}\-[0-9]{2,2}[T][0-9]{2,2}:[0-9]{2,2}:[0-9]{2,2}[\S]*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Instruction3Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="CHQB"/>
			<xs:enumeration value="HOLD"/>
			<xs:enumeration value="PHOB"/>
			<xs:enumeration value="TELB"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="InstructionForCreditorAgent1">
		<xs:sequence>
			<xs:element name="Cd" type="Instruction3Code" minOccurs="0"/>
			<xs:element name="InstrInf" type="Max140Text" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!-- Spaces Allowed in Local Instrument, 2012-03-23 -->
	<xs:complexType name="S2SCTLocalInstrument2Choice">
		<xs:choice>
			<xs:element name="Cd" type="ExternalLocalInstrument1Code"/>
			<xs:element name="Prtry" type="S2SCTId8"/>
		</xs:choice>
	</xs:complexType>
	<xs:simpleType name="Max10Text">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="10"/>
			<xs:whiteSpace value="collapse"/>
			<xs:pattern value="\S+.*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Max140Text">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="140"/>
			<xs:whiteSpace value="collapse"/>
			<xs:pattern value="\S+.*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Max15NumericText">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9]{1,15}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Max2048Text">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="2048"/>
			<xs:whiteSpace value="collapse"/>
			<xs:pattern value="\S+.*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Max35Text">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="35"/>
			<xs:whiteSpace value="collapse"/>
			<xs:pattern value="\S+.*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Max4Text">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="4"/>
			<xs:whiteSpace value="collapse"/>
			<xs:pattern value="\S+.*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Max16Text">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="16"/>
			<xs:whiteSpace value="collapse"/>
			<xs:pattern value="\S+.*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Max70Text">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="70"/>
			<xs:whiteSpace value="collapse"/>
			<xs:pattern value="\S+.*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="NameAndAddress10">
		<xs:sequence>
			<xs:element name="Nm" type="Max140Text"/>
			<xs:element name="Adr" type="S2SCTPostalAddress6"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="NamePrefix1Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="DOCT"/>
			<xs:enumeration value="MIST"/>
			<xs:enumeration value="MISS"/>
			<xs:enumeration value="MADM"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="S2SCTOrganisationIdentification4">
		<xs:sequence>
			<xs:element name="BICOrBEI" type="AnyBICIdentifier" minOccurs="0"/>
			<xs:element name="Othr" type="GenericOrganisationIdentification1" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="OrganisationIdentificationSchemeName1Choice">
		<xs:sequence>
			<xs:choice>
				<xs:element name="Cd" type="ExternalOrganisationIdentification1Code"/>
				<xs:element name="Prtry" type="Max35Text"/>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTParty7Choice">
		<xs:sequence>
			<xs:choice>
				<xs:element name="OrgId" type="S2SCTOrganisationIdentification4"/>
				<xs:element name="PrvtId" type="S2SCTPersonIdentification5"/>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<!-- per Ultimate Debtor Creditor Dbtr,Cdtr-->
	<xs:complexType name="S2SCTOrganisationIdentification41">
		<xs:sequence>
			<xs:choice>
				<xs:element name="BICOrBEI" type="AnyBICIdentifier"/>
				<xs:element name="Othr" type="GenericOrganisationIdentification1"/>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<!-- per Ultimate Debtor Creditor, Dbtr,Cdtr-->
	<xs:complexType name="S2SCTParty6Choice">
		<xs:sequence>
			<xs:choice>
				<xs:element name="OrgId" type="S2SCTOrganisationIdentification41"/>
				<xs:element name="PrvtId" type="S2SCTPersonIdentification51"/>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<!-- per Ultimate Debtor Creditor-->
	<xs:complexType name="S2SCTPartyIdentification321">
		<xs:sequence>
			<xs:element name="Nm" type="Max70Text" minOccurs="0"/>
			<xs:element name="PstlAdr" type="S2SCTPostalAddress6" minOccurs="0"/>
			<xs:element name="Id" type="S2SCTParty6Choice" minOccurs="0"/>
			<xs:element name="CtryOfRes" type="CountryCode" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!-- solo per Dbtr  Cdtr-->
	<xs:complexType name="S2SCTPartyIdentification322">
		<xs:sequence>
			<xs:element name="Nm" type="Max70Text"/>
			<xs:element name="PstlAdr" type="S2SCTPostalAddress6" minOccurs="0"/>
			<xs:element name="Id" type="S2SCTParty6Choice" minOccurs="0"/>
			<xs:element name="CtryOfRes" type="CountryCode" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!-- solo per   RmtInf Strd  Invcr  Invce-->
	<xs:complexType name="S2SCTPartyIdentification32">
		<xs:sequence>
			<xs:element name="Nm" type="Max140Text" minOccurs="0"/>
			<xs:element name="PstlAdr" type="S2SCTPostalAddress6" minOccurs="0"/>
			<xs:element name="Id" type="S2SCTParty7Choice" minOccurs="0"/>
			<xs:element name="CtryOfRes" type="CountryCode" minOccurs="0"/>
			<xs:element name="CtctDtls" type="ContactDetails2" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTPaymentIdentification3"/>
	<xs:complexType name="S2SCTPaymentTypeInformation21">
		<xs:sequence>
			<xs:element name="SvcLvl" type="S2SCTServiceLevel8Choice"/>
			<xs:element name="LclInstrm" type="S2SCTLocalInstrument2Choice" minOccurs="0"/>
			<xs:element name="CtgyPurp" type="CategoryPurpose1Choice" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!-- per Ultimate Debtor Creditor Dbtr,Cdtr-->
	<xs:complexType name="S2SCTPersonIdentification51">
		<xs:sequence>
			<xs:choice>
				<xs:element name="DtAndPlcOfBirth" type="DateAndPlaceOfBirth"/>
				<xs:element name="Othr" type="GenericPersonIdentification1"/>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTPersonIdentification5">
		<xs:sequence>
			<xs:element name="DtAndPlcOfBirth" type="DateAndPlaceOfBirth" minOccurs="0"/>
			<xs:element name="Othr" type="GenericPersonIdentification1" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PersonIdentificationSchemeName1Choice">
		<xs:sequence>
			<xs:choice>
				<xs:element name="Cd" type="ExternalPersonIdentification1Code"/>
				<xs:element name="Prtry" type="Max35Text"/>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="PhoneNumber">
		<xs:restriction base="xs:string">
			<xs:pattern value="\+[0-9]{1,3}-[0-9()+\-]{1,30}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="S2SCTPostalAddress6">
		<xs:sequence>
			<xs:element name="Ctry" type="CountryCode" minOccurs="0"/>
			<xs:element name="AdrLine" type="Max70Text" minOccurs="0" maxOccurs="2"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Purpose2Choice">
		<xs:sequence>
			<xs:choice>
				<xs:element name="Cd" type="ExternalPurpose1Code"/>
				<xs:element name="Prtry" type="Max35Text"/>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTReferredDocumentInformation3">
		<xs:sequence>
			<xs:element name="Tp" type="S2SCTReferredDocumentType2" minOccurs="0"/>
			<xs:element name="Nb" type="Max35Text" minOccurs="0"/>
			<xs:element name="RltdDt" type="ISODate" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ReferredDocumentType1Choice">
		<xs:sequence>
			<xs:choice>
				<xs:element name="Cd" type="DocumentType5Code"/>
				<xs:element name="Prtry" type="Max35Text"/>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTReferredDocumentType2">
		<xs:sequence>
			<xs:element name="CdOrPrtry" type="ReferredDocumentType1Choice"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="RegulatoryAuthority2">
		<xs:sequence>
			<xs:element name="Nm" type="Max140Text" minOccurs="0"/>
			<xs:element name="Ctry" type="CountryCode" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTRegulatoryReporting3">
		<xs:sequence>
			<xs:element name="DbtCdtRptgInd" type="RegulatoryReportingType1Code" minOccurs="0"/>
			<xs:element name="Authrty" type="RegulatoryAuthority2" minOccurs="0"/>
			<xs:element name="Dtls" type="S2SCTStructuredRegulatoryReporting3" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="RegulatoryReportingType1Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="CRED"/>
			<xs:enumeration value="DEBT"/>
			<xs:enumeration value="BOTH"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="S2SCTRemittanceAmount1">
		<xs:sequence>
			<xs:element name="DuePyblAmt" type="S2SCTCurrencyAndAmount" minOccurs="0"/>
			<xs:element name="DscntApldAmt" type="S2SCTCurrencyAndAmount" minOccurs="0"/>
			<xs:element name="CdtNoteAmt" type="S2SCTCurrencyAndAmount" minOccurs="0"/>
			<xs:element name="TaxAmt" type="S2SCTCurrencyAndAmount" minOccurs="0"/>
			<xs:element name="AdjstmntAmtAndRsn" type="S2SCTDocumentAdjustment1" minOccurs="0"/>
			<xs:element name="RmtdAmt" type="S2SCTCurrencyAndAmount" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTRemittanceInformation5">
		<xs:sequence>
			<!-- STEP2 Removed choice between Ustrd and Strd of RmtInf for CR 3649, December 21th 2018 -->
			<!-- STEP2 Set MaxOccurs of Ustrd to 1 for CR 3649, December 21th 2018 -->
			<xs:element name="Ustrd" type="Max140Text" minOccurs="0" maxOccurs="1"/>
			<!-- STEP2 AOS2 999 occurrences for Structured Remittance Information for CR 3525, September 18th 2015 -->
			<xs:element name="Strd" type="S2SCTStructuredRemittanceInformation7" minOccurs="0" maxOccurs="999"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="RemittanceLocation2">
		<xs:sequence>
			<xs:element name="RmtId" type="Max35Text" minOccurs="0"/>
			<xs:element name="RmtLctnMtd" type="RemittanceLocationMethod2Code" minOccurs="0"/>
			<xs:element name="RmtLctnElctrncAdr" type="Max2048Text" minOccurs="0"/>
			<xs:element name="RmtLctnPstlAdr" type="NameAndAddress10" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="RemittanceLocationMethod2Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="FAXI"/>
			<xs:enumeration value="EDIC"/>
			<xs:enumeration value="URID"/>
			<xs:enumeration value="EMAL"/>
			<xs:enumeration value="POST"/>
			<xs:enumeration value="SMSM"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="S2SCTServiceLevel8Choice">
		<xs:sequence>
			<xs:element name="Cd" type="S2SCTExternalServiceLevel1Code"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTSettlementInformation13">
		<xs:sequence>
			<xs:element name="SttlmMtd" type="S2SCTSettlementMethod1Code"/>
			<xs:element name="ClrSys" type="S2SCTClearingSystemIdentification3Choice"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="S2SCTSettlementMethod1Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="CLRG"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="S2SCTStructuredRegulatoryReporting3">
		<xs:sequence>
			<xs:element name="Tp" type="Max35Text" minOccurs="0"/>
			<xs:element name="Dt" type="ISODate" minOccurs="0"/>
			<xs:element name="Ctry" type="CountryCode" minOccurs="0"/>
			<xs:element name="Cd" type="Max10Text" minOccurs="0"/>
			<xs:element name="Amt" type="S2SCTCurrencyAndAmount" minOccurs="0"/>
			<xs:element name="Inf" type="Max35Text" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTStructuredRemittanceInformation7">
		<xs:sequence>
			<xs:element name="RfrdDocInf" type="S2SCTReferredDocumentInformation3" minOccurs="0"/>
			<xs:element name="RfrdDocAmt" type="S2SCTRemittanceAmount1" minOccurs="0"/>
			<xs:element name="CdtrRefInf" type="S2SCTCreditorReferenceInformation2" minOccurs="0"/>
			<xs:element name="Invcr" type="S2SCTPartyIdentification32" minOccurs="0"/>
			<xs:element name="Invcee" type="S2SCTPartyIdentification32" minOccurs="0"/>
			<xs:element name="AddtlRmtInf" type="Max140Text" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="S2SCTId7">
		<xs:restriction base="xs:string">
			<xs:pattern value="([A-Za-z0-9]|[+|\?|/|\-|:|\(|\)|\.|,|']){1,35}"/>
			<!--TxId and MsgId extended to support full character "+" on 05-06-07-->
			<!--TxId and MsgId extended to support full Latin character set on 08-03-07-->
			<!--xs:pattern value="([A-Za-z0-9]|[\?|/|\-|:|\(|\)|\.|,|']){1,35}"/-->
		</xs:restriction>
	</xs:simpleType>
	<!-- Added new type S2SCTId8 on 2012-03-23-->
	<xs:simpleType name="S2SCTId8">
		<xs:restriction base="xs:string">
			<xs:pattern value="([A-Za-z0-9]|[+|\?|/|\-|:|\(|\)|\.|,|'|\s]){1,35}"/>
		</xs:restriction>
	</xs:simpleType>
</xs:schema>
