<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- STEP2 SCT Schema, pacs.028.001.01, January 22th 2019, Release November 2019 -->
<!-- STEP2 ReqdExctnDt compliant to ISO, January 22th 2018 -->
<!-- STEP2 Set choice for Ustrd and Strd, each one mandatory for CR 3649, December 21th 2018 -->
<!-- STEP2 SCT Schema, multiple occurrences for FIToFIPmtStsReq/TxInf for CR 3648, Dec 21 2018-->
<!-- STEP2 SCT Schema, added pattern for camt.027 and camt.087 for CR 3648, Dec 21 2018-->
<!-- STEP2 SCT Schema, deleted choice between AnyBIC and Othr, July 12 2018-->
<!-- STEP2 SCT Schema, replaced  BICOrBEI with AnyBIC in S2SCTOrganisationIdentification4, July 09 2018-->
<!-- STEP2 SCT Schema, pacs.028.001.01, Mar 16th 2018, Release November 2018 -->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="urn:iso:std:iso:20022:tech:xsd:pacs.028.001.01" targetNamespace="urn:iso:std:iso:20022:tech:xsd:pacs.028.001.01" elementFormDefault="qualified">
	<xs:element name="Document" type="Document"/>
	<xs:complexType name="S2SCTAccountIdentification3Choice">
		<xs:sequence>
			<xs:element name="IBAN" type="IBANIdentifier"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTAccountIdentification4Choice">
		<xs:choice>
			<xs:element name="IBAN" type="IBANIdentifier"/>
			<xs:element name="Othr" type="S2SCTOrgnlDbtrAcctOther2"/>
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="S2SCTAmendmentInformationDetails1">
		<xs:sequence>
			<xs:element name="OrgnlMndtId" type="S2SCTId8" minOccurs="0"/>
			<xs:element name="OrgnlCdtrSchmeId" type="S2SCTCdtrSchmeId1" minOccurs="0"/>
			<xs:element name="OrgnlDbtrAcct" type="S2SCTCashAccount8" minOccurs="0"/>
			<xs:element name="OrgnlDbtrAgt" type="S2SCTOrgnlDbtrAgt" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="BICIdentifier">
		<xs:restriction base="xs:string">
			<xs:pattern value="[A-Z]{6,6}[A-Z2-9][A-NP-Z0-9]([A-Z0-9]{3,3}){0,1}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="S2SCTOrgnlDbtrAgt">
		<xs:sequence>
			<xs:element name="FinInstnId" type="S2SCTOrgnlDbtrAgt1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTBranchAndFinancialInstitutionIdentification3">
		<xs:sequence>
			<xs:element name="FinInstnId" type="S2SCTFinancialInstitutionIdentification5Choice"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTCashAccount7">
		<xs:sequence>
			<xs:element name="Id" type="S2SCTAccountIdentification3Choice"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTCashAccount8">
		<xs:sequence>
			<xs:element name="Id" type="S2SCTAccountIdentification4Choice"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="CountryCode">
		<xs:restriction base="xs:string">
			<xs:pattern value="[A-Z]{2,2}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="S2SCTCurrencyAndAmount_SimpleType">
		<xs:restriction base="xs:decimal">
			<xs:minInclusive value="0"/>
			<xs:fractionDigits value="2"/>
			<xs:totalDigits value="18"/>
			<xs:pattern value="[0-9]{0,15}([\.]([0-9]{0,2})){0,1}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="S2SCTCurrencyAndAmount">
		<xs:simpleContent>
			<xs:extension base="S2SCTCurrencyAndAmount_SimpleType">
				<xs:attribute name="Ccy" type="S2SCTCurrencyCode" use="required"/>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	<xs:simpleType name="S2SCTCurrencyCode">
		<xs:restriction base="xs:string">
			<xs:enumeration value="EUR"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="DateAndPlaceOfBirth">
		<xs:sequence>
			<xs:element name="BirthDt" type="ISODate"/>
			<xs:element name="PrvcOfBirth" type="Max35Text" minOccurs="0"/>
			<xs:element name="CityOfBirth" type="Max35Text"/>
			<xs:element name="CtryOfBirth" type="CountryCode"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Document">
		<xs:sequence>
			<xs:element name="FIToFIPmtStsReq" type="FIToFIPaymentInstantStatusInquiryForInvestigationV01"/>
		</xs:sequence>
		<!--17/07/2017 renamed tag FIToFIStsReq in FIToFIPmtStsReq and type FIToFIInstantStatusInquiryForInvestigationV01 in FIToFIPaymentInstantStatusInquiryForInvestigationV01-->
	</xs:complexType>
	<xs:complexType name="S2SCTGroupHeader5">
		<xs:sequence>
			<xs:element name="MsgId" type="S2SCTId7"/>
			<!--17/02/2017 tags S2SCTGroupHeader->CreDtTm, S2SCTOriginalGroupInformation->OrgnlCreDtTm: aligned format to ISO 8601 standard without negative date-->
			<xs:element name="CreDtTm" type="ISODateTime"/>
			<xs:element name="InstgAgt" type="S2SCTBranchAndFinancialInstitutionIdentification3" minOccurs="0"/>
			<xs:element name="InstdAgt" type="S2SCTBranchAndFinancialInstitutionIdentification3" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="IBANIdentifier">
		<xs:restriction base="xs:string">
			<xs:pattern value="[a-zA-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ISODate">
		<xs:restriction base="xs:date">
			<xs:pattern value="[0-9]{4,4}\-[0-9]{2,2}\-[0-9]{2,2}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ISODateTime">
		<xs:restriction base="xs:dateTime">
			<xs:pattern value="[0-9]{4,4}\-[0-9]{2,2}\-[0-9]{2,2}[T][0-9]{2,2}:[0-9]{2,2}:[0-9]{2,2}[\S]*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="S2SCTLocalInstrument1Choice">
		<xs:choice>
			<xs:element name="Cd" type="ExternalLocalInstrument1Code"/>
			<xs:element name="Prtry" type="S2SCTId8"/>
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="S2SCTMandateRelatedInformation1">
		<xs:sequence>
			<xs:element name="MndtId" type="S2SCTId8"/>
			<xs:element name="DtOfSgntr" type="ISODate"/>
			<xs:element name="AmdmntInd" type="TrueFalseIndicator" minOccurs="0"/>
			<xs:element name="AmdmntInfDtls" type="S2SCTAmendmentInformationDetails1" minOccurs="0"/>
			<xs:element name="ElctrncSgntr" type="Max1025Text" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="Max1025Text">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="1025"/>
			<xs:whiteSpace value="collapse"/>
			<xs:pattern value="\S+.*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Max140Text">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="140"/>
			<xs:whiteSpace value="collapse"/>
			<xs:pattern value="\S+.*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Max34Text">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="34"/>
			<xs:whiteSpace value="collapse"/>
			<xs:pattern value="\S+.*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Max35Text">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="35"/>
			<xs:whiteSpace value="collapse"/>
			<xs:pattern value="\S+.*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Max70Text">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="70"/>
			<xs:whiteSpace value="collapse"/>
			<xs:pattern value="\S+.*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Max105Text">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="105"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="S2SCTOriginalGroupInformation1">
		<xs:sequence>
			<xs:element name="OrgnlMsgId" type="S2SCTId7"/>
			<xs:element name="OrgnlMsgNmId" type="S2SCTOrgnlMsgNmId"/>
			<!--17/02/2017 tags S2SCTGroupHeader->CreDtTm, S2SCTOriginalGroupInformation->OrgnlCreDtTm: aligned format to ISO 8601 standard without negative date-->
			<xs:element name="OrgnlCreDtTm" type="ISODateTime" minOccurs="0" maxOccurs="1"/>
			<xs:element name="OrgnlNbOfTxs" type="Max15NumericText" minOccurs="0" maxOccurs="1"/>
			<xs:element name="OrgnlCtrlSum" type="S2SCTCurrencyAndAmount_SimpleType" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<!-- STEP2 SCT Schema, added pattern for camt.027 and camt.087 for CR 3648, Dec 21 2018-->
	<xs:simpleType name="S2SCTOrgnlMsgNmId">
		<xs:restriction base="xs:string">
			<xs:pattern value="camt\.056[A-Za-z0-9\.]{0,27}"/>
			<xs:pattern value="CAMT\.056[A-Za-z0-9\.]{0,27}"/>
			<xs:pattern value="camt\.027[A-Za-z0-9\.]{0,27}"/>
			<xs:pattern value="CAMT\.027[A-Za-z0-9\.]{0,27}"/>
			<xs:pattern value="camt\.087[A-Za-z0-9\.]{0,27}"/>
			<xs:pattern value="CAMT\.087[A-Za-z0-9\.]{0,27}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ActiveOrHistoricCurrencyAndAmount_SimpleType">
		<xs:restriction base="xs:decimal">
			<xs:minInclusive value="0"/>
			<xs:fractionDigits value="5"/>
			<xs:totalDigits value="18"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="ActiveOrHistoricCurrencyAndAmount">
		<xs:simpleContent>
			<xs:extension base="ActiveOrHistoricCurrencyAndAmount_SimpleType">
				<xs:attribute name="Ccy" type="ActiveOrHistoricCurrencyCode" use="required"/>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	<xs:simpleType name="ActiveOrHistoricCurrencyCode">
		<xs:restriction base="xs:string">
			<xs:pattern value="[A-Z]{3,3}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="AmountType4Choice">
		<xs:sequence>
			<xs:choice>
				<xs:element name="InstdAmt" type="ActiveOrHistoricCurrencyAndAmount"/>
				<xs:element name="EqvtAmt" type="EquivalentAmount2"/>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="EquivalentAmount2">
		<xs:sequence>
			<xs:element name="Amt" type="ActiveOrHistoricCurrencyAndAmount"/>
			<xs:element name="CcyOfTrf" type="ActiveOrHistoricCurrencyCode"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTOriginalTransactionReference1">
		<xs:sequence/>
	</xs:complexType>
	<xs:complexType name="CategoryPurpose1Choice">
		<xs:sequence>
			<xs:choice>
				<xs:element name="Cd" type="ExternalCategoryPurpose1Code"/>
				<xs:element name="Prtry" type="Max35Text"/>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="ExternalCategoryPurpose1Code">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="4"/>
			<xs:whiteSpace value="collapse"/>
			<xs:pattern value="\S+.*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="S2SCTSettlementInformation3">
		<xs:sequence>
			<xs:element name="SttlmMtd" type="S2SCTSettlementMethod1Code"/>
			<xs:element name="ClrSys" type="S2SCTClearingSystemIdentification1Choice"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTClearingSystemIdentification1Choice">
		<xs:sequence>
			<xs:element name="Prtry" type="Max35Text"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTPartyIdentification9">
		<xs:sequence>
			<xs:element name="Nm" type="Max70Text"/>
			<xs:element name="PstlAdr" type="S2SCTPostalAddress1" minOccurs="0"/>
			<xs:element name="Id" type="S2SCTParty4Choice" minOccurs="0"/>
			<xs:element name="CtryOfRes" type="CountryCode" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTPartyIdentification10">
		<xs:sequence>
			<xs:element name="Nm" type="Max70Text" minOccurs="0"/>
			<xs:element name="PstlAdr" type="S2SCTPostalAddress1" minOccurs="0"/>
			<xs:element name="Id" type="S2SCTParty4Choice" minOccurs="0"/>
			<xs:element name="CtryOfRes" type="CountryCode" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTOrgnlTxRef">
		<xs:sequence>
			<xs:element name="IntrBkSttlmAmt" minOccurs="0">
				<xs:complexType>
					<xs:simpleContent>
						<xs:restriction base="S2SCTCurrencyAndAmount">
							<xs:minInclusive value="0.01"/>
							<xs:maxInclusive value="*********.99"/>
						</xs:restriction>
					</xs:simpleContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="Amt" type="AmountType4Choice" minOccurs="0"/>
			<xs:element name="IntrBkSttlmDt" type="ISODate"/>
			<!-- STEP2 ReqdExctnDt compliant to ISO, January 22th 2018 -->
			<xs:element name="ReqdExctnDt" type="S2SCTDateAndDateTimeChoice" minOccurs="0" maxOccurs="1"/>
			<xs:element name="SttlmInf" type="S2SCTSettlementInformation3"/>
			<xs:element name="PmtTpInf" type="S2SCTPaymentTypeInformation6"/>
			<xs:element name="PmtMtd" type="PaymentMethod4Code" minOccurs="0" maxOccurs="1"/>
			<xs:element name="RmtInf" type="S2SCTRemittanceInformation1" minOccurs="0"/>
			<xs:element name="UltmtDbtr" type="S2SCTPartyIdentification10" minOccurs="0"/>
			<xs:element name="Dbtr" type="S2SCTPartyIdentification9"/>
			<xs:element name="DbtrAcct" type="S2SCTCashAccount7"/>
			<xs:element name="DbtrAgt" type="S2SCTBranchAndFinancialInstitutionIdentification3"/>
			<xs:element name="DbtrAgtAcct" type="S2SCTCashAccount16" minOccurs="0"/>
			<xs:element name="CdtrAgt" type="S2SCTBranchAndFinancialInstitutionIdentification3"/>
			<xs:element name="CdtrAgtAcct" type="S2SCTCashAccount16" minOccurs="0"/>
			<xs:element name="Cdtr" type="S2SCTPartyIdentification9"/>
			<xs:element name="CdtrAcct" type="S2SCTCashAccount7"/>
			<xs:element name="UltmtCdtr" type="S2SCTPartyIdentification10" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTDateAndDateTimeChoice">
		<xs:choice>
			<xs:element name="Dt" type="ISODate"/>
			<xs:element name="DtTm" type="ISODateTime"/>
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="S2SCTPaymentTransactionInformation1">
		<xs:sequence>
			<xs:element name="StsReqId" type="S2SCTId7"/>
			<xs:element name="OrgnlInstrId" type="S2SCTId7"/>
			<xs:element name="OrgnlEndToEndId" type="Max35Text"/>
			<xs:element name="OrgnlTxId" type="S2SCTId7"/>
			<xs:element name="AccptncDtTm" type="ISODateTime" minOccurs="0"/>
			<xs:element name="OrgnlTxRef" type="S2SCTOrgnlTxRef"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTCashAccount16">
		<xs:sequence>
			<xs:element name="Id" type="S2SCTAccountIdentification4Choice"/>
			<xs:element name="Tp" type="S2SCTCashAccountType2" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Ccy" type="ActiveOrHistoricCurrencyCode" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Nm" type="Max70Text" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTCashAccountType2">
		<xs:sequence>
			<xs:choice>
				<xs:element name="Cd" type="S2SCTCashAccountType4Code"/>
				<xs:element name="Prtry" type="Max35Text"/>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="PaymentMethod4Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="CHK"/>
			<xs:enumeration value="TRF"/>
			<xs:enumeration value="DD"/>
			<xs:enumeration value="TRA"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="S2SCTCashAccountType4Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="CASH"/>
			<xs:enumeration value="CHAR"/>
			<xs:enumeration value="COMM"/>
			<xs:enumeration value="TAXE"/>
			<xs:enumeration value="CISH"/>
			<xs:enumeration value="TRAS"/>
			<xs:enumeration value="SACC"/>
			<xs:enumeration value="CACC"/>
			<xs:enumeration value="SVGS"/>
			<xs:enumeration value="ONDP"/>
			<xs:enumeration value="MGLD"/>
			<xs:enumeration value="NREX"/>
			<xs:enumeration value="MOMA"/>
			<xs:enumeration value="LOAN"/>
			<xs:enumeration value="SLRY"/>
			<xs:enumeration value="ODFT"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="S2SCTChargesInformation1">
		<xs:sequence>
			<xs:element name="Amt">
				<xs:complexType>
					<xs:simpleContent>
						<xs:restriction base="S2SCTCurrencyAndAmount">
							<xs:minInclusive value="0.01"/>
							<xs:maxInclusive value="*********.99"/>
						</xs:restriction>
					</xs:simpleContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="Pty" type="S2SCTBranchAndFinancialInstitutionIdentification3"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTPaymentTypeInformation6">
		<xs:sequence>
			<!--06/03/2017 tag SvcLvl: removed restriction to value "SEPA"-->
			<xs:element name="SvcLvl" type="S2SCTServiceLevel2Choice"/>
			<xs:element name="LclInstrm" type="S2SCTLocalInstrument1Choice" minOccurs="0"/>
			<xs:element name="CtgyPurp" type="CategoryPurpose1Choice" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTPostalAddress1">
		<xs:sequence>
			<xs:element name="Ctry" type="CountryCode" minOccurs="0"/>
			<xs:element name="AdrLine" type="Max70Text" minOccurs="0" maxOccurs="2"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTRemittanceInformation1">
		<xs:choice>
			<!-- STEP2 Set choice for Ustrd and Strd, each one mandatory for CR 3649, December 21th 2018 -->
			<xs:element name="Ustrd" type="Max140Text"/>
			<xs:element name="Strd" type="S2SCTStructuredRemittanceInformation6"/>
		</xs:choice>
	</xs:complexType>
	<xs:simpleType name="S2SCTSequenceType1Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="FRST"/>
			<xs:enumeration value="RCUR"/>
			<xs:enumeration value="FNAL"/>
			<xs:enumeration value="OOFF"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="S2SCTServiceLevel1Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="SEPA"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="S2SCTServiceLevel2Choice">
		<xs:sequence>
			<xs:element name="Cd" type="S2SCTServiceLevel1Code"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="S2SCTSettlementMethod1Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="CLRG"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="S2SCTStatusReason1Choice">
		<xs:sequence>
			<xs:choice>
				<xs:element name="Cd" type="S2SCTTransactionRejectReason3Code"/>
				<xs:element name="Prtry" type="Max35Text"/>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTStatusReasonInformation1">
		<xs:sequence>
			<xs:element name="Orgtr" type="S2SCTId5"/>
			<xs:element name="Rsn" type="S2SCTStatusReason1Choice"/>
			<xs:element name="AddtlInf" type="Max105Text" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTOrganisationIdentification2">
		<xs:sequence>
			<xs:element name="BICOrBEI" type="BICIdentifier"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTStructuredRemittanceInformation6">
		<xs:sequence>
			<xs:element name="RfrdDocInf" type="S2SCTReferredDocumentInformation3" minOccurs="0"/>
			<xs:element name="RfrdDocAmt" type="S2SCTReferredDocumentAmount1Choice" minOccurs="0"/>
			<xs:element name="CdtrRefInf" type="S2SCTCreditorReferenceInformation2" minOccurs="0"/>
			<xs:element name="Invcr" type="S2SCTPartyIdentification32" minOccurs="0"/>
			<xs:element name="Invcee" type="S2SCTPartyIdentification32" minOccurs="0"/>
			<xs:element name="AddtlRmtInf" type="Max140Text" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTReferredDocumentInformation3">
		<xs:sequence>
			<xs:element name="Tp" type="S2SCTReferredDocumentType2" minOccurs="0"/>
			<xs:element name="Nb" type="Max35Text" minOccurs="0"/>
			<xs:element name="RltdDt" type="ISODate" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTReferredDocumentType2">
		<xs:sequence>
			<xs:element name="CdOrPrtry" type="S2SCTReferredDocumentType1Choice"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTReferredDocumentType1Choice">
		<xs:sequence>
			<xs:choice>
				<xs:element name="Cd" type="S2SCTDocumentType5Code"/>
				<xs:element name="Prtry" type="Max35Text"/>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="S2SCTDocumentType5Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="MSIN"/>
			<xs:enumeration value="CNFA"/>
			<xs:enumeration value="DNFA"/>
			<xs:enumeration value="CINV"/>
			<xs:enumeration value="CREN"/>
			<xs:enumeration value="DEBN"/>
			<xs:enumeration value="HIRI"/>
			<xs:enumeration value="SBIN"/>
			<xs:enumeration value="CMCN"/>
			<xs:enumeration value="SOAC"/>
			<xs:enumeration value="DISP"/>
			<xs:enumeration value="BOLD"/>
			<xs:enumeration value="VCHR"/>
			<xs:enumeration value="AROI"/>
			<xs:enumeration value="TSUT"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="S2SCTReferredDocumentAmount1Choice">
		<xs:sequence>
			<xs:element name="DuePyblAmt" type="S2SCTCurrencyAndAmount" minOccurs="0"/>
			<xs:element name="DscntApldAmt" type="S2SCTCurrencyAndAmount" minOccurs="0"/>
			<xs:element name="CdtNoteAmt" type="S2SCTCurrencyAndAmount" minOccurs="0"/>
			<xs:element name="TaxAmt" type="S2SCTCurrencyAndAmount" minOccurs="0"/>
			<xs:element name="AdjstmntAmtAndRsn" type="S2SCTDocumentAdjustment1" minOccurs="0"/>
			<xs:element name="RmtdAmt" type="S2SCTCurrencyAndAmount" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTDocumentAdjustment1">
		<xs:sequence>
			<xs:element name="Amt" type="S2SCTCurrencyAndAmount"/>
			<xs:element name="CdtDbtInd" type="S2SCTCreditDebitCode" minOccurs="0"/>
			<xs:element name="Rsn" type="Max4Text" minOccurs="0"/>
			<xs:element name="AddtlInf" type="Max140Text" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="S2SCTCreditDebitCode">
		<xs:restriction base="xs:string">
			<xs:enumeration value="CRDT"/>
			<xs:enumeration value="DBIT"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Max4Text">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="4"/>
			<xs:whiteSpace value="collapse"/>
			<xs:pattern value="\S+.*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="S2SCTCreditorReferenceInformation2">
		<xs:sequence>
			<xs:element name="Tp" type="S2SCTCreditorReferenceType2"/>
			<xs:element name="Ref" type="Max35Text"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTCreditorReferenceType2">
		<xs:sequence>
			<xs:element name="CdOrPrtry" type="S2SCTCreditorReferenceType1Choice"/>
			<xs:element name="Issr" type="Max35Text" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTCreditorReferenceType1Choice">
		<xs:sequence>
			<xs:choice>
				<xs:element name="Cd" type="S2SCTDocumentType3Code"/>
				<xs:element name="Prtry" type="Max35Text"/>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="S2SCTDocumentType3Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="SCOR"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="S2SCTPartyIdentification32">
		<xs:sequence>
			<xs:element name="Nm" type="Max140Text" minOccurs="0"/>
			<xs:element name="PstlAdr" type="S2SCTPostalAddress1" minOccurs="0"/>
			<xs:element name="Id" type="S2SCTParty6Choice" minOccurs="0"/>
			<xs:element name="CtryOfRes" type="CountryCode" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTParty6Choice">
		<xs:sequence>
			<xs:choice>
				<xs:element name="OrgId" type="S2SCTOrganisationIdentification4"/>
				<xs:element name="PrvtId" type="S2SCTPersonIdentification5"/>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="S2SCTNamePrefix1Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="DOCT"/>
			<xs:enumeration value="MIST"/>
			<xs:enumeration value="MISS"/>
			<xs:enumeration value="MADM"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="S2SCTPhoneNumber">
		<xs:restriction base="xs:string">
			<xs:pattern value="\+[0-9]{1,3}-[0-9()+\-]{1,30}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="DecimalNumber">
		<xs:restriction base="xs:decimal">
			<xs:fractionDigits value="17"/>
			<xs:totalDigits value="18"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Max15NumericText">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9]{1,15}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Max2048Text">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="2048"/>
			<xs:whiteSpace value="collapse"/>
			<xs:pattern value="\S+.*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="S2SCTTransactionGroupStatus1Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="PART"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="S2SCTTransactionIndividualStatus1Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ACSC"/>
			<xs:enumeration value="RJCT"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="S2SCTTransactionRejectReason3Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="AC01"/>
			<xs:enumeration value="AC04"/>
			<xs:enumeration value="AC06"/>
			<xs:enumeration value="AC13"/>
			<xs:enumeration value="AG01"/>
			<xs:enumeration value="AG02"/>
			<xs:enumeration value="AM04"/>
			<xs:enumeration value="AM05"/>
			<xs:enumeration value="CNOR"/>
			<xs:enumeration value="DNOR"/>
			<xs:enumeration value="FF01"/>
			<xs:enumeration value="MD01"/>
			<xs:enumeration value="MD02"/>
			<xs:enumeration value="MD07"/>
			<xs:enumeration value="MS02"/>
			<xs:enumeration value="MS03"/>
			<xs:enumeration value="RC01"/>
			<xs:enumeration value="RR01"/>
			<xs:enumeration value="RR02"/>
			<xs:enumeration value="RR03"/>
			<xs:enumeration value="RR04"/>
			<xs:enumeration value="SL01"/>
			<xs:enumeration value="BE05"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TrueFalseIndicator">
		<xs:restriction base="xs:boolean"/>
	</xs:simpleType>
	<xs:complexType name="FIToFIPaymentInstantStatusInquiryForInvestigationV01">
		<xs:sequence>
			<xs:element name="GrpHdr" type="S2SCTGroupHeader5"/>
			<xs:element name="OrgnlGrpInf" type="S2SCTOriginalGroupInformation1"/>
			<!-- STEP2 SCT Schema, multiple occurrences for FIToFIPmtStsReq/TxInf for CR 3648, Dec 21 2018-->
			<xs:element name="TxInf" type="S2SCTPaymentTransactionInformation1" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTCdtrSchmeId">
		<xs:sequence>
			<xs:element name="Id" type="S2SCTPrivateIdentification2"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTPrivateIdentification">
		<xs:sequence>
			<xs:element name="PrvtId" type="S2SCTPrty1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTPrty1">
		<xs:sequence>
			<xs:element name="Othr" type="S2SCTId4"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTId4">
		<xs:sequence>
			<xs:element name="Id" type="S2SCTId8"/>
			<xs:element name="SchmeNm" type="FinancialIdentificationSchemeName2"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="FinancialIdentificationSchemeName2">
		<xs:sequence>
			<xs:element name="Prtry" type="S2SCTId9"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="S2SCTId9">
		<xs:restriction base="xs:string">
			<xs:enumeration value="SEPA"/>
		</xs:restriction>
	</xs:simpleType>
	<!--
	<xs:complexType name="S2SCTId2">
		<xs:sequence>
			<xs:element name="Nm" type="Max70Text"/>
			<xs:element name="PstlAdr" type="S2SCTPostalAddress1" minOccurs="0"/>
			<xs:element name="CtryOfRes" type="CountryCode" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
-->
	<!--03/05/2017 renamed tag BIC in BICFI in following elements:GrpHdr+InstgAgt++FinInstnId,GrpHdr+InstdAgt++FinInstnId,TxInf+OrgnlTxRef++CdtrAgt+++FinInstnId,TxInf+OrgnlTxRef++DbtrAgt+++FinInstnId
-->
	<xs:complexType name="S2SCTFinancialInstitutionIdentification5Choice">
		<xs:sequence>
			<xs:element name="BICFI" type="BICIdentifier"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTOrgnlDbtrAgt1">
		<xs:choice>
			<xs:element name="BIC" type="BICIdentifier"/>
			<xs:element name="Othr" type="S2SCTOrgnlDbtrAgtOther2"/>
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="S2SCTOrgnlDbtrAcctOther2">
		<xs:sequence>
			<xs:element name="Id" type="Max34Text"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTOrgnlDbtrAgtOther2">
		<xs:sequence>
			<xs:element name="Id" type="Max35Text"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTId5">
		<xs:sequence>
			<xs:choice>
				<xs:element name="Nm" type="Max70Text"/>
				<xs:element name="Id" type="S2SCTParty3Choice"/>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTParty3Choice">
		<xs:sequence>
			<xs:element name="OrgId" type="S2SCTOrganisationIdentification2"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="S2SCTId7">
		<xs:restriction base="xs:string">
			<xs:pattern value="([A-Za-z0-9]|[+|\?|/|\-|:|\(|\)|\.|,|']){1,35}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="S2SCTParty4Choice">
		<xs:sequence>
			<xs:choice>
				<xs:element name="OrgId" type="S2SCTOrganisationIdentification4"/>
				<xs:element name="PrvtId" type="S2SCTPersonIdentification5"/>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTOrganisationIdentification4">
		<xs:sequence>
			<!-- STEP2 SCT Schema, deleted choice between AnyBIC and Othr, July 12 2018-->
			<!--<xs:choice>-->
			<!-- STEP2 SCT Schema, replaced  BICOrBEI with AnyBIC in S2SCTOrganisationIdentification4, July 09 2018-->
			<!--<xs:element name="BICOrBEI" type="S2SCTAnyBICIdentifier" minOccurs="0"/>-->
			<xs:element name="AnyBIC" type="S2SCTAnyBICIdentifier" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Othr" type="S2SCTGenericOrganisationIdentification1" minOccurs="0"/>
			<!--</xs:choice>-->
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="S2SCTAnyBICIdentifier">
		<xs:restriction base="xs:string">
			<xs:pattern value="[A-Z]{6,6}[A-Z2-9][A-NP-Z0-9]([A-Z0-9]{3,3}){0,1}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="S2SCTGenericOrganisationIdentification1">
		<xs:sequence>
			<xs:element name="Id" type="Max35Text"/>
			<xs:element name="SchmeNm" type="S2SCTOrganisationIdentificationSchemeName1Choice" minOccurs="0"/>
			<xs:element name="Issr" type="Max35Text" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTOrganisationIdentificationSchemeName1Choice">
		<xs:sequence>
			<xs:choice>
				<xs:element name="Cd" type="S2SCTExternalOrganisationIdentification1Code"/>
				<xs:element name="Prtry" type="Max35Text"/>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="S2SCTExternalOrganisationIdentification1Code">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="4"/>
			<xs:whiteSpace value="collapse"/>
			<xs:pattern value="\S+.*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="S2SCTPersonIdentification5">
		<xs:sequence>
			<xs:choice>
				<xs:element name="DtAndPlcOfBirth" type="DateAndPlaceOfBirth" minOccurs="0"/>
				<xs:element name="Othr" type="S2SCTGenericPersonIdentification1" minOccurs="0"/>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTGenericPersonIdentification1">
		<xs:sequence>
			<xs:element name="Id" type="Max35Text"/>
			<xs:element name="SchmeNm" type="S2SCTPersonIdentificationSchemeName1Choice" minOccurs="0"/>
			<xs:element name="Issr" type="Max35Text" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTPersonIdentificationSchemeName1Choice">
		<xs:sequence>
			<xs:choice>
				<xs:element name="Cd" type="S2SCTExternalPersonIdentification1Code"/>
				<xs:element name="Prtry" type="Max35Text"/>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="S2SCTExternalPersonIdentification1Code">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="4"/>
			<xs:whiteSpace value="collapse"/>
			<xs:pattern value="\S+.*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="S2SCTCdtrSchmeId1">
		<xs:sequence>
			<xs:element name="Nm" type="Max70Text" minOccurs="0"/>
			<xs:element name="Id" type="S2SCTPrivateIdentification" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="S2SCTId8">
		<xs:restriction base="xs:string">
			<xs:pattern value="([A-Za-z0-9]|[+|\?|/|\-|:|\(|\)|\.|,|'|\s]){1,35}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ExternalLocalInstrument1Code">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="35"/>
			<xs:whiteSpace value="collapse"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="S2SCTPrivateIdentification2">
		<xs:sequence>
			<xs:element name="PrvtId" type="S2SCTPrty2"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTPrty2">
		<xs:sequence>
			<xs:element name="Othr" type="S2SCTId10"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTId10">
		<xs:sequence>
			<xs:element name="Id" type="S2SCTId8"/>
			<xs:element name="SchmeNm" type="FinancialIdentificationSchemeName2"/>
		</xs:sequence>
	</xs:complexType>
</xs:schema>
