<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- STEP2 SCT Schema, camt.087.001.05, January 22th 2019, Release November 2019 -->
<xs:schema xmlns="urn:iso:std:iso:20022:tech:xsd:camt.087.001.05" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="urn:iso:std:iso:20022:tech:xsd:camt.087.001.05" elementFormDefault="qualified">
	<xs:element name="Document" type="Document"/>
	<xs:simpleType name="S2SCTCurrencyAndAmount_SimpleType">
		<xs:restriction base="xs:decimal">
			<xs:minInclusive value="0"/>
			<xs:fractionDigits value="2"/>
			<xs:totalDigits value="18"/>
			<xs:pattern value="[0-9]{0,15}([\.]([0-9]{0,2})){0,1}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="S2SCTCurrencyAndAmount">
		<xs:simpleContent>
			<xs:extension base="S2SCTCurrencyAndAmount_SimpleType">
				<xs:attribute name="Ccy" type="S2SCTCurrencyCode" use="required"/>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	<xs:simpleType name="S2SCTCurrencyCode">
		<xs:restriction base="xs:string">
			<xs:enumeration value="EUR"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="AccountIdentification4Choice">
		<xs:choice>
			<xs:element name="IBAN" type="IBAN2007Identifier"/>
			<xs:element name="Othr" type="GenericAccountIdentification1"/>
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="GenericAccountIdentification1">
		<xs:sequence>
			<xs:element name="Id" type="Max34Text"/>
			<xs:element name="SchmeNm" type="AccountSchemeName1Choice" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Issr" type="Max35Text" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="AccountSchemeName1Choice">
		<xs:choice>
			<xs:element name="Cd" type="ExternalAccountIdentification1Code"/>
			<xs:element name="Prtry" type="Max35Text"/>
		</xs:choice>
	</xs:complexType>
	<xs:simpleType name="ExternalAccountIdentification1Code">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="4"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="S2SCTAccountIdentification4Choice">
		<xs:sequence>
			<xs:element name="IBAN" type="IBAN2007Identifier"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="AddressType2Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ADDR"/>
			<xs:enumeration value="PBOX"/>
			<xs:enumeration value="HOME"/>
			<xs:enumeration value="BIZZ"/>
			<xs:enumeration value="MLTO"/>
			<xs:enumeration value="DLVY"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="AnyBICIdentifier">
		<xs:restriction base="xs:string">
			<xs:pattern value="[A-Z]{6,6}[A-Z2-9][A-NP-Z0-9]([A-Z0-9]{3,3}){0,1}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="BICFIIdentifier">
		<xs:restriction base="xs:string">
			<xs:pattern value="[A-Z]{6,6}[A-Z2-9][A-NP-Z0-9]([A-Z0-9]{3,3}){0,1}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="S2SCTBranchAndFinancialInstitutionIdentification5">
		<xs:sequence>
			<xs:element name="FinInstnId" type="S2SCTFinancialInstitutionIdentification8"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Case4">
		<xs:sequence>
			<xs:element name="Id" type="Max35Text"/>
			<xs:element name="Cretr" type="S2SCTParty35ChoiceV2"/>
			<xs:element name="ReopCaseIndctn" type="YesNoIndicator" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CaseAssignment4">
		<xs:sequence>
			<xs:element name="Id" type="S2SCTId7"/>
			<xs:element name="Assgnr" type="S2SCTParty35ChoiceV2"/>
			<xs:element name="Assgne" type="S2SCTParty35ChoiceV2"/>
			<xs:element name="CreDtTm" type="ISODateTime"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CashAccount24">
		<xs:sequence>
			<xs:element name="Id" type="AccountIdentification4Choice"/>
			<xs:element name="Tp" type="CashAccountType2Choice" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Ccy" type="ActiveOrHistoricCurrencyCode" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Nm" type="Max70Text" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CashAccountType2Choice">
		<xs:choice>
			<xs:element name="Cd" type="ExternalCashAccountType1Code"/>
			<xs:element name="Prtry" type="Max35Text"/>
		</xs:choice>
	</xs:complexType>
	<xs:simpleType name="ExternalCashAccountType1Code">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="4"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="S2SCTCashAccount24">
		<xs:sequence>
			<xs:element name="Id" type="S2SCTAccountIdentification4Choice"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CategoryPurpose1Choice">
		<xs:choice>
			<xs:element name="Cd" type="ExternalCategoryPurpose1CodeCW"/>
			<xs:element name="Prtry" type="Max35TextCW"/>
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="S2SCTClearingSystemIdentification3Choice">
		<xs:sequence>
			<xs:element name="Prtry" type="Max35Text"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ContactDetails2">
		<xs:sequence>
			<xs:element name="NmPrfx" type="NamePrefix1Code" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Nm" type="Max140Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="PhneNb" type="PhoneNumber" minOccurs="0" maxOccurs="1"/>
			<xs:element name="MobNb" type="PhoneNumber" minOccurs="0" maxOccurs="1"/>
			<xs:element name="FaxNb" type="PhoneNumber" minOccurs="0" maxOccurs="1"/>
			<xs:element name="EmailAdr" type="Max2048Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Othr" type="Max35Text" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="CountryCode">
		<xs:restriction base="xs:string">
			<xs:pattern value="[A-Z]{2,2}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="S2SCTCreditorReferenceInformation2">
		<xs:sequence>
			<xs:element name="Tp" type="CreditorReferenceType2"/>
			<xs:element name="Ref" type="Max35TextCW"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CreditorReferenceType1Choice">
		<xs:choice>
			<xs:element name="Cd" type="S2SCTDocumentType3Code"/>
			<xs:element name="Prtry" type="Max35TextCW"/>
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="CreditorReferenceType2">
		<xs:sequence>
			<xs:element name="CdOrPrtry" type="CreditorReferenceType1Choice"/>
			<xs:element name="Issr" type="Max35TextCW" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="DateAndPlaceOfBirth1">
		<xs:sequence>
			<xs:element name="BirthDt" type="ISODate"/>
			<xs:element name="PrvcOfBirth" type="Max35Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="CityOfBirth" type="Max35Text"/>
			<xs:element name="CtryOfBirth" type="CountryCode"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Document">
		<xs:sequence>
			<xs:element name="ReqToModfyPmt" type="S2SCTRequestToModifyPaymentV05"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="S2SCTDocumentType3Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="SCOR"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="S2SCTExternalAgentInstruction1Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="INQR"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ExternalCategoryPurpose1CodeCW">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="4"/>
			<xs:whiteSpace value="collapse"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ExternalOrganisationIdentification1Code">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="4"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ExternalPersonIdentification1Code">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="4"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ExternalPurpose1CodeCW">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="4"/>
			<xs:whiteSpace value="collapse"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="S2SCTExternalServiceLevel1Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="SEPA"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="S2SCTFinancialInstitutionIdentification8">
		<xs:sequence>
			<xs:element name="BICFI" type="BICFIIdentifier"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="GenericOrganisationIdentification1">
		<xs:sequence>
			<xs:element name="Id" type="Max35Text"/>
			<xs:element name="SchmeNm" type="OrganisationIdentificationSchemeName1Choice" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Issr" type="Max35Text" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="GenericPersonIdentification1">
		<xs:sequence>
			<xs:element name="Id" type="Max35Text"/>
			<xs:element name="SchmeNm" type="PersonIdentificationSchemeName1Choice" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Issr" type="Max35Text" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="IBAN2007Identifier">
		<xs:restriction base="xs:string">
			<xs:pattern value="[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ISODate">
		<xs:restriction base="xs:date">
			<xs:pattern value="[0-9]{4,4}\-[0-9]{2,2}\-[0-9]{2,2}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ISODateTime">
		<xs:restriction base="xs:dateTime">
			<xs:pattern value="[0-9]{4,4}\-[0-9]{2,2}\-[0-9]{2,2}[T][0-9]{2,2}:[0-9]{2,2}:[0-9]{2,2}[\S]*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="InstructionForAssignee1">
		<xs:sequence>
			<xs:element name="Cd" type="S2SCTExternalAgentInstruction1Code"/>
			<xs:element name="InstrInf" type="Max140Text"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTLocalInstrument2Choice">
		<xs:choice>
			<xs:element name="Cd" type="Max35TextCW"/>
			<xs:element name="Prtry" type="Max35Text"/>
		</xs:choice>
	</xs:complexType>
	<xs:simpleType name="Max140Text">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="140"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Max140TextCW">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="140"/>
			<xs:whiteSpace value="collapse"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Max16Text">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="16"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Max2048Text">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="2048"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="S2SCTId7">
		<xs:restriction base="xs:string">
			<xs:pattern value="([A-Za-z0-9]|[+|\?|/|\-|:|\(|\)|\.|,|']){1,35}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="S2SCTOrgnlMsgNmId">
		<xs:restriction base="xs:string">
			<xs:pattern value="pacs\.008[A-Za-z0-9\.]{0,27}"/>
			<xs:pattern value="PACS\.008[A-Za-z0-9\.]{0,27}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Max34Text">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="34"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Max35Text">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="35"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Max35TextCW">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="35"/>
			<xs:whiteSpace value="collapse"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Max4Text">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="4"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Max70Text">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="70"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Max70TextCW">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="70"/>
			<xs:whiteSpace value="collapse"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="NamePrefix1Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="DOCT"/>
			<xs:enumeration value="MIST"/>
			<xs:enumeration value="MISS"/>
			<xs:enumeration value="MADM"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="OrganisationIdentification8">
		<xs:sequence>
			<xs:element name="AnyBIC" type="AnyBICIdentifier" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Othr" type="GenericOrganisationIdentification1" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTOrganisationIdentification8">
		<xs:sequence>
			<xs:element name="AnyBIC" type="AnyBICIdentifier" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Othr" type="GenericOrganisationIdentification1" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="OrganisationIdentificationSchemeName1Choice">
		<xs:choice>
			<xs:element name="Cd" type="ExternalOrganisationIdentification1Code"/>
			<xs:element name="Prtry" type="Max35Text"/>
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="S2SCTOriginalTransactionReference27">
		<xs:sequence>
			<xs:element name="IntrBkSttlmAmt" type="ActiveOrHistoricCurrencyAndAmount" minOccurs="0"/>
			<xs:element name="Amt" type="AmountType4Choice" minOccurs="0"/>
			<xs:element name="IntrBkSttlmDt" type="ISODate" minOccurs="0"/>
			<xs:element name="SttlmInf" type="S2SCTSettlementInstruction4" minOccurs="0"/>
			<xs:element name="PmtTpInf" type="S2SCTPaymentTypeInformation25" minOccurs="0"/>
			<xs:element name="PmtMtd" type="PaymentMethod4Code" minOccurs="0"/>
			<xs:element name="RmtInf" type="S2SCTRemittanceInformation15" minOccurs="0"/>
			<xs:element name="UltmtDbtr" type="S2SCTParty35ChoiceV4" minOccurs="0"/>
			<xs:element name="Dbtr" type="S2SCTParty35ChoiceV4" minOccurs="0"/>
			<xs:element name="DbtrAcct" type="S2SCTCashAccount24" minOccurs="0"/>
			<xs:element name="DbtrAgt" type="S2SCTBranchAndFinancialInstitutionIdentification5"/>
			<xs:element name="DbtrAgtAcct" type="S2SCTCashAccount24" minOccurs="0"/>
			<xs:element name="CdtrAgt" type="S2SCTBranchAndFinancialInstitutionIdentification5"/>
			<xs:element name="CdtrAgtAcct" type="S2SCTCashAccount24" minOccurs="0"/>
			<xs:element name="Cdtr" type="S2SCTParty35ChoiceV4" minOccurs="0"/>
			<xs:element name="CdtrAcct" type="S2SCTCashAccount24" minOccurs="0"/>
			<xs:element name="UltmtCdtr" type="S2SCTParty35ChoiceV4" minOccurs="0"/>
			<xs:element name="Purp" type="S2SCTPurpose2Choice" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="PaymentMethod4Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="CHK"/>
			<xs:enumeration value="TRF"/>
			<xs:enumeration value="DD"/>
			<xs:enumeration value="TRA"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="AmountType4Choice">
		<xs:choice>
			<xs:element name="InstdAmt" type="ActiveOrHistoricCurrencyAndAmount"/>
			<xs:element name="EqvtAmt" type="EquivalentAmount2"/>
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="EquivalentAmount2">
		<xs:sequence>
			<xs:element name="Amt" type="ActiveOrHistoricCurrencyAndAmount"/>
			<xs:element name="CcyOfTrf" type="ActiveOrHistoricCurrencyCode"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="ActiveOrHistoricCurrencyAndAmount_SimpleType">
		<xs:restriction base="xs:decimal">
			<xs:fractionDigits value="5"/>
			<xs:totalDigits value="18"/>
			<xs:minInclusive value="0"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="ActiveOrHistoricCurrencyAndAmount">
		<xs:simpleContent>
			<xs:extension base="ActiveOrHistoricCurrencyAndAmount_SimpleType">
				<xs:attribute name="Ccy" type="ActiveOrHistoricCurrencyCode" use="required"/>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	<xs:simpleType name="ActiveOrHistoricCurrencyCode">
		<xs:restriction base="xs:string">
			<xs:pattern value="[A-Z]{3,3}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="Party34Choice">
		<xs:choice>
			<xs:element name="OrgId" type="OrganisationIdentification8"/>
			<xs:element name="PrvtId" type="PersonIdentification13"/>
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="S2SCTParty34Choice">
		<xs:choice>
			<xs:element name="OrgId" type="S2SCTOrganisationIdentification8"/>
			<xs:element name="PrvtId" type="S2SCTPersonIdentification13"/>
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="S2SCTParty35ChoiceV2">
		<xs:sequence>
			<xs:element name="Agt" type="S2SCTBranchAndFinancialInstitutionIdentification5"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTParty35ChoiceV4">
		<xs:sequence>
			<xs:element name="Pty" type="S2SCTPartyIdentification125"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PartyIdentification125">
		<xs:sequence>
			<xs:element name="Nm" type="Max140Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="PstlAdr" type="PostalAddress6" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Id" type="Party34Choice" minOccurs="0" maxOccurs="1"/>
			<xs:element name="CtryOfRes" type="CountryCode" minOccurs="0" maxOccurs="1"/>
			<xs:element name="CtctDtls" type="ContactDetails2" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTPartyIdentification125">
		<xs:sequence>
			<xs:element name="Nm" type="Max70TextCW" minOccurs="0"/>
			<xs:element name="PstlAdr" type="S2SCTPostalAddress6" minOccurs="0"/>
			<xs:element name="Id" type="S2SCTParty34Choice" minOccurs="0"/>
			<xs:element name="CtryOfRes" type="CountryCode" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTPaymentTypeInformation25">
		<xs:sequence>
			<xs:element name="SvcLvl" type="S2SCTServiceLevel8Choice"/>
			<xs:element name="LclInstrm" type="S2SCTLocalInstrument2Choice" minOccurs="0"/>
			<xs:element name="CtgyPurp" type="CategoryPurpose1Choice" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PersonIdentification13">
		<xs:sequence>
			<xs:element name="DtAndPlcOfBirth" type="DateAndPlaceOfBirth1" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Othr" type="GenericPersonIdentification1" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTPersonIdentification13">
		<xs:sequence>
			<xs:element name="DtAndPlcOfBirth" type="DateAndPlaceOfBirth1" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Othr" type="GenericPersonIdentification1" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PersonIdentificationSchemeName1Choice">
		<xs:choice>
			<xs:element name="Cd" type="ExternalPersonIdentification1Code"/>
			<xs:element name="Prtry" type="Max35Text"/>
		</xs:choice>
	</xs:complexType>
	<xs:simpleType name="PhoneNumber">
		<xs:restriction base="xs:string">
			<xs:pattern value="\+[0-9]{1,3}-[0-9()+\-]{1,30}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="PostalAddress6">
		<xs:sequence>
			<xs:element name="AdrTp" type="AddressType2Code" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Dept" type="Max70Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="SubDept" type="Max70Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="StrtNm" type="Max70Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="BldgNb" type="Max16Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="PstCd" type="Max16Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="TwnNm" type="Max35Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="CtrySubDvsn" type="Max35Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Ctry" type="CountryCode" minOccurs="0" maxOccurs="1"/>
			<xs:element name="AdrLine" type="Max70Text" minOccurs="0" maxOccurs="7"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTPostalAddress6">
		<xs:sequence>
			<xs:element name="Ctry" type="CountryCode" minOccurs="0" maxOccurs="1"/>
			<xs:element name="AdrLine" type="Max70TextCW" minOccurs="0" maxOccurs="2"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTPurpose2Choice">
		<xs:sequence>
			<xs:element name="Cd" type="ExternalPurpose1CodeCW"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTRemittanceInformation15">
		<xs:choice>
			<xs:element name="Ustrd" type="Max140TextCW"/>
			<xs:element name="Strd" type="S2SCTStructuredRemittanceInformation15"/>
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="S2SCTRequestToModifyPaymentV05">
		<xs:sequence>
			<xs:element name="Assgnmt" type="CaseAssignment4"/>
			<xs:element name="Case" type="Case4"/>
			<xs:element name="Undrlyg" type="S2SCTUnderlyingTransaction4Choice"/>
			<xs:element name="Mod" type="S2SCTRequestedModification7"/>
			<xs:element name="InstrForAssgne" type="InstructionForAssignee1" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTRequestedModification7">
		<xs:sequence>
			<xs:element name="IntrBkSttlmDt" type="ISODate"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTServiceLevel8Choice">
		<xs:sequence>
			<xs:element name="Cd" type="S2SCTExternalServiceLevel1Code"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTSettlementInstruction4">
		<xs:sequence>
			<xs:element name="SttlmMtd" type="S2SCTSettlementMethod1Code"/>
			<xs:element name="ClrSys" type="S2SCTClearingSystemIdentification3Choice"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="S2SCTSettlementMethod1Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="CLRG"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="S2SCTStructuredRemittanceInformation15">
		<xs:sequence>
			<xs:element name="RfrdDocInf" type="S2SCTReferredDocumentInformation7" minOccurs="0" maxOccurs="1"/>
			<xs:element name="RfrdDocAmt" type="S2SCTRemittanceAmount2" minOccurs="0" maxOccurs="1"/>
			<xs:element name="CdtrRefInf" type="S2SCTCreditorReferenceInformation2" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Invcr" type="S2SCTPartyIdentification125" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Invcee" type="S2SCTPartyIdentification125" minOccurs="0" maxOccurs="1"/>
			<xs:element name="AddtlRmtInf" type="Max140TextCW" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTRemittanceAmount2">
		<xs:sequence>
			<xs:element name="DuePyblAmt" type="ActiveOrHistoricCurrencyAndAmount" minOccurs="0" maxOccurs="1"/>
			<xs:element name="DscntApldAmt" type="S2SCTDiscountAmountAndType1" minOccurs="0"/>
			<xs:element name="CdtNoteAmt" type="ActiveOrHistoricCurrencyAndAmount" minOccurs="0" maxOccurs="1"/>
			<xs:element name="TaxAmt" type="S2SCTTaxAmountAndType1" minOccurs="0"/>
			<xs:element name="AdjstmntAmtAndRsn" type="DocumentAdjustment1" minOccurs="0"/>
			<xs:element name="RmtdAmt" type="ActiveOrHistoricCurrencyAndAmount" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTReferredDocumentInformation7">
		<xs:sequence>
			<xs:element name="Tp" type="S2SCTReferredDocumentType4" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Nb" type="Max35Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="RltdDt" type="ISODate" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="DocumentLineInformation1">
		<xs:sequence>
			<xs:element name="Id" type="DocumentLineIdentification1" minOccurs="1" maxOccurs="unbounded"/>
			<xs:element name="Desc" type="Max2048Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Amt" type="RemittanceAmount3" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="RemittanceAmount3">
		<xs:sequence>
			<xs:element name="DuePyblAmt" type="ActiveOrHistoricCurrencyAndAmount" minOccurs="0" maxOccurs="1"/>
			<xs:element name="DscntApldAmt" type="DiscountAmountAndType1" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="CdtNoteAmt" type="ActiveOrHistoricCurrencyAndAmount" minOccurs="0" maxOccurs="1"/>
			<xs:element name="TaxAmt" type="TaxAmountAndType1" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="AdjstmntAmtAndRsn" type="DocumentAdjustment1" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="RmtdAmt" type="ActiveOrHistoricCurrencyAndAmount" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="DocumentAdjustment1">
		<xs:sequence>
			<xs:element name="Amt" type="ActiveOrHistoricCurrencyAndAmount"/>
			<xs:element name="CdtDbtInd" type="CreditDebitCode" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Rsn" type="Max4Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="AddtlInf" type="Max140Text" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="CreditDebitCode">
		<xs:restriction base="xs:string">
			<xs:enumeration value="CRDT"/>
			<xs:enumeration value="DBIT"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="TaxAmountAndType1">
		<xs:sequence>
			<xs:element name="Tp" type="TaxAmountType1Choice" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Amt" type="ActiveOrHistoricCurrencyAndAmount"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTTaxAmountAndType1">
		<xs:sequence>
			<xs:element name="Amt" type="ActiveOrHistoricCurrencyAndAmount"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TaxAmountType1Choice">
		<xs:choice>
			<xs:element name="Cd" type="ExternalTaxAmountType1Code"/>
			<xs:element name="Prtry" type="Max35Text"/>
		</xs:choice>
	</xs:complexType>
	<xs:simpleType name="ExternalTaxAmountType1Code">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="4"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="DiscountAmountAndType1">
		<xs:sequence>
			<xs:element name="Tp" type="DiscountAmountType1Choice" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Amt" type="ActiveOrHistoricCurrencyAndAmount"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTDiscountAmountAndType1">
		<xs:sequence>
			<xs:element name="Amt" type="ActiveOrHistoricCurrencyAndAmount"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="DiscountAmountType1Choice">
		<xs:choice>
			<xs:element name="Cd" type="ExternalDiscountAmountType1Code"/>
			<xs:element name="Prtry" type="Max35Text"/>
		</xs:choice>
	</xs:complexType>
	<xs:simpleType name="ExternalDiscountAmountType1Code">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="4"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="DocumentLineIdentification1">
		<xs:sequence>
			<xs:element name="Tp" type="DocumentLineType1" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Nb" type="Max35Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="RltdDt" type="ISODate" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="DocumentLineType1">
		<xs:sequence>
			<xs:element name="CdOrPrtry" type="DocumentLineType1Choice"/>
			<xs:element name="Issr" type="Max35Text" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="DocumentLineType1Choice">
		<xs:choice>
			<xs:element name="Cd" type="ExternalDocumentLineType1Code"/>
			<xs:element name="Prtry" type="Max35Text"/>
		</xs:choice>
	</xs:complexType>
	<xs:simpleType name="ExternalDocumentLineType1Code">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="4"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="S2SCTReferredDocumentType4">
		<xs:sequence>
			<xs:element name="CdOrPrtry" type="ReferredDocumentType3Choice"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ReferredDocumentType3Choice">
		<xs:choice>
			<xs:element name="Cd" type="DocumentType6Code"/>
			<xs:element name="Prtry" type="Max35Text"/>
		</xs:choice>
	</xs:complexType>
	<xs:simpleType name="DocumentType6Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="MSIN"/>
			<xs:enumeration value="CNFA"/>
			<xs:enumeration value="DNFA"/>
			<xs:enumeration value="CINV"/>
			<xs:enumeration value="CREN"/>
			<xs:enumeration value="DEBN"/>
			<xs:enumeration value="HIRI"/>
			<xs:enumeration value="SBIN"/>
			<xs:enumeration value="CMCN"/>
			<xs:enumeration value="SOAC"/>
			<xs:enumeration value="DISP"/>
			<xs:enumeration value="BOLD"/>
			<xs:enumeration value="VCHR"/>
			<xs:enumeration value="AROI"/>
			<xs:enumeration value="TSUT"/>
			<xs:enumeration value="PUOR"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="S2SCTUnderlyingGroupInformation1">
		<xs:sequence>
			<xs:element name="OrgnlMsgId" type="S2SCTId7"/>
			<xs:element name="OrgnlMsgNmId" type="S2SCTOrgnlMsgNmId"/>
			<xs:element name="OrgnlCreDtTm" type="ISODateTime" minOccurs="0" maxOccurs="1"/>
			<xs:element name="OrgnlMsgDlvryChanl" type="Max35Text" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTUnderlyingPaymentTransaction3">
		<xs:sequence>
			<xs:element name="OrgnlGrpInf" type="S2SCTUnderlyingGroupInformation1"/>
			<xs:element name="OrgnlInstrId" type="S2SCTId7" minOccurs="0"/>
			<xs:element name="OrgnlEndToEndId" type="Max35TextCW"/>
			<xs:element name="OrgnlTxId" type="S2SCTId7"/>
			<xs:element name="OrgnlIntrBkSttlmAmt">
				<xs:complexType>
					<xs:simpleContent>
						<xs:restriction base="S2SCTCurrencyAndAmount">
							<xs:maxInclusive value="999999999.99"/>
							<xs:minInclusive value="0.01"/>
						</xs:restriction>
					</xs:simpleContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="OrgnlIntrBkSttlmDt" type="ISODate"/>
			<xs:element name="OrgnlTxRef" type="S2SCTOriginalTransactionReference27"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTUnderlyingTransaction4Choice">
		<xs:sequence>
			<xs:element name="IntrBk" type="S2SCTUnderlyingPaymentTransaction3"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="YesNoIndicator">
		<xs:restriction base="xs:boolean"/>
	</xs:simpleType>
</xs:schema>
