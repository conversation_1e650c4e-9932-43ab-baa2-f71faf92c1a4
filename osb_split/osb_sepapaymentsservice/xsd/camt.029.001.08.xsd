<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- STEP2 SCT Schema, camt.029.001.08, January 22th 2019, Release November 2019 -->
<xs:schema xmlns="urn:iso:std:iso:20022:tech:xsd:camt.029.001.08" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="urn:iso:std:iso:20022:tech:xsd:camt.029.001.08" elementFormDefault="qualified">
	<xs:element name="Document" type="Document"/>
	<xs:complexType name="AccountIdentification4Choice">
		<xs:choice>
			<xs:element name="IBAN" type="IBAN2007Identifier"/>
			<xs:element name="Othr" type="GenericAccountIdentification1"/>
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="S2SCTAccountIdentification4Choice">
		<xs:sequence>
			<xs:element name="IBAN" type="IBAN2007Identifier"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="AccountSchemeName1Choice">
		<xs:choice>
			<xs:element name="Cd" type="ExternalAccountIdentification1Code"/>
			<xs:element name="Prtry" type="Max35Text"/>
		</xs:choice>
	</xs:complexType>
	<xs:simpleType name="S2SCTCurrencyAndAmount_SimpleType">
		<xs:restriction base="xs:decimal">
			<xs:minInclusive value="0"/>
			<xs:fractionDigits value="2"/>
			<xs:totalDigits value="18"/>
			<xs:pattern value="[0-9]{0,15}([\.]([0-9]{0,2})){0,1}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="S2SCTCurrencyAndAmount">
		<xs:simpleContent>
			<xs:extension base="S2SCTCurrencyAndAmount_SimpleType">
				<xs:attribute name="Ccy" type="S2SCTCurrencyCode" use="required"/>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	<xs:simpleType name="S2SCTCurrencyCode">
		<xs:restriction base="xs:string">
			<xs:enumeration value="EUR"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ActiveOrHistoricCurrencyAndAmount_SimpleType">
		<xs:restriction base="xs:decimal">
			<xs:fractionDigits value="5"/>
			<xs:totalDigits value="18"/>
			<xs:minInclusive value="0"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="ActiveOrHistoricCurrencyAndAmount">
		<xs:simpleContent>
			<xs:extension base="ActiveOrHistoricCurrencyAndAmount_SimpleType">
				<xs:attribute name="Ccy" type="ActiveOrHistoricCurrencyCode" use="required"/>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	<xs:simpleType name="ActiveOrHistoricCurrencyCode">
		<xs:restriction base="xs:string">
			<xs:pattern value="[A-Z]{3,3}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="AddressType2Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ADDR"/>
			<xs:enumeration value="PBOX"/>
			<xs:enumeration value="HOME"/>
			<xs:enumeration value="BIZZ"/>
			<xs:enumeration value="MLTO"/>
			<xs:enumeration value="DLVY"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="AmountType4Choice">
		<xs:choice>
			<xs:element name="InstdAmt" type="ActiveOrHistoricCurrencyAndAmount"/>
			<xs:element name="EqvtAmt" type="EquivalentAmount2"/>
		</xs:choice>
	</xs:complexType>
	<xs:simpleType name="AnyBICIdentifier">
		<xs:restriction base="xs:string">
			<xs:pattern value="[A-Z]{6,6}[A-Z2-9][A-NP-Z0-9]([A-Z0-9]{3,3}){0,1}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="BICFIIdentifier">
		<xs:restriction base="xs:string">
			<xs:pattern value="[A-Z]{6,6}[A-Z2-9][A-NP-Z0-9]([A-Z0-9]{3,3}){0,1}"/>
		</xs:restriction>
	</xs:simpleType>
	<!--
	<xs:complexType name="BranchAndFinancialInstitutionIdentification5">
		<xs:sequence>
			<xs:element name="FinInstnId" type="FinancialInstitutionIdentification8"/>
			<xs:element name="BrnchId" type="BranchData2" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
-->
	<xs:complexType name="S2SCTBranchAndFinancialInstitutionIdentification5">
		<xs:sequence>
			<xs:element name="FinInstnId" type="S2SCTFinancialInstitutionIdentification8"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="BranchData2">
		<xs:sequence>
			<xs:element name="Id" type="Max35Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Nm" type="Max140Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="PstlAdr" type="PostalAddress6" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<!--
	<xs:complexType name="Case4">
		<xs:sequence>
			<xs:element name="Id" type="Max35Text"/>
			<xs:element name="Cretr" type="Party35Choice"/>
			<xs:element name="ReopCaseIndctn" type="YesNoIndicator" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
-->
	<xs:complexType name="S2SCTCase4">
		<xs:sequence>
			<xs:element name="Id" type="S2SCTId7"/>
			<xs:element name="Cretr" type="S2SCTParty35ChoiceV2"/>
			<xs:element name="ReopCaseIndctn" type="YesNoIndicator" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTCaseAssignment4">
		<xs:sequence>
			<xs:element name="Id" type="S2SCTId7"/>
			<xs:element name="Assgnr" type="S2SCTParty35ChoiceV2"/>
			<xs:element name="Assgne" type="S2SCTParty35ChoiceV2"/>
			<xs:element name="CreDtTm" type="ISODateTime"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CashAccount24">
		<xs:sequence>
			<xs:element name="Id" type="AccountIdentification4Choice"/>
			<xs:element name="Tp" type="CashAccountType2Choice" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Ccy" type="ActiveOrHistoricCurrencyCode" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Nm" type="Max70Text" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTCashAccount24">
		<xs:sequence>
			<xs:element name="Id" type="S2SCTAccountIdentification4Choice"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CashAccountType2Choice">
		<xs:choice>
			<xs:element name="Cd" type="ExternalCashAccountType1Code"/>
			<xs:element name="Prtry" type="Max35Text"/>
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="CategoryPurpose1Choice">
		<xs:choice>
			<xs:element name="Cd" type="ExternalCategoryPurpose1Code"/>
			<xs:element name="Prtry" type="Max35Text"/>
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="S2SCTCharges2">
		<xs:sequence>
			<xs:element name="Amt">
				<xs:complexType>
					<xs:simpleContent>
						<xs:restriction base="S2SCTCurrencyAndAmount">
							<xs:maxInclusive value="*********.99"/>
							<xs:minInclusive value="0.01"/>
						</xs:restriction>
					</xs:simpleContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="Agt" type="S2SCTBranchAndFinancialInstitutionIdentification5"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTClaimNonReceipt1">
		<xs:sequence>
			<xs:element name="DtPrcd" type="ISODate"/>
			<xs:element name="OrgnlNxtAgt" type="S2SCTBranchAndFinancialInstitutionIdentification5"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTClaimNonReceipt1Choice">
		<xs:choice>
			<xs:element name="Accptd" type="S2SCTClaimNonReceipt1"/>
			<xs:element name="Rjctd" type="S2SCTClaimNonReceiptRejectReason1Choice"/>
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="S2SCTClaimNonReceiptRejectReason1Choice">
		<xs:sequence>
			<xs:element name="Cd" type="ExternalClaimNonReceiptRejection1Code"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ClearingSystemIdentification2Choice">
		<xs:choice>
			<xs:element name="Cd" type="ExternalClearingSystemIdentification1Code"/>
			<xs:element name="Prtry" type="Max35Text"/>
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="S2SCTClearingSystemIdentification3Choice">
		<xs:sequence>
			<xs:element name="Prtry" type="Max35Text"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ClearingSystemMemberIdentification2">
		<xs:sequence>
			<xs:element name="ClrSysId" type="ClearingSystemIdentification2Choice" minOccurs="0"/>
			<xs:element name="MmbId" type="Max35Text"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ContactDetails2">
		<xs:sequence>
			<xs:element name="NmPrfx" type="NamePrefix1Code" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Nm" type="Max140Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="PhneNb" type="PhoneNumber" minOccurs="0" maxOccurs="1"/>
			<xs:element name="MobNb" type="PhoneNumber" minOccurs="0" maxOccurs="1"/>
			<xs:element name="FaxNb" type="PhoneNumber" minOccurs="0" maxOccurs="1"/>
			<xs:element name="EmailAdr" type="Max2048Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Othr" type="Max35Text" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="CountryCode">
		<xs:restriction base="xs:string">
			<xs:pattern value="[A-Z]{2,2}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="S2SCTCreditorReferenceInformation2">
		<xs:sequence>
			<xs:element name="Tp" type="S2SCTCreditorReferenceType2"/>
			<xs:element name="Ref" type="Max35TextCW"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTCreditorReferenceType1Choice">
		<xs:choice>
			<xs:element name="Cd" type="S2SCTDocumentType3Code"/>
			<xs:element name="Prtry" type="Max35TextCW"/>
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="S2SCTCreditorReferenceType2">
		<xs:sequence>
			<xs:element name="CdOrPrtry" type="S2SCTCreditorReferenceType1Choice"/>
			<xs:element name="Issr" type="Max35TextCW" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="DateAndDateTime2Choice">
		<xs:choice>
			<xs:element name="Dt" type="ISODate"/>
			<xs:element name="DtTm" type="ISODateTime"/>
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="DateAndPlaceOfBirth1">
		<xs:sequence>
			<xs:element name="BirthDt" type="ISODate"/>
			<xs:element name="PrvcOfBirth" type="Max35Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="CityOfBirth" type="Max35Text"/>
			<xs:element name="CtryOfBirth" type="CountryCode"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="DecimalNumber">
		<xs:restriction base="xs:decimal">
			<xs:fractionDigits value="17"/>
			<xs:totalDigits value="18"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="DiscountAmountAndType1">
		<xs:sequence>
			<xs:element name="Tp" type="DiscountAmountType1Choice" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Amt" type="ActiveOrHistoricCurrencyAndAmount"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTDiscountAmountAndType1">
		<xs:sequence>
			<xs:element name="Amt" type="ActiveOrHistoricCurrencyAndAmount"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="DiscountAmountType1Choice">
		<xs:choice>
			<xs:element name="Cd" type="ExternalDiscountAmountType1Code"/>
			<xs:element name="Prtry" type="Max35Text"/>
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="Document">
		<xs:sequence>
			<xs:element name="RsltnOfInvstgtn" type="S2SCTResolutionOfInvestigationV08"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="DocumentType3Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="RADM"/>
			<xs:enumeration value="RPIN"/>
			<xs:enumeration value="FXDR"/>
			<xs:enumeration value="DISP"/>
			<xs:enumeration value="PUOR"/>
			<xs:enumeration value="SCOR"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="S2SCTDocumentType3Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="SCOR"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="DocumentType6Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="MSIN"/>
			<xs:enumeration value="CNFA"/>
			<xs:enumeration value="DNFA"/>
			<xs:enumeration value="CINV"/>
			<xs:enumeration value="CREN"/>
			<xs:enumeration value="DEBN"/>
			<xs:enumeration value="HIRI"/>
			<xs:enumeration value="SBIN"/>
			<xs:enumeration value="CMCN"/>
			<xs:enumeration value="SOAC"/>
			<xs:enumeration value="DISP"/>
			<xs:enumeration value="BOLD"/>
			<xs:enumeration value="VCHR"/>
			<xs:enumeration value="AROI"/>
			<xs:enumeration value="TSUT"/>
			<xs:enumeration value="PUOR"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="EquivalentAmount2">
		<xs:sequence>
			<xs:element name="Amt" type="ActiveOrHistoricCurrencyAndAmount"/>
			<xs:element name="CcyOfTrf" type="ActiveOrHistoricCurrencyCode"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="Exact2NumericText">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9]{2}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ExternalAccountIdentification1Code">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="4"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ExternalCashAccountType1Code">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="4"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ExternalCashClearingSystem1Code">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="3"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ExternalCategoryPurpose1Code">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="4"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ExternalChargeType1Code">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="4"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ExternalClaimNonReceiptRejection1Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="NOOR"/>
			<xs:enumeration value="RNPR"/>
			<xs:enumeration value="ARJT"/>
			<xs:enumeration value="ARDT"/>
			<xs:enumeration value="RR04"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ExternalClearingSystemIdentification1Code">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="5"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ExternalDiscountAmountType1Code">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="4"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ExternalDocumentLineType1Code">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="4"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ExternalFinancialInstitutionIdentification1Code">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="4"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ExternalGarnishmentType1Code">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="4"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ExternalInvestigationExecutionConfirmation1Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ACNR"/>
			<xs:enumeration value="RJNR"/>
			<xs:enumeration value="ACVA"/>
			<xs:enumeration value="RJVA"/>
			<xs:enumeration value="CVAA"/>
			<xs:enumeration value="MODI"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ExternalLocalInstrument1Code">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="35"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="S2SCTExternalLocalInstrument1Code">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="35"/>
			<xs:whiteSpace value="collapse"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ExternalOrganisationIdentification1Code">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="4"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ExternalPaymentCancellationRejection1Code">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="4"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ExternalPaymentCompensationReason1Code">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="4"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="S2SCTExternalPaymentCompensationReason1Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="VADA"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ExternalPaymentModificationRejection1Code">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="4"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ExternalPersonIdentification1Code">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="4"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ExternalPurpose1Code">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="4"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ExternalPurpose1CodeCW">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="4"/>
			<xs:whiteSpace value="collapse"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ExternalServiceLevel1Code">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="4"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="S2SCTExternalServiceLevel1Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="SEPA"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ExternalTaxAmountType1Code">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="4"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="FinancialIdentificationSchemeName1Choice">
		<xs:choice>
			<xs:element name="Cd" type="ExternalFinancialInstitutionIdentification1Code"/>
			<xs:element name="Prtry" type="Max35Text"/>
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="FinancialInstitutionIdentification8">
		<xs:sequence>
			<xs:element name="BICFI" type="BICFIIdentifier" minOccurs="0" maxOccurs="1"/>
			<xs:element name="ClrSysMmbId" type="ClearingSystemMemberIdentification2" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Nm" type="Max140Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="PstlAdr" type="PostalAddress6" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Othr" type="GenericFinancialIdentification1" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTFinancialInstitutionIdentification8">
		<xs:sequence>
			<xs:element name="BICFI" type="BICFIIdentifier"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Frequency36Choice">
		<xs:choice>
			<xs:element name="Tp" type="Frequency6Code"/>
			<xs:element name="Prd" type="FrequencyPeriod1"/>
			<xs:element name="PtInTm" type="FrequencyAndMoment1"/>
		</xs:choice>
	</xs:complexType>
	<xs:simpleType name="Frequency6Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="YEAR"/>
			<xs:enumeration value="MNTH"/>
			<xs:enumeration value="QURT"/>
			<xs:enumeration value="MIAN"/>
			<xs:enumeration value="WEEK"/>
			<xs:enumeration value="DAIL"/>
			<xs:enumeration value="ADHO"/>
			<xs:enumeration value="INDA"/>
			<xs:enumeration value="FRTN"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="FrequencyAndMoment1">
		<xs:sequence>
			<xs:element name="Tp" type="Frequency6Code"/>
			<xs:element name="PtInTm" type="Exact2NumericText"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="FrequencyPeriod1">
		<xs:sequence>
			<xs:element name="Tp" type="Frequency6Code"/>
			<xs:element name="CntPerPrd" type="DecimalNumber"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="GenericAccountIdentification1">
		<xs:sequence>
			<xs:element name="Id" type="Max34Text"/>
			<xs:element name="SchmeNm" type="AccountSchemeName1Choice" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Issr" type="Max35Text" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="GenericFinancialIdentification1">
		<xs:sequence>
			<xs:element name="Id" type="Max35Text"/>
			<xs:element name="SchmeNm" type="FinancialIdentificationSchemeName1Choice" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Issr" type="Max35Text" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="GenericIdentification3">
		<xs:sequence>
			<xs:element name="Id" type="Max35Text"/>
			<xs:element name="Issr" type="Max35Text" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="GenericOrganisationIdentification1">
		<xs:sequence>
			<xs:element name="Id" type="Max35Text"/>
			<xs:element name="SchmeNm" type="OrganisationIdentificationSchemeName1Choice" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Issr" type="Max35Text" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="GenericPersonIdentification1">
		<xs:sequence>
			<xs:element name="Id" type="Max35Text"/>
			<xs:element name="SchmeNm" type="PersonIdentificationSchemeName1Choice" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Issr" type="Max35Text" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="GroupCancellationStatus1Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="PACR"/>
			<xs:enumeration value="RJCR"/>
			<xs:enumeration value="ACCR"/>
			<xs:enumeration value="PDCR"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="IBAN2007Identifier">
		<xs:restriction base="xs:string">
			<xs:pattern value="[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ISODate">
		<xs:restriction base="xs:date">
			<xs:pattern value="[0-9]{4,4}\-[0-9]{2,2}\-[0-9]{2,2}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ISODateTime">
		<xs:restriction base="xs:dateTime">
			<xs:pattern value="[0-9]{4,4}\-[0-9]{2,2}\-[0-9]{2,2}[T][0-9]{2,2}:[0-9]{2,2}:[0-9]{2,2}[\S]*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="S2SCTInvestigationStatus4Choice">
		<xs:sequence>
			<xs:element name="Conf" type="ExternalInvestigationExecutionConfirmation1Code"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="LocalInstrument2Choice">
		<xs:choice>
			<xs:element name="Cd" type="ExternalLocalInstrument1Code"/>
			<xs:element name="Prtry" type="Max35Text"/>
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="S2SCTLocalInstrument2Choice">
		<xs:choice>
			<xs:element name="Cd" type="S2SCTExternalLocalInstrument1Code"/>
			<xs:element name="Prtry" type="Max35Text"/>
		</xs:choice>
	</xs:complexType>
	<xs:simpleType name="Max1025Text">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="1025"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Max105Text">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="105"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Max140Text">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="140"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Max15NumericText">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9]{1,15}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Max140TextCW">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="140"/>
			<xs:whiteSpace value="collapse"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Max16Text">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="16"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Max2048Text">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="2048"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Max34Text">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="34"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Max4Text">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="4"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Max35Text">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="35"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Max35TextCW">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="35"/>
			<xs:whiteSpace value="collapse"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="S2SCTOrgnlMsgNmId">
		<xs:restriction base="xs:string">
			<xs:pattern value="pacs\.008[A-Za-z0-9\.]{0,27}"/>
			<xs:pattern value="PACS\.008[A-Za-z0-9\.]{0,27}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="S2SCTId7">
		<xs:restriction base="xs:string">
			<xs:pattern value="([A-Za-z0-9]|[+|\?|/|\-|:|\(|\)|\.|,|']){1,35}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Max70Text">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="70"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Max70TextCW">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="70"/>
			<xs:whiteSpace value="collapse"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="NamePrefix1Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="DOCT"/>
			<xs:enumeration value="MIST"/>
			<xs:enumeration value="MISS"/>
			<xs:enumeration value="MADM"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="OrganisationIdentification8">
		<xs:sequence>
			<xs:element name="AnyBIC" type="AnyBICIdentifier" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Othr" type="GenericOrganisationIdentification1" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTOrganisationIdentification8">
		<xs:sequence>
			<xs:element name="AnyBIC" type="AnyBICIdentifier" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Othr" type="GenericOrganisationIdentification1" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTOrganisationIdentification81">
		<xs:sequence>
			<xs:element name="AnyBIC" type="AnyBICIdentifier" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="OrganisationIdentificationSchemeName1Choice">
		<xs:choice>
			<xs:element name="Cd" type="ExternalOrganisationIdentification1Code"/>
			<xs:element name="Prtry" type="Max35Text"/>
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="S2SCTOriginalGroupInformation29">
		<xs:sequence>
			<xs:element name="OrgnlMsgId" type="Max35Text"/>
			<xs:element name="OrgnlMsgNmId" type="S2SCTOrgnlMsgNmId"/>
			<xs:element name="OrgnlCreDtTm" type="ISODateTime" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTOriginalGroupInformation291">
		<xs:sequence>
			<xs:element name="OrgnlMsgId" type="Max35Text"/>
			<xs:element name="OrgnlMsgNmId" type="S2SCTOrgnlMsgNmId"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTOriginalTransactionReference27">
		<xs:sequence>
			<xs:element name="IntrBkSttlmAmt" minOccurs="0">
				<xs:complexType>
					<xs:simpleContent>
						<xs:restriction base="S2SCTCurrencyAndAmount">
							<xs:maxInclusive value="*********.99"/>
							<xs:minInclusive value="0.01"/>
						</xs:restriction>
					</xs:simpleContent>
				</xs:complexType>
			</xs:element>
			<!-- <xs:element name="Amt" type="AmountType4Choice" minOccurs="0"/> -->
			<xs:element name="IntrBkSttlmDt" type="ISODate" minOccurs="0"/>
			<!-- <xs:element name="ReqdColltnDt" type="ISODate" minOccurs="0"/>
			<xs:element name="ReqdExctnDt" type="DateAndDateTime2Choice" minOccurs="0"/>
			<xs:element name="CdtrSchmeId" type="PartyIdentification125" minOccurs="0"/> -->
			<xs:element name="SttlmInf" type="S2SCTSettlementInstruction4" minOccurs="0"/>
			<xs:element name="PmtTpInf" type="S2SCTPaymentTypeInformation25" minOccurs="0"/>
			<!-- <xs:element name="PmtMtd" type="PaymentMethod4Code" minOccurs="0"/>
			<xs:element name="MndtRltdInf" type="MandateRelatedInformation12" minOccurs="0"/> -->
			<xs:element name="RmtInf" type="S2SCTRemittanceInformation15" minOccurs="0"/>
			<xs:element name="UltmtDbtr" type="S2SCTParty35ChoiceV3" minOccurs="0"/>
			<xs:element name="Dbtr" type="S2SCTParty35ChoiceV3" minOccurs="0"/>
			<xs:element name="DbtrAcct" type="S2SCTCashAccount24" minOccurs="0"/>
			<xs:element name="DbtrAgt" type="S2SCTBranchAndFinancialInstitutionIdentification5"/>
			<xs:element name="CdtrAgt" type="S2SCTBranchAndFinancialInstitutionIdentification5"/>
			<!-- <xs:element name="CdtrAgtAcct" type="CashAccount24" minOccurs="0"/> -->
			<xs:element name="Cdtr" type="S2SCTParty35ChoiceV3" minOccurs="0"/>
			<xs:element name="CdtrAcct" type="S2SCTCashAccount24" minOccurs="0"/>
			<xs:element name="UltmtCdtr" type="S2SCTParty35ChoiceV3" minOccurs="0"/>
			<!--<xs:element name="Purp" type="S2SCTPurpose2Choice" minOccurs="0"/> -->
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTOriginalTransactionReference271">
		<xs:sequence>
			<xs:element name="IntrBkSttlmAmt" minOccurs="0">
				<xs:complexType>
					<xs:simpleContent>
						<xs:restriction base="S2SCTCurrencyAndAmount">
							<xs:maxInclusive value="*********.99"/>
							<xs:minInclusive value="0.01"/>
						</xs:restriction>
					</xs:simpleContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="Amt" type="AmountType4Choice" minOccurs="0"/>
			<xs:element name="IntrBkSttlmDt" type="ISODate" minOccurs="0"/>
			<xs:element name="ReqdColltnDt" type="ISODate" minOccurs="0"/>
			<xs:element name="ReqdExctnDt" type="DateAndDateTime2Choice" minOccurs="0"/>
			<xs:element name="CdtrSchmeId" type="S2SCTPartyIdentification125" minOccurs="0"/>
			<xs:element name="SttlmInf" type="S2SCTSettlementInstruction4" minOccurs="0"/>
			<xs:element name="PmtTpInf" type="S2SCTPaymentTypeInformation25" minOccurs="0"/>
			<xs:element name="PmtMtd" type="PaymentMethod4Code" minOccurs="0"/>
			<xs:element name="MndtRltdInf" type="MandateRelatedInformation12" minOccurs="0"/>
			<xs:element name="RmtInf" type="S2SCTRemittanceInformation15" minOccurs="0"/>
			<xs:element name="UltmtDbtr" type="S2SCTParty35ChoiceV3" minOccurs="0"/>
			<xs:element name="Dbtr" type="S2SCTParty35ChoiceV3" minOccurs="0"/>
			<xs:element name="DbtrAcct" type="S2SCTCashAccount24" minOccurs="0"/>
			<xs:element name="DbtrAgt" type="S2SCTBranchAndFinancialInstitutionIdentification5"/>
			<xs:element name="DbtrAgtAcct" type="S2SCTCashAccount24" minOccurs="0"/>
			<xs:element name="CdtrAgt" type="S2SCTBranchAndFinancialInstitutionIdentification5"/>
			<xs:element name="CdtrAgtAcct" type="S2SCTCashAccount24" minOccurs="0"/>
			<xs:element name="Cdtr" type="S2SCTParty35ChoiceV3" minOccurs="0"/>
			<xs:element name="CdtrAcct" type="S2SCTCashAccount24" minOccurs="0"/>
			<xs:element name="UltmtCdtr" type="S2SCTParty35ChoiceV3" minOccurs="0"/>
			<xs:element name="Purp" type="S2SCTPurpose2Choice" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="MandateRelatedInformation12">
		<xs:sequence>
			<xs:element name="MndtId" type="Max35Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="DtOfSgntr" type="ISODate" minOccurs="0" maxOccurs="1"/>
			<xs:element name="AmdmntInd" type="TrueFalseIndicator" minOccurs="0" maxOccurs="1"/>
			<xs:element name="AmdmntInfDtls" type="S2SCTAmendmentInformationDetails12" minOccurs="0" maxOccurs="1"/>
			<xs:element name="ElctrncSgntr" type="Max1025Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="FrstColltnDt" type="ISODate" minOccurs="0" maxOccurs="1"/>
			<xs:element name="FnlColltnDt" type="ISODate" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Frqcy" type="Frequency36Choice" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Rsn" type="MandateSetupReason1Choice" minOccurs="0" maxOccurs="1"/>
			<xs:element name="TrckgDays" type="Exact2NumericText" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTAmendmentInformationDetails12">
		<xs:sequence>
			<xs:element name="OrgnlMndtId" type="Max35Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="OrgnlCdtrSchmeId" type="S2SCTPartyIdentification125" minOccurs="0" maxOccurs="1"/>
			<xs:element name="OrgnlCdtrAgt" type="S2SCTBranchAndFinancialInstitutionIdentification5" minOccurs="0" maxOccurs="1"/>
			<xs:element name="OrgnlCdtrAgtAcct" type="CashAccount24" minOccurs="0" maxOccurs="1"/>
			<xs:element name="OrgnlDbtr" type="S2SCTPartyIdentification125" minOccurs="0" maxOccurs="1"/>
			<xs:element name="OrgnlDbtrAcct" type="CashAccount24" minOccurs="0" maxOccurs="1"/>
			<xs:element name="OrgnlDbtrAgt" type="S2SCTBranchAndFinancialInstitutionIdentification5" minOccurs="0" maxOccurs="1"/>
			<xs:element name="OrgnlDbtrAgtAcct" type="CashAccount24" minOccurs="0" maxOccurs="1"/>
			<xs:element name="OrgnlFnlColltnDt" type="ISODate" minOccurs="0" maxOccurs="1"/>
			<xs:element name="OrgnlFrqcy" type="Frequency36Choice" minOccurs="0" maxOccurs="1"/>
			<xs:element name="OrgnlRsn" type="MandateSetupReason1Choice" minOccurs="0" maxOccurs="1"/>
			<xs:element name="OrgnlTrckgDays" type="Exact2NumericText" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<!--
	<xs:complexType name="OriginalTransactionReference27">
		<xs:sequence>
			<xs:element name="IntrBkSttlmAmt" type="ActiveOrHistoricCurrencyAndAmount" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Amt" type="AmountType4Choice" minOccurs="0" maxOccurs="1"/>
			<xs:element name="IntrBkSttlmDt" type="ISODate" minOccurs="0" maxOccurs="1"/>
			<xs:element name="ReqdColltnDt" type="ISODate" minOccurs="0" maxOccurs="1"/>
			<xs:element name="ReqdExctnDt" type="DateAndDateTime2Choice" minOccurs="0" maxOccurs="1"/>
			<xs:element name="CdtrSchmeId" type="PartyIdentification125" minOccurs="0" maxOccurs="1"/>
			<xs:element name="SttlmInf" type="SettlementInstruction4" minOccurs="0" maxOccurs="1"/>
			<xs:element name="PmtTpInf" type="PaymentTypeInformation25" minOccurs="0" maxOccurs="1"/>
			<xs:element name="PmtMtd" type="PaymentMethod4Code" minOccurs="0" maxOccurs="1"/>
			<xs:element name="MndtRltdInf" type="MandateRelatedInformation12" minOccurs="0" maxOccurs="1"/>
			<xs:element name="RmtInf" type="RemittanceInformation15" minOccurs="0" maxOccurs="1"/>
			<xs:element name="UltmtDbtr" type="Party35Choice" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Dbtr" type="Party35Choice" minOccurs="0" maxOccurs="1"/>
			<xs:element name="DbtrAcct" type="CashAccount24" minOccurs="0" maxOccurs="1"/>
			<xs:element name="DbtrAgt" type="BranchAndFinancialInstitutionIdentification5" minOccurs="0" maxOccurs="1"/>
			<xs:element name="DbtrAgtAcct" type="CashAccount24" minOccurs="0" maxOccurs="1"/>
			<xs:element name="CdtrAgt" type="BranchAndFinancialInstitutionIdentification5" minOccurs="0" maxOccurs="1"/>
			<xs:element name="CdtrAgtAcct" type="CashAccount24" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Cdtr" type="Party35Choice" minOccurs="0" maxOccurs="1"/>
			<xs:element name="CdtrAcct" type="CashAccount24" minOccurs="0" maxOccurs="1"/>
			<xs:element name="UltmtCdtr" type="Party35Choice" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Purp" type="Purpose2Choice" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
-->
	<xs:complexType name="Party34Choice">
		<xs:choice>
			<xs:element name="OrgId" type="OrganisationIdentification8"/>
			<xs:element name="PrvtId" type="PersonIdentification13"/>
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="S2SCTParty34ChoiceV2">
		<xs:choice>
			<xs:element name="OrgId" type="S2SCTOrganisationIdentification8"/>
			<xs:element name="PrvtId" type="S2SCTPersonIdentification13"/>
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="S2SCTParty34ChoiceV21">
		<xs:choice>
			<xs:element name="OrgId" type="S2SCTOrganisationIdentification81"/>
		</xs:choice>
	</xs:complexType>
	<!--
	<xs:complexType name="Party35Choice">
		<xs:choice>
			<xs:element name="Pty" type="PartyIdentification125"/>
			<xs:element name="Agt" type="BranchAndFinancialInstitutionIdentification5"/>
		</xs:choice>
	</xs:complexType>
-->
	<xs:complexType name="S2SCTParty35Choice">
		<xs:choice>
			<xs:element name="Pty" type="S2SCTPartyIdentification125"/>
			<xs:element name="Agt" type="S2SCTBranchAndFinancialInstitutionIdentification5"/>
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="S2SCTParty35ChoiceV2">
		<xs:sequence>
			<xs:element name="Agt" type="S2SCTBranchAndFinancialInstitutionIdentification5"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTParty35ChoiceV3">
		<xs:sequence>
			<xs:element name="Pty" type="S2SCTPartyIdentification125"/>
		</xs:sequence>
	</xs:complexType>
	<!--
	<xs:complexType name="S2SCTParty35ChoiceV4">
		<xs:sequence>
			<xs:element name="Pty" type="S2SCTPartyIdentification125"/>
		</xs:sequence>
	</xs:complexType>
-->
	<xs:complexType name="PartyIdentification125">
		<xs:sequence>
			<xs:element name="Nm" type="Max140Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="PstlAdr" type="PostalAddress6" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Id" type="Party34Choice" minOccurs="0" maxOccurs="1"/>
			<xs:element name="CtryOfRes" type="CountryCode" minOccurs="0" maxOccurs="1"/>
			<xs:element name="CtctDtls" type="ContactDetails2" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTPartyIdentification125">
		<xs:sequence>
			<xs:element name="Nm" type="Max70TextCW" minOccurs="0"/>
			<xs:element name="PstlAdr" type="S2SCTPostalAddress6" minOccurs="0"/>
			<xs:element name="Id" type="S2SCTParty34ChoiceV2" minOccurs="0"/>
			<xs:element name="CtryOfRes" type="CountryCode" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTPartyIdentification1251">
		<xs:sequence>
			<xs:element name="Nm" type="Max70TextCW" minOccurs="0"/>
			<xs:element name="Id" type="S2SCTParty34ChoiceV21" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="PaymentMethod4Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="CHK"/>
			<xs:enumeration value="TRF"/>
			<xs:enumeration value="DD"/>
			<xs:enumeration value="TRA"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="S2SCTPaymentTransaction90">
		<xs:sequence>
			<xs:element name="ModStsId" type="Max35Text"/>
			<xs:element name="RslvdCase" type="S2SCTCase4" minOccurs="0"/>
			<xs:element name="OrgnlGrpInf" type="S2SCTOriginalGroupInformation29"/>
			<xs:element name="OrgnlPmtInfId" type="Max35Text" minOccurs="0"/>
			<xs:element name="OrgnlInstrId" type="Max35Text" minOccurs="0"/>
			<xs:element name="OrgnlEndToEndId" type="Max35TextCW" minOccurs="0"/>
			<xs:element name="OrgnlTxId" type="Max35Text" minOccurs="0"/>
			<xs:element name="OrgnlClrSysRef" type="Max35Text" minOccurs="0"/>
			<xs:element name="ModStsRsnInf" type="S2SCTModificationStatusReason1" minOccurs="0" maxOccurs="1"/>
			<xs:element name="RsltnRltdInf" type="S2SCTResolutionInformation2" minOccurs="0"/>
			<xs:element name="OrgnlIntrBkSttlmAmt" type="ActiveOrHistoricCurrencyAndAmount" minOccurs="0"/>
			<xs:element name="OrgnlIntrBkSttlmDt" type="ISODate" minOccurs="0"/>
			<xs:element name="Assgnr" type="S2SCTParty35ChoiceV2" minOccurs="0"/>
			<xs:element name="Assgne" type="S2SCTParty35ChoiceV2" minOccurs="0"/>
			<xs:element name="OrgnlTxRef" type="S2SCTOriginalTransactionReference271"/>
		</xs:sequence>
	</xs:complexType>
	<!--
	<xs:complexType name="ResolutionInformation2">
		<xs:sequence>
			<xs:element name="IntrBkSttlmAmt" type="ActiveOrHistoricCurrencyAndAmount" minOccurs="0" maxOccurs="1"/>
			<xs:element name="IntrBkSttlmDt" type="ISODate" minOccurs="0" maxOccurs="1"/>
			<xs:element name="ClrChanl" type="ClearingChannel2Code" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Compstn" type="Compensation1" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Chrgs" type="Charges2" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
-->
	<!--
	<xs:complexType name="Charges2">
		<xs:sequence>
			<xs:element name="Amt" type="ActiveOrHistoricCurrencyAndAmount"/>
			<xs:element name="Agt" type="BranchAndFinancialInstitutionIdentification5"/>
		</xs:sequence>
	</xs:complexType>
-->
	<xs:complexType name="Compensation1">
		<xs:sequence>
			<xs:element name="Amt">
				<xs:complexType>
					<xs:simpleContent>
						<xs:restriction base="S2SCTCurrencyAndAmount">
							<xs:maxInclusive value="*********.99"/>
							<xs:minInclusive value="0.01"/>
						</xs:restriction>
					</xs:simpleContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="DbtrAgt" type="S2SCTBranchAndFinancialInstitutionIdentification5"/>
			<xs:element name="CdtrAgt" type="S2SCTBranchAndFinancialInstitutionIdentification5"/>
			<xs:element name="Rsn" type="S2SCTCompensationReason1Choice"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTCompensationReason1Choice">
		<xs:choice>
			<xs:element name="Cd" type="S2SCTExternalPaymentCompensationReason1Code"/>
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="ActiveCurrencyAndAmount">
		<xs:simpleContent>
			<xs:extension base="ActiveCurrencyAndAmount_SimpleType">
				<xs:attribute name="Ccy" type="ActiveCurrencyCode" use="required"/>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	<xs:simpleType name="ActiveCurrencyCode">
		<xs:restriction base="xs:string">
			<xs:pattern value="[A-Z]{3,3}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ActiveCurrencyAndAmount_SimpleType">
		<xs:restriction base="xs:decimal">
			<xs:fractionDigits value="5"/>
			<xs:totalDigits value="18"/>
			<xs:minInclusive value="0"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ClearingChannel2Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="RTGS"/>
			<xs:enumeration value="RTNS"/>
			<xs:enumeration value="MPNS"/>
			<xs:enumeration value="BOOK"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="S2SCTModificationStatusReason1">
		<xs:sequence>
			<xs:element name="Orgtr" type="S2SCTPartyIdentification125" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Rsn" type="ModificationStatusReason1Choice" minOccurs="0" maxOccurs="1"/>
			<xs:element name="AddtlInf" type="Max105Text" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ModificationStatusReason1Choice">
		<xs:choice>
			<xs:element name="Cd" type="ExternalPaymentModificationRejection1Code"/>
			<xs:element name="Prtry" type="Max35Text"/>
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="S2SCTPaymentTypeInformation25">
		<xs:sequence>
			<xs:element name="SvcLvl" type="S2SCTServiceLevel8Choice"/>
			<xs:element name="LclInstrm" type="S2SCTLocalInstrument2Choice" minOccurs="0"/>
			<xs:element name="CtgyPurp" type="CategoryPurpose1Choice" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PersonIdentification13">
		<xs:sequence>
			<xs:element name="DtAndPlcOfBirth" type="DateAndPlaceOfBirth1" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Othr" type="GenericPersonIdentification1" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTPersonIdentification13">
		<xs:sequence>
			<xs:element name="DtAndPlcOfBirth" type="DateAndPlaceOfBirth1" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Othr" type="GenericPersonIdentification1" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PersonIdentificationSchemeName1Choice">
		<xs:choice>
			<xs:element name="Cd" type="ExternalPersonIdentification1Code"/>
			<xs:element name="Prtry" type="Max35Text"/>
		</xs:choice>
	</xs:complexType>
	<xs:simpleType name="PhoneNumber">
		<xs:restriction base="xs:string">
			<xs:pattern value="\+[0-9]{1,3}-[0-9()+\-]{1,30}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="PostalAddress6">
		<xs:sequence>
			<xs:element name="AdrTp" type="AddressType2Code" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Dept" type="Max70Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="SubDept" type="Max70Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="StrtNm" type="Max70Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="BldgNb" type="Max16Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="PstCd" type="Max16Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="TwnNm" type="Max35Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="CtrySubDvsn" type="Max35Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Ctry" type="CountryCode" minOccurs="0" maxOccurs="1"/>
			<xs:element name="AdrLine" type="Max70Text" minOccurs="0" maxOccurs="7"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTPostalAddress6">
		<xs:sequence>
			<xs:element name="Ctry" type="CountryCode" minOccurs="0" maxOccurs="1"/>
			<xs:element name="AdrLine" type="Max70TextCW" minOccurs="0" maxOccurs="2"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTPurpose2Choice">
		<xs:choice>
			<xs:element name="Cd" type="ExternalPurpose1CodeCW"/>
			<xs:element name="Prtry" type="Max35TextCW"/>
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="S2SCTRemittanceInformation15">
		<xs:choice>
			<xs:element name="Ustrd" type="Max140TextCW"/>
			<xs:element name="Strd" type="S2SCTStructuredRemittanceInformation15"/>
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="S2SCTResolutionInformation2">
		<xs:sequence>
			<xs:element name="IntrBkSttlmDt" type="ISODate" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Compstn" type="Compensation1" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Chrgs" type="S2SCTCharges2" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTResolutionOfInvestigationV08">
		<xs:sequence>
			<xs:element name="Assgnmt" type="S2SCTCaseAssignment4"/>
			<xs:element name="RslvdCase" type="S2SCTCase4"/>
			<xs:element name="Sts" type="S2SCTInvestigationStatus4Choice"/>
			<xs:element name="CxlDtls" type="S2SCTUnderlyingTransaction19" minOccurs="0" maxOccurs="1"/>
			<xs:element name="ModDtls" type="S2SCTPaymentTransaction90" minOccurs="0"/>
			<xs:element name="ClmNonRctDtls" type="S2SCTClaimNonReceipt1Choice" minOccurs="0"/>
			<xs:element name="StmtDtls" type="S2SCTStatementResolutionEntry3" minOccurs="0"/>
			<xs:element name="CrrctnTx" type="CorrectiveTransaction3Choice" minOccurs="0"/>
			<xs:element name="RsltnRltdInf" type="S2SCTResolutionInformation2" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CorrectiveTransaction3Choice">
		<xs:choice>
			<xs:element name="Initn" type="CorrectivePaymentInitiation3"/>
			<xs:element name="IntrBk" type="CorrectiveInterbankTransaction1"/>
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="CorrectiveInterbankTransaction1">
		<xs:sequence>
			<xs:element name="GrpHdr" type="CorrectiveGroupInformation1" minOccurs="0" maxOccurs="1"/>
			<xs:element name="InstrId" type="Max35Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="EndToEndId" type="Max35Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="TxId" type="Max35Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="IntrBkSttlmAmt" type="ActiveOrHistoricCurrencyAndAmount"/>
			<xs:element name="IntrBkSttlmDt" type="ISODate"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CorrectiveGroupInformation1">
		<xs:sequence>
			<xs:element name="MsgId" type="Max35Text"/>
			<xs:element name="MsgNmId" type="Max35Text"/>
			<xs:element name="CreDtTm" type="ISODateTime" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CorrectivePaymentInitiation3">
		<xs:sequence>
			<xs:element name="GrpHdr" type="CorrectiveGroupInformation1" minOccurs="0" maxOccurs="1"/>
			<xs:element name="PmtInfId" type="Max35Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="InstrId" type="Max35Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="EndToEndId" type="Max35Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="InstdAmt" type="ActiveOrHistoricCurrencyAndAmount"/>
			<xs:element name="ReqdExctnDt" type="DateAndDateTime2Choice" minOccurs="0" maxOccurs="1"/>
			<xs:element name="ReqdColltnDt" type="ISODate" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTStatementResolutionEntry3">
		<xs:sequence>
			<xs:element name="OrgnlGrpInf" type="S2SCTOriginalGroupInformation29" minOccurs="0" maxOccurs="1"/>
			<xs:element name="OrgnlStmtId" type="Max35Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="AcctSvcrRef" type="Max35Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="CrrctdAmt" type="ActiveOrHistoricCurrencyAndAmount" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Chrgs" type="S2SCTCharges3" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Purp" type="Purpose2Choice" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTCharges3">
		<xs:sequence>
			<xs:element name="TtlChrgsAndTaxAmt" type="ActiveOrHistoricCurrencyAndAmount" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Rcrd" type="S2SCTChargesRecord1" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTChargesRecord1">
		<xs:sequence>
			<xs:element name="Amt" type="ActiveOrHistoricCurrencyAndAmount"/>
			<xs:element name="CdtDbtInd" type="CreditDebitCode" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Tp" type="ChargeType3Choice" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Rate" type="PercentageRate" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Br" type="ChargeBearerType1Code" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Agt" type="S2SCTBranchAndFinancialInstitutionIdentification5" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Tax" type="TaxCharges2" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TaxCharges2">
		<xs:sequence>
			<xs:element name="Id" type="Max35Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Rate" type="PercentageRate" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Amt" type="ActiveOrHistoricCurrencyAndAmount" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="ChargeBearerType1Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="DEBT"/>
			<xs:enumeration value="CRED"/>
			<xs:enumeration value="SHAR"/>
			<xs:enumeration value="SLEV"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="ChargeType3Choice">
		<xs:choice>
			<xs:element name="Cd" type="ExternalChargeType1Code"/>
			<xs:element name="Prtry" type="GenericIdentification3"/>
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="S2SCTServiceLevel8Choice">
		<xs:sequence>
			<xs:element name="Cd" type="S2SCTExternalServiceLevel1Code"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTSettlementInstruction4">
		<xs:sequence>
			<xs:element name="SttlmMtd" type="S2SCTSettlementMethod1Code"/>
			<xs:element name="ClrSys" type="S2SCTClearingSystemIdentification3Choice"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="S2SCTSettlementMethod1Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="CLRG"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="S2SCTStructuredRemittanceInformation15">
		<xs:sequence>
			<xs:element name="RfrdDocInf" type="S2SCTReferredDocumentInformation7" minOccurs="0" maxOccurs="1"/>
			<xs:element name="RfrdDocAmt" type="S2SCTRemittanceAmount2" minOccurs="0" maxOccurs="1"/>
			<xs:element name="CdtrRefInf" type="S2SCTCreditorReferenceInformation2" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Invcr" type="S2SCTPartyIdentification125" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Invcee" type="S2SCTPartyIdentification125" minOccurs="0" maxOccurs="1"/>
			<xs:element name="AddtlRmtInf" type="Max140TextCW" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="RemittanceAmount2">
		<xs:sequence>
			<xs:element name="DuePyblAmt" type="ActiveOrHistoricCurrencyAndAmount" minOccurs="0" maxOccurs="1"/>
			<xs:element name="DscntApldAmt" type="DiscountAmountAndType1" minOccurs="0"/>
			<xs:element name="CdtNoteAmt" type="ActiveOrHistoricCurrencyAndAmount" minOccurs="0" maxOccurs="1"/>
			<xs:element name="TaxAmt" type="TaxAmountAndType1" minOccurs="0"/>
			<xs:element name="AdjstmntAmtAndRsn" type="DocumentAdjustment1" minOccurs="0"/>
			<xs:element name="RmtdAmt" type="ActiveOrHistoricCurrencyAndAmount" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTRemittanceAmount2">
		<xs:sequence>
			<xs:element name="DuePyblAmt" type="ActiveOrHistoricCurrencyAndAmount" minOccurs="0" maxOccurs="1"/>
			<xs:element name="DscntApldAmt" type="S2SCTDiscountAmountAndType1" minOccurs="0"/>
			<xs:element name="CdtNoteAmt" type="ActiveOrHistoricCurrencyAndAmount" minOccurs="0" maxOccurs="1"/>
			<xs:element name="TaxAmt" type="S2SCTTaxAmountAndType1" minOccurs="0"/>
			<xs:element name="AdjstmntAmtAndRsn" type="DocumentAdjustment1" minOccurs="0"/>
			<xs:element name="RmtdAmt" type="ActiveOrHistoricCurrencyAndAmount" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="DocumentAdjustment1">
		<xs:sequence>
			<xs:element name="Amt" type="ActiveOrHistoricCurrencyAndAmount"/>
			<xs:element name="CdtDbtInd" type="CreditDebitCode" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Rsn" type="Max4Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="AddtlInf" type="Max140Text" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="CreditDebitCode">
		<xs:restriction base="xs:string">
			<xs:enumeration value="CRDT"/>
			<xs:enumeration value="DBIT"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="TaxAmountAndType1">
		<xs:sequence>
			<xs:element name="Tp" type="TaxAmountType1Choice" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Amt" type="ActiveOrHistoricCurrencyAndAmount"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTTaxAmountAndType1">
		<xs:sequence>
			<xs:element name="Amt" type="ActiveOrHistoricCurrencyAndAmount"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TaxAmountType1Choice">
		<xs:choice>
			<xs:element name="Cd" type="ExternalTaxAmountType1Code"/>
			<xs:element name="Prtry" type="Max35Text"/>
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="S2SCTReferredDocumentInformation7">
		<xs:sequence>
			<xs:element name="Tp" type="S2SCTReferredDocumentType4" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Nb" type="Max35Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="RltdDt" type="ISODate" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="DocumentLineInformation1">
		<xs:sequence>
			<xs:element name="Id" type="DocumentLineIdentification1" minOccurs="1" maxOccurs="unbounded"/>
			<xs:element name="Desc" type="Max2048Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Amt" type="RemittanceAmount3" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="RemittanceAmount3">
		<xs:sequence>
			<xs:element name="DuePyblAmt" type="ActiveOrHistoricCurrencyAndAmount" minOccurs="0" maxOccurs="1"/>
			<xs:element name="DscntApldAmt" type="DiscountAmountAndType1" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="CdtNoteAmt" type="ActiveOrHistoricCurrencyAndAmount" minOccurs="0" maxOccurs="1"/>
			<xs:element name="TaxAmt" type="TaxAmountAndType1" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="AdjstmntAmtAndRsn" type="DocumentAdjustment1" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="RmtdAmt" type="ActiveOrHistoricCurrencyAndAmount" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="DocumentLineIdentification1">
		<xs:sequence>
			<xs:element name="Tp" type="DocumentLineType1" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Nb" type="Max35Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="RltdDt" type="ISODate" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="DocumentLineType1">
		<xs:sequence>
			<xs:element name="CdOrPrtry" type="DocumentLineType1Choice"/>
			<xs:element name="Issr" type="Max35Text" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="DocumentLineType1Choice">
		<xs:choice>
			<xs:element name="Cd" type="ExternalDocumentLineType1Code"/>
			<xs:element name="Prtry" type="Max35Text"/>
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="S2SCTReferredDocumentType4">
		<xs:sequence>
			<xs:element name="CdOrPrtry" type="ReferredDocumentType3Choice"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ReferredDocumentType3Choice">
		<xs:choice>
			<xs:element name="Cd" type="DocumentType6Code"/>
			<xs:element name="Prtry" type="Max35Text"/>
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="S2SCTUnderlyingTransaction19">
		<xs:sequence>
			<xs:element name="TxInfAndSts" type="S2SCTPaymentTransaction85" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Garnishment2">
		<xs:sequence>
			<xs:element name="Tp" type="GarnishmentType1"/>
			<xs:element name="Grnshee" type="PartyIdentification125" minOccurs="0" maxOccurs="1"/>
			<xs:element name="GrnshmtAdmstr" type="PartyIdentification125" minOccurs="0" maxOccurs="1"/>
			<xs:element name="RefNb" type="Max140Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Dt" type="ISODate" minOccurs="0" maxOccurs="1"/>
			<xs:element name="RmtdAmt" type="ActiveOrHistoricCurrencyAndAmount" minOccurs="0" maxOccurs="1"/>
			<xs:element name="FmlyMdclInsrncInd" type="TrueFalseIndicator" minOccurs="0" maxOccurs="1"/>
			<xs:element name="MplyeeTermntnInd" type="TrueFalseIndicator" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="GarnishmentType1">
		<xs:sequence>
			<xs:element name="CdOrPrtry" type="GarnishmentType1Choice"/>
			<xs:element name="Issr" type="Max35Text" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="GarnishmentType1Choice">
		<xs:choice>
			<xs:element name="Cd" type="ExternalGarnishmentType1Code"/>
			<xs:element name="Prtry" type="Max35Text"/>
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="TaxInformation7">
		<xs:sequence>
			<xs:element name="Cdtr" type="TaxParty1" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Dbtr" type="TaxParty2" minOccurs="0" maxOccurs="1"/>
			<xs:element name="UltmtDbtr" type="TaxParty2" minOccurs="0" maxOccurs="1"/>
			<xs:element name="AdmstnZone" type="Max35Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="RefNb" type="Max140Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Mtd" type="Max35Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="TtlTaxblBaseAmt" type="ActiveOrHistoricCurrencyAndAmount" minOccurs="0" maxOccurs="1"/>
			<xs:element name="TtlTaxAmt" type="ActiveOrHistoricCurrencyAndAmount" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Dt" type="ISODate" minOccurs="0" maxOccurs="1"/>
			<xs:element name="SeqNb" type="Number" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Rcrd" type="TaxRecord2" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TaxRecord2">
		<xs:sequence>
			<xs:element name="Tp" type="Max35Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Ctgy" type="Max35Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="CtgyDtls" type="Max35Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="DbtrSts" type="Max35Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="CertId" type="Max35Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="FrmsCd" type="Max35Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Prd" type="TaxPeriod2" minOccurs="0" maxOccurs="1"/>
			<xs:element name="TaxAmt" type="TaxAmount2" minOccurs="0" maxOccurs="1"/>
			<xs:element name="AddtlInf" type="Max140Text" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TaxAmount2">
		<xs:sequence>
			<xs:element name="Rate" type="PercentageRate" minOccurs="0" maxOccurs="1"/>
			<xs:element name="TaxblBaseAmt" type="ActiveOrHistoricCurrencyAndAmount" minOccurs="0" maxOccurs="1"/>
			<xs:element name="TtlAmt" type="ActiveOrHistoricCurrencyAndAmount" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Dtls" type="TaxRecordDetails2" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TaxRecordDetails2">
		<xs:sequence>
			<xs:element name="Prd" type="TaxPeriod2" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Amt" type="ActiveOrHistoricCurrencyAndAmount"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="PercentageRate">
		<xs:restriction base="xs:decimal">
			<xs:fractionDigits value="10"/>
			<xs:totalDigits value="11"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="TaxPeriod2">
		<xs:sequence>
			<xs:element name="Yr" type="ISODate" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Tp" type="TaxRecordPeriod1Code" minOccurs="0" maxOccurs="1"/>
			<xs:element name="FrToDt" type="DatePeriod2" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="DatePeriod2">
		<xs:sequence>
			<xs:element name="FrDt" type="ISODate"/>
			<xs:element name="ToDt" type="ISODate"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="TaxRecordPeriod1Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="MM01"/>
			<xs:enumeration value="MM02"/>
			<xs:enumeration value="MM03"/>
			<xs:enumeration value="MM04"/>
			<xs:enumeration value="MM05"/>
			<xs:enumeration value="MM06"/>
			<xs:enumeration value="MM07"/>
			<xs:enumeration value="MM08"/>
			<xs:enumeration value="MM09"/>
			<xs:enumeration value="MM10"/>
			<xs:enumeration value="MM11"/>
			<xs:enumeration value="MM12"/>
			<xs:enumeration value="QTR1"/>
			<xs:enumeration value="QTR2"/>
			<xs:enumeration value="QTR3"/>
			<xs:enumeration value="QTR4"/>
			<xs:enumeration value="HLF1"/>
			<xs:enumeration value="HLF2"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Number">
		<xs:restriction base="xs:decimal">
			<xs:fractionDigits value="0"/>
			<xs:totalDigits value="18"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="TaxParty1">
		<xs:sequence>
			<xs:element name="TaxId" type="Max35Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="RegnId" type="Max35Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="TaxTp" type="Max35Text" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TaxParty2">
		<xs:sequence>
			<xs:element name="TaxId" type="Max35Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="RegnId" type="Max35Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="TaxTp" type="Max35Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Authstn" type="TaxAuthorisation1" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TaxAuthorisation1">
		<xs:sequence>
			<xs:element name="Titl" type="Max35Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Nm" type="Max140Text" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CreditorReferenceInformation2">
		<xs:sequence>
			<xs:element name="Tp" type="CreditorReferenceType2" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Ref" type="Max35Text" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CreditorReferenceType2">
		<xs:sequence>
			<xs:element name="CdOrPrtry" type="CreditorReferenceType1Choice"/>
			<xs:element name="Issr" type="Max35Text" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CreditorReferenceType1Choice">
		<xs:choice>
			<xs:element name="Cd" type="DocumentType3Code"/>
			<xs:element name="Prtry" type="Max35Text"/>
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="Purpose2Choice">
		<xs:choice>
			<xs:element name="Cd" type="ExternalPurpose1Code"/>
			<xs:element name="Prtry" type="Max35Text"/>
		</xs:choice>
	</xs:complexType>
	<!--
	<xs:complexType name="MandateRelatedInformation12">
		<xs:sequence>
			<xs:element name="MndtId" type="Max35Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="DtOfSgntr" type="ISODate" minOccurs="0" maxOccurs="1"/>
			<xs:element name="AmdmntInd" type="TrueFalseIndicator" minOccurs="0" maxOccurs="1"/>
			<xs:element name="AmdmntInfDtls" type="AmendmentInformationDetails12" minOccurs="0" maxOccurs="1"/>
			<xs:element name="ElctrncSgntr" type="Max1025Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="FrstColltnDt" type="ISODate" minOccurs="0" maxOccurs="1"/>
			<xs:element name="FnlColltnDt" type="ISODate" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Frqcy" type="Frequency36Choice" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Rsn" type="MandateSetupReason1Choice" minOccurs="0" maxOccurs="1"/>
			<xs:element name="TrckgDays" type="Exact2NumericText" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
-->
	<xs:complexType name="MandateSetupReason1Choice">
		<xs:choice>
			<xs:element name="Cd" type="ExternalMandateSetupReason1Code"/>
			<xs:element name="Prtry" type="Max70Text"/>
		</xs:choice>
	</xs:complexType>
	<xs:simpleType name="ExternalMandateSetupReason1Code">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="4"/>
		</xs:restriction>
	</xs:simpleType>
	<!--
	<xs:complexType name="AmendmentInformationDetails12">
		<xs:sequence>
			<xs:element name="OrgnlMndtId" type="Max35Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="OrgnlCdtrSchmeId" type="PartyIdentification125" minOccurs="0" maxOccurs="1"/>
			<xs:element name="OrgnlCdtrAgt" type="BranchAndFinancialInstitutionIdentification5" minOccurs="0" maxOccurs="1"/>
			<xs:element name="OrgnlCdtrAgtAcct" type="CashAccount24" minOccurs="0" maxOccurs="1"/>
			<xs:element name="OrgnlDbtr" type="PartyIdentification125" minOccurs="0" maxOccurs="1"/>
			<xs:element name="OrgnlDbtrAcct" type="CashAccount24" minOccurs="0" maxOccurs="1"/>
			<xs:element name="OrgnlDbtrAgt" type="BranchAndFinancialInstitutionIdentification5" minOccurs="0" maxOccurs="1"/>
			<xs:element name="OrgnlDbtrAgtAcct" type="CashAccount24" minOccurs="0" maxOccurs="1"/>
			<xs:element name="OrgnlFnlColltnDt" type="ISODate" minOccurs="0" maxOccurs="1"/>
			<xs:element name="OrgnlFrqcy" type="Frequency36Choice" minOccurs="0" maxOccurs="1"/>
			<xs:element name="OrgnlRsn" type="MandateSetupReason1Choice" minOccurs="0" maxOccurs="1"/>
			<xs:element name="OrgnlTrckgDays" type="Exact2NumericText" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
-->
	<xs:complexType name="PaymentTypeInformation25">
		<xs:sequence>
			<xs:element name="InstrPrty" type="Priority2Code" minOccurs="0" maxOccurs="1"/>
			<xs:element name="ClrChanl" type="ClearingChannel2Code" minOccurs="0" maxOccurs="1"/>
			<xs:element name="SvcLvl" type="ServiceLevel8Choice" minOccurs="0" maxOccurs="1"/>
			<xs:element name="LclInstrm" type="LocalInstrument2Choice" minOccurs="0" maxOccurs="1"/>
			<xs:element name="SeqTp" type="SequenceType3Code" minOccurs="0" maxOccurs="1"/>
			<xs:element name="CtgyPurp" type="CategoryPurpose1Choice" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="SequenceType3Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="FRST"/>
			<xs:enumeration value="RCUR"/>
			<xs:enumeration value="FNAL"/>
			<xs:enumeration value="OOFF"/>
			<xs:enumeration value="RPRE"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="ServiceLevel8Choice">
		<xs:choice>
			<xs:element name="Cd" type="ExternalServiceLevel1Code"/>
			<xs:element name="Prtry" type="Max35Text"/>
		</xs:choice>
	</xs:complexType>
	<xs:simpleType name="Priority2Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="HIGH"/>
			<xs:enumeration value="NORM"/>
		</xs:restriction>
	</xs:simpleType>
	<!--
	<xs:complexType name="SettlementInstruction4">
		<xs:sequence>
			<xs:element name="SttlmMtd" type="SettlementMethod1Code"/>
			<xs:element name="SttlmAcct" type="CashAccount24" minOccurs="0" maxOccurs="1"/>
			<xs:element name="ClrSys" type="ClearingSystemIdentification3Choice" minOccurs="0" maxOccurs="1"/>
			<xs:element name="InstgRmbrsmntAgt" type="BranchAndFinancialInstitutionIdentification5" minOccurs="0" maxOccurs="1"/>
			<xs:element name="InstgRmbrsmntAgtAcct" type="CashAccount24" minOccurs="0" maxOccurs="1"/>
			<xs:element name="InstdRmbrsmntAgt" type="BranchAndFinancialInstitutionIdentification5" minOccurs="0" maxOccurs="1"/>
			<xs:element name="InstdRmbrsmntAgtAcct" type="CashAccount24" minOccurs="0" maxOccurs="1"/>
			<xs:element name="ThrdRmbrsmntAgt" type="BranchAndFinancialInstitutionIdentification5" minOccurs="0" maxOccurs="1"/>
			<xs:element name="ThrdRmbrsmntAgtAcct" type="CashAccount24" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
-->
	<xs:complexType name="ClearingSystemIdentification3Choice">
		<xs:choice>
			<xs:element name="Cd" type="ExternalCashClearingSystem1Code"/>
			<xs:element name="Prtry" type="Max35Text"/>
		</xs:choice>
	</xs:complexType>
	<xs:simpleType name="SettlementMethod1Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="INDA"/>
			<xs:enumeration value="INGA"/>
			<xs:enumeration value="COVE"/>
			<xs:enumeration value="CLRG"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="NumberOfCancellationsPerStatus1">
		<xs:sequence>
			<xs:element name="DtldNbOfTxs" type="Max15NumericText"/>
			<xs:element name="DtldSts" type="CancellationIndividualStatus1Code"/>
			<xs:element name="DtldCtrlSum" type="DecimalNumber" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTCancellationStatusReason3">
		<xs:sequence>
			<xs:element name="Orgtr" type="S2SCTPartyIdentification1251" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Rsn" type="CancellationStatusReason3Choice" minOccurs="0" maxOccurs="1"/>
			<xs:element name="AddtlInf" type="Max105Text" minOccurs="0" maxOccurs="11"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CancellationStatusReason3Choice">
		<xs:choice>
			<xs:element name="Cd" type="ExternalPaymentCancellationRejection1Code"/>
			<xs:element name="Prtry" type="Max35Text"/>
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="NumberOfTransactionsPerStatus1">
		<xs:sequence>
			<xs:element name="DtldNbOfTxs" type="Max15NumericText"/>
			<xs:element name="DtldSts" type="TransactionIndividualStatus1Code"/>
			<xs:element name="DtldCtrlSum" type="DecimalNumber" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="TransactionIndividualStatus1Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ACTC"/>
			<xs:enumeration value="RJCT"/>
			<xs:enumeration value="PDNG"/>
			<xs:enumeration value="ACCP"/>
			<xs:enumeration value="ACSP"/>
			<xs:enumeration value="ACSC"/>
			<xs:enumeration value="ACCR"/>
			<xs:enumeration value="ACWC"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="S2SCTPaymentTransaction85">
		<xs:sequence>
			<xs:element name="CxlStsId" type="Max35Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="OrgnlGrpInf" type="S2SCTOriginalGroupInformation291" minOccurs="0" maxOccurs="1"/>
			<xs:element name="OrgnlInstrId" type="Max35Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="OrgnlEndToEndId" type="Max35Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="OrgnlTxId" type="Max35Text" minOccurs="0" maxOccurs="1"/>
			<xs:element name="TxCxlSts" type="CancellationIndividualStatus1Code" minOccurs="0" maxOccurs="1"/>
			<xs:element name="CxlStsRsnInf" type="S2SCTCancellationStatusReason3" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Assgnr" type="S2SCTParty35ChoiceV2" minOccurs="0" maxOccurs="1"/>
			<xs:element name="OrgnlTxRef" type="S2SCTOriginalTransactionReference27" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="TrueFalseIndicator">
		<xs:restriction base="xs:boolean"/>
	</xs:simpleType>
	<xs:simpleType name="YesNoIndicator">
		<xs:restriction base="xs:boolean"/>
	</xs:simpleType>
	<xs:simpleType name="CancellationIndividualStatus1Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="RJCR"/>
			<xs:enumeration value="ACCR"/>
			<xs:enumeration value="PDCR"/>
		</xs:restriction>
	</xs:simpleType>
</xs:schema>
