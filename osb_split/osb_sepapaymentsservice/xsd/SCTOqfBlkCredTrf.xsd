<?xml version="1.0" encoding="UTF-8"?>
<!-- STEP2 SCT Schema, SCTOqfBlkCredTrf, January 22th 2019, Release November 2019 -->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:sw27="urn:iso:std:iso:20022:tech:xsd:camt.027.001.06" xmlns:sw87="urn:iso:std:iso:20022:tech:xsd:camt.087.001.05" xmlns:sw9="urn:iso:std:iso:20022:tech:xsd:camt.029.001.08" xmlns:sw28="urn:iso:std:iso:20022:tech:xsd:pacs.028.001.01" xmlns:S2SCTOqf="urn:S2SCTOqf:xsd:$SCTOqfBlkCredTrf" targetNamespace="urn:S2SCTOqf:xsd:$SCTOqfBlkCredTrf" elementFormDefault="qualified">
	<xs:import namespace="urn:iso:std:iso:20022:tech:xsd:camt.027.001.06" schemaLocation="camt.027.001.06.xsd"/>
	<xs:import namespace="urn:iso:std:iso:20022:tech:xsd:camt.087.001.05" schemaLocation="camt.087.001.05.xsd"/>
	<xs:import namespace="urn:iso:std:iso:20022:tech:xsd:camt.029.001.08" schemaLocation="camt.029.001.08.xsd"/>
	<xs:import namespace="urn:iso:std:iso:20022:tech:xsd:pacs.028.001.01" schemaLocation="pacs.028.001.01.xsd"/>
	<xs:element name="SCTOqfBlkCredTrf">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="SndgInst" type="S2SCTOqf:BICIdentifier"/>
				<xs:element name="RcvgInst" type="S2SCTOqf:BICIdentifier"/>
				<xs:element name="SrvcId" type="S2SCTOqf:SrvcID"/>
				<xs:element name="TstCode" type="S2SCTOqf:TestCodeType"/>
				<xs:element name="FType" type="S2SCTOqf:FTpType"/>
				<xs:element name="FileRef" type="S2SCTOqf:Max16Text"/>
				<xs:element name="RoutingInd" type="S2SCTOqf:RoutingInd"/>
				<xs:element name="FileBusDt" type="S2SCTOqf:ISODate"/>
				<xs:element name="FileCycleNo" type="S2SCTOqf:Max2NumericText"/>
				<xs:element name="ClmNonRct" type="sw27:S2SCTClaimNonReceiptV06" minOccurs="0" maxOccurs="unbounded"/>
				<xs:element name="ReqToModfyPmt" type="sw87:S2SCTRequestToModifyPaymentV05" minOccurs="0" maxOccurs="unbounded"/>
				<xs:element name="RsltnOfInvstgtn" type="sw9:S2SCTResolutionOfInvestigationV08" minOccurs="0" maxOccurs="unbounded"/>
				<xs:element name="FIToFIPmtStsReq" type="sw28:FIToFIPaymentInstantStatusInquiryForInvestigationV01" minOccurs="0" maxOccurs="unbounded"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:simpleType name="RoutingInd">
		<xs:restriction base="xs:string">
			<xs:length value="3" fixed="true"/>
			<xs:enumeration value="DIR"/>
			<xs:enumeration value="IND"/>
			<xs:enumeration value="RET"/>
			<xs:enumeration value="ALL"/>
			<xs:enumeration value="RCL"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="FTpType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="SCF"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TestCodeType">
		<xs:restriction base="xs:string">
			<xs:length value="1"/>
			<xs:enumeration value="T"/>
			<xs:enumeration value="P"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="SrvcID">
		<xs:restriction base="xs:string">
			<xs:enumeration value="SCT"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Max2NumericText">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9]{2,2}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ISODateTime">
		<xs:restriction base="xs:dateTime">
			<xs:pattern value="[0-9]{4,4}\-[0-9]{2,2}\-[0-9]{2,2}[T][0-9]{2,2}:[0-9]{2,2}:[0-9]{2,2}[\S]*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="BICIdentifier">
		<xs:restriction base="xs:string">
			<xs:pattern value="[A-Z]{6,6}[A-Z2-9][A-NP-Z0-9]([A-Z0-9]{3,3}){0,1}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Max16Text">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9A-Z]{16,16}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ISODate">
		<xs:restriction base="xs:date">
			<xs:pattern value="[0-9]{4,4}\-[0-9]{2,2}\-[0-9]{2,2}"/>
		</xs:restriction>
	</xs:simpleType>
</xs:schema>
