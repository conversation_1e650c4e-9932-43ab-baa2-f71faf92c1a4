<?xml version="1.0" encoding="UTF-8"?>
<!-- STEP2 SCT Schema, SCTIqfBlkCredTrf, January 22th 2019, Release November 2019 -->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:sw27="urn:iso:std:iso:20022:tech:xsd:camt.027.001.06" xmlns:sw87="urn:iso:std:iso:20022:tech:xsd:camt.087.001.05" xmlns:sw9="urn:iso:std:iso:20022:tech:xsd:camt.029.001.08" xmlns:sw28="urn:iso:std:iso:20022:tech:xsd:pacs.028.001.01" xmlns:S2SCTIqf="urn:S2SCTIqf:xsd:$SCTIqfBlkCredTrf" targetNamespace="urn:S2SCTIqf:xsd:$SCTIqfBlkCredTrf" elementFormDefault="qualified">
	<xs:import namespace="urn:iso:std:iso:20022:tech:xsd:camt.027.001.06" schemaLocation="camt.027.001.06.xsd"/>
	<xs:import namespace="urn:iso:std:iso:20022:tech:xsd:camt.087.001.05" schemaLocation="camt.087.001.05.xsd"/>
	<xs:import namespace="urn:iso:std:iso:20022:tech:xsd:camt.029.001.08" schemaLocation="camt.029.001.08.xsd"/>
	<xs:import namespace="urn:iso:std:iso:20022:tech:xsd:pacs.028.001.01" schemaLocation="pacs.028.001.01.xsd"/>
	<xs:element name="SCTIqfBlkCredTrf">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="SndgInst" type="S2SCTIqf:BICIdentifier"/>
				<xs:element name="RcvgInst" type="S2SCTIqf:BICIdentifier"/>
				<xs:element name="FileRef" type="S2SCTIqf:Max16Text"/>
				<xs:element name="SrvcId" type="S2SCTIqf:SrvcId"/>
				<xs:element name="TstCode" type="S2SCTIqf:TestCodeType"/>
				<xs:element name="FType" type="S2SCTIqf:FType"/>
				<xs:element name="FDtTm" type="S2SCTIqf:ISODateTime"/>
				<xs:element name="NumCNRBlk" type="S2SCTIqf:Max8NumericText"/>
				<xs:element name="NumRMPBlk" type="S2SCTIqf:Max8NumericText"/>
				<xs:element name="NumROQBlk" type="S2SCTIqf:Max8NumericText"/>
				<xs:element name="NumSRBlk" type="S2SCTIqf:Max8NumericText"/>
				<xs:element name="ClmNonRct" type="sw27:S2SCTClaimNonReceiptV06" minOccurs="0" maxOccurs="unbounded"/>
				<xs:element name="ReqToModfyPmt" type="sw87:S2SCTRequestToModifyPaymentV05" minOccurs="0" maxOccurs="unbounded"/>
				<xs:element name="RsltnOfInvstgtn" type="sw9:S2SCTResolutionOfInvestigationV08" minOccurs="0" maxOccurs="unbounded"/>
				<xs:element name="FIToFIPmtStsReq" type="sw28:FIToFIPaymentInstantStatusInquiryForInvestigationV01" minOccurs="0" maxOccurs="unbounded"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:simpleType name="Max16Text">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9A-Z]{16,16}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="SrvcId">
		<xs:restriction base="xs:string">
			<xs:enumeration value="SCT"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="FType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="IQF"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TestCodeType">
		<xs:restriction base="xs:string">
			<xs:length value="1"/>
			<xs:enumeration value="T"/>
			<xs:enumeration value="P"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Max8NumericText">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9]{1,8}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ISODateTime">
		<xs:restriction base="xs:dateTime">
			<xs:pattern value="[0-9]{4,4}\-[0-9]{2,2}\-[0-9]{2,2}[T][0-9]{2,2}:[0-9]{2,2}:[0-9]{2,2}[\S]*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="BICIdentifier">
		<xs:restriction base="xs:string">
			<xs:pattern value="[A-Z]{6,6}[A-Z2-9][A-NP-Z0-9]([A-Z0-9]{3,3}){0,1}"/>
		</xs:restriction>
	</xs:simpleType>
</xs:schema>
