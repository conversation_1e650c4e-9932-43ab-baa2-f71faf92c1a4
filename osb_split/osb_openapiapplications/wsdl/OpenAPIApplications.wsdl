<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xs="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns="http://homecredit.eu/openapi/applications"
                  targetNamespace="http://homecredit.eu/openapi/applications">
    <wsdl:types>
        <xs:schema targetNamespace="http://homecredit.eu/openapi/applications">
            <xs:include schemaLocation="OpenAPIApplications.xsd"/>
        </xs:schema>
    </wsdl:types>
    <wsdl:message name="SimpleFaultMessage">
        <wsdl:part element="simpleFault" name="simpleFault"/>
    </wsdl:message>
    <wsdl:message name="getProductApprovedApplicationsRequestMessage">
        <wsdl:part element="getProductApprovedApplicationsRequest" name="getProductApprovedApplicationsRequest"/>
    </wsdl:message>
    <wsdl:message name="getProductApprovedApplicationsResponseMessage">
        <wsdl:part element="getProductApprovedApplicationsResponse" name="getProductApprovedApplicationsResponse"/>
    </wsdl:message>

    <wsdl:portType name="OpenAPIApplicationsPort">
        <wsdl:operation name="getProductApprovedApplications">
            <wsdl:documentation>
                <![CDATA[called REST service: GET /devportal_system//products/{code}/approved_applications]]>
            </wsdl:documentation>
            <wsdl:input message="getProductApprovedApplicationsRequestMessage"/>
            <wsdl:output message="getProductApprovedApplicationsResponseMessage"/>
            <wsdl:fault name="simpleFault" message="SimpleFaultMessage"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="OpenAPIApplicationsBinding" type="OpenAPIApplicationsPort">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

        <wsdl:operation name="getProductApprovedApplications">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="simpleFault">
                <soap:fault name="simpleFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="OpenAPIApplications">
        <wsdl:documentation>Service providing operations related to document reminders.</wsdl:documentation>
        <wsdl:port name="OpenAPIApplicationsPort" binding="OpenAPIApplicationsBinding">
            <soap:address location="http://airbank.cz/dor/ws/calendarManager"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>
