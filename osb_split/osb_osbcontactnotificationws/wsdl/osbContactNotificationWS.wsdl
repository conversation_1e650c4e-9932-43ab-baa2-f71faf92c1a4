<?xml version="1.0" encoding="UTF-8" ?>
<definitions targetNamespace="http://osb.airbank.cz/contact/notification" xmlns="http://schemas.xmlsoap.org/wsdl/"
             xmlns:tns="http://osb.airbank.cz/contact/notification" xmlns:xsd="http://www.w3.org/2001/XMLSchema"
             xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/">
  <types>
    <xsd:schema targetNamespace="http://osb.airbank.cz/contact/notification" elementFormDefault="qualified">
      <xsd:include schemaLocation="osbContactNotificationWS.xsd"/>
    </xsd:schema>
  </types>
  <message name="FinishContactBusinessSummaryRequest">
    <part name="FinishContactBusinessSummaryRequest" element="tns:FinishContactBusinessSummaryRequest"/>
  </message>
  <message name="FinishContactBusinessSummaryResponse">
    <part name="FinishContactBusinessSummaryResponse" element="tns:FinishContactBusinessSummaryResponse"/>
  </message>
  <portType name="ContactNotificationWSPort">
    <operation name="FinishContactBusinessSummary">
      <input message="tns:FinishContactBusinessSummaryRequest"/>
      <output message="tns:FinishContactBusinessSummaryResponse"/>
    </operation>
  </portType>
  <binding name="ContactNotificationWSSoap11" type="tns:ContactNotificationWSPort">
    <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <operation name="FinishContactBusinessSummary">
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
  </binding>
  <service name="ContactNotificationWSService">
    <port binding="tns:ContactNotificationWSSoap11" name="ContactNotificationWSSoap11">
      <soap:address location="http://TO-BE-SPECIFIED/cml/ws/osbContactNotificationWS"/>
    </port>
  </service>
</definitions>