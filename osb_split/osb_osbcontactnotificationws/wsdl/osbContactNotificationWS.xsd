<?xml version="1.0" encoding="windows-1250" ?>
<xsd:schema targetNamespace="http://osb.airbank.cz/contact/notification" elementFormDefault="qualified"
            xmlns:comm="http://osb.airbank.cz/contact/common/dto" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <xsd:import schemaLocation="../xsd/Common.xsd" namespace="http://osb.airbank.cz/contact/common/dto"/>
  <xsd:element name="FinishContactBusinessSummaryResponse">
    <xsd:complexType/>
  </xsd:element>
  <xsd:element name="FinishContactBusinessSummaryRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="contactId" type="comm:ContactIdTO"/>
        <xsd:element name="employeeNumber" type="xsd:string">
          <xsd:annotation>
            <xsd:documentation>Number that identifies an employer</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
</xsd:schema>
