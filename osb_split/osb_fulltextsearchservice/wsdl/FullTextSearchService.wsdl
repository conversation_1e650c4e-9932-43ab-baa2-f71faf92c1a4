<wsdl:definitions name="FullTextSearchWS" targetNamespace="http://fts.airbank.cz/ws" xmlns:tns="http://fts.airbank.cz/ws" xmlns:inp1="http://fts.airbank.cz/ws" xmlns:inp2="http://fts.airbank.cz/ws" xmlns:plnk="http://docs.oasis-open.org/wsbpel/2.0/plnktype" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/">
  <plnk:partnerLinkType name="RestReference">
    <plnk:role name="RestReferenceProvider" portType="tns:FullTextSearchWS"/>
  </plnk:partnerLinkType>
  <wsdl:types>
    <xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema">
      <xsd:import namespace="http://fts.airbank.cz/ws" schemaLocation="../xsd/FullTextSearchService.xsd"/>
    </xsd:schema>
  </wsdl:types>
  <wsdl:message name="suggest_inputMessage">
    <wsdl:part name="request" element="tns:suggestRequest"/>
  </wsdl:message>
  <wsdl:message name="suggest_outputMessage">
    <wsdl:part name="reply" element="inp1:suggestResult"/>
  </wsdl:message>
  <wsdl:message name="search_inputMessage">
    <wsdl:part name="request" element="tns:searchRequest"/>
  </wsdl:message>
  <wsdl:message name="search_outputMessage">
    <wsdl:part name="reply" element="inp2:searchResult"/>
  </wsdl:message>
  <wsdl:message name="warmUp_inputMessage">
    <wsdl:part name="request" element="tns:warmUpRequest"/>
  </wsdl:message>
  <wsdl:message name="warmUp_outputMessage">
    <wsdl:part name="request" element="tns:warmUpResult"/>
  </wsdl:message>
  <wsdl:message name="update_inputMessage">
    <wsdl:part name="request" element="tns:updateRequest"/>
  </wsdl:message>
  <wsdl:message name="update_outputMessage">
    <wsdl:part name="replay" element="tns:updateResult"/>
  </wsdl:message>
  <wsdl:portType name="FullTextSearchWS">
    <wsdl:operation name="suggest">
      <wsdl:input message="tns:suggest_inputMessage"/>
      <wsdl:output message="tns:suggest_outputMessage"/>
    </wsdl:operation>
    <wsdl:operation name="search">
      <wsdl:input message="tns:search_inputMessage"/>
      <wsdl:output message="tns:search_outputMessage"/>
    </wsdl:operation>
    <wsdl:operation name="warmUp">
      <wsdl:input message="tns:warmUp_inputMessage"/>
      <wsdl:output message="tns:warmUp_outputMessage"/>
    </wsdl:operation>
    <wsdl:operation name="update">
      <wsdl:input message="tns:update_inputMessage"/>
      <wsdl:output message="tns:update_outputMessage"/>
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="FullTextSearchWSSOAP" type="tns:FullTextSearchWS">
    <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="suggest">
      <soap:operation soapAction="suggest"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="search">
      <soap:operation soapAction="search"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="warmUp">
      <soap:operation soapAction="warmUp"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="update">
      <soap:operation soapAction="update"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="FullTextSearchWS">
    <wsdl:port binding="tns:FullTextSearchWSSOAP" name="FullTextSearchWSSOAP">
      <soap:address location="http://fts01.np.ab:8000/"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>