<?xml version="1.0" encoding="UTF-8"?>
    <xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:jxb="http://java.sun.com/xml/ns/jaxb" targetNamespace="http://airbank.cz/osb/bisnode/subscription" xmlns="http://airbank.cz/osb/bisnode/subscription" jxb:version="2.1" elementFormDefault="qualified">

        <xsd:element name="SubscribeRequest">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="group_id" type="xsd:long" maxOccurs="1" minOccurs="1">
                        <xsd:annotation>
                            <xsd:documentation>group id</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="country" type="xsd:string" maxOccurs="1" minOccurs="1">
                        <xsd:annotation>
                            <xsd:documentation>country id</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="deleteExisting" type="xsd:boolean" maxOccurs="1" minOccurs="1">
                        <xsd:annotation>
                            <xsd:documentation>true, false</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="regNbrList" type="xsd:string" maxOccurs="unbounded" minOccurs="1">
                        <xsd:annotation>
                            <xsd:documentation>ICO</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>

        <xsd:element name="SubscribeResponse">
            <xsd:annotation>
                <xsd:documentation>Client's BisNode complex info</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType/>
        </xsd:element>

    <xsd:element name="UnsubscribeRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="group_id" type="xsd:long">
                    <xsd:annotation>
                        <xsd:documentation>group id</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="ent_id" type="xsd:long" maxOccurs="unbounded">
                    <xsd:annotation>
                        <xsd:documentation>entID from Get members method</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="UnsubscribeResponse">
        <xsd:annotation>
            <xsd:documentation>Client's BisNode complex info</xsd:documentation>
        </xsd:annotation>
        <xsd:complexType/>
    </xsd:element>

        <xsd:element name="GetMembersRequest">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="groupId" type="xsd:long">
                        <xsd:annotation>
                            <xsd:documentation>group id</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="firstRow" type="xsd:long">
                        <xsd:annotation>
                            <xsd:documentation>first row number</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="lastRow" type="xsd:long">
                        <xsd:annotation>
                            <xsd:documentation>last row number</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>

        <xsd:element name="GetMembersResponse">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="totalCount" type="xsd:long" />
                    <xsd:element name="members" type="Member" minOccurs="0" maxOccurs="unbounded" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>

        <xsd:complexType name="Member">
            <xsd:sequence>
                <xsd:element name="entId" type="xsd:long" />
                <xsd:element name="name">
                  <xsd:simpleType>
                    <xsd:restriction base="xsd:string">
                      <xsd:minLength value="1" />
                    </xsd:restriction>
                  </xsd:simpleType>
                </xsd:element>                                        
                <xsd:element name="regNbr">
                  <xsd:simpleType>
                    <xsd:restriction base="xsd:string">
                      <xsd:minLength value="1" />
                    </xsd:restriction>
                  </xsd:simpleType>
                </xsd:element>                                        
                <xsd:element name="address">
                  <xsd:simpleType>
                    <xsd:restriction base="xsd:string">
                      <xsd:minLength value="1" />
                    </xsd:restriction>
                  </xsd:simpleType>
                </xsd:element>                                        
            </xsd:sequence>
        </xsd:complexType>

    </xsd:schema>
