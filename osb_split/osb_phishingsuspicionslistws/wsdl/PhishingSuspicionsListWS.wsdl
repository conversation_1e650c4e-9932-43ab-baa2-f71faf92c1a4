<?xml version="1.0" encoding="UTF-8"?>
    <wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:phi="http://airbank.cz/osb/ws/phishingSuspicionsList" targetNamespace="http://airbank.cz/osb/ws/phishingSuspicionsList">

        <wsdl:types>
            <xsd:schema targetNamespace="http://airbank.cz/osb/ws/phishingSuspicionsList">
                <xsd:include schemaLocation="../xsd/PhishingSuspicionsListWS.xsd" />
            </xsd:schema>
        </wsdl:types>

        <wsdl:message name="GetIPsRequest">
            <wsdl:part element="phi:GetIPsRequest" name="GetIPsRequest" />
        </wsdl:message>
        <wsdl:message name="GetIPsResponse">
            <wsdl:part element="phi:GetIPsResponse" name="GetIPsResponse" />
        </wsdl:message>

        <wsdl:message name="GetApplicationsRequest">
            <wsdl:part element="phi:GetApplicationsRequest" name="GetApplicationsRequest" />
        </wsdl:message>
        <wsdl:message name="GetApplicationsResponse">
            <wsdl:part element="phi:GetApplicationsResponse" name="GetApplicationsResponse" />
        </wsdl:message>

        <wsdl:portType name="PhishingSuspicionsListWS">

            <wsdl:operation name="GetIPs">
                <wsdl:input message="phi:GetIPsRequest" />
                <wsdl:output message="phi:GetIPsResponse" />
            </wsdl:operation>

            <wsdl:operation name="GetApplications">
                <wsdl:input message="phi:GetApplicationsRequest" />
                <wsdl:output message="phi:GetApplicationsResponse" />
            </wsdl:operation>

        </wsdl:portType>


        <wsdl:binding name="PhishingSuspicionsListBinding" type="phi:PhishingSuspicionsListWS">
            <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />

            <wsdl:operation name="GetIPs">
                <soap:operation soapAction="" />
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
            </wsdl:operation>

            <wsdl:operation name="GetApplications">
                <soap:operation soapAction="" />
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
            </wsdl:operation>

        </wsdl:binding>

        <wsdl:service name="PhishingSuspicionsListWS">
            <wsdl:port binding="phi:PhishingSuspicionsListBinding" name="PhishingSuspicionsListBinding">
                <soap:address location="/lap/ext/SuspicionsListProcessWS" />
            </wsdl:port>
        </wsdl:service>
    </wsdl:definitions>
