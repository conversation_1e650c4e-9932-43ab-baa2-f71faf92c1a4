<?xml version="1.0" encoding="UTF-8"?>
<!-- $Id: a4015c90ffda6632c74d387ee46b6b5c818b39b1 $ -->
<xs:schema targetNamespace="http://osb.banka.hci/CodeList/Notification/" elementFormDefault="qualified"
	xmlns:xs="http://www.w3.org/2001/XMLSchema"
    xmlns:dto="http://osb.banka.hci/CodeList/Notification/data"
    xmlns:Q1="http://osb.banka.hci/CommonTypes">

    <xs:import namespace="http://osb.banka.hci/CodeList/Notification/data" schemaLocation="../xsd/CodeListNotification.xsd" />
    <xs:import namespace="http://osb.banka.hci/CommonTypes" schemaLocation="../xsd/CommonTypes.xsd" />

	<xs:element name="processCodeListNotification">
		<xs:complexType>
			<xs:sequence>
                <xs:element name="publisher" type="Q1:SystemCode" maxOccurs="1" minOccurs="1">
             				<xs:annotation>
             					<xs:documentation>
             						Code of the system which published the
             						CodeList.
             						</xs:documentation>
             				</xs:annotation>
             			</xs:element>
             			<xs:element maxOccurs="1" minOccurs="1" name="notification" type="dto:CodeListNotification">
             				<xs:annotation>
             					<xs:documentation>
             						Code list notification
             						</xs:documentation>
             				</xs:annotation>
             			</xs:element>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
<!--
    <xs:element name="processCodeListNotification">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="notification" type="dto:CodeListNotificationRequest" maxOccurs="1" minOccurs="1">
					<xs:annotation>
						<xs:documentation>Single code list update notification
						</xs:documentation>
					</xs:annotation>
				</xs:element>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
-->
	<xs:element name="processCodeListNotificationResponse">
		<xs:annotation>
			<xs:documentation>Empty response</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence />
		</xs:complexType>
	</xs:element>
</xs:schema>
