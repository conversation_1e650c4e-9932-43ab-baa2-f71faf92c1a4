<?xml version="1.0" encoding="UTF-8"?>
<!-- $Id: 7362c76dff8018714f7e3eb037089f58ee5d6812 $ -->
<wsdl:definitions name="CodeListChangeNotificationWS" targetNamespace="http://osb.banka.hci/CodeList/Notification/"
	xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tns="http://osb.banka.hci/CodeList/Notification/"
	xmlns:dto="http://osb.banka.hci/CodeList/Notification/data" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
	<wsdl:types>
		<xs:schema targetNamespace="http://osb.banka.hci/CodeList/Notification/" xmlns:xs="http://www.w3.org/2001/XMLSchema">
			<xs:include schemaLocation="CodeListNotificationWS.xsd" />
		</xs:schema>
	</wsdl:types>

	<wsdl:message name="processCodeListNotificationRequest">
		<wsdl:part name="processCodeListNotificationRequest" element="tns:processCodeListNotification" />
	</wsdl:message>
	<wsdl:message name="processCodeListNotificationResponse">
		<wsdl:part name="processCodeListNotificationResponse" element="tns:processCodeListNotificationResponse" />
	</wsdl:message>

	<wsdl:portType name="CodeListNotificationWS">
		<wsdl:documentation>
						Service updates master data consumers of a code list change
		</wsdl:documentation>

		<wsdl:operation name="processCodeListNotification">
			<wsdl:documentation>
								Processes master data update notification request
			</wsdl:documentation>
			<wsdl:input name="processCodeListNotificationRequest" message="tns:processCodeListNotificationRequest" />
			<wsdl:output name="processCodeListNotificationResponse" message="tns:processCodeListNotificationResponse" />
		</wsdl:operation>

	</wsdl:portType>

	<wsdl:binding name="CodeListNotificationWSSoap" type="tns:CodeListNotificationWS">
		<soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />
		<wsdl:operation name="processCodeListNotification">
			<soap:operation soapAction="" />
			<wsdl:input name="processCodeListNotificationRequest">
				<soap:body use="literal" />
			</wsdl:input>
			<wsdl:output name="processCodeListNotificationResponse">
				<soap:body use="literal" />
			</wsdl:output>
		</wsdl:operation>
	</wsdl:binding>

	<wsdl:service name="CodeListNotificationWSService">
		<wsdl:port binding="tns:CodeListNotificationWSSoap" name="CodeListNotificationWS">
			<soap:address location="LATER" />
		</wsdl:port>
	</wsdl:service>

</wsdl:definitions>
